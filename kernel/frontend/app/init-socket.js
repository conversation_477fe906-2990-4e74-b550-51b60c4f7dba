import _ from 'lodash';
import axios from 'axios';
import io from 'socket.io-client';
import socketio from 'framework/socket.io/client';
import * as socketParser from 'framework/socket.io/parsers/notepack';
import * as socketEJSONParser from 'framework/socket.io/parsers/ejson';

export default function (app) {
    const socket = io(`${app.absoluteUrl()}`, {
        path: '/ws/',
        transports: ['websocket'],
        upgrade: false,
        // parser: app.get('isProduction') ? socketParser : socketEJSONParser
        parser: socketParser
    });
    app.configure(
        socketio(socket, {
            timeout: 3 * 60 * 60 * 1000
        })
    );
    socket.io.on('reconnect', async () => {
        app.store.commit('shell/setIsOnline', {isOnline: true});

        if (app.get('isProduction')) {
            const response = await axios.get('/client-version');
            const clientVersion = response.data;

            if (app.get('clientVersion') !== clientVersion) {
                // localStorage.clear();
                localStorage.setItem('clientVersion', clientVersion);
                // window.location.reload();
            }
        }
    });
    socket.on('disconnect', async () => {
        app.store.commit('shell/setIsOnline', {isOnline: false});
    });

    if (app.get('isProduction')) {
        (function () {
            const nativeLog = window.console.log;
            const disallowedPhrases = [
                'socket.io-client',
                'engine.io-client',
                'feathers:application',
                '@feathersjs/transport-commons'
            ];

            window.console.log = function (message) {
                let showLog = true;

                if (typeof message === 'string') {
                    if (disallowedPhrases.some(p => message.indexOf(p) === -1)) {
                        showLog = false;
                    }
                }

                if (showLog) {
                    nativeLog.apply(window.console, arguments);
                }
            };
        })();
    }
}
