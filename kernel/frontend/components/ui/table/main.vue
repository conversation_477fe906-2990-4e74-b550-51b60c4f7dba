<template>
    <ag-grid-vue
        ref="ag-grid-vue"
        class="ui-table ag-theme-balham"
        :class="classes"
        :gridOptions="tableOptions"
        :columnDefs="tableColumns"
        :rowData="inMemoryData"
        @sortChanged="onSorted"
        @filterChanged="onFiltered"
        @rowSelected="onSelected"
        @rowClicked="onRowClicked"
        @rowDoubleClicked="onRowDoubleClicked"
        @cellValueChanged="onCellValueChanged"
        @modelUpdated="onModelUpdated"
        @rowGroupOpened="onRowGroupOpened"
        @rowGroupClosed="onRowGroupClosed"
        @animationQueueEmpty="onAnimationQueueEmpty"
        @columnResized="onColumnResizedDebounced"
        @columnVisible="onColumnChangedDebounced"
        @columnPinned="onColumnChangedDebounced"
        @columnMoved="onColumnChangedDebounced"
        @cellEditingStarted="onCellEditingStarted"
        @cellEditingStopped="onCellEditingStopped"
        @gridReady="onReady"
        v-resize="onGridSizeChanged"
        v-observe-visibility="onVisibilityChanged"
        :modules="allModules"
        v-if="isInitialized"
    />
</template>

<script>
import _ from 'lodash';
import fastCopy from 'fast-copy';
import $ from 'jquery';
import sift from 'sift';
import AgGridVue from 'ag-grid-vue';
import PerfectScrollbar from 'perfect-scrollbar';
import Random from 'framework/random';
import EJSON from 'framework/ejson';
import {
    traverse,
    template,
    rawMongoQuery,
    toLower,
    firstUpper,
    hash,
    copyToClipboard,
    stripHtml
} from 'framework/helpers';
import {cleanModel} from '../form/utils';
import ViewportDataSource from './libraries/ViewportDataSource';
import ServerSideDataSource from './libraries/ServerSideDataSource';
import {cleanFieldName, createMongoSearchQuery, prepareColumnsFromSchema} from './libraries/utils';
import ExportPopup from '../view/export-popup.vue';

// Renderer components
import BooleanRenderer from './renderers/boolean.vue';
import TagRenderer from './renderers/tags.vue';
import RelationRenderer from './renderers/relation.vue';
import ProgressRenderer from './renderers/progress.vue';
import PhoneRenderer from './renderers/phone.vue';
import ImageRenderer from './renderers/image.vue';
import AvatarRenderer from './renderers/avatar.vue';
import WorkflowApprovalStatusRenderer from './renderers/worklfow-approval-status.vue';

// Editor components
import TextEditor from './editors/text.vue';
import NumberEditor from './editors/number.vue';
import DateEditor from './editors/date.vue';
import TimeEditor from './editors/time.vue';
import SelectEditor from './editors/select.vue';
import AutocompleteEditor from './editors/autocomplete.vue';

// Custom components
import LoadingOverlay from './components/loading.vue';
import NoRowsOverlay from './components/empty.vue';

const LicenseManager = AgGridVue.LicenseManager;
LicenseManager.setLicenseKey('[TRIAL]_30_October_2019_[v2]_MTU3MjM5MzYwMDAwMA==e077364abc05b2a14e64d813d6e5f7b6');

// @TODO -> Problem on editable table. When data updated, editing cell loses focus.
export default {
    props: {
        id: String,

        // Data options.
        collection: String,
        columns: {
            type: Array,
            default() {
                return [];
            }
        },
        items: {
            type: [Array, Object],
            default() {
                return null;
            }
        },
        schema: {
            type: Object,
            default() {
                return {};
            }
        },
        sort: {
            type: Array,
            default() {
                return [];
            }
        },
        groupBy: {
            type: [String, Object, Array],
            default() {
                return [];
            }
        },
        filters: {
            type: Object,
            default() {
                return {};
            }
        },
        search: {
            type: String,
            default() {
                return '';
            }
        },
        searchText: String,
        enableTextSearch: Boolean,
        forceFullTextSearch: Boolean,
        extraFields: {
            type: Array,
            default() {
                return [];
            }
        },

        // Hooks.
        beforeInit: Function,
        beforeValidate: Function,
        beforeCreate: Function,
        beforeUpdate: Function,
        beforeRemove: Function,
        afterCreate: Function,
        afterUpdate: Function,
        afterRemove: Function,
        processResult: Function,

        // Table options
        options: {
            type: Object,
            default() {
                return {};
            }
        },
        loading: {
            type: Boolean,
            default: false
        },
        enableSorting: {
            type: Boolean,
            default: true
        },
        enableSelection: {
            type: Boolean,
            default: true
        },
        enableRowDeselection: Boolean,
        enableColumnResize: {
            type: Boolean,
            default: true
        },
        enableEditing: {
            type: Boolean,
            default: false
        },
        enableReadOnly: {
            type: Boolean,
            default: false
        },
        enableOrdering: {
            type: Boolean,
            default: false
        },
        enableSaveOrdering: {
            type: Boolean,
            default: true
        },
        enableRowHandle: {
            type: Boolean,
            default: true
        },
        enableAddRemove: {
            type: Boolean,
            default() {
                return this.enableEditing;
            }
        },
        enableEnlarge: {
            type: Boolean,
            default: false
        },
        enlargementDefault: {
            type: Boolean,
            default: false
        },
        enablePopupEdit: {
            type: Boolean,
            default: false
        },
        enableAutoHeight: Boolean,
        summaryRow: Function,
        sizeToFit: {
            type: Boolean,
            default: true
        },
        rowHeight: {
            type: Number,
            default: 24
        },
        rowModel: String,
        rowSelection: {
            type: [Boolean, String],
            default: 'multiple'
        },
        isRowSelectable: Function,
        singleSelect: Boolean,
        selectAgain: Boolean, // Emit selected even if row is already selected.
        multiSelectWithClick: Boolean,
        minEmptyRows: {
            type: Number,
            default: 1
        },
        getRows: Function,
        updateParams: Function,
        noZebra: {
            type: Boolean,
            default() {
                return this.enableEditing;
            }
        },
        contextMenuActions: Array,
        disableNoRowsOverlay: Boolean,
        detailView: String,
        viewHasExport: Boolean,
        disableHeaderNameNormalization: Boolean,
        disableHeaderNameTranslation: Boolean
    },

    data: () => ({
        inMemoryData: [],
        inMemorySearchFilters: {},
        isEnlarged: false,
        editingCell: null,
        isEditing: false,
        validationError: null,
        isInitialized: false
    }),

    computed: {
        api() {
            return this.tableOptions.api;
        },
        columnApi() {
            return this.tableOptions.columnApi;
        },
        tableId() {
            let id = null;

            if (!!this.id) {
                // const clientVersion = this.$app.get('clientVersion') || '';
                const clientVersion = '';

                id = clientVersion + '-' + this.id;

                if (!!this.groupBy) {
                    id += `-group-${JSON.stringify(fastCopy(this.rowGroups))}`;
                }
            }

            return id;
        },
        classes() {
            const options = this.options;
            const hasRowGroup = this.columns.some(column => column.rowGroup) || this.rowGroups.length > 0;
            let list = [];

            if (hasRowGroup) list.push('has-row-group');
            if (this.enableEditing) list.push('is-editable');
            if (this.enableEditing && !_.isNull(this.editingCell)) list.push('is-editing');
            if (this.calculatedEnableAutoHeight && !this.isEnlarged) list.push('auto-height');
            if (this.noZebra || !!options.treeData) list.push('no-zebra');
            if (this.calculatedRowModel === 'serverSide') list.push('has-server-side-row-model');

            if ((!this.calculatedEnableAutoHeight && !hasRowGroup && _.isEmpty(this.groupBy)) || this.enableEditing) {
                list.push('hide-loading-stub-row');
            }

            return list;
        },
        calculatedRowModel() {
            if (_.isString(this.rowModel)) return this.rowModel;

            return (!!this.collection || !!this.getRows) && !this.enableOrdering
                ? 'serverSide' // _.isFunction(this.getRows) ? 'serverSide' : 'viewport'
                : 'inMemory';
        },
        calculatedEnableAutoHeight() {
            if (_.isBoolean(this.enableAutoHeight)) return this.enableAutoHeight;

            return !(_.isString(this.collection) || _.isFunction(this.getRows));
        },
        collectionAttributes() {
            let attributes = [];

            if (_.isString(this.collection)) {
                const programName = this.collection.split('.')[0];
                const definition = fastCopy(
                    _.find(this.$shell.getProgram(programName).collections, {name: this.collection})
                );

                if (Array.isArray(definition.attributes)) {
                    attributes = definition.attributes;
                }
            }

            return attributes;
        },
        collectionPopulations() {
            let populations = [];

            if (_.isString(this.collection)) {
                const programName = this.collection.split('.')[0];
                const definition = fastCopy(
                    _.find(this.$shell.getProgram(programName).collections, {name: this.collection})
                );

                if (Array.isArray(definition.populations)) {
                    populations = definition.populations;
                }
            }

            return populations;
        },
        hasTextSearch() {
            if (!!this.enableTextSearch) {
                return true;
            }

            const programName = this.collection.split('.')[0];
            const definition = fastCopy(
                _.find(this.$shell.getProgram(programName).collections, {name: this.collection})
            );

            return this.findIndexes(definition.schema).filter(i => i.type === 'text').length > 0;
        },
        rowGroups() {
            const groupBy = this.groupBy;
            let rowGroups = [];

            if (_.isString(groupBy)) {
                rowGroups.push({
                    field: groupBy,
                    aggregate: {count: 'count'}
                });
            } else if (_.isPlainObject(groupBy)) {
                rowGroups.push({
                    field: groupBy.field,
                    aggregate: _.isPlainObject(groupBy.aggregate) ? groupBy.aggregate : {count: 'count'}
                });
            } else if (Array.isArray(groupBy)) {
                groupBy.forEach(gb => {
                    if (_.isString(gb)) {
                        rowGroups.push({
                            field: gb,
                            aggregate: {count: 'count'}
                        });
                    } else if (_.isPlainObject(gb)) {
                        rowGroups.push({
                            field: gb.field,
                            aggregate: _.isPlainObject(gb.aggregate) ? gb.aggregate : {count: 'count'}
                        });
                    }
                });
            }

            return rowGroups;
        }
    },

    watch: {
        async search(value, oldValue) {
            if (value !== oldValue) {
                if (!!this.applySearchDebounced) {
                    this.applySearchDebounced(value);
                } else {
                    this.applySearch(value);
                }

                // if (this.calculatedRowModel === 'viewport') {
                //     this.dataSource.applySearch(value, () => {
                //         this.$nextTick(() => {
                //             this.initializeSummaryRow();
                //
                //             this.scroller.goToTop();
                //         });
                //     });
                // } else if (this.calculatedRowModel === 'serverSide') {
                //     this.dataSource.applySearch(value);
                // } else if (this.calculatedRowModel === 'inMemory') {
                //     this.api.setQuickFilter(value);
                //
                //     this.$nextTick(() => {
                //         this.initializeSummaryRow(this.inMemoryData);
                //
                //         this.scroller.goToTop();
                //     });
                // }
            }
        },
        async searchText(value, oldValue) {
            if (_.isString(value) && value !== oldValue) {
                if (this.calculatedRowModel === 'viewport') {
                    this.dataSource.applySearch(
                        value,
                        () => {
                            this.$nextTick(() => {
                                this.initializeSummaryRow();

                                this.scroller.goToTop();
                            });
                        },
                        true
                    );
                } else if (this.calculatedRowModel === 'serverSide') {
                    this.dataSource.applySearch(value, _.noop, true);
                } else if (this.calculatedRowModel === 'inMemory') {
                    this.api.setQuickFilter(value);

                    this.$nextTick(() => {
                        this.initializeSummaryRow(this.inMemoryData);

                        this.scroller.goToTop();
                    });
                }
            }
        },
        async filters(value, oldValue) {
            if (!_.isEqual(value, oldValue || {})) {
                this.api.showLoadingOverlay();

                if (this.calculatedRowModel === 'viewport') {
                    this.dataSource.applyFilters(value, true, () => {
                        this.$nextTick(() => {
                            this.initializeSummaryRow();

                            this.scroller.goToTop();
                        });
                    });
                } else if (this.calculatedRowModel === 'serverSide') {
                    this.api.showLoadingOverlay();

                    this.dataSource.applyFilters(value, true);
                } else if (this.calculatedRowModel === 'inMemory') {
                    if (this.collection) {
                        await this.initializeInMemoryData();
                    }

                    this.api.setFilterModel(null);
                    this.api.onFilterChanged();

                    this.$nextTick(() => {
                        this.initializeSummaryRow(this.inMemoryData);

                        this.api.hideOverlay();
                    });
                }
            }
        },
        loading(value) {
            if (value) {
                this.api.showLoadingOverlay();
            } else {
                this.api.hideOverlay();
            }
        },
        enableReadOnly(value, oldValue) {
            if (value !== oldValue) {
                this.api.clearFocusedCell();
                this.api.refreshHeader();
                this.inMemoryData = fastCopy(this.inMemoryData);

                this.$nextTick(() => {
                    if (!value && this.enableEditing) {
                        this.initializeEmptyRows();
                    }

                    // this.api.redrawRows();
                });
            }
        },
        rowGroups(value, oldValue) {
            if (this.calculatedRowModel === 'serverSide') {
                if (!_.isEqual(value, oldValue || [])) {
                    this.api.showLoadingOverlay();

                    const columns = this.tableColumns;
                    const columnApi = this.tableOptions.columnApi;
                    const fields = value.map(v => v.field);

                    columnApi.setColumnsVisible(
                        columns.filter(c => c.hide),
                        true
                    );
                    columnApi.setColumnsVisible(fields, false);

                    columnApi.setRowGroupColumns(fields);

                    this.tableOptions.api.sizeColumnsToFit();
                }
            }
        }
    },

    methods: {
        // Api
        refreshData(callback) {
            if (this.disableRefresh) return;

            if (this.calculatedRowModel === 'viewport' || this.calculatedRowModel === 'serverSide') {
                this.dataSource.refreshData(() => {
                    this.$emit('refreshed');

                    if (_.isFunction(callback)) {
                        callback();
                    }

                    setTimeout(() => {
                        this.api.redrawRows();
                    }, 150);
                });
            } else if (this.calculatedRowModel === 'inMemory' && this.collection) {
                this.initializeInMemoryData().then(() => {
                    this.$emit('refreshed');

                    if (_.isFunction(callback)) {
                        callback();
                    }

                    setTimeout(() => {
                        this.api.redrawRows();
                    }, 150);
                });
            } else if (this.calculatedRowModel === 'inMemory') {
                this.inMemoryData = fastCopy(this.inMemoryData);

                this.$emit('refreshed');

                if (_.isFunction(callback)) {
                    callback();
                }
            }
        },

        // Events
        onSorted() {
            if (this.calculatedRowModel === 'viewport' || this.calculatedRowModel === 'serverSide') {
                const columnApi = this.tableOptions.columnApi;
                const sort = columnApi
                    .getColumnState()
                    .map(state => {
                        if (_.isString(state.sort)) {
                            let item = {};

                            item.field = state.colId;
                            item.sort = state.sort;

                            return item;
                        }

                        return null;
                    })
                    .filter(sort => _.isPlainObject(sort));

                if (this.calculatedRowModel === 'serverSide') {
                    this.api.showLoadingOverlay();
                }

                this.dataSource.applySort(sort);
            }
        },
        onFiltered() {
            if (this.calculatedRowModel === 'viewport' || this.calculatedRowModel === 'serverSide') {
                this.dataSource.applyFilters(fastCopy(this.api.getFilterModel()), false);
            }
        },
        onModelUpdated() {
            if (!this.enableEditing) {
                this.scroller.update();
            }

            if (this.enableEditing && _.isObject(this.editingCell) && !_.isEmpty(this.inMemoryData)) {
                this.api.startEditingCell(this.editingCell);
            }
        },
        onGridSizeChanged() {
            if (this.sizeToFit && this.$refs['ag-grid-vue'].$el.clientWidth > 0) {
                this.initializeColumnSizes();
            }

            this.scroller.update();

            if (this.enableEditing) {
                this.$nextTick(() => {
                    this.initializeEmptyRows();
                });
            }
        },
        onVisibilityChanged(isVisible) {
            if (isVisible && this.initialVisibility !== isVisible && this.enableEditing) {
                this.$nextTick(() => {
                    this.initializeEmptyRows();
                });
            }

            this.initialVisibility = isVisible;
        },
        onAnimationQueueEmpty() {
            this.scroller.update();
        },
        onReady() {
            // Load columns state.
            if (!!this.tableId) {
                const columnApi = this.tableOptions.columnApi;
                const key = `grid-columns-sate-${this.tableId}`;
                const state = this.$registry(key);

                if (!!state) {
                    columnApi.setColumnState(state);
                }
            }

            if (this.sizeToFit && this.$refs['ag-grid-vue'].$el.clientWidth > 0) {
                this.initializeColumnSizes();
            }

            if (!this.enableEditing) {
                this.$nextTick(this.scroller.update);
            }

            if (this.loading) {
                this.api.showLoadingOverlay();
            }

            if (
                (this.calculatedRowModel === 'viewport' || this.calculatedRowModel === 'serverSide') &&
                this.dataSource
            ) {
                this.api.showLoadingOverlay();
            }

            setTimeout(() => {
                this.gridInitialized = true;
            }, 1000);
        },
        onColumnResized() {
            this.scroller.update();
        },
        onColumnChanged() {
            this.scroller.update();

            // Persist columns
            if (this.gridInitialized && !!this.tableId) {
                const columnApi = this.tableOptions.columnApi;
                const key = `grid-columns-sate-${this.tableId}`;
                const state = columnApi.getColumnState().map(s => _.omit(s, ['width']));

                this.$registry(key, state);
            }
        },
        onSelected() {
            if (this.selectionInProgress) return;
            this.selectionInProgress = true;

            if (!this.selectAgain) {
                this.$emit('selected', this.api.getSelectedRows());
            }

            this.$nextTick(() => {
                this.selectionInProgress = false;
            });
        },
        onRowClicked(params) {
            this.api.lastEditedCell = null;

            this.$nextTick(() => {
                const selected = this.api.getSelectedRows();

                if (this.selectAgain && Array.isArray(selected) && selected.length > 0) {
                    this.$emit('selected', selected);
                }
            });

            this.$emit('row-clicked', params.data, params);
        },
        onRowDoubleClicked(params) {
            if (params.node.selectable) {
                this.$emit('double-clicked', params.data);

                //this.api.deselectAll();
            }
        },
        onRowGroupOpened() {
            this.$nextTick(this.scroller.update);
        },
        onRowGroupClosed() {
            this.$nextTick(this.scroller.update);
        },
        onCellEditingStarted(node) {
            this.editingCell = {rowIndex: node.rowIndex, colKey: node.column.colId};

            this.api.lastEditedCell = {
                rowIndex: node.rowIndex,
                colKey: node.column.colId
            };
        },
        onCellEditingStopped(node) {},
        async onCellValueChanged(params) {
            this.isEditing = true;
            this.updatingRecord = true;

            // Check if value changed.
            if (params.newValue === params.oldValue) {
                // this.api.redrawRows({rowNodes: [params.node]});
                this.api.hideOverlay();
                this.editingCell = null;
                this.isEditing = false;
                this.updatingRecord = false;

                return;
            }

            // Start loading.
            this.api.showLoadingOverlay();
            this.editingCell = null;
            this.validationError = null;

            // Get schema, id, row and field.
            const schema = {};
            for (const key of Object.keys(this.schema)) {
                const s = fastCopy(this.schema[key]);

                s.required = false;

                schema[key] = s;
            }
            const id = params.data._id;
            const field = params.colDef.field;
            let row = fastCopy(_.omit(params.data, '_id'));

            // Turn empty strings to null.
            // row = traverse(row).map(function(x) {
            //     if (typeof x === 'string' && x.trim() === '') {
            //         this.update(null, false);
            //     }
            // });
            const fieldDefinition = _.get(schema, field);
            const fieldValue = _.get(row, field);
            if (
                !!fieldDefinition &&
                _.isString(fieldDefinition.type) &&
                (fieldDefinition.type === 'decimal' ||
                    fieldDefinition.type === 'integer' ||
                    fieldDefinition.type === 'number') &&
                _.isNumber(fieldDefinition.default) &&
                _.isString(fieldValue) &&
                fieldValue.trim() === ''
            ) {
                _.set(row, field, fieldDefinition.default);
            }

            // Find the operation.
            const operation = _.isUndefined(id) ? 'create' : 'patch';

            // Clean data.
            const cleanOptions = {
                filter: true,
                autoConvert: true,
                removeEmptyStrings: false,
                trimStrings: true,
                getAutoValues: true,
                removeNullsFromArrays: true
            };
            let cleaned, validation, validationOptions, result;
            if (operation === 'patch') {
                cleaned = await this.$app.schema.clean(schema, row, cleanOptions);
                // console.log(cleaned);
                validationOptions = {};
            } else {
                cleaned = await this.$app.schema.clean(schema, row, cleanOptions);
                validationOptions = {};
            }

            if (operation === 'patch') {
                let data = {};

                // Normalize data.
                row = cleaned;
                data = row;

                if (_.isEmpty(data)) {
                    this.api.hideOverlay();
                    this.isEditing = false;
                    this.updatingRecord = false;

                    return;
                }

                // Call before update hook.
                const beforeUpdateHookResult = await this.callHook('beforeUpdate', {
                    row: _.assign(fastCopy(data), {_id: id}),
                    originalRow: _.assign(fastCopy(data), {_id: id}, {[field]: params.oldValue}),
                    params
                });

                if (beforeUpdateHookResult.isOk) {
                    if (_.isObject(beforeUpdateHookResult) && !_.isEmpty(beforeUpdateHookResult.result)) {
                        row = beforeUpdateHookResult.result;
                    }

                    await this.inMemoryCollection.patch({_id: id}, fastCopy(row));

                    result = fastCopy(await this.inMemoryCollection.find());

                    if (_.isFunction(this.processResult)) {
                        await this.inMemoryCollection.remove({});
                        result = await this.processResult(result);

                        result = result.map(item => {
                            if (!_.isString(item._id) || !item._id) {
                                item._id = Random.id(8);
                            }

                            return item;
                        });

                        await this.inMemoryCollection.create(result);
                    }

                    this.initializeSelectEditorMap(result);

                    params.node.setData(_.assign(fastCopy(_.omit(params.data, '_id')), row));

                    this.$emit('changed', result, params);
                    this.$emit('updated', row, result);
                    await this.callHook('afterUpdate', {row, result, params});

                    this.$nextTick(() => {
                        this.initializeSummaryRow(result);
                    });
                } else {
                    const error = beforeUpdateHookResult.result;

                    this.$emit('error', error.message, field);
                }
            } else {
                // Editable table needs ids.
                cleaned._id = Random.id(8);

                // Call before create hook.
                const beforeCreateHookResult = await this.callHook('beforeCreate', {
                    row: cleaned,
                    originalRow: _.assign(fastCopy(cleaned), {[field]: params.oldValue}),
                    params
                });

                if (beforeCreateHookResult.isOk) {
                    if (_.isObject(beforeCreateHookResult) && !_.isEmpty(beforeCreateHookResult.result)) {
                        row = beforeCreateHookResult.result;
                    }

                    await this.inMemoryCollection.create(fastCopy(row));

                    result = fastCopy(await this.inMemoryCollection.find());

                    if (_.isFunction(this.processResult)) {
                        await this.inMemoryCollection.remove({});
                        result = await this.processResult(result);

                        result = result.map(item => {
                            if (!_.isString(item._id) || !item._id) {
                                item._id = Random.id(8);
                            }

                            return item;
                        });

                        await this.inMemoryCollection.create(result);
                    }

                    this.initializeSelectEditorMap(result);

                    params.node.setData(_.assign(fastCopy(_.omit(params.data, '_id')), row));

                    this.$emit('changed', result, params);
                    this.$emit('created', row, result);
                    await this.callHook('afterCreate', {row, result, params});

                    this.$nextTick(() => {
                        this.initializeSummaryRow(result);
                    });
                } else {
                    const error = beforeCreateHookResult.result;

                    this.$emit('error', error.message, field);
                }
            }

            // Call before validate hook.
            const beforeValidateHookResult = await this.callHook('beforeValidate', {row: cleaned, params});
            if (beforeValidateHookResult.isOk) {
                // Validate row.
                validation = await this.$app.schema.validate(
                    schema,
                    beforeValidateHookResult.result,
                    validationOptions
                );

                if (!validation.isValid) {
                    let validationError = validation.errors.find(error => error.field === field);

                    if (_.isObject(validationError)) {
                        validationError.rowIndex = params.node.rowIndex;
                        this.validationError = validationError;
                        this.$emit('error', this.validationError.message, field);
                    }
                }
            } else {
                const error = beforeValidateHookResult.result;

                this.$emit('error', error.message, field);
            }

            this.inMemoryData = result;

            this.$nextTick(() => {
                this.initializeEmptyRows();
                this.api.hideOverlay();

                this.$nextTick(() => {
                    setTimeout(() => {
                        this.api.selectIndex(params.node.rowIndex, false);

                        this.isEditing = false;
                    }, 25);

                    setTimeout(() => {
                        if (
                            !!this.api.lastEditedCell &&
                            this.api.lastEditedCell.rowIndex === params.node.rowIndex &&
                            this.api.lastEditedCell.colKey === field
                        ) {
                            if (!!this.validationError) {
                                this.api.lastEditedCell = null;
                            }

                            this.api.startEditingCell({
                                rowIndex: params.node.rowIndex,
                                colKey: field
                            });
                        }

                        this.updatingRecord = false;
                    }, 75);
                });
            });
        },
        async onRowRemoved(params) {
            if (!!params.data && (!!params.data._fakeItem || !params.data._id)) {
                return;
            }
            if (!this.enableAddRemove) return;

            if (!this.isRemovingItem) {
                this.isRemovingItem = true;
            } else {
                return;
            }

            this.editingCell = null;
            this.api.lastEditedCell = null;
            this.api.stopEditing({cancel: true});

            this.api.showLoadingOverlay();

            if (_.isFunction(this.beforeRemove) && !this.enableReadOnly) {
                let error = null;

                try {
                    error = await this.beforeRemove(params.data, params);
                } catch (e) {
                    error = e.message;
                }

                if (_.isString(error) && error) {
                    this.$program.message('error', error);

                    this.api.hideOverlay();
                    this.isRemovingItem = false;

                    return;
                }
            }

            if (!this.enableReadOnly) {
                const data = params.node.data;

                if (_.isObject(this.validationError) && params.node.rowIndex === this.validationError.rowIndex) {
                    this.validationError = null;
                }

                if (_.isObject(data) && data._id) {
                    await this.inMemoryCollection.remove(data._id);

                    let result = fastCopy(await this.inMemoryCollection.find());

                    if (_.isFunction(this.processResult)) {
                        await this.inMemoryCollection.remove({});
                        result = await this.processResult(result);

                        result = result.map(item => {
                            if (!_.isString(item._id) || !item._id) {
                                item._id = Random.id(8);
                            }

                            return item;
                        });

                        if (result.length > 0) {
                            await this.inMemoryCollection.create(result);
                        }
                    }

                    // Clear select map.
                    this.selectEditorMap = [];
                    this.initializeSelectEditorMap(fastCopy(result));

                    this.$emit('changed', result, params);

                    this.api.applyTransaction({remove: [data]});

                    if (_.isFunction(this.afterRemove)) {
                        await this.afterRemove({row: params.data, result, params});
                    }

                    this.inMemoryData = result;

                    this.$nextTick(async () => {
                        this.initializeEmptyRows();

                        await this.initializeSummaryRow(result);

                        this.api.hideOverlay();
                        this.isRemovingItem = false;
                    });
                } else {
                    this.api.hideOverlay();
                    this.isRemovingItem = false;
                }
            } else {
                this.api.hideOverlay();
                this.isRemovingItem = false;
            }
        },
        onRowDragMove(event) {
            let movingNode = event.node;
            let overNode = event.overNode;
            let rowNeedsToMove = movingNode !== overNode;

            if (rowNeedsToMove && _.isObject(movingNode) && _.isObject(overNode)) {
                // The list of rows we have is data,
                // not row nodes, so extract the data
                let movingData = movingNode.data;
                let overData = overNode.data;

                // Move element in array.
                let fromIndex = this.inMemoryData.indexOf(movingData);
                let toIndex = this.inMemoryData.indexOf(overData);
                let newStore = fastCopy(this.inMemoryData);
                let element = newStore[fromIndex];
                newStore.splice(fromIndex, 1);
                newStore.splice(toIndex, 0, element);
                newStore = newStore.map((item, index) => {
                    item.order = index + 1;

                    return item;
                });

                // Update store.
                this.inMemoryData = newStore;
                this.api.setRowData(newStore);
                this.api.clearFocusedCell();
            }
        },
        async onRowDragEnd() {
            // Save changes.
            if (this.collection) {
                let payload = this.inMemoryData.map(item => {
                    return {
                        _id: item._id,
                        order: item.order
                    };
                });

                if (this.enableSaveOrdering) {
                    this.disableRefresh = true;
                    await this.$rpc('kernel.component.order', {collection: this.collection, payload});
                    this.disableRefresh = false;

                    this.$nextTick(() => {
                        this.$emit('ordered');
                    });
                } else {
                    this.$emit('ordered', fastCopy(this.inMemoryData));
                }
            } else {
                this.$nextTick(() => {
                    this.$emit('ordered', fastCopy(this.inMemoryData));
                });
            }
        },
        async onRowPopupEdit(gridParams) {
            if (!!gridParams.data && (!!gridParams.data._fakeItem || !gridParams.data._id)) {
                return;
            }
            if (!this.detailView) return;

            this.api.lastEditedCell = null;

            const self = this;
            const column = this.tableOptions.columnApi.getColumn('popup-edit-cell');
            const id = gridParams.data._id;
            let params = {};

            params.id = id;
            params.forcedPreview = this.enableReadOnly;
            params.model = fastCopy(gridParams.data);

            if (_.isFunction(this.updateParams)) {
                params = await this.updateParams(params, 'detail', gridParams.data);
            }

            this.$program.dialog({
                component: this.detailView,
                params,
                async onSubmit(data) {
                    const originalData = fastCopy(gridParams.data);

                    await cleanModel(self.$app, self.schema, originalData, data);

                    const preparedParams = gridParams;
                    preparedParams.colDef = column.colDef;
                    preparedParams.oldValue = null;
                    preparedParams.newValue = 'changed';
                    preparedParams.data = _.assign(originalData, {_id: gridParams.data._id});

                    self.onCellValueChanged(preparedParams, true);
                }
            });

            return false;
        },

        // Internal
        async callHook(hook, payload, callback = _.noop) {
            let hasSet = _.isPlainObject(payload.row.$set);
            let result = null;
            let isOk = true;

            payload.row = hasSet ? payload.row.$set : payload.row;

            if (_.isFunction(this[hook])) {
                try {
                    result = await this[hook](payload);
                    result = hasSet ? {$set: result} : result;

                    // if (hook.indexOf('after') === 0) {
                    //     this.$nextTick(() => {
                    //         this.api.redrawRows();
                    //     });
                    // }
                } catch (error) {
                    console.error(error);

                    isOk = false;
                    result = error;
                }
            } else {
                result = hasSet ? {$set: payload.row} : payload.row;
            }

            return {isOk, result};
        },
        initializeDefaults() {
            const self = this;

            // Set default enlargement.
            this.isEnlarged = this.enlargementDefault;

            // Prepare default options.
            this.tableOptions = _.defaultsDeep(fastCopy(this.options), {
                rowHeight: this.rowHeight,
                rowMultiSelectWithClick: !!this.multiSelectWithClick,
                suppressRowDeselection: false,
                immutableData: !this.enableEditing && this.calculatedRowModel === 'inMemory',
                suppressMakeColumnVisibleAfterUnGroup: true,
                localeText: {
                    loadingOoo: this.$t('Loading..'),
                    filterOoo: this.$t('Filter..'),
                    noRowsToShow: this.$t('No rows to show.'),
                    pinColumn: this.$t('Pin column'),
                    autosizeThiscolumn: this.$t('Auto-size this column'),
                    autosizeAllColumns: this.$t('Auto-size all columns'),
                    resetColumns: this.$t('Reset columns'),
                    pinLeft: this.$t('Pin left'),
                    pinRight: this.$t('Pin right'),
                    noPin: this.$t('No pin')
                },
                enableCellChangeFlash: false,
                suppressMenuHide: true,
                suppressDragLeaveHidesColumns: true,
                // suppressContextMenu: true,
                suppressCellSelection: true,
                suppressColumnMoveAnimation: true,
                animateRows: false,
                accentedSort: true,
                // ensureDomOrder: true,
                viewportRowModelPageSize: 10,
                viewportRowModelBufferSize: 30,
                scrollbarWidth: 0,
                suppressAnimationFrame: true,
                suppressScrollOnNewData: true,
                suppressAggFuncInHeader: true,
                icons: {
                    menu: '<i class="far fa-bars"/>',
                    filter: '<i class="far fa-filter"/>',
                    columns: '<i class="far fa-bars" style="transform: rotate(90deg);"/>',
                    sortAscending: '<i class="far fa-arrow-down"/>',
                    sortDescending: '<i class="far fa-arrow-up"/>'
                },
                getMainMenuItems(params) {
                    let items = params.defaultItems;

                    items.splice(items.indexOf('toolPanel'), 1);
                    items.splice(items.indexOf('resetColumns'), 1);
                    items.push({
                        name: self.$t('Reset columns'),
                        action() {
                            if (!!self.tableId) {
                                const columnApi = self.tableOptions.columnApi;
                                const key = `grid-columns-sate-${self.tableId}`;

                                self.$registry(key, null);
                                columnApi.resetColumnState();

                                if (self.sizeToFit && self.$refs['ag-grid-vue'].$el.clientWidth > 0) {
                                    self.initializeColumnSizes();
                                }

                                if (!self.enableEditing) {
                                    self.$nextTick(self.scroller.update);
                                }
                            }
                        }
                    });

                    return items;
                },
                popupParent: document.querySelector('body')
            });

            if (!this.tableOptions.suppressContextMenu) {
                this.tableOptions.getContextMenuItems = params => {
                    const data = (params.node || {}).data || {};
                    const items = [];
                    const getColumnCopyValue = (column, value) => {
                        let copyValue = value;

                        if (!!column.relationParams) {
                            const relationParams = column.relationParams({...params, data});

                            if (_.isPlainObject(relationParams)) {
                                if (!!relationParams.template) {
                                    copyValue = template(relationParams.template, value);
                                } else if (!_.isUndefined(relationParams.customLabel)) {
                                    copyValue = _.get(value, relationParams.customLabel);
                                }
                            }
                        } else if (!!column.tagLabels) {
                            const tagLabel = column.tagLabels.find(tl => tl.value === value);

                            if (!!tagLabel) {
                                copyValue = !!column.translateLabels ? this.$t(tagLabel.label) : tagLabel.label;
                            }
                        } else if (!!column.valueLabels) {
                            const valueLabel = column.valueLabels.find(vl => vl.value === value);

                            if (!!valueLabel) {
                                copyValue = column.translateLabels ? this.$t(valueLabel.label) : valueLabel.label;
                            }
                        } else if (!!column.render) {
                            copyValue = column.render({...params, data});
                        } else if (!!column.editor) {
                            if (!!column.populate) {
                                const population = data[column.populate];
                                const labelFrom = !!column.editor.labelFrom ? column.editor.labelFrom : 'name';

                                if (!!population) {
                                    copyValue = population[labelFrom];
                                }
                            }
                        }

                        return copyValue;
                    };

                    let columns = this.columns;
                    if (this.enableEditing) {
                        columns = prepareColumnsFromSchema(fastCopy(this.schema));
                    }

                    if (Array.isArray(this.contextMenuActions) && this.contextMenuActions.length > 0) {
                        for (const action of this.contextMenuActions) {
                            let disabled = !!action.disabled;
                            if (_.isFunction(action.disabled)) {
                                disabled = action.disabled(data, params);
                            }

                            items.push({
                                name: firstUpper(toLower(action.title)),
                                ...(!!action.icon ? {icon: `<i class="fal fa-${action.icon}"></i>`} : {}),
                                disabled,
                                action: () => action.action(data, params)
                            });
                        }

                        items.push('separator');
                    }

                    // Copy.
                    items.push({
                        name: this.$t('Copy'),
                        icon: '<i class="fal fa-copy"></i>',
                        action: () => {
                            const column = columns.find(c => c.field === params.column.colDef.field);

                            if (!!column && !column.hidden) {
                                copyToClipboard(getColumnCopyValue(column, params.value));
                            }
                        }
                    });
                    if (this.enableEditing && !!data && !data._fakeItem) {
                        items.push({
                            name: this.$t('Copy row'),
                            icon: '<i class="fal fa-clone"></i>',
                            action: () => {
                                copyToClipboard(EJSON.stringify(_.omit(data, ['_id', 'id'])));
                            }
                        });
                    }
                    items.push({
                        name: this.$t('Copy row for excel'),
                        icon: '<i class="fal fa-clone"></i>',
                        action: () => {
                            const values = [];

                            for (const displayedColumn of params.columnApi.columnController.allDisplayedColumns) {
                                const column = columns.find(c => c.field === displayedColumn.colDef.field);
                                if (!!column && !column.hidden) {
                                    const value = _.get(data, column.field);

                                    if (!_.isUndefined(value)) {
                                        values.push(getColumnCopyValue(column, value));
                                    }
                                }
                            }

                            copyToClipboard(values.join('\t'));
                        }
                    });

                    // Paste.
                    if (this.enableEditing) {
                        items.push({
                            name: this.$t('Paste row'),
                            icon: '<i class="fal fa-paste"></i>',
                            action: async () => {
                                let newData = null;
                                try {
                                    const text = await navigator.clipboard.readText();

                                    newData = EJSON.parse(text);
                                } catch (error) {}

                                if (_.isPlainObject(newData)) {
                                    const preparedParams = params;
                                    preparedParams.colDef = {
                                        ...params.column.colDef,
                                        field: '_id'
                                    };
                                    preparedParams.oldValue = null;
                                    preparedParams.newValue = 'changed';
                                    preparedParams.data = newData;

                                    self.onCellValueChanged(preparedParams, true);
                                }
                            }
                        });
                    }

                    if (this.enableEditing && !!data && !data._fakeItem) {
                        if (this.minEmptyRows > 0) {
                            items.push({
                                name: this.$t('Add a row above'),
                                icon: '<i class="fal fa-arrow-to-top"></i>',
                                disabled: this.enableReadOnly,
                                action: async () => {
                                    const result = fastCopy(await this.inMemoryCollection.find());
                                    const index = result.findIndex(r => r._id === data._id);

                                    if (index === -1) {
                                        return;
                                    }

                                    const row = {_id: Random.id(8)};

                                    await this.inMemoryCollection.remove({});

                                    result.splice(index, 0, row);

                                    await this.inMemoryCollection.create(result);

                                    this.selectEditorMap = [];
                                    this.initializeSelectEditorMap(result);

                                    this.$nextTick(() => {
                                        this.api.applyTransaction({
                                            addIndex: index,
                                            add: [row]
                                        });

                                        this.$nextTick(() => {
                                            this.initializeEmptyRows();

                                            this.$nextTick(self.scroller.update);
                                        });
                                    });
                                }
                            });
                            items.push({
                                name: this.$t('Add a row below'),
                                icon: '<i class="fal fa-arrow-to-bottom"></i>',
                                disabled: this.enableReadOnly,
                                action: async () => {
                                    const result = fastCopy(await this.inMemoryCollection.find());
                                    const index = result.findIndex(r => r._id === data._id);

                                    if (index === -1) {
                                        return;
                                    }

                                    const row = {_id: Random.id(8)};

                                    await this.inMemoryCollection.remove({});

                                    result.splice(index + 1, 0, row);

                                    await this.inMemoryCollection.create(result);

                                    this.selectEditorMap = [];
                                    this.initializeSelectEditorMap(result);

                                    this.$nextTick(() => {
                                        this.api.applyTransaction({
                                            addIndex: index + 1,
                                            add: [row]
                                        });

                                        this.$nextTick(() => {
                                            this.initializeEmptyRows();

                                            this.$nextTick(self.scroller.update);
                                        });
                                    });
                                }
                            });
                        }

                        items.push({
                            name: this.$t('Move up'),
                            icon: '<i class="fal fa-arrow-up"></i>',
                            disabled: this.enableReadOnly,
                            action: async () => {
                                let result = fastCopy(await this.inMemoryCollection.find());
                                const index = result.findIndex(r => r._id === data._id);

                                if (index < 1) {
                                    return;
                                }

                                await this.inMemoryCollection.remove({});

                                arrayMove(result, index, index - 1);

                                await this.inMemoryCollection.create(result);

                                this.selectEditorMap = [];
                                this.initializeSelectEditorMap(result);

                                this.$emit('changed', result, params);

                                this.$nextTick(() => {
                                    const rowData = [];
                                    this.api.forEachNode(node => {
                                        rowData.push(node.data);
                                    });
                                    this.api.applyTransaction({
                                        remove: rowData
                                    });
                                    this.api.applyTransaction({
                                        add: result
                                    });

                                    this.$nextTick(() => {
                                        this.initializeEmptyRows();

                                        this.$nextTick(self.scroller.update);
                                    });
                                });
                            }
                        });
                        items.push({
                            name: this.$t('Move down'),
                            icon: '<i class="fal fa-arrow-down"></i>',
                            disabled: this.enableReadOnly,
                            action: async () => {
                                let result = fastCopy(await this.inMemoryCollection.find());
                                const index = result.findIndex(r => r._id === data._id);

                                if (index === -1 || index === result.length - 1) {
                                    return;
                                }

                                await this.inMemoryCollection.remove({});

                                arrayMove(result, index, index + 1);

                                await this.inMemoryCollection.create(result);

                                this.selectEditorMap = [];
                                this.initializeSelectEditorMap(result);

                                this.$emit('changed', result, params);

                                this.$nextTick(() => {
                                    const rowData = [];
                                    this.api.forEachNode(node => {
                                        rowData.push(node.data);
                                    });
                                    this.api.applyTransaction({
                                        remove: rowData
                                    });
                                    this.api.applyTransaction({
                                        add: result
                                    });

                                    this.$nextTick(() => {
                                        this.initializeEmptyRows();

                                        this.$nextTick(self.scroller.update);
                                    });
                                });
                            }
                        });

                        function arrayMove(arr, fromIndex, toIndex) {
                            let element = arr[fromIndex];

                            arr.splice(fromIndex, 1);
                            arr.splice(toIndex, 0, element);
                        }
                    }

                    // Column functions.
                    items.push('autoSizeAll');
                    items.push({
                        name: this.$t('Reset columns'),
                        action() {
                            if (!!self.tableId) {
                                const columnApi = self.tableOptions.columnApi;
                                const key = `grid-columns-sate-${self.tableId}`;

                                self.$registry(key, null);
                                columnApi.resetColumnState();

                                if (self.sizeToFit && self.$refs['ag-grid-vue'].$el.clientWidth > 0) {
                                    self.initializeColumnSizes();
                                }

                                if (!self.enableEditing) {
                                    self.$nextTick(self.scroller.update);
                                }
                            }
                        }
                    });

                    // Export.
                    items.push({
                        name: firstUpper(toLower(this.$t('Export'))),
                        icon: '<i class="fal fa-cloud-download"></i>',
                        disabled:
                            (!this.viewHasExport &&
                                (!this.id ||
                                    // !!this.enableEditing ||
                                    (this.columns.some(column => column.rowGroup) && !_.isEmpty(this.groupBy)) ||
                                    !!this.tableOptions.treeData)) ||
                            !!this.tableOptions.groupSuppressAutoColumn,
                        action: () => {
                            this.exportRows(params);
                        }
                    });

                    // console.log(params);
                    return items;
                };
            }

            // if (/(safari|chrome)/.test(navigator.userAgent.toLowerCase())) {
            //     this.tableOptions.scrollbarWidth = 10;
            // }

            if (!this.enableEditing && _.isUndefined(this.tableOptions.getRowNodeId)) {
                this.tableOptions.getRowNodeId = data => data._id;
            }

            if (
                (!this.calculatedEnableAutoHeight &&
                    !(this.columns.some(column => column.rowGroup) && !!this.collection) &&
                    _.isEmpty(this.groupBy)) ||
                this.enableEditing
            ) {
                this.tableOptions.loadingOverlayComponentFramework = LoadingOverlay;
            }

            if (!this.enableEditing && !this.calculatedEnableAutoHeight && !this.disableNoRowsOverlay) {
                this.tableOptions.noRowsOverlayComponentFramework = NoRowsOverlay;
            }

            if (this.rowSelection) {
                this.tableOptions.rowSelection = this.rowSelection;
            }

            if (this.calculatedEnableAutoHeight !== false && !this.isEnlarged) {
                this.tableOptions.domLayout = 'autoHeight';
            }

            if (this.enableOrdering) {
                this.tableOptions = _.assign(this.tableOptions, {
                    rowDragManaged: false,
                    animateRows: true,
                    suppressMoveWhenRowDragging: true,
                    onRowDragMove: this.onRowDragMove,
                    onRowDragEnd: this.onRowDragEnd
                });
            }

            if (_.isBoolean(this.singleSelect)) {
                this.tableOptions.rowSelection = this.singleSelect ? 'single' : 'multiple';
            }

            if (this.tableOptions.rowSelection === 'single') {
                this.tableOptions.suppressRowDeselection = true;
            }

            if (this.enableRowDeselection) {
                this.tableOptions.suppressRowDeselection = false;
            }

            if (!this.enableSelection) {
                this.tableOptions.rowSelection = false;
            }

            if (_.isFunction(this.isRowSelectable)) {
                this.tableOptions.isRowSelectable = rowNode => {
                    return this.isRowSelectable(rowNode.data);
                };
            }

            if (this.calculatedRowModel === 'inMemory' && !_.isString(this.collection)) {
                this.tableOptions.isExternalFilterPresent = () => {
                    return !_.isEmpty(rawMongoQuery(fastCopy(this.filters)));
                };
                this.tableOptions.doesExternalFilterPass = node => {
                    const filters = rawMongoQuery(fastCopy(this.filters));
                    const data = [node.data];

                    return data.filter(sift(filters)).length > 0;
                };
            }

            // Auto Group Column
            if (!_.isObject(this.tableOptions.autoGroupColumnDef)) {
                this.tableOptions.autoGroupColumnDef = {
                    headerName: this.$t('Group'),
                    lockPosition: true,
                    lockVisible: true,
                    lockPinned: true,
                    editable: false,
                    suppressMovable: true,
                    sortable: false,
                    suppressMenu: true,
                    resizable: true,
                    suppressColumnsToolPanel: true
                };
            }
        },
        initializeColumns(tableColumns = null, isEditableTable = false) {
            let hasPhoneCell = false;
            let hasRowHandle = false;

            this.fields = [];
            this.searchFields = [];
            this.populations = [];
            this.originalColumns = _.isNull(tableColumns) ? this.columns : tableColumns;

            this.tableColumns = this.originalColumns.map((column, columnIndex) => {
                // Defaults
                let col = _.omit(fastCopy(column), [
                    'label',
                    'visible',
                    'hidden',
                    'checkCell',
                    'dragCell',
                    'sortable',
                    'movable',
                    'resizable',
                    'navigable',
                    'populate',
                    'searchable',
                    'type',
                    'format',
                    'render',
                    'editor',
                    'isEditable',
                    'valueLabels',
                    'scope',
                    'formatOptions',
                    'view',
                    'tagsCell',
                    'tagLabels',
                    'dotTags',
                    'progressCell',
                    'phoneCell',
                    'imageCell',
                    'translateLabels',
                    'relationParams',
                    'min',
                    'max',
                    'subSelect',
                    'iconColumn',
                    'isDisabled',
                    'avatarCell',
                    'nameFrom',
                    'backgroundColor',
                    'visibleForExport',
                    'exportField',
                    'selectedForExport',
                    'nameFrom',
                    'forceShowSwitch',
                    'workflowApprovalStatusCell',
                    'documentCollection'
                ]);
                col.headerName = !this.disableHeaderNameTranslation ? this.$t(column.label) : column.label;
                col.headerTooltip = col.headerName;
                col.menuTabs = ['columnsMenuTab', 'generalMenuTab'];
                col.cellClassRules = _.isObject(column.cellClassRules) ? column.cellClassRules : {};

                // Normalize
                let isHidden = false;
                if (_.isBoolean(column.hidden)) {
                    col.hide = column.hidden;

                    isHidden = !!column.hidden;
                }
                if (_.isBoolean(column.visible)) col.hide = !column.visible;
                if (_.isBoolean(column.checkCell) && column.checkCell) col.checkboxSelection = column.checkCell;
                if (_.isBoolean(column.dragCell) && column.dragCell) col.rowDrag = column.dragCell;
                if (_.isBoolean(column.sortable)) col.sortable = column.sortable;
                if (_.isBoolean(column.movable)) col.suppressMovable = !column.movable;
                if (_.isBoolean(column.resizable)) col.resizable = column.resizable;
                if (_.isBoolean(column.navigable)) col.suppressNavigable = !column.navigable;
                if (!_.isUndefined(column.render)) col.cellRenderer = column.render;

                // Normalize header name.
                if (_.isString(col.headerName) && !this.disableHeaderNameNormalization) {
                    col.headerName = col.headerName
                        .split(' ')
                        .map(w => firstUpper(toLower(w)))
                        .join(' ');
                }

                // Globally manage sorting.
                if (!_.isBoolean(column.sortable)) {
                    col.sortable = !!this.enableSorting;
                }

                // Enable resize by default.
                if (!_.isBoolean(column.resizable)) {
                    col.resizable = true;
                }

                // Hidden column.
                if (_.isFunction(column.hidden)) {
                    col.hide = isHidden = column.hidden();

                    if (_.isBoolean(column.visible)) col.hide = !column.visible;
                }
                if (isHidden) {
                    col.suppressColumnsToolPanel = true;
                    col.suppressMenu = true;
                    col.lockVisible = true;
                }

                // Don't size column to fit if it has fixed width.
                if (_.isNumber(column.width)) {
                    col.suppressSizeToFit = true;
                }

                // Enable ordering.
                if (this.enableOrdering && columnIndex === 0 && !this.$params('inDialog')) col.rowDrag = true;
                try {
                    if (this.enableOrdering && !this.$params('isPreview')) col.sortable = false;
                } catch {}

                // Has group.
                const groupIndex = _.findIndex(this.rowGroups, group => group.field === column.field);
                if (groupIndex !== -1) {
                    col.rowGroup = true;
                    col.rowGroupIndex = groupIndex;
                }

                // Has options (editable, select)
                const hasOptions = _.isObject(column.editor) && Array.isArray(column.editor.options);

                // Format options.
                let formatOptions = !!this.$store
                    ? fastCopy(this.$store.getters['session/formatterOptions'])
                    : fastCopy(this.$app.store.getters['session/formatterOptions']);
                if (_.isObject(column.formatOptions)) {
                    formatOptions = _.merge(formatOptions, column.formatOptions);
                }

                // Initialize field.
                if (_.isString(column.field) && !column.checkCell && !column.dragCell) {
                    // Populate field.
                    let attributeName = column.field;
                    let hasPopulation = false;
                    if (column.field.indexOf('.') !== -1) attributeName = column.field.split('.')[0];
                    if (_.includes(this.collectionPopulations, attributeName)) {
                        const attribute = this.collectionAttributes.find(a => a.field === attributeName);

                        if (_.isObject(attribute) && column.field.indexOf('.') !== -1) {
                            const subField = column.field.split('.')[1];
                            const existingIndex = _.findIndex(this.populations, p => p.field === attributeName);

                            if (existingIndex === -1) {
                                this.populations.push({
                                    field: attributeName,
                                    query: {$select: [subField]}
                                });
                            } else {
                                this.populations[existingIndex].query.$select.push(subField);

                                this.populations[existingIndex].query.$select = _.uniq(
                                    this.populations[existingIndex].query.$select
                                );
                            }
                        } else if (Array.isArray(column.subSelect)) {
                            this.populations.push({
                                field: attributeName,
                                query: {$select: column.subSelect}
                            });
                        } else {
                            this.populations.push(attributeName);
                        }

                        hasPopulation = true;

                        if (_.isObject(attribute) && attribute.parentField !== '_id') {
                            this.fields.push(attribute.parentField);
                        }
                    }

                    if (!hasPopulation) {
                        this.fields.push(cleanFieldName(column.field));
                    }
                }

                // Has format.
                if (_.isString(column.format)) {
                    if (
                        column.format === 'currency' ||
                        column.format === 'money' ||
                        column.format === 'percentage' ||
                        column.format === 'decimal' ||
                        column.format === 'amount' ||
                        column.format === 'unit' ||
                        column.format === 'unit-price' ||
                        column.format === 'number' ||
                        column.format === 'integer' ||
                        column.format === 'shorten-number'
                    ) {
                        col.cellClassRules['text-right'] = () => true;
                        col.cellClassRules['is-number-cell'] = () => true;
                    }

                    let precision = 2;
                    if (column.format === 'currency') {
                        precision = this.$setting('system.currencyPrecision');
                    } else if (column.format === 'amount' || column.format === 'decimal') {
                        precision = this.$setting('system.amountPrecision');
                    } else if (column.format === 'percentage') {
                        precision = this.$setting('system.percentagePrecision');
                    } else if (column.format === 'unit-price') {
                        precision = this.$setting('system.unitPricePrecision');
                    } else if (column.format === 'unit') {
                        precision = this.$setting('system.unitPrecision');
                    }
                    formatOptions.currency.precision = precision;
                    formatOptions.number.precision = precision;

                    col.valueFormatter = params => {
                        if (_.isFunction(column.formatOptions)) {
                            formatOptions = _.merge(formatOptions, column.formatOptions(params.data));
                        }

                        return !_.isUndefined(params.value) && !_.isNull(params.value)
                            ? column.format === 'date' || column.format === 'datetime'
                                ? _.isDate(params.value)
                                    ? this.$app.format(params.value, column.format, formatOptions)
                                    : ''
                                : this.$app.format(params.value, column.format, formatOptions)
                            : '';
                    };
                }

                // Column has selectOption.
                if (
                    Array.isArray(column.valueLabels) &&
                    !_.isFunction(col.valueFormatter) &&
                    !_.isFunction(column.render) &&
                    !_.isString(column.render)
                ) {
                    col.valueFormatter = params => {
                        const selectValue = params.value
                            ? column.valueLabels.find(option => option.value === params.value)
                            : null;
                        let label = _.isObject(selectValue) ? selectValue.label : '';

                        if (label && column.translateLabels !== false) {
                            label = this.$t(label);
                        }

                        return label;
                    };

                    col.sortable = false;
                }

                // Tags cell
                if (column.tagsCell || column.dotTags) {
                    col.cellRendererFramework = TagRenderer;
                    col.cellRendererParams = {
                        dotTags: column.dotTags,
                        tagLabels: column.tagLabels,
                        translateLabels: _.isBoolean(column.translateLabels) ? column.translateLabels : false
                    };
                    col.sortable = false;
                }

                // Progress cell
                if (column.progressCell) {
                    col.cellRendererFramework = ProgressRenderer;
                }

                // Phone cell
                if (column.phoneCell) {
                    hasPhoneCell = true;

                    col.cellRendererFramework = PhoneRenderer;
                    col.cellRendererParams = {
                        defaultCountryCode: (
                            this.$store.getters['session/company'].phoneCountryCode || 'TR'
                        ).toLowerCase()
                    };
                }

                // Image cell
                if (column.imageCell) {
                    col.cellRendererFramework = ImageRenderer;
                    col.cellStyle = params => ({padding: 0});
                }

                // Avatar cell
                if (column.avatarCell) {
                    col.cellRendererFramework = AvatarRenderer;
                    col.cellRendererParams = {
                        nameFrom: column.nameFrom,
                        backgroundColor: column.backgroundColor
                    };
                    col.cellStyle = params => ({padding: 0});
                }

                // Workflow approval status cell
                if (column.workflowApprovalStatusCell) {
                    col.cellRendererFramework = WorkflowApprovalStatusRenderer;
                    col.cellRendererParams = {
                        documentCollection: column.documentCollection
                    };
                }

                // Relation cell.
                if (!_.isUndefined(column.view) || _.isFunction(column.relationParams)) {
                    const attribute = _.find(this.collectionAttributes, {
                        field: column.field.indexOf('.') !== -1 ? column.field.split('.')[0] : column.field
                    });

                    if (_.isObject(attribute) || _.isFunction(column.relationParams)) {
                        col.cellClassRules['ui-table-relation-renderer-cell'] = () => true;
                        col.cellRendererFramework = RelationRenderer;
                        col.cellRendererParams = params => {
                            let relation = {};

                            if (!_.isObject(params.data)) {
                                return {};
                            }

                            if (_.isString(column.view)) {
                                relation.view = column.view;
                            }

                            relation.dialog = this.$program.dialog.bind(this.$program);
                            relation.inSummaryRow = params.node.rowPinned === 'bottom';
                            if (_.isObject(attribute)) relation.id = params.data[attribute.parentField];
                            relation.renderOnly = true;
                            relation.isPreview = true;
                            relation.refreshData = this.refreshData.bind(this);

                            if (_.isFunction(column.relationParams)) {
                                relation = _.merge(relation, fastCopy(column.relationParams(params)));
                            }

                            return {relation};
                        };
                    }
                }

                // Boolean cell.
                if (column.type === 'boolean') {
                    col.cellRendererFramework = BooleanRenderer;

                    if (!isEditableTable) {
                        col.cellRendererParams = {
                            collection: this.collection,
                            enableReadOnly: this.enableReadOnly,
                            isEditable: column.isEditable,
                            isDisabled: column.isDisabled,
                            inDialog: !column.forceShowSwitch && this.$params('inDialog'),
                            cellValueChanged: (row, isChecked) => {
                                this.$emit('row-switch-changed', row, isChecked);
                            }
                        };
                    } else {
                        col.cellRendererParams = {
                            enableReadOnly: () => {
                                return this.enableReadOnly;
                            },
                            isEditable: false,
                            isEditableTable,
                            disabled: _.isObject(column.editor) ? column.editor.disabled : false,
                            cellValueChanged: params => {
                                this.onCellValueChanged(params);
                            }
                        };
                    }
                }

                // Number cell
                if (
                    !column.format &&
                    !hasOptions &&
                    (column.type === 'number' || column.type === 'decimal' || column.type === 'integer')
                ) {
                    let options = fastCopy(formatOptions);

                    options.number.precision =
                        _.isObject(column.editor) && _.isNumber(column.editor.precision)
                            ? column.editor.precision
                            : formatOptions.number.precision;

                    if (column.type === 'integer') {
                        options.number.precision = 0;
                    }

                    if (isEditableTable) {
                        col.cellClassRules['text-right'] = () => true;
                    }

                    col.valueFormatter = params => {
                        return _.isNumber(params.value) ? this.$app.format(params.value, column.type, options) : '';
                    };
                }

                // Searchable
                if (
                    _.isString(column.field) &&
                    !column.checkCell &&
                    !column.dragCell &&
                    column.searchable !== false &&
                    !column.imageCell &&
                    !_.isString(column.format) &&
                    !Array.isArray(column.valueLabels) &&
                    !(column.type === 'number' || column.type === 'decimal' || column.type === 'integer') &&
                    column.type !== 'boolean' &&
                    !(column.tagsCell || column.dotTags) &&
                    !_.isFunction(column.render) &&
                    !_.isString(column.render)
                ) {
                    this.searchFields.push(column.field.replace(/\[\w+\]/, ''));

                    if (this.calculatedRowModel === 'inMemory') {
                        col.getQuickFilterText = params => {
                            const value = params.value;

                            if (_.isString(value)) {
                                return toLower(value);
                            }

                            return '';
                        };
                    }
                }

                // Editable table columns.
                if (isEditableTable) {
                    col.cellClassRules['ui-field'] = () => true;
                    col.cellClassRules['has-error'] = params => {
                        return (
                            _.isObject(this.validationError) &&
                            this.validationError.field === params.colDef.field &&
                            this.validationError.rowIndex === params.node.rowIndex
                        );
                    };

                    // Disable sorting on editable tables.
                    col.sortable = false;

                    // Make column editable.
                    col.editable = params => {
                        let editable = !this.isEditing && !this.enableReadOnly && params.node.rowPinned !== 'bottom';

                        // Disabled
                        if (editable) {
                            if (_.isFunction(column.editor.disabled)) {
                                editable = !column.editor.disabled(params.data);
                            } else {
                                editable = !column.editor.disabled;
                            }
                        }

                        // Boolean field. Edit custom
                        if (editable && column.type === 'boolean') {
                            editable = false;
                        }

                        // Fake empty row.
                        if (!!params.node.data && params.node.data._fakeItem) {
                            editable = false;
                        }

                        if (editable && !_.isUndefined(column.editor.isEditable)) {
                            if (_.isFunction(column.editor.isEditable)) {
                                editable = !!column.editor.isEditable(params.data);
                            } else {
                                editable = !!column.editor.isEditable;
                            }
                        }

                        return editable;
                    };

                    // Remove general menu tab.
                    col.menuTabs = ['columnsMenuTab'];

                    // Disable arrow key navigation.
                    col.suppressKeyboardEvent = params => {
                        return true;
                        // if (this.enableReadOnly)
                        //     return true;
                        //
                        // if (
                        //     (
                        //         (column.type === 'date') ||
                        //         (
                        //             (
                        //                 column.type === 'string' ||
                        //                 column.type === 'number' ||
                        //                 column.type === 'decimal' ||
                        //                 column.type === 'integer'
                        //             ) &&
                        //             _.isObject(column.editor) &&
                        //             column.editor.fieldType !== 'autocomplete' &&
                        //             (
                        //                 Array.isArray(column.editor.options) ||
                        //                 _.isFunction(column.editor.options) ||
                        //                 _.isString(column.editor.collection)
                        //             )
                        //         )
                        //     ) &&
                        //     params.editing && params.event.keyCode === 13
                        // ) {
                        //     return true;
                        // }

                        // if (params.editing && params.event.keyCode >= 37 && params.event.keyCode <= 40)
                        //     return true;
                    };

                    // Disabled
                    if (_.isObject(column.editor) && !_.isUndefined(column.editor.disabled)) {
                        if (_.isFunction(column.editor.disabled)) {
                            col.cellClassRules['is-disabled'] = params =>
                                !this.enableReadOnly && column.editor.disabled(params.data);
                        } else {
                            col.cellClassRules['is-disabled'] = () => !this.enableReadOnly;
                        }
                    }

                    // Editor component.
                    col.cellEditorParams = {
                        dialog: this.$program.dialog.bind(this.$program),
                        formatOptions,
                        translateLabels: column.editor.translateLabels === true,
                        editorParams: _.assign(column.editor, {
                            autocorrect: 'off',
                            autocapitalize: 'off',
                            spellcheck: false
                        }),
                        goToNextCell: ({data, rowPinned}) => {
                            if (!!this.validationError) {
                                return;
                            }

                            this.api.stopEditing();

                            const idx = setInterval(() => {
                                if (!this.isEditing) {
                                    clearInterval(idx);

                                    let count = 0;
                                    while (true) {
                                        if (count > 500) {
                                            break;
                                        }

                                        this.api.tabToNextCell();

                                        if (this.api.getEditingCells().length < 1 && !!this.api.getFocusedCell()) {
                                            const focusedCell = this.api.getFocusedCell();
                                            const column = tableColumns.find(c => c.field === focusedCell.column.colId);
                                            if (!column) return;

                                            let editable = rowPinned !== 'bottom';
                                            if (editable) {
                                                if (_.isFunction(column.editor.disabled)) {
                                                    editable = !column.editor.disabled(data);
                                                } else {
                                                    editable = !column.editor.disabled;
                                                }
                                            }
                                            if (editable && column.type === 'boolean') {
                                                editable = false;
                                            }
                                            if (editable && !!data && data._fakeItem) {
                                                editable = false;
                                            }
                                            if (editable && !_.isUndefined(column.editor.isEditable)) {
                                                if (_.isFunction(column.editor.isEditable)) {
                                                    editable = !!column.editor.isEditable(data);
                                                } else {
                                                    editable = !!column.editor.isEditable;
                                                }
                                            }

                                            if (editable) {
                                                this.api.startEditingCell({
                                                    rowIndex: focusedCell.rowIndex,
                                                    colKey: focusedCell.column.colId
                                                });

                                                break;
                                            }
                                        }

                                        count++;
                                    }
                                }
                            }, 50);
                        },
                        goToPreviousCell: ({data, rowPinned}) => {
                            if (!!this.validationError) {
                                return;
                            }

                            this.api.stopEditing();

                            const idx = setInterval(() => {
                                if (!this.isEditing) {
                                    clearInterval(idx);

                                    let count = 0;
                                    while (true) {
                                        if (count > 500) {
                                            break;
                                        }

                                        this.api.tabToPreviousCell();

                                        if (this.api.getEditingCells().length < 1 && !!this.api.getFocusedCell()) {
                                            const focusedCell = this.api.getFocusedCell();
                                            const column = tableColumns.find(c => c.field === focusedCell.column.colId);
                                            if (!column) return;

                                            let editable = rowPinned !== 'bottom';
                                            if (editable) {
                                                if (_.isFunction(column.editor.disabled)) {
                                                    editable = !column.editor.disabled(data);
                                                } else {
                                                    editable = !column.editor.disabled;
                                                }
                                            }
                                            if (editable && column.type === 'boolean') {
                                                editable = false;
                                            }
                                            if (editable && !!data && data._fakeItem) {
                                                editable = false;
                                            }
                                            if (editable && !_.isUndefined(column.editor.isEditable)) {
                                                if (_.isFunction(column.editor.isEditable)) {
                                                    editable = !!column.editor.isEditable(data);
                                                } else {
                                                    editable = !!column.editor.isEditable;
                                                }
                                            }

                                            if (editable) {
                                                this.api.startEditingCell({
                                                    rowIndex: focusedCell.rowIndex,
                                                    colKey: focusedCell.column.colId
                                                });

                                                break;
                                            }
                                        }

                                        count++;
                                    }
                                }
                            }, 50);
                        }
                    };
                    if (_.isObject(column.editor) && _.isNumber(column.editor.precision)) {
                        col.cellEditorParams.formatOptions.precision = column.editor.precision;
                    }

                    // Text
                    col.cellEditorFramework = TextEditor;

                    // Number
                    if (
                        !hasOptions &&
                        (column.type === 'number' || column.type === 'decimal' || column.type === 'integer')
                    ) {
                        col.cellEditorFramework = NumberEditor;

                        if (column.type === 'integer') {
                            col.cellEditorParams.editorParams.isInteger = true;
                        }
                    }

                    // Date
                    if (column.type === 'date' || column.type === 'datetime' || column.type === 'time') {
                        col.cellEditorFramework =
                            column.type === 'date' || column.type === 'datetime' ? DateEditor : TimeEditor;
                        col.cellEditorParams.editorParams.isDateEditor =
                            column.type === 'date' || column.type === 'datetime' || column.type === 'time';
                        col.cellEditorParams.editorParams.isDatetime = column.type === 'datetime';

                        if (
                            !_.isFunction(col.valueFormatter) &&
                            !_.isFunction(column.render) &&
                            !_.isString(column.render)
                        ) {
                            col.valueFormatter = params => {
                                return _.isDate(params.value) ? this.$app.format(params.value, column.type) : '';
                            };
                        }
                    }

                    // Auto complete
                    if (_.isObject(column.editor) && column.editor.fieldType === 'autocomplete') {
                        col.cellEditorFramework = AutocompleteEditor;
                    }

                    // Select
                    if (
                        (column.type === 'string' ||
                            column.type === 'number' ||
                            column.type === 'decimal' ||
                            column.type === 'integer') &&
                        _.isObject(column.editor) &&
                        column.editor.fieldType !== 'autocomplete' &&
                        (Array.isArray(column.editor.options) ||
                            _.isFunction(column.editor.options) ||
                            _.isString(column.editor.collection) ||
                            _.isString(column.editor.method))
                    ) {
                        col.cellEditorFramework = SelectEditor;

                        if (
                            !_.isFunction(col.valueFormatter) &&
                            !_.isFunction(column.render) &&
                            !_.isString(column.render)
                        ) {
                            if (Array.isArray(column.editor.options)) {
                                col.valueFormatter = params => {
                                    if (!_.isUndefined(params.value) && params.value !== '' && params.value !== null) {
                                        return column.editor.translateLabels === true
                                            ? this.$t(
                                                  column.editor.options.find(option => option.value === params.value)
                                                      .label
                                              )
                                            : column.editor.options.find(option => option.value === params.value).label;
                                    }

                                    return '';
                                };
                            } else if (_.isFunction(column.editor.options)) {
                                col.valueFormatter = params => {
                                    if (!_.isUndefined(params.value) && params.value !== '' && params.value !== null) {
                                        const columnOptions = column.editor.options(params.data, params);

                                        if (Array.isArray(columnOptions) && columnOptions.length > 0) {
                                            return column.editor.translateLabels === true
                                                ? this.$t(
                                                      (
                                                          columnOptions.find(option => option.value === params.value) ??
                                                          {}
                                                      ).label
                                                  )
                                                : (columnOptions.find(option => option.value === params.value) ?? {})
                                                      .label;
                                        }
                                    }

                                    return '';
                                };
                            } else {
                                col.valueSetter = params => {
                                    if (!_.isEqual(params.oldValue, params.newValue)) {
                                        if (_.isObject(params.newValue)) {
                                            const mapIndex = this.selectEditorMap.findIndex(
                                                v =>
                                                    v.rowIndex === params.node.rowIndex &&
                                                    v.field === params.colDef.field
                                            );

                                            if (params.newValue.label === '' && mapIndex !== -1) {
                                                this.selectEditorMap.splice(mapIndex, 1);
                                                params.data[params.colDef.field] = '';
                                                // delete params.data[params.colDef.field];
                                            } else {
                                                if (mapIndex === -1) {
                                                    this.selectEditorMap.push({
                                                        rowIndex: params.node.rowIndex,
                                                        field: params.colDef.field,
                                                        option: params.newValue
                                                    });
                                                } else {
                                                    this.selectEditorMap[mapIndex].option = params.newValue;
                                                }

                                                params.data[params.colDef.field] = params.newValue.value;
                                            }
                                        }

                                        return true;
                                    }

                                    return false;
                                };
                                if (column.editor.view && !column.editor.disableDetail) {
                                    col.cellClassRules['ui-table-relation-renderer-cell'] = () => true;
                                    col.cellRendererFramework = RelationRenderer;
                                    col.cellRendererParams = params => {
                                        let relation = {};

                                        relation.inEditableTable = true;
                                        relation.view = column.editor.view;
                                        relation.collection = column.editor.collection;
                                        relation.inSummaryRow = params.node.rowPinned === 'bottom';
                                        relation.valueFrom = column.editor.valueFrom;
                                        relation.labelFrom = column.editor.labelFrom;
                                        relation.idFrom = column.editor.idFrom || relation.valueFrom;
                                        relation.template = column.editor.template;
                                        relation.dialog = col.cellEditorParams.dialog;
                                        relation.selectEditorMap = this.selectEditorMap;
                                        relation.isPreview = this.enableReadOnly;

                                        const mapValue = this.selectEditorMap.find(
                                            v => v.rowIndex === params.node.rowIndex && v.field === params.colDef.field
                                        );
                                        if (_.isObject(mapValue)) {
                                            relation.label = mapValue.option.label;
                                        }

                                        return {relation};
                                    };
                                } else {
                                    col.valueFormatter = params => {
                                        if (!params.value) {
                                            return '';
                                        }

                                        const mapValue = this.selectEditorMap.find(
                                            v => v.rowIndex === params.node.rowIndex && v.field === params.colDef.field
                                        );
                                        if (_.isObject(mapValue) && params.node.rowPinned !== 'bottom') {
                                            return mapValue.option.label;
                                        }

                                        return params.value;
                                    };
                                }
                            }
                        }
                    }
                }

                // Icon Column.
                if (!!column.iconColumn) {
                    col = {
                        ...col,
                        headerName: '',
                        headerClass: 'popup-edit-header-cell',
                        width: 35,
                        maxWidth: 35,
                        lockPosition: false,
                        lockVisible: true,
                        lockPinned: true,
                        editable: false,
                        resizable: false,
                        suppressNavigable: true,
                        suppressMovable: true,
                        suppressSizeToFit: true,
                        sortable: false,
                        suppressMenu: true,
                        suppressColumnsToolPanel: true,
                        cellClass: 'pr0 pl0',
                        headerComponentParams: {
                            template:
                                '<div class="ag-cell-label-container" role="presentation">' +
                                '  <span ref="eMenu" class="ag-header-icon ag-header-cell-menu-button"></span>' +
                                '  <div ref="eLabel" class="ag-header-cell-label" role="presentation">' +
                                `     <span class="ag-header-cell-text" role="columnheader"></span>` +
                                '  </div>' +
                                '</div>'
                        }
                    };
                }

                return col;
            });

            if (
                !this.enableEditing &&
                !this.tableColumns.some(c => !!c.rowGroup) &&
                !this.options.treeData &&
                this.enableRowHandle
            ) {
                this.tableColumns.unshift({
                    headerName: '',
                    width: 55,
                    pinned: 'left',
                    field: 'rowCount',
                    lockPosition: true,
                    lockVisible: true,
                    lockPinned: true,
                    editable: false,
                    cellClass: 'row-handle',
                    headerClass: 'row-handle-header',
                    resizable: true,
                    suppressNavigable: true,
                    suppressMovable: true,
                    suppressSizeToFit: true,
                    sortable: false,
                    suppressColumnsToolPanel: true,
                    menuTabs: ['columnsMenuTab', 'generalMenuTab'],
                    cellStyle(params) {
                        if (
                            _.isObject(params.data) &&
                            !_.isUndefined(params.data.rowCount) &&
                            !_.isNull(params.data.rowCount) &&
                            params.data.rowCount !== 0
                        ) {
                            return {
                                display: 'block !important'
                            };
                        }

                        return null;
                    },
                    valueGetter(params) {
                        if (
                            _.isObject(params.data) &&
                            !_.isUndefined(params.data.rowCount) &&
                            !_.isNull(params.data.rowCount) &&
                            params.data.rowCount !== 0
                        ) {
                            return params.data.rowCount;
                        }

                        return params.node.rowIndex + 1;
                    }
                });

                hasRowHandle = true;
            }

            if (hasPhoneCell) {
                this.fields = _.uniq(this.fields.concat('phoneCountryCode'));
            }

            // Extra table fields.
            if (this.extraFields.length > 0) {
                this.fields = this.fields.concat(this.extraFields);
                this.searchFields = this.searchFields.concat(this.extraFields);
            }

            if (this.enableEditing || hasRowHandle) {
                this.tableColumns = this.tableColumns.map(col => {
                    if (col.field !== 'rowCount') {
                        col.suppressMenu = true;
                    }

                    return col;
                });
            }
        },
        initializeInitialSorting() {
            if (this.sort.length > 0) {
                this.sort.forEach(sort => {
                    const colIndex = _.findIndex(this.tableColumns, {field: sort.field});

                    if (colIndex !== -1) {
                        this.tableColumns[colIndex].sort = sort.sort;
                    }
                });
            }
        },
        initializeEditableTable() {
            const self = this;

            // Check if schema exist.
            if (_.isEmpty(this.schema)) {
                throw new Error('Editable table needs a schema!');
            }

            // Create in memory collection.
            if (!_.isString(this.collection)) {
                this.$app.collection('ui-editable-table-in-memory-collection', {
                    store: {}
                });

                this.inMemoryCollection = this.$app.collection('ui-editable-table-in-memory-collection');
            }

            // Build columns from schema.
            this.initializeColumns(prepareColumnsFromSchema(fastCopy(this.schema)), true);

            // Row handle column.
            if (this.enableRowHandle) {
                this.tableColumns.unshift({
                    headerName: '',
                    field: 'rowCount',
                    width: 50,
                    maxWidth: 50,
                    pinned: 'left',
                    lockPosition: true,
                    lockVisible: true,
                    lockPinned: true,
                    editable: false,
                    cellClass: 'row-handle',
                    headerClass: 'row-handle-header',
                    resizable: false,
                    suppressNavigable: true,
                    suppressMovable: true,
                    suppressSizeToFit: true,
                    sortable: false,
                    suppressColumnsToolPanel: true,
                    menuTabs: ['columnsMenuTab', 'generalMenuTab'],
                    valueGetter(params) {
                        if (_.isPlainObject(params.data)) {
                            if (_.isNumber(params.data.rowCount)) {
                                return params.data.rowCount;
                            }

                            return params.data._id ? params.node.rowIndex + 1 : '';
                        }

                        return '';
                    }
                });
            }

            // Popup edit cell.
            if (this.enablePopupEdit) {
                this.tableColumns.push({
                    headerName: '',
                    field: 'popup-edit-cell',
                    width: 35,
                    maxWidth: 35,
                    pinned: 'right',
                    lockPosition: false,
                    lockVisible: true,
                    lockPinned: true,
                    editable: false,
                    cellClass: params => {
                        let classes = 'popup-edit-cell';

                        if (!!params.data && (!!params.data._fakeItem || !params.data._id)) {
                            classes += ' cursor-default';
                        }

                        return classes;
                    },
                    headerClass: 'popup-edit-header-cell',
                    resizable: false,
                    suppressNavigable: true,
                    suppressMovable: true,
                    suppressSizeToFit: true,
                    sortable: false,
                    suppressMenu: true,
                    suppressColumnsToolPanel: true,
                    headerComponentParams: {
                        template:
                            '<div class="ag-cell-label-container" role="presentation">' +
                            '  <span ref="eMenu" class="ag-header-icon ag-header-cell-menu-button"></span>' +
                            '  <div ref="eLabel" class="ag-header-cell-label" role="presentation">' +
                            `     <span class="ag-header-cell-text" role="columnheader"></span>` +
                            '  </div>' +
                            '</div>'
                    },
                    cellRenderer(params) {
                        if (!!params.data && (!!params.data._fakeItem || !params.data._id)) {
                            return '';
                        }

                        return '<i class="fal fa-pencil-alt"></i>';
                    },
                    onCellClicked: this.onRowPopupEdit
                });
            }

            // Add remove column.
            if (this.enableAddRemove) {
                let addRemoveCell = {
                    headerName: ' ',
                    width: 35,
                    maxWidth: 35,
                    pinned: 'right',
                    lockPosition: false,
                    lockVisible: true,
                    lockPinned: true,
                    editable: false,
                    headerClass: () => {
                        return this.enableEnlarge ? 'enlarge-header-cell' : '';
                    },
                    headerComponentParams: params => {
                        if (this.enableEnlarge) {
                            return {
                                template:
                                    '<div class="ag-cell-label-container" role="presentation">' +
                                    '  <span ref="eMenu" class="ag-header-icon ag-header-cell-menu-button"></span>' +
                                    '  <div ref="eLabel" class="ag-header-cell-label" role="presentation">' +
                                    `     <span class="ag-header-cell-text" role="columnheader"><i class="fal fa-expand-arrows text-primary"></i></span>` +
                                    '  </div>' +
                                    '</div>'
                            };
                        }

                        return {};
                    },
                    cellClass: params => {
                        let classes =
                            !this.enableReadOnly && this.enableAddRemove
                                ? 'add-remove-cell'
                                : 'add-remove-cell is-disabled';

                        if (!!params.data && (!!params.data._fakeItem || !params.data._id)) {
                            classes += ' cursor-default';
                        }

                        return classes;
                    },
                    resizable: false,
                    suppressNavigable: true,
                    suppressMovable: true,
                    suppressSizeToFit: true,
                    sortable: false,
                    suppressMenu: true,
                    suppressColumnsToolPanel: true,
                    cellRenderer(params) {
                        if (!!params.data && (!!params.data._fakeItem || !params.data._id)) {
                            return '';
                        }

                        return '<i class="fal fa-trash-alt text-danger"></i>';
                    },
                    onCellClicked: _.debounce(this.onRowRemoved, 250, {leading: true, trailing: false})
                };

                // if (!this.calculatedEnableAutoHeight) {
                //     delete addRemoveCell.pinned;
                //     delete addRemoveCell.lockPosition;
                //     delete addRemoveCell.lockPinned;
                // }

                this.tableColumns.push(addRemoveCell);
            }

            // Disable un-needed table features.
            // if (!this.tableColumns.some(c => !!c.checkboxSelection)) {
            //     this.tableOptions.rowSelection = false;
            // }
            this.tableOptions.suppressCellSelection = false;
            this.tableOptions.stopEditingWhenGridLosesFocus = false;
            this.tableOptions.singleClickEdit = true;

            this.selectEditorMap = [];
        },
        initializeSelectEditorMap(rows) {
            // this.selectEditorMap = [];

            if (!Array.isArray(rows)) rows = [rows];

            rows.forEach((row, index) => {
                this.originalColumns.forEach(column => {
                    if (_.isString(column.populate) && _.isObject(row[column.populate])) {
                        const population = row[column.populate];
                        const mapIndex = this.selectEditorMap.findIndex(
                            v => v.rowIndex === index && v.field === column.field
                        );
                        const valueFrom = column.editor.valueFrom || '_id';
                        const labelFrom = column.editor.labelFrom || 'name';
                        let label = population[labelFrom];
                        let value = population[valueFrom];

                        if (column.editor.template) {
                            label = template(column.editor.template, population);
                        }

                        if (mapIndex === -1) {
                            this.selectEditorMap.push({
                                rowIndex: index,
                                field: column.field,
                                option: {label, value}
                            });
                        } else {
                            this.selectEditorMap[mapIndex].option = {label, value};
                        }

                        // Remove item if the value is null.
                        if (row[column.field] === null && mapIndex !== -1) {
                            this.selectEditorMap.splice(mapIndex, 1);
                        }
                    }
                });
            });
        },
        async initializeSummaryRow(rows) {
            if (_.isFunction(this.summaryRow)) {
                const row = await this.summaryRow(rows || []);

                this.api.setPinnedBottomRowData(_.isObject(row) ? [row] : []);

                this.$nextTick(() => {
                    this.scroller.update();
                });
            }
        },
        initializeRowModel() {
            const self = this;

            // Viewport row model.
            if (this.calculatedRowModel === 'viewport') {
                let params = {};

                params.app = this.$app;
                params.collection = this.collection;
                params.sort = [];
                this.tableColumns.forEach(column => {
                    if (_.isString(column.sort)) {
                        params.sort.push({
                            field: column.field,
                            sort: column.sort
                        });
                    }
                });
                params.externalFilters = fastCopy(this.filters);
                params.filters = {};
                params.search = this.search;
                params.columns = this.tableColumns;
                params.fields = this.fields;
                params.searchFields = this.searchFields;
                params.populations = this.populations;
                params.hasTextSearch = this.hasTextSearch;

                this.dataSource = new ViewportDataSource(params);

                this.tableOptions.rowModelType = 'viewport';
                this.tableOptions.getRowNodeId = data => data._id;
                this.tableOptions.viewportDatasource = this.dataSource;

                this.dataSource.on('loading', () => {
                    this.api.showLoadingOverlay();
                });
                // this.dataSource.on('loaded', () => {
                //     this.api.hideOverlay();
                // });
            } else if (this.calculatedRowModel === 'serverSide') {
                let params = {};

                params.app = this.$app;
                params.collection = this.collection;
                params.sort = [];
                this.tableColumns.forEach(column => {
                    if (_.isString(column.sort)) {
                        params.sort.push({
                            field: column.field,
                            sort: column.sort
                        });
                    }
                });
                params.externalFilters = fastCopy(this.filters);
                params.filters = {};
                params.search = this.search;
                params.columns = this.tableColumns;
                params.fields = this.fields;
                params.searchFields = this.searchFields;
                params.populations = this.populations;
                params.getRowGroups = () => {
                    return this.rowGroups;
                };
                params.getRows = this.getRows;
                params.processResult = this.processResult;
                params.goToTop = () => {
                    this.scroller.goToTop();
                };

                if (this.collection) {
                    params.hasTextSearch = this.hasTextSearch;
                    params.forceFullTextSearch = this.forceFullTextSearch;
                }

                this.dataSource = new ServerSideDataSource(params);

                this.tableOptions.rowModelType = 'serverSide';
                // this.tableOptions.serverSideStoreType = 'partial';
                this.tableOptions.maxConcurrentDatasourceRequests = 2;
                this.tableOptions.cacheBlockSize = 75;
                this.tableOptions.rowGroupPanelShow = 'never';
                this.tableOptions.maxBlocksInCache = 2;
                // this.tableOptions.purgeClosedRowNodes = true;
                // this.tableOptions.icons = {
                //     groupLoading: '<i class="fal fa-spin fa-spinner"></i>'
                // };
                this.tableOptions.getRowNodeId = data => {
                    if (this.rowGroups.length > 0) {
                        return Random.id(6);
                    }

                    return data._id;
                };

                this.dataSource.on('loading', () => {
                    this.api.showLoadingOverlay();
                });

                this.dataSource.on('loaded', state => {
                    this.$nextTick(() => {
                        if (state.isSearching || state.isFiltering || state.isRefreshing || state.isInitializing) {
                            this.initializeSummaryRow();

                            if (!state.isRefreshing) {
                                this.scroller.goToTop();
                            }
                        }
                    });
                });

                this.$nextTick(() => {
                    this.api.setServerSideDatasource(this.dataSource);
                });
            }
        },
        initializeColumnSizes() {
            const allColumnIds = [];
            this.tableOptions.columnApi.getAllColumns().forEach(column => {
                if (!_.isNumber(column.colDef.width)) {
                    allColumnIds.push(column.colId);
                }
            });
            this.tableOptions.columnApi.autoSizeColumns(allColumnIds);

            const viewport = this.$refs['ag-grid-vue'].$el.querySelector('.ag-center-cols-viewport');
            const container = this.$refs['ag-grid-vue'].$el.querySelector('.ag-center-cols-container');

            if (container.clientWidth < viewport.clientWidth) {
                this.tableOptions.api.sizeColumnsToFit();
            }
        },
        initializeScroller() {
            const viewportY = this.$refs['ag-grid-vue'].$el.querySelector('.ag-body-viewport');
            const viewportX = this.$refs['ag-grid-vue'].$el.querySelector('.ag-body-horizontal-scroll-viewport');
            const scrollerY = new PerfectScrollbar(viewportY, {
                minScrollbarLength: 30,
                suppressScrollY: this.calculatedEnableAutoHeight && !this.isEnlarged,
                suppressScrollX: true,
                wheelPropagation: true
            });
            const scrollerX = new PerfectScrollbar(viewportX, {
                minScrollbarLength: 30,
                suppressScrollY: true,
                suppressScrollX: false,
                wheelPropagation: true
            });

            const updateScroll = () => {
                if (this.$refs['ag-grid-vue']) {
                    const element = this.$refs['ag-grid-vue'].$el;
                    const horizontalScroll = element.querySelector('.ag-body-horizontal-scroll');
                    const horizontalScrollViewport = element.querySelector('.ag-body-horizontal-scroll-viewport');
                    const horizontalScrollContainer = element.querySelector('.ag-body-horizontal-scroll-container');
                    const floatingBottom = element.querySelector('.ag-floating-bottom');

                    if (horizontalScrollContainer.clientWidth <= horizontalScrollViewport.clientWidth) {
                        horizontalScroll.style.visibility = 'hidden';
                    } else {
                        horizontalScroll.style.visibility = 'visible';
                    }

                    horizontalScroll.style.bottom = floatingBottom.clientHeight + 'px';
                }
            };

            updateScroll();

            this.scroller = {
                scrollerX,
                scrollerY,
                update() {
                    updateScroll();

                    scrollerY.update();
                    scrollerX.update();
                    // horizontalScroll.style.bottom = floatingBottom.clientHeight + 'px';
                },
                destroy() {
                    scrollerY.destroy();
                    scrollerX.destroy();
                },
                goToTop() {
                    viewportY.scrollTop = 0;
                },
                goToBottom() {
                    setTimeout(() => {
                        viewportY.scrollTop = viewportY.scrollHeight;
                    }, 0);
                }
            };
        },
        initializeEmptyRows() {
            this.$nextTick(() => {
                if (!this.$refs['ag-grid-vue']) return;
                if (this.calculatedRowModel !== 'inMemory') return;
                if (!this.enableEditing) return;
                // if (this.enableReadOnly) return;
                if (this.minEmptyRows === 0) return;

                const itemsToRemove = [];
                let itemCount = 0;
                this.api.forEachNodeAfterFilter(node => {
                    if (_.isEmpty(node.data) || node.data._fakeItem) {
                        itemsToRemove.push(node.data);
                    } else {
                        itemCount++;
                    }
                });

                const emptyItems = [];
                let minEmptyRows = this.minEmptyRows || 0;

                if (
                    this.$refs['ag-grid-vue'] &&
                    this.enableEditing &&
                    (!this.calculatedEnableAutoHeight || this.isEnlarged)
                ) {
                    const viewport = this.$refs['ag-grid-vue'].$el.querySelector('.ag-body-viewport');

                    minEmptyRows = Math.ceil(viewport.clientHeight / this.rowHeight);
                }

                let diff = minEmptyRows - itemCount;
                if (diff < 1 && minEmptyRows !== 0) diff = 1;

                for (let i = 0; i < diff; i++) {
                    emptyItems.push(i === 0 ? {} : {_fakeItem: true});
                }

                this.api.applyTransaction({add: emptyItems, remove: itemsToRemove});
            });
        },
        async initializeInMemoryData() {
            let items = fastCopy(this.items) || null;

            if (this.collection) {
                let idx = setTimeout(() => {
                    if (_.isObject(this.api)) {
                        this.api.showLoadingOverlay();
                    }
                }, 50);

                items = await this.loadAllRecords();

                clearTimeout(idx);

                if (_.isObject(this.api)) {
                    this.api.hideOverlay();
                }
            }

            if (Array.isArray(items) && items.length > 0 && _.isFunction(this.beforeInit)) {
                let idx = setTimeout(() => {
                    if (_.isObject(this.api)) {
                        this.api.showLoadingOverlay();
                    }
                }, 50);

                items = await this.beforeInit(items);

                clearTimeout(idx);

                if (_.isObject(this.api)) {
                    this.api.hideOverlay();
                }
            }

            if (_.isFunction(this.processResult)) {
                let idx = setTimeout(() => {
                    if (_.isObject(this.api)) {
                        this.api.showLoadingOverlay();
                    }
                }, 50);

                items = await this.processResult(items);

                clearTimeout(idx);

                if (_.isObject(this.api)) {
                    this.api.hideOverlay();
                }
            }

            if (!_.isString(this.collection) && !this.enableEditing && Array.isArray(items) && items.length > 0) {
                items = items.map(item => {
                    if (_.isPlainObject(item) && (!_.isString(item._id) || !item._id)) {
                        item._id = Random.id(8);
                    }

                    return item;
                });
            }

            if (!_.isString(this.collection) && this.enableEditing && Array.isArray(items) && items.length > 0) {
                this.initializeSelectEditorMap(items);
            }

            // Initialize in memory store when editing.
            if (this.enableEditing && Array.isArray(items)) {
                await this.inMemoryCollection.remove({});

                if (items.length > 0) {
                    await this.inMemoryCollection.create(
                        fastCopy(
                            items.map(item => {
                                if (_.isPlainObject(item) && (!_.isString(item._id) || !item._id)) {
                                    item._id = Random.id(8);
                                }

                                return item;
                            })
                        )
                    );
                }
            }

            this.inMemoryData = items;

            if (this.enableEditing) {
                this.$nextTick(() => {
                    this.initializeEmptyRows();
                });
            }
        },
        async loadAllRecords(query = null) {
            let params = {};
            params.app = this.$app;
            params.collection = this.collection;
            params.sort = [{order: 'asc'}];
            params.externalFilters = fastCopy(this.filters);
            params.filters = {};
            params.search = this.search;
            params.columns = this.tableColumns;
            params.fields = this.fields;
            params.searchFields = this.searchFields;
            params.populations = this.populations;
            params.isOrderable = this.enableOrdering;

            const dataSource = new ViewportDataSource(params);

            return new Promise((resolve, reject) => {
                dataSource.getResult(async result => {
                    if (_.isFunction(this.processResult)) {
                        result = await this.processResult(result);
                    }

                    resolve(result);
                });
            });
        },
        findIndexes(schema, parentKey = '', indexes = []) {
            _.each(schema, (prop, key) => {
                if (Array.isArray(prop) && prop.every(p => _.isPlainObject(p))) {
                    prop.forEach(p => {
                        this.findIndexes(p, parentKey ? `${parentKey}.${key}` : key, indexes);
                    });
                } else if (_.isPlainObject(prop) && _.isUndefined(prop.type)) {
                    this.findIndexes(prop, parentKey ? `${parentKey}.${key}` : key, indexes);
                } else {
                    if (_.isPlainObject(prop) && (!_.isUndefined(prop.index) || !_.isUndefined(prop.text))) {
                        let definition = {};

                        definition.type = !_.isUndefined(prop.text) ? 'text' : 'normal';
                        definition.key = (parentKey ? `${parentKey}.${key}` : key).replace(/\.\$\./g, '.');
                        definition.value = prop.index === true ? 1 : -1;

                        indexes.push(definition);
                    }
                }
            });

            return indexes;
        },
        async exportRows(params) {
            if (!!this.viewHasExport) {
                this.$emit('export-requested');
                return;
            }

            // Get columns.
            const columns = await (() => {
                const displayedColumnFields = params.columnApi.columnController.allDisplayedColumns.map(
                    c => c.colDef.field
                );
                const columns = [];
                let tableColumns = this.columns;

                if (this.enableEditing) {
                    tableColumns = prepareColumnsFromSchema(fastCopy(this.schema)).map(column => {
                        if (!!column.populate) {
                            column.valueGetter = params => {
                                if (_.isPlainObject(params.data) && _.isPlainObject(params.data[column.populate])) {
                                    const data = params.data[column.populate];
                                    let labelFrom = 'name';

                                    if (_.isPlainObject(column.editor)) {
                                        if (!!column.editor.template) {
                                            return template(column.editor.template, data);
                                        } else if (!!column.editor.labelFrom) {
                                            labelFrom = column.editor.labelFrom;
                                        }
                                    }

                                    return _.get(data, labelFrom);
                                }

                                return '';
                            };
                        }

                        return column;
                    });
                }

                for (const tableColumn of tableColumns) {
                    if (
                        !tableColumn.label ||
                        !!tableColumn.dotTags ||
                        !!tableColumn.avatarCell ||
                        !!tableColumn.imageCell ||
                        (!!tableColumn.cellRendererFramework && !tableColumn.valueFormatter) ||
                        tableColumn.headerName === '#' ||
                        tableColumn.field === 'popup-edit-cell'
                    ) {
                        continue;
                    }

                    const column = {};

                    column.field = tableColumn.field;
                    column.label = tableColumn.label;
                    column.selected = displayedColumnFields.includes(column.field) || !!tableColumn.selectedForExport;
                    column.type = tableColumn.type;
                    column.format = tableColumn.format;
                    column.translateLabels = tableColumn.translateLabels;
                    column.exportField = tableColumn.exportField;
                    column.visibleForExport = tableColumn.visibleForExport;
                    if (!column.visibleForExport) {
                        column.hidden = tableColumn.hidden;
                    }
                    column.width = 10;
                    if (!!tableColumn.tagLabels) {
                        column.valueLabels = tableColumn.tagLabels;
                    } else if (!!tableColumn.valueLabels) {
                        column.valueLabels = tableColumn.valueLabels;
                    }
                    if (!!tableColumn.relationParams) {
                        column.relationParams = tableColumn.relationParams;
                        column.isRelation = true;
                    }
                    if (!!tableColumn.render) {
                        column.render = tableColumn.render;
                    }
                    if (!!tableColumn.valueGetter) {
                        column.valueGetter = tableColumn.valueGetter;
                    }
                    if (!!tableColumn.valueFormatter) {
                        column.valueFormatter = tableColumn.valueFormatter;
                    }

                    column.isCurrencyColumn = column.format === 'currency';

                    columns.push(column);
                }

                return new Promise(resolve => {
                    this.$program.dialog({
                        component: ExportPopup,
                        params: {
                            title: this.$t('Export'),
                            scope: 'filtered',
                            columns
                        },
                        onSubmit({columns}) {
                            resolve(columns);
                        }
                    });
                });
            })();

            let exportPayload = null;

            const prepareItem = async row => {
                const item = {};

                for (const column of columns) {
                    if (!!column.hidden && !column.visibleForExport) {
                        if (_.isFunction(column.hidden)) {
                            column.hidden = await column.hidden(row);
                        } else {
                            column.hidden = true;

                            continue;
                        }
                    }

                    item[column.field] = _.get(row, column.field);

                    if (!!column.valueFormatter) {
                        let value = column.valueFormatter({...params, data: row, value: item[column.field]});

                        if (_.isString(value)) {
                            value = stripHtml(value);
                        }

                        item[column.field] = value;
                    } else if (!!column.valueGetter) {
                        let value = column.valueGetter({...params, data: row, value: item[column.field]});

                        if (_.isString(value)) {
                            value = stripHtml(value);
                        }

                        item[column.field] = value;

                        if (!!column.valueLabels) {
                            if (Array.isArray(value)) {
                                const valueLabels = [];

                                for (const v of value) {
                                    const valueLabel = column.valueLabels.find(vl => vl.value === v);

                                    if (!!valueLabel) {
                                        valueLabels.push(this.$t(valueLabel.label));
                                    }
                                }

                                if (valueLabels.length > 0) {
                                    item[column.field] = valueLabels.join(', ');
                                } else {
                                    item[column.field] = '';
                                }
                            } else {
                                const valueLabel = column.valueLabels.find(vl => vl.value === item[column.field]);

                                if (!!valueLabel) {
                                    item[column.field] = this.$t(valueLabel.label);
                                } else {
                                    item[column.field] = '';
                                }
                            }
                        }
                    } else if (!!column.isRelation) {
                        const relationParams = column.relationParams({...params, data: row, value: item[column.field]});

                        if (_.isPlainObject(relationParams)) {
                            if (!!relationParams.template) {
                                try {
                                    item[column.field] = template(relationParams.template, item[column.field]);
                                } catch (error) {}
                            } else if (!_.isUndefined(relationParams.customLabel)) {
                                if (_.isString(relationParams.customLabel)) {
                                    item[column.field] = relationParams.customLabel;
                                } else {
                                    item[column.field] = _.get(item[column.field], relationParams.customLabel);
                                }
                            }
                        } else {
                            item[column.field] = '';
                        }
                    } else if (!!column.valueLabels) {
                        const value = item[column.field];

                        if (Array.isArray(value)) {
                            const valueLabels = [];

                            for (const v of value) {
                                const valueLabel = column.valueLabels.find(vl => vl.value === v);

                                if (!!valueLabel) {
                                    valueLabels.push(this.$t(valueLabel.label));
                                }
                            }

                            if (valueLabels.length > 0) {
                                item[column.field] = valueLabels.join(', ');
                            } else {
                                item[column.field] = '';
                            }
                        } else {
                            const valueLabel = column.valueLabels.find(vl => vl.value === value);

                            if (!!valueLabel) {
                                item[column.field] = this.$t(valueLabel.label);
                            } else {
                                item[column.field] = '';
                            }
                        }
                    } else if (!!column.template) {
                        item[column.field] = template(column.template, item[column.field]);
                    } else if (column.type === 'boolean') {
                        item[column.field] = !!item[column.field] ? this.$t('Yes') : this.$t('No');
                    }
                    if (!!column.render) {
                        try {
                            item[column.field] = stripHtml(
                                column.render({
                                    ...params,
                                    data: row,
                                    value: item[column.field]
                                }) || ''
                            );
                        } catch (error) {}
                    }
                    if (!!column.exportField) {
                        item[column.field] = _.get(row, column.exportField);
                    }

                    if (!!item[column.field]) {
                        let width = item[column.field].toString().length;
                        if (column.format === 'date' || column.format === 'datetime') {
                            width = 20;
                        }

                        if (width > column.width) {
                            column.width = width;
                        }
                    }

                    column.disableFormatting = _.isDate(item[column.field]) || _.isNumber(item[column.field]);
                }

                return item;
            };

            if (!!this.collection || !!this.getRows) {
                // Start progress.
                this.$program.progress({
                    status: 'started'
                });

                // Get query.
                const query = (() => {
                    let params = {};
                    params.app = this.$app;
                    params.collection = this.collection;
                    params.sort = [];
                    this.tableColumns.forEach(column => {
                        if (_.isString(column.sort)) {
                            params.sort.push({
                                field: column.field,
                                sort: column.sort
                            });
                        }
                    });
                    params.externalFilters = fastCopy(this.filters);
                    params.filters = {};
                    params.search = this.search;
                    params.columns = this.tableColumns;
                    params.fields = this.fields;
                    params.searchFields = this.searchFields;
                    params.populations = this.populations;
                    params.getRowGroups = () => {
                        return this.rowGroups;
                    };

                    if (
                        !this.search &&
                        !!this.$params('search') &&
                        typeof this.$params('search') === 'string' &&
                        this.$params('search').length > 0
                    ) {
                        params.search = this.$params('search');
                    }

                    if (this.collection && this.hasTextSearch) {
                        params.hasTextSearch = this.hasTextSearch;
                        params.searchText = params.search;
                        delete params.search;
                    }

                    const dataSource = new ServerSideDataSource(params);
                    if (!!this.collection && params.hasTextSearch) {
                        dataSource.searchText = params.searchText;
                    } else if (!!this.collection && !!params.search) {
                        dataSource.search = params.search;
                    }
                    const query = dataSource.getQuery();

                    query.$sort = {
                        // ...(query.$sort || {}),
                        _id: 1
                    };

                    delete query.$skip;
                    delete query.$limit;
                    delete query.$paginate;
                    delete query.$disableWholeCount;

                    return query;
                })();

                const limit = 100;
                let total = null;
                let page = 0;
                let rowCount = 0;

                if (!!this.collection) {
                    const lastRecord = await this.$collection(this.collection).findOne({
                        ...query,
                        $sort: {
                            _id: -1
                        },
                        $select: ['createdAt']
                    });
                    if (!!lastRecord) {
                        if (!Array.isArray(query.$and)) {
                            query.$and = [];
                        }

                        query.$and.push({_id: {$lte: lastRecord._id}});
                    }
                }

                try {
                    do {
                        const q = fastCopy(query);
                        let items = [];

                        if (!!this.getRows) {
                            let result = null;

                            q.$skip = page * limit;
                            q.$limit = limit;
                            q.$paginate = {
                                default: limit
                            };

                            const params = {request: {}};
                            params.request.startRow = q.$skip;
                            params.request.endRow = q.$skip + limit;

                            result = await this.getRows(q, params);

                            if (_.isFunction(this.processResult)) {
                                result = await this.processResult(result);
                            }

                            for (const row of result.data) {
                                items.push(await prepareItem(row));
                            }

                            total = result.total;
                            rowCount += result.data.length;
                            page++;
                        } else {
                            let result = null;

                            q.$skip = page * limit;
                            q.$limit = limit;
                            q.$paginate = {
                                default: limit
                            };
                            if (total !== null) {
                                delete q.$paginate;
                            }

                            const r = await this.$collection(this.collection).find(q);

                            if (total === null) {
                                result = r;
                            } else {
                                result = {data: r, total};
                            }

                            if (_.isFunction(this.processResult)) {
                                result = await this.processResult(result);
                            }

                            for (const row of result.data) {
                                items.push(await prepareItem(row));
                            }

                            if (total === null) {
                                total = result.total;
                            }

                            rowCount += result.data.length;

                            page++;
                        }

                        const exportParams = {
                            id: JSON.stringify(`${this.$user._id}${this.tableId}`),
                            name: this.$t('Records'),
                            columns: columns.map(c => ({
                                field: c.field,
                                label: this.$t(c.label)
                                    .split(' ')
                                    .map(w => firstUpper(toLower(w)))
                                    .join(' '),
                                width: c.width,
                                disableFormatting: c.disableFormatting
                            })),
                            items,
                            isLastBock: !(total > 0 && rowCount < total)
                        };
                        if (exportParams.isLastBock && !!this.summaryRow) {
                            exportParams.summary = await this.summaryRow([]);
                        }

                        exportPayload = await this.$rpc('kernel.component.export-table-data', exportParams);

                        // Send progress percentage.
                        if (total !== 0) {
                            const percentage = (rowCount / total) * 100;
                            this.$program.progress({
                                status: 'info',
                                percentage
                            });
                        }
                    } while (total > 0 && rowCount < total);

                    // Finalize progress.
                    this.$program.progress({
                        status: 'success',
                        percentage: 100
                    });
                } catch (error) {
                    const percentage = (rowCount / (total || 0)) * 100;
                    this.$program.progress({
                        status: 'error',
                        message: error.message,
                        percentage
                    });
                }
            } else {
                this.api.showLoadingOverlay();

                const items = [];

                for (const row of this.inMemoryData) {
                    items.push(await prepareItem(row));
                }

                const exportParams = {
                    id: JSON.stringify(`${this.$user._id}${this.tableId}`),
                    name: this.$t('Records'),
                    columns: columns
                        .filter(c => !c.hidden)
                        .map(c => ({
                            field: c.field,
                            label: this.$t(c.label)
                                .split(' ')
                                .map(w => firstUpper(toLower(w)))
                                .join(' '),
                            width: c.width,
                            disableFormatting: c.disableFormatting,
                            isCurrencyColumn: !!c.isCurrencyColumn
                        })),
                    items,
                    isLastBock: true
                };
                if (!!this.summaryRow) {
                    exportParams.summary = await this.summaryRow(this.inMemoryData);
                }

                exportPayload = await this.$rpc('kernel.component.export-table-data', exportParams);

                this.api.hideOverlay();
            }

            const link = document.createElement('a');
            link.href = this.$app.absoluteUrl(`temp-files/${exportPayload.file}?filename=${exportPayload.name}`);
            link.download = exportPayload.name;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        },
        applySearch(value) {
            if (this.calculatedRowModel === 'viewport') {
                this.dataSource.applySearch(value, () => {
                    this.$nextTick(() => {
                        this.initializeSummaryRow();

                        this.scroller.goToTop();
                    });
                });
            } else if (this.calculatedRowModel === 'serverSide') {
                this.dataSource.applySearch(value);
            } else if (this.calculatedRowModel === 'inMemory') {
                this.api.setQuickFilter(value);

                this.$nextTick(() => {
                    this.initializeSummaryRow(this.inMemoryData);

                    this.scroller.goToTop();
                });
            }
        }
    },

    created() {
        // All modules.
        this.allModules = AgGridVue.AllModules;

        // Debounced.
        this.refreshDataDebounced = _.debounce(this.refreshData, 100, {leading: false, trailing: true});
        this.onColumnResizedDebounced = _.debounce(this.onColumnResized, 100, {leading: false, trailing: true});
        this.onColumnChangedDebounced = _.debounce(this.onColumnChanged, 500, {leading: false, trailing: true});
        this.applySearchDebounced = _.debounce(this.applySearch, 300, {leading: false, trailing: true});

        // Initialize default table options.
        this.initializeDefaults();

        // Initialize table columns.
        this.initializeColumns();

        // Initialize initial table sorting.
        this.initializeInitialSorting();

        // Initialize editable table.
        if (this.enableEditing) {
            this.initializeEditableTable();
        }

        // Initialize table row model.
        this.initializeRowModel();

        // Initialize in-memory table data.
        if (this.calculatedRowModel === 'inMemory') {
            const itemsWatcher = _.debounce(
                async value => {
                    if (this.updatingRecord) return;

                    if (Array.isArray(value)) {
                        if (this.enableEditing) {
                            if (value.length === 0) {
                                this.selectEditorMap = [];
                            }

                            this.updatingRecord = true;
                            await this.initializeInMemoryData();

                            this.$nextTick(async () => {
                                await this.initializeSummaryRow(this.inMemoryData);

                                this.initializeSelectEditorMap(this.inMemoryData);

                                if (_.isObject(this.api) && _.isFunction(this.api.redrawRows)) {
                                    this.$nextTick(() => {
                                        this.api.redrawRows();
                                    });
                                }

                                this.updatingRecord = false;
                            });
                        } else {
                            this.updatingRecord = true;

                            await this.initializeInMemoryData();

                            this.$nextTick(async () => {
                                await this.initializeSummaryRow(this.inMemoryData);

                                if (_.isObject(this.api) && _.isFunction(this.api.redrawRows)) {
                                    this.$nextTick(() => {
                                        this.api.redrawRows();
                                    });
                                }

                                this.updatingRecord = false;
                            });
                        }
                    }
                },
                25,
                {leading: false, trailing: true}
            );
            this.$watch('items', itemsWatcher, {deep: true});

            this.initializeInMemoryData();
        }

        // Initialize summary row.
        if (this.calculatedRowModel !== 'serverSide') {
            this.initializeSummaryRow(this.inMemoryData);
        }

        // Enable real-time data change.
        if (_.isString(this.collection)) {
            this.$collection(this.collection).on('all', this.refreshDataDebounced);
        }

        this.isInitialized = true;
    },

    mounted() {
        // Initialize scroller.
        this.$nextTick(() => {
            this.initializeScroller();
        });

        this.$nextTick(() => {
            if (this.calculatedRowModel === 'serverSide' || this.calculatedRowModel === 'viewport') {
                this.dataSource.gridApi = this.api;
            }
        });

        // Initialize enlarge cell header action.
        if (this.enableEditing && this.enableEnlarge) {
            $(this.$el).on('click', '.enlarge-header-cell', () => {
                this.isEnlarged = !this.isEnlarged;

                this.$emit('enlarge-changed', this.isEnlarged);
            });
        }

        // Fix editable table focus problem.
        if (this.enableEditing) {
            this.onDocumentMousedown = event => {
                const leftContainer = $(this.$el).find('.ag-pinned-left-cols-container');
                const centerContainer = $(this.$el).find('.ag-center-cols-container');
                const rightContainer = $(this.$el).find('.ag-pinned-right-cols-container');

                if (_.isObject(this.validationError)) return;
                if (leftContainer[0].contains(event.target)) return;
                if (centerContainer[0].contains(event.target)) return;
                if (rightContainer[0].contains(event.target)) return;
                if ($(event.target).closest('.ui-table-boolean-editor-renderer').length > 0) return;
                if ($(event.target).closest('.el-popper').length > 0) return;
                if ($(event.target).closest('.vue-treeselect').length > 0) return;
                if ($(event.target).closest(`.program-dialog:not([data-id="${this.$params('dialogId')}"])`).length > 0)
                    return;
                if (this.isEditing) return;

                this.api.lastEditedCell = null;
                this.api.clearFocusedCell();
            };
            document.addEventListener('mousedown', this.onDocumentMousedown, true);
        }

        // Initialize middle click.
        $(this.$el).mousedown(event => {
            if (event.which === 2) {
                const $row = $(event.target).closest('.ag-row');
                const id = $row.attr('row-id');

                if (_.isString(id) && !!id.trim()) {
                    this.$emit('middle-clicked', {_id: id.trim()});
                }
            }
        });

        // Tab - Enter Event
        if (this.enableEditing) {
            this.onKeyupForEnterTab = e => {
                const params = this.api.currentEditingCellParams;
                if (!params) {
                    return;
                }

                // Fix
                if (params.editorParams.isDateEditor && e.target.classList.contains('el-input__inner')) {
                    return;
                }

                // Enter key.
                if (e.keyCode === 13 && !!params.eGridCell && params.eGridCell.contains(e.target)) {
                    // this.$nextTick(() => {
                    //     params.api.stopEditing();
                    // });

                    return;
                }

                // Escape key.
                if (e.keyCode === 27 && !!params.eGridCell && params.eGridCell.contains(e.target)) {
                    this.$nextTick(() => {
                        params.api.stopEditing({cancel: true});
                    });

                    return;
                }

                // Previous next.
                if (e.keyCode === 9) {
                    if (
                        params.editorParams.isRequired &&
                        (_.isUndefined(params.value) || _.isNull(params.value) || params.value === '')
                    ) {
                        const validationError = {
                            rowIndex: params.rowIndex,
                            field: params.colDef.field,
                            message: this.$t('{{label}} is required', {label: params.colDef.headerName})
                        };
                        this.validationError = validationError;

                        if (!!params.eGridCell) {
                            params.eGridCell.classList.add('has-error');

                            const input = params.eGridCell.querySelector('.el-input__inner');
                            if (!!input) {
                                input.focus();
                            }
                        }

                        this.$program.message('error', validationError.message);

                        return false;
                    }

                    if (e.shiftKey) {
                        params.goToPreviousCell({
                            data: params.node.data,
                            rowPinned: params.node.rowPinned
                        });
                    } else {
                        params.goToNextCell({
                            data: params.node.data,
                            rowPinned: params.node.rowPinned
                        });
                    }
                }
            };

            document.addEventListener('keyup', this.onKeyupForEnterTab);
        }
    },

    beforeDestroy() {
        if (this.enableEditing) {
            document.removeEventListener('mousedown', this.onDocumentMousedown, true);
            document.removeEventListener('keyup', this.onKeyupForEnterTab);
        }

        // Enable real-time data change.
        if (_.isString(this.collection)) {
            this.$collection(this.collection).removeListener('all', this.refreshDataDebounced);
        }
    },

    destroyed() {
        // Destroy scroller.
        this.scroller.destroy();
    },

    components: {
        AgGridVue
    }
};
</script>
