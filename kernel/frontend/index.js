import './assets/scss/app.scss';
import init from './app/index';

// import {wasm} from 'framework/wasm';
// wasm.validate({
//     name: {
//         type: 'string',
//         label: 'Name'
//     },
//     age: {
//         type: 'integer',
//         label: 'Age'
//     },
//     phoneNumbers: {
//         type: [
//             {
//                 type: 'object',
//                 blackbox: true
//             }
//         ],
//         label: 'Phone Numbers',
//         default: []
//     }
// });

(async () => {
    await init();
})();

// Enable Service workers.
if ('serviceWorker' in navigator && process.env.NODE_ENV !== 'development') {
    window.addEventListener('load', () => {
        navigator.serviceWorker.register('/sw.js').catch(error => console.error(error));
    });
}
