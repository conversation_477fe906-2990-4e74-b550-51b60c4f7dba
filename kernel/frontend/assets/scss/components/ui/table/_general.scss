.ui-table {
    position: relative;

    &:not(.auto-height) {
        width: 100%;
        height: 100%;
        overflow: hidden;
    }

    //&.auto-height, &.auto-height .ag-root {
    //    min-height: 60px;
    //}
    .ag-body-viewport.ag-layout-auto-height .ag-center-cols-clipper,
    .ag-body-viewport.ag-layout-auto-height .ag-center-cols-container {
        min-height: 24px;
    }

    .ag-body-viewport {
        position: relative;
        overflow: hidden;
    }

    .ag-center-cols-viewport {
        overflow: hidden;
        padding-right: 1px;
    }

    .ag-floating-bottom {
        overflow: hidden !important;
    }

    .ag-body-horizontal-scroll {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 12px !important;
        max-height: 12px !important;
        min-height: 12px !important;

        .ag-body-horizontal-scroll-viewport {
            position: relative;
            width: 100%;
            height: 12px !important;
            max-height: 12px !important;
            min-height: 12px !important;
            overflow: hidden;
        }

        .ag-body-horizontal-scroll-container {
            height: 12px !important;
            max-height: 12px !important;
            min-height: 12px !important;
        }

        .ag-horizontal-left-spacer {
            border-right: none;
        }

        .ag-horizontal-left-spacer, .ag-horizontal-right-spacer {
            overflow: hidden;
        }
    }

    .ag-root:hover .ag-body-horizontal-scroll {
        .ps__rail-x {
            opacity: 1;
        }

        .ps .ps__rail-x:hover,
        .ps .ps__rail-x:focus,
        .ps .ps__rail-x.ps--clicking {
            opacity: 1;
        }
    }

    &.ag-theme-balham .ag-horizontal-right-spacer {
        border-left: none;
    }

    &:not(.is-editable) .ag-floating-bottom {
        margin-top: 0;

        .row-handle, .add-remove-cell {
            display: none !important;
        }

        .ag-row {
            border-bottom: none !important;
            background-color: #fff !important;
        }

        .ag-cell {
            border: none !important;
            font-weight: $font-weight-bold;
            font-size: 12px;
            background-color: #fff !important;
        }
    }

    &.no-zebra.ag-theme-balham .ag-row-even:not(.ag-row-hover):not(.ag-row-selected):not(.ag-row-group) {
        background-color: transparent;
    }

    &.has-server-side-row-model.ag-theme-balham {
        .ag-full-width-viewport-wrapper {
            display: none;
        }
    }

    &:not(.is-editable) {
        .ag-floating-bottom {
            margin-top: 0;
        }

        .ag-floating-bottom-viewport .ag-floating-bottom-container,
        .ag-pinned-left-floating-bottom,
        .ag-pinned-right-floating-bottom {
            .ag-row {
                height: 27px !important;
                background-color: $primary !important;

                .ag-cell {
                    height: 27px;
                    line-height: 25px;
                    color: #fff !important;
                    background-color: $primary !important;
                }
            }
        }

        .ag-cell {
            line-height: 21px;
        }
    }

    .ag-cell {
        &.is-success {
            background-color: $success-lighter;
        }

        &.is-warning {
            background-color: $warning-lighter;
        }

        &.is-danger {
            background-color: $danger-lighter;
        }
    }

    .ag-row:hover .ag-cell {
        &.is-success {
            background-color: $success-light;
        }

        &.is-warning {
            background-color: $warning-light;
        }

        &.is-danger {
            background-color: $danger-light;
        }
    }

    .ag-row.has-warning .ag-cell:not(.add-remove-cell) {
        background-color: $warning-lighter !important;
    }

    .ag-row.has-error .ag-cell:not(.add-remove-cell) {
        background-color: $danger-lighter !important;
    }

    .popup-edit-cell {
        background-color: #fff;
        cursor: pointer;
        padding: 0;
        text-align: center;

        &.is-disabled {
            cursor: default;

            i {
                color: $text-color-lighter !important;
            }
        }
    }

    &.hide-loading-stub-row {
        .ag-row-stub.ag-full-width-row {
            display: none;
        }
    }

    &.ag-theme-balham .ag-overlay-loading-wrapper {
        background-color: transparent;
    }

    .ag-header-select-all {
        margin-left: -3px;
    }

    .ag-row.bg-green {
        .ag-cell.row-handle {
            background-color: palate('green', '600') !important;
            color: #fff !important;
        }

        .ag-cell:not(.add-remove-cell):not(.row-handle):not(.popup-edit-cell):not(.is-danger), .ag-cell.is-disabled {
            background-color: mix(#fff, palate('green', '600'), 90%) !important;
        }

        &.ag-row-hover {
            .ag-cell:not(.add-remove-cell):not(.row-handle):not(.popup-edit-cell):not(.is-danger), .ag-cell.is-disabled {
                background-color: mix(#fff, palate('green', '600'), 80%) !important;
            }

            .ag-cell.row-handle {
                background-color: palate('green', '600') !important;
                color: #fff !important;
            }
        }
    }

    .ag-row.bg-red {
        .ag-cell.row-handle {
            background-color: palate('red', '600') !important;
            color: #fff !important;
        }

        .ag-cell:not(.add-remove-cell):not(.row-handle):not(.popup-edit-cell), .ag-cell.is-disabled {
            background-color: mix(#fff, palate('red', '600'), 90%) !important;
        }

        &.ag-row-hover {
            .ag-cell:not(.add-remove-cell):not(.row-handle):not(.popup-edit-cell), .ag-cell.is-disabled {
                background-color: mix(#fff, palate('red', '600'), 80%) !important;
            }

            .ag-cell.row-handle {
                background-color: palate('red', '600') !important;
                color: #fff !important;
            }
        }
    }

    .ag-row.bg-orange {
        .ag-cell.row-handle {
            background-color: palate('orange', '600') !important;
            color: #fff !important;
        }

        .ag-cell:not(.add-remove-cell):not(.row-handle):not(.popup-edit-cell), .ag-cell.is-disabled {
            background-color: mix(#fff, palate('orange', '600'), 90%) !important;
        }

        &.ag-row-hover {
            .ag-cell:not(.add-remove-cell):not(.row-handle):not(.popup-edit-cell), .ag-cell.is-disabled {
                background-color: mix(#fff, palate('orange', '600'), 80%) !important;
            }

            .ag-cell.row-handle {
                background-color: palate('orange', '600') !important;
                color: #fff !important;
            }
        }
    }

    .ag-row.bg-purple {
        .ag-cell.row-handle {
            background-color: palate('purple', '600') !important;
            color: #fff !important;
        }

        .ag-cell:not(.add-remove-cell):not(.row-handle):not(.popup-edit-cell), .ag-cell.is-disabled {
            background-color: mix(#fff, palate('purple', '600'), 90%) !important;
        }

        &.ag-row-hover {
            .ag-cell:not(.add-remove-cell):not(.row-handle):not(.popup-edit-cell), .ag-cell.is-disabled {
                background-color: mix(#fff, palate('purple', '600'), 80%) !important;
            }

            .ag-cell.row-handle {
                background-color: palate('purple', '600') !important;
                color: #fff !important;
            }
        }
    }

    .ag-row.is-disabled {
        opacity: 0.75;

        &:not(.ag-row-selected) {
            .ag-cell:not(.add-remove-cell):not(.row-handle):not(.popup-edit-cell), .ag-cell.is-disabled {
                background-color: $light-100 !important;
            }
        }

        &:not(.ag-row-selected).ag-row-hover {
            .ag-cell:not(.add-remove-cell):not(.row-handle):not(.popup-edit-cell), .ag-cell.is-disabled {
                background-color: $light-200 !important;
            }
        }
    }
}

.ui-table:not(.is-editable) {
    .ag-floating-bottom-viewport,
    .ag-floating-bottom-viewport .ag-floating-bottom-container {
        background-color: darken($light-25, 4%) !important;
    }

    .ag-floating-bottom-viewport .ag-floating-bottom-container,
    .ag-pinned-left-floating-bottom,
    .ag-pinned-right-floating-bottom {
        .ag-row {
            border-color: darken($light-25, 4%) !important;
            background-color: darken($light-25, 4%) !important;

            .ag-cell {
                color: $text-color !important;
                background-color: darken($light-25, 4%) !important;
            }
        }
    }
}

.program:not(.is-active) {
    .ui-table:not(.is-editable) .ag-root:not(.ag-layout-auto-height) {
        .ag-floating-bottom-viewport .ag-floating-bottom-container,
        .ag-pinned-left-floating-bottom,
        .ag-pinned-right-floating-bottom {
            .ag-row {
                background-color: darken($light-25, 4%) !important;

                .ag-cell {
                    color: $text-color !important;
                    background-color: darken($light-25, 4%) !important;
                }
            }
        }
    }
}

.ui-table-with-scope {
    position: relative;
    width: 100%;
    height: 100%;
    padding-top: 45px;
    overflow: hidden;

    .table-with-scope-header {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 45px;
        padding: 0 10px;
        overflow: hidden;
    }

    .table-with-scope-content {
        position: relative;
        width: 100%;
        height: 100%;
        overflow: hidden;
    }
}
