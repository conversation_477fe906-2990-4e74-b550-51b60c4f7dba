import _ from 'lodash';
import {ObjectId} from 'mongodb';

export default {
    name: 'orders',
    title: 'Orders',
    branch: true,
    assignable: true,
    view: 'sale.sales.orders',
    labelParams: {
        from: 'code'
    },
    locationFrom: 'deliveryAddress',
    extraIndexes: [
        {type: 'normal', key: 'extra.*', value: 1},
        {type: 'normal', key: 'additionalInformation.*', value: 1}
    ],
    schema: {
        // General
        module: {
            type: 'string',
            label: 'Module',
            allowed: ['sale', 'ecommerce', 'service', 'project', 'rental'],
            default: 'sale',
            index: true
        },
        status: {
            type: 'string',
            label: 'Status',
            allowed: ['draft', 'payment-planned', 'approved', 'to-invoice', 'invoiced', 'canceled'],
            default: 'draft',
            index: true
        },
        stageId: {
            type: 'string',
            label: 'Stage',
            required: false,
            index: true
        },
        storeId: {
            type: 'string',
            label: 'Store',
            required: false,
            index: true
        },
        documentTypeId: {
            type: 'string',
            label: 'Document type',
            index: true
        },
        code: {
            type: 'string',
            label: 'Code',
            unique: true,
            index: true
        },
        financialEntryIds: {
            type: ['string'],
            label: 'Financial entries',
            index: true,
            default: []
        },
        receiptId: {
            type: 'string',
            label: 'Payment',
            index: true,
            required: false
        },
        reference: {
            type: 'string',
            label: 'Reference',
            index: true,
            required: false
        },
        partnerGroupId: {
            type: 'string',
            label: 'Customer group',
            required: false,
            index: true
        },
        partnerId: {
            type: 'string',
            label: 'Customer',
            index: true
        },
        currencyId: {
            type: 'string',
            label: 'Currency',
            index: true
        },
        currencyRate: {
            type: 'decimal',
            label: 'Currency rate',
            default: 1,
            index: true
        },
        contactPersonId: {
            type: 'string',
            label: 'Contact person',
            required: false
        },
        recordDate: {
            type: 'date',
            label: 'Record date',
            default: 'date:now',
            index: true
        },
        orderDate: {
            type: 'date',
            label: 'Order date',
            default: 'date:now',
            index: true
        },
        dueDate: {
            type: 'date',
            label: 'Due date',
            default: 'date:now',
            index: true
        },
        scheduledDate: {
            type: 'date',
            label: 'Delivery date',
            default: 'date:now',
            index: true
        },
        note: {
            type: 'string',
            label: 'Note',
            required: false
        },

        // Totals
        subTotal: {
            type: 'decimal',
            label: 'Subtotal',
            default: 0,
            index: true
        },
        discount: {
            type: 'decimal',
            label: 'Discount %',
            default: 0,
            index: true
        },
        discountAmount: {
            type: 'decimal',
            label: 'Discount',
            default: 0,
            index: true
        },
        subTotalAfterDiscount: {
            type: 'decimal',
            label: 'Subtotal after discount',
            default: 0,
            index: true
        },
        taxTotal: {
            type: 'decimal',
            label: 'Tax total',
            default: 0,
            index: true
        },
        rounding: {
            type: 'decimal',
            label: 'Rounding',
            default: 0,
            index: true
        },
        grandTotal: {
            type: 'decimal',
            label: 'Grand total',
            default: 0,
            index: true
        },
        paidTotal: {
            type: 'decimal',
            label: 'Paid total',
            default: 0
        },
        plannedTotal: {
            type: 'decimal',
            label: 'Planned total',
            default: 0
        },
        appliedTaxes: {
            type: [
                {
                    type: 'object',
                    blackbox: true,
                    required: false
                }
            ],
            default: []
        },

        // Items
        items: {
            type: [
                {
                    id: {
                        type: 'string',
                        required: false
                    },
                    productId: {
                        type: 'string',
                        label: 'Product',
                        index: true
                    },
                    productCode: {
                        type: 'string',
                        label: 'Product code'
                    },
                    productDefinition: {
                        type: 'string',
                        label: 'Product definition'
                    },
                    productCategoryId: {
                        type: 'string',
                        label: 'Product category',
                        required: false
                    },
                    productCategoryPath: {
                        type: 'string',
                        label: 'Product category path',
                        required: false
                    },
                    productType: {
                        type: 'string',
                        label: 'Product type',
                        index: true
                    },
                    barcode: {
                        type: 'string',
                        label: 'Barcode',
                        required: false,
                        index: true
                    },
                    description: {
                        type: 'string',
                        label: 'Description',
                        required: false
                    },
                    scheduledDate: {
                        type: 'date',
                        label: 'Delivery Date',
                        default: 'date:now'
                    },
                    customerCatalogNo: {
                        type: 'string',
                        label: 'Customer catalog no',
                        required: false
                    },
                    branchId: {
                        type: 'string',
                        label: 'Branch Office'
                    },
                    warehouseId: {
                        type: 'string',
                        label: 'Warehouse'
                    },
                    vendorId: {
                        type: 'string',
                        label: 'Vendor',
                        required: false
                    },
                    quantity: {
                        type: 'decimal',
                        label: 'Quantity',
                        default: 1
                    },
                    unitId: {
                        type: 'string',
                        label: 'Unit'
                    },
                    baseUnitId: {
                        type: 'string',
                        label: 'Base unit'
                    },
                    baseQuantity: {
                        type: 'decimal',
                        label: 'Base quantity',
                        default: 1
                    },
                    priceListId: {
                        type: 'string',
                        label: 'Price list',
                        required: false
                    },
                    currencyId: {
                        type: 'string',
                        label: 'Currency',
                        required: false
                    },
                    currencyRate: {
                        type: 'decimal',
                        label: 'Currency rate',
                        default: 1
                    },
                    unitPrice: {
                        type: 'decimal',
                        label: 'Unit Price',
                        default: 0
                    },
                    unitPriceFC: {
                        type: 'decimal',
                        label: 'Unit price (FC)',
                        default: 0
                    },
                    grossUnitPrice: {
                        type: 'decimal',
                        label: 'Gross Unit Price',
                        required: false
                    },
                    surplusGoodDescription: {
                        type: 'string',
                        label: 'Surplus good description',
                        required: false
                    },
                    hasSurplusGood: {
                        type: 'boolean',
                        label: 'Has surplus good',
                        required: false
                    },
                    selloutId: {
                        type: 'string',
                        label: 'Sellout',
                        required: false
                    },
                    selloutCode: {
                        type: 'string',
                        label: 'Sellout code',
                        required: false
                    },
                    selloutName: {
                        type: 'string',
                        label: 'Sellout name',
                        required: false
                    },
                    selloutAmount: {
                        type: 'decimal',
                        label: 'Sellout amount',
                        required: false
                    },
                    discount: {
                        type: 'decimal',
                        label: 'Discount %',
                        default: 0
                    },
                    discountPayload: {
                        type: 'object',
                        blackbox: true,
                        required: false,
                        column: {
                            hidden: true
                        }
                    },
                    unitPriceAfterDiscount: {
                        type: 'decimal',
                        label: 'Unit price after discount',
                        default: 0
                    },
                    grossUnitPriceAfterDiscount: {
                        type: 'decimal',
                        label: 'Gross unit price after discount',
                        default: 0
                    },
                    freight: {
                        type: 'decimal',
                        label: 'Freight',
                        default: 0
                    },
                    taxId: {
                        type: 'string',
                        label: 'Tax',
                        required: false
                    },
                    taxPayload: {
                        type: 'object',
                        blackbox: true,
                        required: false
                    },
                    taxDetail: {
                        type: 'object',
                        blackbox: true,
                        required: false
                    },
                    taxTotal: {
                        type: 'decimal',
                        label: 'Tax Total',
                        default: 0
                    },
                    grossTotal: {
                        type: 'decimal',
                        label: 'Gross total',
                        default: 0
                    },
                    realTotal: {
                        type: 'decimal',
                        label: 'Real Total',
                        default: 0,
                        index: true
                    },
                    stockQuantity: {
                        type: 'decimal',
                        label: 'Stock on hand',
                        default: 0
                    },
                    orderedQuantity: {
                        type: 'decimal',
                        label: 'Ordered quantity',
                        default: 0
                    },
                    assignedQuantity: {
                        type: 'decimal',
                        label: 'Assigned quantity',
                        default: 0
                    },
                    availableQuantity: {
                        type: 'decimal',
                        label: 'Available quantity',
                        default: 0
                    },
                    warehouseStockQuantity: {
                        type: 'decimal',
                        label: 'Warehouse stock on hand',
                        default: 0
                    },
                    warehouseOrderedQuantity: {
                        type: 'decimal',
                        label: 'Warehouse ordered quantity',
                        default: 0
                    },
                    warehouseAssignedQuantity: {
                        type: 'decimal',
                        label: 'Warehouse assigned quantity',
                        default: 0
                    },
                    warehouseAvailableQuantity: {
                        type: 'decimal',
                        label: 'Warehouse available quantity',
                        default: 0
                    },
                    financialProjectId: {
                        type: 'string',
                        label: 'Project',
                        required: false
                    },
                    total: {
                        type: 'decimal',
                        label: 'Total',
                        default: 0,
                        index: true
                    },
                    totalFC: {
                        type: 'decimal',
                        label: 'Total (FC)',
                        default: 0
                    },

                    // Internal
                    partnerId: {
                        type: 'string',
                        required: false
                    },
                    invoiceResponsibleId: {
                        type: 'string',
                        label: 'Invoice responsible',
                        required: false
                    },
                    invoiceAddressId: {
                        type: 'string',
                        required: false
                    },
                    invoiceAddress: {
                        type: 'object',
                        label: 'Invoice address',
                        blackbox: true,
                        required: false
                    },
                    deliveryReceiverId: {
                        type: 'string',
                        label: 'Delivery receiver',
                        required: false
                    },
                    deliveryAddressId: {
                        type: 'string',
                        required: false
                    },
                    deliveryAddressCode: {
                        type: 'string',
                        label: 'Delivery address code',
                        required: false
                    },
                    deliveryAddress: {
                        type: 'object',
                        label: 'Delivery address',
                        blackbox: true,
                        required: false
                    },
                    deliveryNote: {
                        type: 'string',
                        label: 'Delivery note',
                        required: false
                    },
                    contractApplied: {
                        type: 'boolean',
                        default: false
                    },
                    discountListApplied: {
                        type: 'boolean',
                        default: false
                    },
                    brandId: {
                        type: 'string',
                        label: 'Brand',
                        required: false
                    },
                    brandName: {
                        type: 'string',
                        label: 'Brand name',
                        required: false
                    },
                    shippingUnitId: {
                        type: 'string',
                        label: 'Shipping unit',
                        required: false
                    },
                    typeOfGoodsId: {
                        type: 'string',
                        label: 'Type of goods',
                        required: false
                    },
                    hsCode: {
                        type: 'string',
                        label: 'HS code',
                        required: false
                    },
                    manufacturerId: {
                        type: 'string',
                        label: 'Manufacturer',
                        required: false
                    },
                    manufacturerProductCode: {
                        type: 'string',
                        label: 'Manufacturer product code',
                        required: false
                    },
                    countryOfManufactureId: {
                        type: 'string',
                        label: 'Country of manufacture',
                        required: false
                    },
                    classificationCode: {
                        type: 'string',
                        label: 'Classification code',
                        required: false
                    },
                    classificationVersion: {
                        type: 'string',
                        label: 'Classification version',
                        required: false
                    },
                    classificationValue: {
                        type: 'string',
                        label: 'Classification value',
                        required: false
                    },
                    countryOfOriginId: {
                        type: 'string',
                        label: 'Country of origin',
                        required: false
                    },
                    containerTypeId: {
                        type: 'string',
                        label: 'Container type',
                        required: false
                    },
                    containerNo: {
                        type: 'string',
                        label: 'Container no',
                        required: false
                    },
                    containerBrand: {
                        type: 'string',
                        label: 'Container brand',
                        required: false
                    },
                    containerQuantity: {
                        type: 'decimal',
                        label: 'Container quantity',
                        required: false
                    },
                    netWeight: {
                        type: 'decimal',
                        label: 'Net weight',
                        required: false
                    },
                    netWeightUnitId: {
                        type: 'string',
                        label: 'Net weight unit',
                        required: false
                    },
                    grossWeight: {
                        type: 'decimal',
                        label: 'Gross weight',
                        required: false
                    },
                    grossWeightUnitId: {
                        type: 'string',
                        label: 'Gross weight unit',
                        required: false
                    },
                    netVolume: {
                        type: 'decimal',
                        label: 'Net volume',
                        required: false
                    },
                    netVolumeUnitId: {
                        type: 'string',
                        label: 'Net volume unit',
                        required: false
                    },
                    grossVolume: {
                        type: 'decimal',
                        label: 'Gross volume',
                        required: false
                    },
                    grossVolumeUnitId: {
                        type: 'string',
                        label: 'Gross volume unit',
                        required: false
                    },
                    height: {
                        type: 'decimal',
                        label: 'Height',
                        required: false
                    },
                    heightUnitId: {
                        type: 'string',
                        label: 'Height unit',
                        required: false
                    },
                    width: {
                        type: 'decimal',
                        label: 'Width',
                        required: false
                    },
                    widthUnitId: {
                        type: 'string',
                        label: 'Width unit',
                        required: false
                    },
                    depth: {
                        type: 'decimal',
                        label: 'Depth',
                        required: false
                    },
                    depthUnitId: {
                        type: 'string',
                        label: 'Depth unit',
                        required: false
                    },
                    paymentResponsibleId: {
                        type: 'string',
                        label: 'Payment responsible',
                        required: false
                    },
                    paymentAddressId: {
                        type: 'string',
                        required: false
                    },
                    paymentAddress: {
                        type: 'object',
                        label: 'Payment address',
                        blackbox: true,
                        required: false
                    },
                    pcmModelId: {
                        type: 'string',
                        required: false
                    },
                    pcmConfigurationId: {
                        type: 'string',
                        required: false
                    },
                    pcmHash: {
                        type: 'string',
                        required: false
                    },
                    costSource: {
                        type: 'string',
                        label: 'Cost source',
                        required: false
                    },
                    unitCost: {
                        type: 'decimal',
                        label: 'Unit cost',
                        default: 0
                    },
                    unitProfit: {
                        type: 'decimal',
                        label: 'Unit profit',
                        default: 0
                    },
                    cost: {
                        type: 'decimal',
                        label: 'Cost',
                        default: 0
                    },
                    profitRate: {
                        type: 'decimal',
                        label: 'Profit rate %',
                        default: 0
                    },
                    profitMargin: {
                        type: 'decimal',
                        label: 'Profit margin %',
                        default: 0
                    },
                    profit: {
                        type: 'decimal',
                        label: 'Profit',
                        default: 0
                    },
                    subItems: {
                        type: [
                            {
                                productId: {
                                    type: 'string',
                                    label: 'Product ID'
                                },
                                productCode: {
                                    type: 'string',
                                    label: 'Product code'
                                },
                                productDefinition: {
                                    type: 'string',
                                    label: 'Product definition'
                                },
                                quantity: {
                                    type: 'decimal',
                                    label: 'Quantity',
                                    default: 0
                                },
                                unitPrice: {
                                    type: 'decimal',
                                    label: 'Unit price',
                                    default: 0
                                },
                                unitId: {
                                    type: 'string',
                                    label: 'Unit ID'
                                },
                                selloutId: {
                                    type: 'string',
                                    label: 'Sellout',
                                    required: false
                                },
                                selloutCode: {
                                    type: 'string',
                                    label: 'Sellout code',
                                    required: false
                                },
                                selloutName: {
                                    type: 'string',
                                    label: 'Sellout name',
                                    required: false
                                },
                                selloutAmount: {
                                    type: 'decimal',
                                    label: 'Sellout amount',
                                    required: false
                                }
                            }
                        ],
                        default: []
                    },
                    copyableItemId: {
                        type: 'string',
                        required: false
                    }
                }
            ],
            default: []
        },

        // Organization
        organizationId: {
            type: 'string',
            label: 'Organization',
            required: false,
            index: true
        },
        salesManagerId: {
            type: 'string',
            label: 'Sales manager',
            required: false,
            index: true
        },
        salespersonId: {
            type: 'string',
            label: 'Salesperson',
            required: false,
            index: true
        },

        // Exchange rates.
        exchangeRates: {
            type: [
                {
                    currencyName: {
                        type: 'string',
                        label: 'Currency'
                    },
                    rate: {
                        type: 'decimal',
                        label: 'Rate',
                        default: 0
                    }
                }
            ],
            default: []
        },
        exchangeRatesMap: {
            type: 'object',
            blackbox: true,
            required: false
        },

        // Details.
        financialProjectId: {
            type: 'string',
            label: 'Project',
            required: false,
            index: true
        },
        sourceId: {
            type: 'string',
            label: 'Source',
            required: false,
            index: true
        },
        communicationChannelId: {
            type: 'string',
            label: 'Communication channel',
            required: false,
            index: true
        },
        referencePartnerId: {
            type: 'string',
            label: 'Reference partner',
            required: false
        },
        partnerOrderReference: {
            type: 'string',
            label: 'Partner order reference',
            required: false
        },
        partnerOrderDate: {
            type: 'date',
            label: 'Partner order date',
            required: false
        },

        // Logistics
        warehouseId: {
            type: 'string',
            label: 'Warehouse'
        },
        invoiceResponsibleId: {
            type: 'string',
            label: 'Invoice responsible',
            required: false
        },
        invoiceAddressId: {
            type: 'string',
            required: false
        },
        invoiceAddress: {
            type: 'object',
            label: 'Invoice address',
            blackbox: true,
            required: false
        },
        deliveryReceiverId: {
            type: 'string',
            label: 'Delivery receiver',
            required: false
        },
        deliveryAddressId: {
            type: 'string',
            required: false
        },
        deliveryAddressCode: {
            type: 'string',
            label: 'Delivery address code',
            required: false
        },
        deliveryAddress: {
            type: 'object',
            label: 'Delivery address',
            blackbox: true,
            required: false
        },
        deliveryPriority: {
            type: 'string',
            label: 'Delivery priority',
            allowed: ['not-urgent', 'normal', 'urgent', 'very-urgent'],
            default: 'normal'
        },
        deliveryPolicy: {
            type: 'string',
            label: 'Delivery policy',
            allowed: ['when-one-ready', 'when-all-ready'],
            default: 'when-one-ready'
        },
        deliveryConditionId: {
            type: 'string',
            label: 'Delivery condition',
            required: false
        },
        deliveryMethodId: {
            type: 'string',
            label: 'Delivery method',
            required: false
        },
        deliveryNote: {
            type: 'string',
            label: 'Delivery note',
            required: false
        },
        imoAndMmsiNo: {
            type: 'string',
            label: 'IMO and MMSI no',
            required: false
        },
        shipName: {
            type: 'string',
            label: 'Ship name',
            required: false
        },
        shipRadioCallName: {
            type: 'string',
            label: 'Ship radio call name',
            required: false
        },
        shipRegistrationName: {
            type: 'string',
            label: 'Ship registration name',
            required: false
        },
        shipNetWeight: {
            type: 'decimal',
            label: 'Ship net weight',
            required: false
        },
        shipGrossWeight: {
            type: 'decimal',
            label: 'Ship gross weight',
            required: false
        },
        shipRequirements: {
            type: 'string',
            label: 'Ship requirements',
            required: false
        },
        shipPortOfRegistration: {
            type: 'string',
            label: 'Ship port of registration',
            required: false
        },
        trainNo: {
            type: 'string',
            label: 'Train no',
            required: false
        },
        trainWagonNo: {
            type: 'string',
            label: 'Train wagon no',
            required: false
        },
        licensePlateNo: {
            type: 'string',
            label: 'License plate no',
            required: false
        },
        aircraftNo: {
            type: 'string',
            label: 'Aircraft no',
            required: false
        },
        carrierId: {
            type: 'string',
            label: 'Carrier',
            required: false
        },
        cargoTrackingCode: {
            type: 'string',
            label: 'Cargo tracking code',
            required: false
        },
        shippingPaymentType: {
            type: 'string',
            label: 'Shipment payment type',
            required: false
        },

        // Financial.
        paymentTermId: {
            type: 'string',
            label: 'Payment term'
        },
        priceListId: {
            type: 'string',
            label: 'Price list',
            required: false
        },
        customerPriceListId: {
            type: 'string',
            label: 'Customer price list',
            required: false
        },
        guaranteeId: {
            type: 'string',
            label: 'Guarantee',
            required: false
        },
        paymentTerm: {
            type: 'object',
            blackbox: true,
            required: false
        },
        paymentPlan: {
            type: 'object',
            blackbox: true,
            required: false
        },
        paymentPlanningDate: {
            type: 'date',
            label: 'Payment planning date',
            required: false
        },
        guarantorIds: {
            type: ['string'],
            label: 'Guarantors',
            default: []
        },
        paymentResponsibleId: {
            type: 'string',
            label: 'Payment responsible',
            required: false
        },
        paymentAddressId: {
            type: 'string',
            required: false
        },
        paymentAddress: {
            type: 'object',
            label: 'Payment address',
            blackbox: true,
            required: false
        },

        // Content
        content: {
            type: 'string',
            label: 'Content',
            required: false
        },

        // Attachments.
        attachments: {
            type: ['string'],
            default: []
        },

        // Internal.
        workflowApprovalStatus: {
            type: 'string',
            label: 'Workflow approval status',
            required: false,
            index: true
        },
        paymentStatus: {
            type: 'string',
            label: 'Payment status',
            default: 'not-paid',
            allowed: ['not-paid', 'partially-paid', 'paid'],
            index: true
        },
        transferIds: {
            type: ['string'],
            default: [],
            index: true
        },
        relatedDocuments: {
            type: [
                {
                    collection: 'string',
                    view: 'string',
                    title: 'string',
                    ids: {
                        type: ['string'],
                        default: []
                    }
                }
            ],
            default: []
        },
        paymentPlanBackup: {
            type: 'object',
            blackbox: true,
            required: false
        },
        returnIds: {
            type: ['string'],
            default: []
        },
        partialDeliveries: {
            type: [
                {
                    type: 'object',
                    blackbox: true,
                    required: false
                }
            ],
            default: []
        },
        limitParams: {
            type: 'object',
            blackbox: true,
            required: false
        },
        contractParams: {
            type: 'object',
            blackbox: true,
            required: false
        },
        campaigns: {
            type: [
                {
                    type: 'object',
                    blackbox: true,
                    required: false
                }
            ],
            default: []
        },
        cancellationRequested: {
            type: 'boolean',
            required: false,
            index: true
        },
        cancellationRequestedAt: {
            type: 'datetime',
            required: false,
            index: true
        },
        extra: {
            cashAmount: {
                type: 'decimal',
                required: false
            },
            moneyTransferAmount: {
                type: 'decimal',
                required: false
            },
            chequeAmount: {
                type: 'decimal',
                required: false
            },
            promissoryNoteAmount: {
                type: 'decimal',
                required: false
            },
            posAmount: {
                type: 'decimal',
                required: false
            },
            cashInstallmentCount: {
                type: 'decimal',
                required: false
            },
            moneyTransferInstallmentCount: {
                type: 'decimal',
                required: false
            },
            chequeInstallmentCount: {
                type: 'decimal',
                required: false
            },
            promissoryNoteInstallmentCount: {
                type: 'decimal',
                required: false
            },
            posInstallmentCount: {
                type: 'decimal',
                required: false
            },
            installmentCount: {
                type: 'decimal',
                required: false
            },
            basePrice: {
                type: 'decimal',
                required: false
            },
            grossProfit: {
                type: 'decimal',
                required: false
            },
            profitRate: {
                type: 'decimal',
                required: false
            },
            profitMargin: {
                type: 'decimal',
                required: false
            }
        },
        // Used by ecommerce (Trendyol, Amazon etc.)
        integrationId: {
            type: 'string',
            label: 'Integration order',
            required: false
        },
        integrationPayload: {
            type: 'object',
            required: false,
            blackbox: true
        },
        freight: {
            type: 'object',
            blackbox: true,
            required: false
        },
        deliveryStatus: {
            type: 'string',
            label: 'Delivery status',
            default: 'none',
            index: true
        },
        deliveries: {
            type: [
                {
                    deliveryId: {
                        type: 'string',
                        label: 'Delivery ID'
                    },
                    deliveryCode: {
                        type: 'string',
                        label: 'Delivery code'
                    },
                    deliveryDate: {
                        type: 'datetime',
                        label: 'Delivery date'
                    },
                    deliveryDocumentNo: {
                        type: 'string',
                        label: 'Delivery document no',
                        required: false
                    },
                    deliveryStatus: {
                        type: 'string',
                        label: 'Delivery status'
                    }
                }
            ],
            default: []
        },
        shipmentStatus: {
            type: 'string',
            label: 'Shipment status',
            default: 'none',
            index: true
        },
        shipments: {
            type: [
                {
                    transactionDate: {
                        type: 'datetime',
                        label: 'Transaction date'
                    },
                    shipmentStatus: {
                        type: 'string',
                        label: 'Shipment status'
                    }
                }
            ],
            default: []
        },
        // Additional information
        additionalInformation: {
            type: 'object',
            blackbox: true,
            required: false
        },
        additionalInformationId: {
            type: 'string',
            label: 'Additional information',
            required: false
        },

        // Stages
        stages: {
            type: [
                {
                    type: 'object',
                    blackbox: true,
                    required: false
                }
            ],
            required: false
        },
        stageHistory: {
            type: [
                {
                    type: 'object',
                    blackbox: true,
                    required: false
                }
            ],
            required: false
        }
    },
    attributes: {
        stage: {
            collection: 'sale.order-stages',
            parentField: 'stageId',
            childField: '_id'
        },
        documentType: {
            collection: 'sale.document-types',
            parentField: 'documentTypeId',
            childField: '_id'
        },
        partnerGroup: {
            collection: 'kernel.partner-groups',
            parentField: 'partnerGroupId',
            childField: '_id'
        },
        partner: {
            collection: 'kernel.partners',
            parentField: 'partnerId',
            childField: '_id'
        },
        referencePartner: {
            collection: 'kernel.partners',
            parentField: 'referencePartnerId',
            childField: '_id'
        },
        contactPerson: {
            collection: 'kernel.contacts',
            parentField: 'contactPersonId',
            childField: '_id'
        },
        branch: {
            collection: 'kernel.branches',
            parentField: 'branchId',
            childField: '_id'
        },
        warehouse: {
            collection: 'inventory.warehouses',
            parentField: 'warehouseId',
            childField: '_id'
        },
        currency: {
            collection: 'kernel.currencies',
            parentField: 'currencyId',
            childField: '_id'
        },
        organization: {
            collection: 'kernel.organizations',
            parentField: 'organizationId',
            childField: '_id'
        },
        salesManager: {
            collection: 'kernel.partners',
            parentField: 'salesManagerId',
            childField: '_id'
        },
        salesperson: {
            collection: 'kernel.partners',
            parentField: 'salespersonId',
            childField: '_id'
        },
        source: {
            collection: 'sale.sources',
            parentField: 'sourceId',
            childField: '_id'
        },
        communicationChannel: {
            collection: 'kernel.communication-channels',
            parentField: 'communicationChannelId',
            childField: '_id'
        },
        priceList: {
            collection: 'sale.price-lists',
            parentField: 'priceListId',
            childField: '_id'
        },
        customerPriceList: {
            collection: 'sale.customer-price-lists',
            parentField: 'customerPriceListId',
            childField: '_id'
        },
        guarantee: {
            collection: 'finance.guarantees',
            parentField: 'guaranteeId',
            childField: '_id'
        },
        financialProject: {
            collection: 'kernel.financial-projects',
            parentField: 'financialProjectId',
            childField: '_id'
        },
        store: {
            collection: 'ecommerce.stores',
            parentField: 'storeId',
            childField: '_id'
        },
        carrier: {
            collection: 'logistics.carriers',
            parentField: 'carrierId',
            childField: '_id'
        },
        deliveryAddress: {
            collection: 'kernel.contacts',
            parentField: 'deliveryAddressId',
            childField: '_id'
        }
    },
    async searchTerms(document) {
        const values = Object.values(_.pick(document, ['code', 'reference', 'partnerOrderReference']));

        if (_.isPlainObject(document.partner)) {
            values.push(document.partner.code);
            values.push(document.partner.name);
        }

        if (_.isPlainObject(document.store)) {
            values.push(document.store.code);
            values.push(document.store.name);
        }

        return values;
    },
    async copy(document) {
        const app = this.app;
        // const company = await app.collection('kernel.company').findOne({});
        const numbering = await app.collection('kernel.numbering').findOne({
            code: 'salesOrderNumbering',
            $select: ['_id'],
            $disableInUseCheck: true,
            $disableActiveCheck: true,
            $disableSoftDelete: true
        });

        document.status = 'draft';
        document.recordDate = app.datetime.local().toJSDate();
        document.orderData = app.datetime.local().toJSDate();
        document.dueDate = app.datetime.local().toJSDate();
        document.relatedDocuments = [];
        document.transferIds = [];
        document.code = await app.rpc('kernel.common.request-number', {numberingId: numbering._id, save: true});
        document.paymentPlan = null;
        document.paymentPlanningDate = null;
        document.paymentPlanBackup = null;
        document.cargoTrackingCode = '';
        document.deliveries = [];
        document.deliveryStatus = 'none';

        // const currencies = await app.collection('kernel.currencies').find({
        //     $select: ['_id', 'name']
        // });
        // const exchangeRates = [];
        // const payloads = [];
        // for (const currency of currencies) {
        //     if (currency.name === company.currency.name) {
        //         continue;
        //     }

        //     payloads.push({
        //         from: currency.name,
        //         to: company.currency.name,
        //         value: 1,
        //         options: {
        //             date: document.issueDate
        //         }
        //     });
        // }
        // for (const payload of await app.rpc('kernel.common.convert-currencies', payloads)) {
        //     exchangeRates.push({
        //         currencyName: payload.from,
        //         rate: payload.rate
        //     });
        // }
        // document.exchangeRates = exchangeRates;
        // if (document.currencyId !== company.currencyId) {
        //     const documentCurrency = currencies.find(currency => currency._id === document.currencyId);

        //     if (!documentCurrency) {
        //         const exchangeRate = exchangeRates.find(
        //             exchangeRate => exchangeRate.currencyName === documentCurrency.name
        //         );

        //         if (!!exchangeRate) {
        //             document.currencyRate = exchangeRate.rate;
        //         }
        //     }
        // }

        for (const item of document.items || []) {
            if (!item.productId) continue;

            const stockQuery = {
                date: document.scheduledDate || app.datetime.local().toJSDate(),
                productId: item.productId
            };
            if (app.hasModule('pcm') && !!item.pcmHash) {
                stockQuery.pcmHash = item.pcmHash;
            }
            const report = await app.rpc('inventory.get-stock-report', stockQuery);

            if (Array.isArray(report) && report.length > 0) {
                const r = report[0];

                item.stockQuantity = r.stockQuantity;
                item.orderedQuantity = r.orderedQuantity;
                item.assignedQuantity = r.assignedQuantity;
                item.availableQuantity = r.availableQuantity;
            } else {
                item.stockQuantity = 0;
                item.orderedQuantity = 0;
                item.assignedQuantity = 0;
                item.availableQuantity = 0;
            }

            if (item.warehouseId) {
                const stockQuery = {
                    date: document.scheduledDate || app.datetime.local().toJSDate(),
                    productId: item.productId,
                    warehouseId: item.warehouseId
                };
                if (app.hasModule('pcm') && !!item.pcmHash) {
                    stockQuery.pcmHash = item.pcmHash;
                }
                const report = await app.rpc('inventory.get-stock-report', stockQuery);

                if (Array.isArray(report) && report.length > 0) {
                    const r = report[0];

                    item.warehouseStockQuantity = r.stockQuantity;
                    item.warehouseOrderedQuantity = r.orderedQuantity;
                    item.warehouseAssignedQuantity = r.assignedQuantity;
                    item.warehouseAvailableQuantity = r.availableQuantity;
                } else {
                    item.warehouseStockQuantity = 0;
                    item.warehouseOrderedQuantity = 0;
                    item.warehouseAssignedQuantity = 0;
                    item.warehouseAvailableQuantity = 0;
                }
            }
        }

        return document;
    },
    hooks: {
        after: {
            create: [updateItemProfits, syncCashFlowRecords],
            update: [updateItemProfits, syncCashFlowRecords],
            patch: [updateItemProfits, syncCashFlowRecords],
            remove: [syncCashFlowRecords, removeConversion]
        }
    }
};

async function updateItemProfits(context) {
    const app = context.app;

    for (const document of Array.isArray(context.result) ? context.result : [context.result]) {
        const profit = await app.collection('sale.document-profits').findOne({
            documentId: document._id
        });

        if (!!profit) {
            document.items = (document.items ?? []).map(item => {
                if (!item.costSource && !!profit.defaultProfitBase) {
                    item.costSource = profit.defaultProfitBase;
                }

                if (!!item.costSource) {
                    const profitItem = (profit.items ?? []).find(
                        profitItem =>
                            profitItem.profitBase === item.costSource &&
                            profitItem.productId === item.productId &&
                            profitItem.unitId === item.unitId &&
                            profitItem.quantity === item.quantity &&
                            profitItem.warehouseId === item.warehouseId
                    );

                    if (!!profitItem) {
                        if (!_.isFinite(item.unitCost) || item.unitCost === 0) {
                            item.unitCost =
                                (profitItem.basePrice ?? 0) / (document.currencyRate !== 0 ? document.currencyRate : 1);
                        }
                        item.cost = item.unitCost * item.quantity;
                        item.profitRate =
                            ((item.realTotal - (item.cost + (item.freight ?? 0))) / (item.cost || 1)) * 100;
                        item.profitMargin =
                            ((item.realTotal - (item.cost + (item.freight ?? 0))) / (item.realTotal || 1)) * 100;
                        item.profit = item.realTotal - (item.cost + (item.freight ?? 0));
                        item.unitProfit = item.profit / item.quantity;
                    }
                }

                return item;
            });

            await app.db.collection('sale_orders').updateOne(
                {_id: new ObjectId(document._id)},
                {
                    $set: {items: document.items}
                },
                {
                    collation: {locale: app.config('app.locale')}
                }
            );
        }
    }

    return context;
}

async function syncCashFlowRecords(context) {
    // noinspection ES6MissingAwait
    (async () => {
        for (const result of Array.isArray(context.result) ? context.result : [context.result]) {
            await context.app.rpc('finance.cash-flow-sync-records', {
                collection: 'sale.orders',
                documentId: result._id
            });
        }
    })();

    return context;
}

async function removeConversion(context) {
    const app = context.app;

    for (const result of Array.isArray(context.result) ? context.result : [context.result]) {
        await app.collection('sale.conversions').remove({
            orderId: result._id,
            isConverted: false
        });
    }

    return context;
}
