export default {
    name: 'order-stages',
    title: 'Order Stages',
    order: true,
    localCache: true,
    cache: true,
    schema: {
        code: {
            type: 'string',
            label: 'Code',
            unique: true,
            index: true
        },
        name: {
            type: 'string',
            label: 'Name',
            index: true
        },
        color: {
            type: 'string',
            label: 'Color'
        },
        description: {
            type: 'string',
            label: 'Short description',
            required: false
        },
        isActive: {
            type: 'boolean',
            label: 'Is active',
            default: true,
            index: true
        }
    }
};
