import _ from 'lodash';

export default {
    name: 'returns',
    title: 'Returns',
    branch: true,
    assignable: true,
    view: 'sale.sales.returns',
    labelParams: {
        from: 'code'
    },
    schema: {
        // General
        module: {
            type: 'string',
            label: 'Module',
            allowed: ['sale', 'ecommerce', 'service', 'project', 'rental'],
            default: 'sale',
            index: true
        },
        status: {
            type: 'string',
            label: 'Status',
            allowed: ['draft', 'requested', 'approved', 'canceled', 'process-completed'],
            default: 'draft',
            index: true
        },
        code: {
            type: 'string',
            label: 'Code',
            unique: true,
            index: true
        },
        storeId: {
            type: 'string',
            label: 'Store',
            required: false,
            index: true
        },
        returnReasonId: {
            type: 'string',
            label: 'Return reason',
            index: true
        },
        partnerGroupId: {
            type: 'string',
            label: 'Customer group',
            required: false,
            index: true
        },
        partnerId: {
            type: 'string',
            label: 'Customer',
            index: true
        },
        currencyId: {
            type: 'string',
            label: 'Currency',
            index: true
        },
        currencyRate: {
            type: 'decimal',
            label: 'Currency rate',
            default: 1,
            index: true
        },
        contactPersonId: {
            type: 'string',
            label: 'Contact person',
            required: false
        },
        recordDate: {
            type: 'date',
            label: 'Record date',
            default: 'date:now',
            index: true
        },
        issueDate: {
            type: 'date',
            label: 'Issue date',
            default: 'date:now',
            index: true
        },
        scheduledDate: {
            type: 'date',
            label: 'Delivery date',
            default: 'date:now',
            index: true
        },
        requests: {
            type: 'string',
            label: 'Requests',
            required: false
        },
        note: {
            type: 'string',
            label: 'Note',
            required: false
        },
        orderId: {
            type: 'string',
            label: 'Order',
            required: false,
            index: true
        },

        // Items
        items: {
            type: [
                {
                    id: {
                        type: 'string',
                        required: false
                    },
                    productId: {
                        type: 'string',
                        label: 'Product',
                        index: true
                    },
                    productCode: {
                        type: 'string',
                        label: 'Product code'
                    },
                    productDefinition: {
                        type: 'string',
                        label: 'Product definition'
                    },
                    productType: {
                        type: 'string',
                        label: 'Product type',
                        index: true
                    },
                    barcode: {
                        type: 'string',
                        label: 'Barcode',
                        required: false,
                        index: true
                    },
                    description: {
                        type: 'string',
                        label: 'Description',
                        required: false
                    },
                    serialNumber: {
                        type: 'string',
                        label: 'Serial number',
                        required: false
                    },
                    lotNumber: {
                        type: 'string',
                        label: 'Lot number',
                        required: false
                    },
                    scheduledDate: {
                        type: 'date',
                        label: 'Delivery Date',
                        default: 'date:now'
                    },
                    branchId: {
                        type: 'string',
                        label: 'Branch Office'
                    },
                    warehouseId: {
                        type: 'string',
                        label: 'Warehouse'
                    },
                    quantity: {
                        type: 'decimal',
                        label: 'Quantity',
                        default: 1
                    },
                    unitId: {
                        type: 'string',
                        label: 'Unit'
                    },
                    baseUnitId: {
                        type: 'string',
                        label: 'Base unit'
                    },
                    baseQuantity: {
                        type: 'decimal',
                        label: 'Base quantity',
                        default: 1
                    },
                    unitCost: {
                        type: 'decimal',
                        label: 'Unit cost',
                        default: 0
                    },
                    stockQuantity: {
                        type: 'decimal',
                        label: 'Stock on hand',
                        default: 0
                    },
                    orderedQuantity: {
                        type: 'decimal',
                        label: 'Ordered quantity',
                        default: 0
                    },
                    assignedQuantity: {
                        type: 'decimal',
                        label: 'Assigned quantity',
                        default: 0
                    },
                    availableQuantity: {
                        type: 'decimal',
                        label: 'Available quantity',
                        default: 0
                    },
                    warehouseStockQuantity: {
                        type: 'decimal',
                        label: 'Warehouse stock on hand',
                        default: 0
                    },
                    warehouseOrderedQuantity: {
                        type: 'decimal',
                        label: 'Warehouse ordered quantity',
                        default: 0
                    },
                    warehouseAssignedQuantity: {
                        type: 'decimal',
                        label: 'Warehouse assigned quantity',
                        default: 0
                    },
                    warehouseAvailableQuantity: {
                        type: 'decimal',
                        label: 'Warehouse available quantity',
                        default: 0
                    },
                    financialProjectId: {
                        type: 'string',
                        label: 'Project',
                        required: false
                    },
                    cost: {
                        type: 'decimal',
                        label: 'Cost',
                        default: 0
                    },

                    // Internal
                    partnerId: {
                        type: 'string',
                        required: false
                    },
                    deliveryReceiverId: {
                        type: 'string',
                        label: 'Delivery receiver',
                        required: false
                    },
                    deliveryAddressId: {
                        type: 'string',
                        required: false
                    },
                    deliveryAddress: {
                        type: 'object',
                        label: 'Delivery address',
                        blackbox: true,
                        required: false
                    },
                    transferId: {
                        type: 'string',
                        required: false
                    },
                    reference: {
                        type: 'string',
                        required: false
                    },
                    referenceId: {
                        type: 'string',
                        required: false,
                        index: true
                    },
                    referenceCollection: {
                        type: 'string',
                        required: false
                    },
                    pcmModelId: {
                        type: 'string',
                        required: false
                    },
                    pcmConfigurationId: {
                        type: 'string',
                        required: false
                    },
                    pcmHash: {
                        type: 'string',
                        required: false
                    }
                }
            ],
            default: []
        },

        // Logistic
        warehouseId: {
            type: 'string',
            label: 'Warehouse'
        },
        deliveryReceiverId: {
            type: 'string',
            label: 'Delivery receiver',
            required: false
        },
        deliveryAddressId: {
            type: 'string',
            required: false
        },
        deliveryAddress: {
            type: 'object',
            label: 'Delivery address',
            blackbox: true,
            required: false
        },
        deliveryPriority: {
            type: 'string',
            label: 'Delivery priority',
            allowed: ['not-urgent', 'normal', 'urgent', 'very-urgent'],
            default: 'normal'
        },
        deliveryPolicy: {
            type: 'string',
            label: 'Delivery policy',
            allowed: ['when-one-ready', 'when-all-ready'],
            default: 'when-one-ready'
        },
        deliveryConditionId: {
            type: 'string',
            label: 'Delivery condition',
            required: false
        },
        deliveryMethodId: {
            type: 'string',
            label: 'Delivery method',
            required: false
        },
        carrierId: {
            type: 'string',
            label: 'Carrier',
            required: false
        },
        cargoTrackingCode: {
            type: 'string',
            label: 'Cargo tracking code',
            required: false
        },

        // Attachments.
        attachments: {
            type: ['string'],
            default: []
        },

        // Internal.
        transferIds: {
            type: ['string'],
            default: [],
            index: true
        },
        relatedDocuments: {
            type: [
                {
                    collection: 'string',
                    view: 'string',
                    title: 'string',
                    ids: {
                        type: ['string'],
                        default: []
                    }
                }
            ],
            default: []
        },
        // Used by ecommerce (Trendyol, Amazon etc.)
        integrationId: {
            type: 'string',
            label: 'Integration order',
            required: false
        }
    },
    attributes: {
        returnReason: {
            collection: 'kernel.return-reasons',
            parentField: 'returnReasonId',
            childField: '_id'
        },
        partner: {
            collection: 'kernel.partners',
            parentField: 'partnerId',
            childField: '_id'
        },
        contactPerson: {
            collection: 'kernel.contacts',
            parentField: 'contactPersonId',
            childField: '_id'
        },
        branch: {
            collection: 'kernel.branches',
            parentField: 'branchId',
            childField: '_id'
        },
        currency: {
            collection: 'kernel.currencies',
            parentField: 'currencyId',
            childField: '_id'
        },
        store: {
            collection: 'ecommerce.stores',
            parentField: 'storeId',
            childField: '_id'
        },
        order: {
            collection: 'sale.orders',
            parentField: 'orderId',
            childField: '_id'
        }
    },
    async searchTerms(document) {
        const values = Object.values(_.pick(document, ['code', 'reference', 'grandTotal']));

        if (_.isPlainObject(document.returnReason)) {
            values.push(document.returnReason.name);
        }
        if (_.isPlainObject(document.partner)) {
            values.push(document.partner.code);
            values.push(document.partner.name);
        }

        if (_.isPlainObject(document.store)) {
            values.push(document.store.code);
            values.push(document.store.name);
        }

        return values;
    }
};
