import _ from 'lodash';

export default {
    version: '12',
    async action(app) {
        try {
            await app.reportDb.schema.alterTable('sale_orders', table => {
                table.text('store_id');
                table.text('store_name');
                table.text('stage_id');
                table.text('stage_name');
            });
        } catch (error) {
            console.log(error.message);
        }

        if (app.absoluteUrl() !== 'https://entererp.fortes.com.tr') {
            try {
                console.log('Refreshing analytic data....');
                await app.rpc('analytic.data-center-refresh-data', {schemaName: 'sale.orders'});

                console.log('Refreshed analytic data.');
            } catch (error) {
                console.log(error.message);
            }
        }
    }
};
