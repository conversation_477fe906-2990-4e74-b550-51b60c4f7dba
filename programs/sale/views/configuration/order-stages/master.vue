<template>
    <ui-view
        type="table"
        collection="sale.order-stages"
        :columns="columns"
        :filters="filters"
        actions="create,remove"
        enable-ordering
    />
</template>

<script>
export default {
    data: () => ({
        columns: [
            {
                field: 'code',
                label: 'Code',
                maxWidth: 120
            },
            {
                field: 'name',
                label: 'Name'
            },
            {
                type: 'boolean',
                field: 'isActive',
                label: 'Is Active',
                isEditable: true,
                maxWidth: 100
            }
        ]
    }),

    computed: {
        filters() {
            let filters = {};

            if (!this.$params('inDialog')) {
                filters.$disableActiveCheck = true;
            }

            return filters;
        }
    }
};
</script>
