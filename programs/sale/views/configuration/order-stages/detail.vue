<template>
    <ui-view type="form" collection="sale.order-stages" :model="model" :title="title" actions="edit,cancel">
        <div class="columns">
            <div class="column is-half">
                <ui-legend title="General Information" />
                <ui-field name="code" />
                <ui-field name="name" />
                <ui-field name="color" field-type="color" />
                <ui-field name="isActive" />
            </div>
            <div class="column is-half">
                <ui-legend title="Description" />
                <ui-field name="description" :rows="4" label="hide" />
            </div>
        </div>
    </ui-view>
</template>

<script>
import _ from 'lodash';

export default {
    data: () => ({
        model: {}
    }),

    computed: {
        title() {
            const model = this.model;

            if (this.$params('id') && !model.name) return '';

            return model.name ? model.name : this.$t('New Order Stage');
        }
    }
};
</script>
