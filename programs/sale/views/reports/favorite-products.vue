<template>
    <ui-view
        type="content"
        :title="'Favorite Products' | t"
        :left-panel-width="270"
        class="sale-reports-favorite-products"
        v-if="initialized"
    >
        <template slot="left-panel" v-if="productIds.length < 1 && module !== 'ecommerce'">
            <div class="sale-reports-favorite-products-categories">
                <div class="category-search">
                    <el-input
                        v-model="categorySearchQuery"
                        :placeholder="'Search category..' | t"
                        prefix-icon="el-icon-search"
                        autocorrect="off"
                        autocapitalize="off"
                        spellcheck="false"
                        clearable
                        size="medium"
                        @input="categorySearchQuery = $event"
                    />
                </div>
                <ui-table
                    ref="table"
                    :items="categories"
                    :columns="categoriesColumns"
                    :search="categorySearchQuery"
                    :enable-row-handle="false"
                    no-zebra
                    :options="categoriesTableOptions"
                    @selected="handleSelectCategory"
                    v-if="categories.length > 0"
                />
            </div>
        </template>

        <template slot="top-panel">
            <ui-scope
                ref="scope"
                id="sale.reports.favorite-products"
                :applied-items="appliedScopeItems"
                :filters="scopeApplicableFilters"
                @changed="handleScopeChange"
            />
        </template>

        <ui-table
            ref="table"
            :key="tableKey"
            :id="`sale.reports.favorite-products`"
            row-model="serverSide"
            :columns="columns"
            :filters="filters"
            :get-rows="getRows"
            :summary-row="summaryRow"
            :enable-sorting="false"
            :enable-selection="false"
        />
    </ui-view>
</template>

<script>
import _ from 'lodash';
import {escapeRegExp} from 'framework/helpers';

export default {
    data: () => ({
        payload: {
            startDate: null,
            endDate: null,
            query: {}
        },
        refreshingForRealTime: false,
        rowCount: null,
        loadKey: _.uniqueId('loadKey_'),
        initialized: false,
        categorySearchQuery: '',
        module: '',
        categories: [],
        categoriesColumns: [
            {
                field: 'name',
                label: 'Category',
                cellRenderer: 'agGroupCellRenderer',
                showRowGroup: true,
                suppressMenu: true,
                suppressMovable: true,
                cellRendererParams: {
                    suppressCount: true,
                    suppressDoubleClickExpand: true,
                    innerRenderer(params) {
                        const data = params.data;

                        if (_.isObject(data)) {
                            return data.name;
                        }
                    }
                },
                getQuickFilterText(params) {
                    const data = params.data;

                    if (_.isObject(data)) {
                        return `${data.code} - ${data.name}`;
                    }
                }
            }
        ],
        categoriesTableOptions: {
            treeData: true,
            groupDefaultExpanded: -1,
            groupSuppressAutoColumn: true,
            getDataPath(data) {
                return data.tree.path.split('/');
            }
        },
        productIds: [],
        categoryFilters: {}
    }),

    computed: {
        tableKey() {
            return this.loadKey;
        },
        filters() {
            return {};
        },
        columns() {
            return [
                {
                    field: 'productCode',
                    label: 'Product code',
                    relationParams(params) {
                        const data = params.data;

                        if (!!data) {
                            return {
                                id: data.productId,
                                view: 'inventory.catalog.products'
                            };
                        }
                    },
                    width: 150
                },
                {
                    field: 'productDefinition',
                    label: 'Product definition',
                    relationParams(params) {
                        const data = params.data;

                        return {
                            id: data.productId,
                            view: 'inventory.catalog.products-detail'
                        };
                    },
                    minWidth: 150
                },
                {
                    field: 'productType',
                    label: 'Product type',
                    visible: false,
                    width: 150,
                    valueLabels: [
                        {value: 'stockable', label: 'Stockable product'},
                        {value: 'service', label: 'Service product'}
                    ],
                    translateLabels: true
                },
                {
                    field: 'favoritesCount',
                    label: 'Favorite count',
                    width: 150,
                    format: 'decimal'
                }
            ];
        },
        appliedScopeItems() {
            return [];
        },
        scopeApplicableFilters() {
            const self = this;

            return [
                {
                    field: 'date',
                    code: 'date',
                    label: 'Date',
                    type: 'date',
                    operator: 'in-range',
                    allowedOperators: ['in-range']
                },
                {
                    field: 'branchId',
                    label: 'Branch office',
                    collection: 'kernel.branches',
                    filters: {
                        _id: {$in: this.$user.branchIds},
                        $sort: {name: 1}
                    },
                    condition() {
                        return self.$setting('system.multiBranch');
                    }
                }
            ];
        }
    },

    methods: {
        async getRows(query, params) {
            if (!!this.isFiltering) {
                this.$params('loading', true);
            }

            const startRow = params.request.startRow;
            const endRow = params.request.endRow;
            const result = await this.$rpc('sale.get-favorite-products-report', {
                ...this.payload,
                categoryFilters: this.categoryFilters,
                module: this.module,
                limit: endRow - startRow,
                skip: startRow
            });

            if (!!this.isFiltering) {
                this.isFiltering = false;
                this.$params('loading', false);
            }

            this.rowCount = result.total;

            return result;
        },
        async summaryRow() {
            const result = await this.$rpc('sale.get-total-favorite-products-report', {
                ...this.payload,
                categoryFilters: this.categoryFilters,
                module: this.module
            });

            return {
                rowCount: this.rowCount,
                productDefinition: this.$t('TOTAL'),
                favoritesCount: result.totalFavorites
            };
        },
        handleScopeChange(model) {
            const payload = _.cloneDeep(this.payload);
            const query = model.query;

            // Reset
            payload.startDate = null;
            payload.endDate = null;
            payload.branchId = [];

            // Start and end date
            if (_.isObject(query.date)) {
                payload.startDate = query.date.$gte;
                payload.endDate = query.date.$lte;

                delete query.date;
            }

            // Get branch ids
            if (_.isObject(query.branchId) && Array.isArray(query.branchId.$in)) {
                payload.branchId = query.branchId.$in;

                delete query.branchId;
            }

            // Get query
            payload.query = {...query, ..._.cloneDeep(this.$params('filters') || {})};

            this.payload = payload;
            this.isFiltering = true;
            this.loadKey = _.uniqueId('loadKey_');
        },
        refresh() {
            const table = this.$refs.table;
            if (_.isObject(table)) {
                table.refreshData();
            }
        },
        handleSelectCategory(selected) {
            const query = {$or: []};

            for (const category of selected) {
                query.$or.push({
                    'categoryPath': {
                        $regex: `^${escapeRegExp(category.path)}/`,
                        $options: 'i'
                    }
                });
                query.$or.push({
                    'categoryPath': {
                        $regex: `^${escapeRegExp(category.path)}$`,
                        $options: 'i'
                    }
                });
            }

            if (query.$or.length < 1) {
                delete query.$or;
            }

            this.loadKey = _.uniqueId('loadKey_');
            this.categoryFilters = query;
        }
    },

    async created() {
        this.refreshForRealTime = _.debounce(this.refresh, 300, {leading: false, trailing: true});

        this.categories = await this.$collection('inventory.product-categories').find();
        this.module = (this.$params('filters') || {}).module;

        this.$collection('inventory.products').on('all', this.refreshForRealTime);

        this.initialized = true;
    },

    beforeDestroy() {
        this.$collection('inventory.products').removeListener('all', this.refreshForRealTime);
    }
};
</script>

<style lang="scss">
//noinspection CssUnknownTarget
@import 'core';

.sale-reports-favorite-products {
    .ui-view-top-panel {
        display: flex;
    }

    .sale-reports-favorite-products-categories {
        position: relative;
        display: flex;
        flex-flow: column nowrap;
        flex: 0 0 300px;
        min-width: 210px;
        height: 100%;
        background-color: #fff;

        .category-search {
            width: 100%;
            flex: 0 0 30px;

            .el-input__inner {
                border-radius: 0;
                border: none;
                border-bottom: 1px solid $border-color;
            }
        }

        .ag-pinned-left-cols-container,
        .ag-pinned-left-header,
        .ag-header-viewport,
        .ag-pinned-right-header,
        .ag-header,
        .ag-header-row {
            display: none !important;
        }
    }
}
</style>
