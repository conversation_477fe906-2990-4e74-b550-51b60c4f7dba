<template>
    <ui-view
        ref="view"
        type="table"
        class="sale-reports-delivery-analysis"
        :filters="filters"
        :columns="columns"
        :get-rows="getRows"
        :summary-row="summaryRow"
        :enable-detail="false"
        :enable-sorting="true"
        v-if="isInitialized"
    >
        <template slot="top-panel">
            <ui-scope
                id="sale.reports.open-items-list"
                :filters="scopeApplicableFilters"
                @changed="handleScopeChange"
            />

            <div class="currency-select">
                <el-select v-model="selectedCurrency" filterable>
                    <el-option
                        v-for="option in currencyOptions"
                        :key="option.value"
                        :label="option.label"
                        :value="option.value"
                    />
                </el-select>
            </div>
        </template>
    </ui-view>
</template>

<script>
import _ from 'lodash';
import fastCopy from 'fast-copy';
import {rawMongoQuery} from 'framework/helpers';

export default {
    data: () => ({
        scopeQuery: {},
        currencies: [],
        totalReport: null,
        selectedCurrency: null,
        isInitialized: false
    }),

    computed: {
        currencyOptions() {
            return this.currencies.map(currency => ({
                value: currency.name,
                label: currency.name
            }));
        },
        filters() {
            return fastCopy(this.scopeQuery);
        },
        columns() {
            return [
                {
                    field: 'date',
                    label: 'Delivery date',
                    format: 'date',
                    width: 120
                },
                {
                    field: 'documentCode',
                    label: 'Document',
                    relationParams(params) {
                        const data = params.data;

                        if (!!data) {
                            return {
                                id: data.documentId,
                                view: data.documentView
                            };
                        }
                    },
                    width: 150,
                    sort: true
                },
                {
                    field: 'documentTitle',
                    label: 'Document type',
                    relationParams(params) {
                        const data = params.data;

                        if (!!data) {
                            return {
                                id: data.documentId,
                                view: data.documentView
                            };
                        }
                    },
                    width: 150
                },
                {
                    field: 'productCode',
                    label: 'Product code',
                    relationParams(params) {
                        const data = params.data;

                        if (!!data) {
                            return {
                                id: data.productId,
                                view: 'inventory.catalog.products'
                            };
                        }
                    },
                    width: 150
                },
                {
                    field: 'productDefinition',
                    label: 'Product definition',
                    relationParams(params) {
                        const data = params.data;

                        if (!!data) {
                            return {
                                id: data.productId,
                                view: 'inventory.catalog.products'
                            };
                        }
                    },
                    minWidth: 210
                },
                {
                    field: 'description',
                    label: 'Description',
                    visible: false,
                    width: 150
                },
                {
                    field: 'partnerCode',
                    label: 'Partner code',
                    relationParams(params) {
                        const data = params.data;

                        if (!!data) {
                            return {
                                id: data.partnerId,
                                view: 'sale.customer-relations.customers'
                            };
                        }
                    },
                    width: 150
                },
                {
                    field: 'partnerName',
                    label: 'Partner name',
                    relationParams(params) {
                        const data = params.data;

                        if (!!data) {
                            return {
                                id: data.partnerId,
                                view: 'sale.customer-relations.customers'
                            };
                        }
                    },
                    minWidth: 210
                },
                {
                    field: 'deliveryAddressInfo',
                    label: 'Delivery address info',
                    relationParams(params) {
                        const data = params.data;

                        if (!!data) {
                            return {
                                id: data.deliveryAddressId,
                                view: 'partners.contacts'
                            };
                        }
                    },
                    visible: false,
                    width: 150
                },
                {
                    field: 'warehouseCode',
                    label: 'Warehouse code',
                    visible: false,
                    width: 150
                },
                {
                    field: 'warehouseName',
                    label: 'Warehouse name',
                    visible: false,
                    minWidth: 210
                },
                {
                    field: 'financialProjectCode',
                    label: 'Project code',
                    visible: false,
                    width: 150
                },
                {
                    field: 'financialProjectName',
                    label: 'Project name',
                    visible: false,
                    minWidth: 210
                },
                {
                    field: 'orderQuantity',
                    label: 'Order quantity',
                    format: 'unit',
                    width: 120
                },
                {
                    field: 'remainingQuantity',
                    label: 'Remaining quantity',
                    format: 'unit',
                    width: 120
                },
                {
                    field: 'deliveredQuantity',
                    label: 'Delivered quantity',
                    format: 'unit',
                    width: 120
                },
                {
                    field: 'unitPrice',
                    label: 'Unit price',
                    width: 120,
                    format: 'currency',
                    valueGetter: params => {
                        const data = params.data;

                        if (
                            _.isPlainObject(data) &&
                            _.isNumber(data.orderQuantity) &&
                            _.isNumber(data.orderTotal) &&
                            data.orderQuantity > 0 &&
                            !!data._id
                        ) {
                            return data.orderTotal / data.orderQuantity;
                        }

                        return '';
                    },
                    formatOptions: () => {
                        const currency = this.currencies.find(c => c.name === this.selectedCurrency);
                        let options = {currency: {}};

                        options.currency.symbol = currency.symbol;
                        options.currency.format = currency.symbolPosition === 'before' ? '%s %v' : '%v %s';

                        return options;
                    }
                },
                {
                    field: 'orderTotal',
                    label: 'Order total',
                    format: 'currency',
                    formatOptions: () => {
                        const currency = this.currencies.find(c => c.name === this.selectedCurrency);
                        let options = {currency: {}};

                        options.currency.symbol = currency.symbol;
                        options.currency.format = currency.symbolPosition === 'before' ? '%s %v' : '%v %s';

                        return options;
                    },
                    width: 150
                },
                {
                    field: 'remainingTotal',
                    label: 'Remaining total',
                    format: 'currency',
                    formatOptions: () => {
                        const currency = this.currencies.find(c => c.name === this.selectedCurrency);
                        let options = {currency: {}};

                        options.currency.symbol = currency.symbol;
                        options.currency.format = currency.symbolPosition === 'before' ? '%s %v' : '%v %s';

                        return options;
                    },
                    width: 150
                },
                {
                    field: 'deliveredTotal',
                    label: 'Delivered total',
                    format: 'currency',
                    formatOptions: () => {
                        const currency = this.currencies.find(c => c.name === this.selectedCurrency);
                        let options = {currency: {}};

                        options.currency.symbol = currency.symbol;
                        options.currency.format = currency.symbolPosition === 'before' ? '%s %v' : '%v %s';

                        return options;
                    },
                    width: 150
                },
            ];
        },
        scopeApplicableFilters() {
            return [
                {field: 'documentCode', label: 'Document code'},
                {
                    field: 'productId',
                    label: 'Product',
                    collection: 'inventory.products',
                    extraFields: ['code', 'definition'],
                    filters: {isSimple: true, type: {$ne: 'service'}, $sort: {name: 1}},
                    template: '{{code}} - {{definition}}'
                },
                {
                    field: 'productGroupIds',
                    label: 'Product group',
                    collection: 'inventory.product-groups'
                },
                {
                    field: 'productCategoryPath',
                    label: 'Product category',
                    collection: 'inventory.product-categories',
                    valueFrom: 'path',
                    labelFrom: 'path',
                    operator: 'starts-with'
                },
                {
                    field: 'productBrandId',
                    label: 'Product brand',
                    collection: 'inventory.product-brands'
                },
                {field: 'productBarcode', label: 'Product barcode'},
                {
                    field: 'partnerId',
                    label: 'Customer',
                    collection: 'kernel.partners',
                    extraFields: ['code'],
                    filters: {type: 'customer', $sort: {name: 1}},
                    template: '{{code}} - {{name}}'
                },
                {
                    field: 'warehouseId',
                    label: 'Warehouse',
                    collection: 'inventory.warehouses',
                    extraFields: ['shortName'],
                    filters: {$sort: {name: 1}},
                    template: '{{shortName}} - {{name}}'
                }
            ];
        }
    },

    watch: {
        selectedCurrency(value, oldValue) {
            if (value !== oldValue && !!this.$refs && !!this.$refs.view) {
                this.$params('loading', true);

                this.$refs.view.$refs.component.refreshData();
            }
        }
    },

    methods: {
        handleScopeChange(model) {
            this.scopeQuery = model.query;
        },
        async getRows(tableQuery, params) {
            const company = this.$store.getters['session/company'];
            const query = fastCopy(rawMongoQuery(tableQuery));
            const startRow = params.request.startRow;
            const endRow = params.request.endRow;
            const limit = endRow - startRow;
            const skip = startRow;
            const result = {
                total: 0,
                data: []
            };

            delete query._id;

            const externalReservationsPipeline = [
                {
                    $match: {
                        type: 'outgoing',
                        documentCollection: {$in: ['sale.orders', 'accounting.customer-invoices']},
                        assigned: {$gt: 0}
                    }
                },
                // {
                //     $lookup: {
                //         from: 'inventory_products',
                //         let: {productId: {$toObjectId: '$productId'}},
                //         pipeline: [
                //             {
                //                 $match: {
                //                     $expr: {$eq: ['$_id', '$$productId']}
                //                 }
                //             },
                //             {
                //                 $project: {
                //                     productId: '$_id',
                //                     productCode: '$code',
                //                     productDefinition: '$definition',
                //                     productGroupIds: '$groupIds',
                //                     productCategoryId: '$categoryId',
                //                     productCategoryPath: '$categoryPath',
                //                     productBarcode: '$barcode',
                //                     productBrandId: '$brandId'
                //                 }
                //             }
                //         ],
                //         as: 'product'
                //     }
                // },
                // {
                //     $replaceRoot: {
                //         newRoot: {
                //             $mergeObjects: [
                //                 {$arrayElemAt: ['$product', 0]},
                //                 '$$ROOT'
                //             ]
                //         }
                //     }
                // },
                {
                    $project: {
                        _id: 0,
                        date: '$date',
                        documentId: '$documentId',
                        documentCode: '$documentCode',
                        documentCollection: '$documentCollection',
                        documentView: '$documentView',
                        documentTitle: '$documentTitle',
                        productId: '$productId',
                        productCode: '$product.code',
                        productDefinition: '$product.definition',
                        productGroupIds: '$product.groupIds',
                        productCategoryId: '$product.categoryId',
                        productCategoryPath: '$product.categoryPath',
                        productBarcode: '$product.barcode',
                        productBrandId: '$product.brandId',
                        warehouseId: '$warehouseId',
                        warehouseCode: '$warehouse.shortName',
                        warehouseName: '$warehouse.name',
                        financialProjectId: '$financialProjectId',
                        financialProjectCode: '$financialProject.code',
                        financialProjectName: '$financialProject.name',
                        partnerId: '$partnerId',
                        partnerCode: '$partner.code',
                        partnerName: '$partner.name',
                        unitId: '$unitId',
                        unitName: '$unit.name',
                        orderQuantity: '$orderQuantity',
                        quantity: '$assigned',
                        unitPrice: '$unitPrice',
                        exchangeRatesMap: '$exchangeRatesMap',
                        description: '$description'
                    }
                },
                {
                    $group: {
                        _id: {productId: '$productId', documentId: '$documentId'},
                        date: {$first: '$date'},
                        documentId: {$first: '$documentId'},
                        documentCode: {$first: '$documentCode'},
                        documentCollection: {$first: '$documentCollection'},
                        documentView: {$first: '$documentView'},
                        documentTitle: {$first: '$documentTitle'},
                        productId: {$first: '$productId'},
                        productCode: {$first: '$productCode'},
                        productDefinition: {$first: '$productDefinition'},
                        productGroupIds: {$first: '$productGroupIds'},
                        productCategoryId: {$first: '$productCategoryId'},
                        productCategoryPath: {$first: '$productCategoryPath'},
                        productBarcode: {$first: '$productBarcode'},
                        productBrandId: {$first: '$productBrandId'},
                        warehouseId: {$first: '$warehouseId'},
                        warehouseCode: {$first: '$warehouseCode'},
                        warehouseName: {$first: '$warehouseName'},
                        financialProjectId: {$first: '$financialProjectId'},
                        financialProjectCode: {$first: '$financialProjectCode'},
                        financialProjectName: {$first: '$financialProjectName'},
                        partnerId: {$first: '$partnerId'},
                        partnerCode: {$first: '$partnerCode'},
                        partnerName: {$first: '$partnerName'},
                        unitId: {$first: '$unitId'},
                        unitName: {$first: '$unitName'},
                        orderQuantity: {$first: '$orderQuantity'},
                        quantity: {$sum: '$quantity'},
                        unitPrice: {$first: '$unitPrice'},
                        exchangeRatesMap: {$first: '$exchangeRatesMap'},
                        description: {$first: '$description'}
                    }
                },
                {
                    $project: {
                        _id: 0
                    }
                }
            ];
            const reservationsPipeline = [
                {
                    $match: {
                        type: 'outgoing',
                        referenceCollection: {$in: ['sale.orders', 'accounting.customer-invoices']},
                        assigned: {$gt: 0}
                    }
                },
                // {
                //     $lookup: {
                //         from: 'inventory_products',
                //         let: {productId: {$toObjectId: '$productId'}},
                //         pipeline: [
                //             {
                //                 $match: {
                //                     $expr: {$eq: ['$_id', '$$productId']}
                //                 }
                //             },
                //             {
                //                 $project: {
                //                     productId: '$_id',
                //                     productCode: '$code',
                //                     productDefinition: '$definition',
                //                     productGroupIds: '$groupIds',
                //                     productCategoryId: '$categoryId',
                //                     productCategoryPath: '$categoryPath',
                //                     productBarcode: '$barcode',
                //                     productBrandId: '$brandId'
                //                 }
                //             }
                //         ],
                //         as: 'product'
                //     }
                // },
                // {
                //     $replaceRoot: {
                //         newRoot: {
                //             $mergeObjects: [
                //                 {$arrayElemAt: ['$product', 0]},
                //                 '$$ROOT'
                //             ]
                //         }
                //     }
                // },
                {
                    $project: {
                        _id: 0,
                        date: '$date',
                        documentId: '$referenceId',
                        documentCode: '$referenceCode',
                        documentCollection: '$referenceCollection',
                        documentView: '$referenceView',
                        documentTitle: '$referenceTitle',
                        productId: '$productId',
                        productCode: '$product.code',
                        productDefinition: '$product.definition',
                        productGroupIds: '$product.groupIds',
                        productCategoryId: '$product.categoryId',
                        productCategoryPath: '$product.categoryPath',
                        productBarcode: '$product.barcode',
                        productBrandId: '$product.brandId',
                        warehouseId: '$warehouseId',
                        warehouseCode: '$warehouse.shortName',
                        warehouseName: '$warehouse.name',
                        financialProjectId: '$financialProjectId',
                        financialProjectCode: '$financialProject.code',
                        financialProjectName: '$financialProject.name',
                        partnerId: '$partnerId',
                        partnerCode: '$partner.code',
                        partnerName: '$partner.name',
                        unitId: '$unitId',
                        unitName: '$unit.name',
                        orderQuantity: '$orderQuantity',
                        quantity: '$assigned',
                        unitPrice: '$unitPrice',
                        exchangeRatesMap: '$exchangeRatesMap',
                        description: '$description'
                    }
                },
                {
                    $group: {
                        _id: {productId: '$productId', documentId: '$documentId'},
                        date: {$first: '$date'},
                        documentId: {$first: '$documentId'},
                        documentCode: {$first: '$documentCode'},
                        documentCollection: {$first: '$documentCollection'},
                        documentView: {$first: '$documentView'},
                        documentTitle: {$first: '$documentTitle'},
                        productId: {$first: '$productId'},
                        productCode: {$first: '$productCode'},
                        productDefinition: {$first: '$productDefinition'},
                        productGroupIds: {$first: '$productGroupIds'},
                        productCategoryId: {$first: '$productCategoryId'},
                        productCategoryPath: {$first: '$productCategoryPath'},
                        productBarcode: {$first: '$productBarcode'},
                        productBrandId: {$first: '$productBrandId'},
                        warehouseId: {$first: '$warehouseId'},
                        warehouseCode: {$first: '$warehouseCode'},
                        warehouseName: {$first: '$warehouseName'},
                        financialProjectId: {$first: '$financialProjectId'},
                        financialProjectCode: {$first: '$financialProjectCode'},
                        financialProjectName: {$first: '$financialProjectName'},
                        partnerId: {$first: '$partnerId'},
                        partnerCode: {$first: '$partnerCode'},
                        partnerName: {$first: '$partnerName'},
                        unitId: {$first: '$unitId'},
                        unitName: {$first: '$unitName'},
                        orderQuantity: {$first: '$orderQuantity'},
                        quantity: {$sum: '$quantity'},
                        unitPrice: {$first: '$unitPrice'},
                        exchangeRatesMap: {$first: '$exchangeRatesMap'},
                        description: {$first: '$description'}
                    }
                },
                {
                    $project: {
                        _id: 0
                    }
                }
            ];
            const pipeline = [
                ...externalReservationsPipeline,
                {
                    $unionWith: {
                        coll: 'inventory_reservations',
                        pipeline: reservationsPipeline
                    }
                },
                {
                    $group: {
                        _id: {productId: '$productId', documentId: '$documentId'},
                        date: {$first: '$date'},
                        documentId: {$first: '$documentId'},
                        documentCode: {$first: '$documentCode'},
                        documentCollection: {$first: '$documentCollection'},
                        documentView: {$first: '$documentView'},
                        documentTitle: {$first: '$documentTitle'},
                        productId: {$first: '$productId'},
                        productCode: {$first: '$productCode'},
                        productDefinition: {$first: '$productDefinition'},
                        productGroupIds: {$first: '$productGroupIds'},
                        productCategoryId: {$first: '$productCategoryId'},
                        productCategoryPath: {$first: '$productCategoryPath'},
                        productBarcode: {$first: '$productBarcode'},
                        productBrandId: {$first: '$productBrandId'},
                        warehouseId: {$first: '$warehouseId'},
                        warehouseCode: {$first: '$warehouseCode'},
                        warehouseName: {$first: '$warehouseName'},
                        financialProjectId: {$first: '$financialProjectId'},
                        financialProjectCode: {$first: '$financialProjectCode'},
                        financialProjectName: {$first: '$financialProjectName'},
                        partnerId: {$first: '$partnerId'},
                        partnerCode: {$first: '$partnerCode'},
                        partnerName: {$first: '$partnerName'},
                        unitId: {$first: '$unitId'},
                        unitName: {$first: '$unitName'},
                        orderQuantity: {$max: '$orderQuantity'},
                        remainingQuantity: {$sum: '$quantity'},
                        unitPrice: {$first: '$unitPrice'},
                        exchangeRatesMap: {$first: '$exchangeRatesMap'},
                        description: {$first: '$description'}
                    }
                },
                {
                    $project: {
                        _id: {$concat: ['$productId', '$documentId']},
                        date: 1,
                        documentId: 1,
                        documentCode: 1,
                        documentCollection: 1,
                        documentView: 1,
                        documentTitle: 1,
                        productId: 1,
                        productCode: 1,
                        productDefinition: 1,
                        productGroupIds: 1,
                        productCategoryId: 1,
                        productCategoryPath: 1,
                        productBarcode: 1,
                        productBrandId: 1,
                        warehouseId: 1,
                        warehouseCode: 1,
                        warehouseName: 1,
                        financialProjectId: 1,
                        financialProjectCode: 1,
                        financialProjectName: 1,
                        partnerId: 1,
                        partnerCode: 1,
                        partnerName: 1,
                        unitId: 1,
                        unitName: 1,
                        orderQuantity: 1,
                        remainingQuantity: 1,
                        deliveredQuantity: {$subtract: ['$orderQuantity', '$remainingQuantity']},
                        unitPrice: 1,
                        exchangeRatesMap: 1,
                        description: 1,
                        ...(this.selectedCurrency !== company.currency.name
                            ? {
                                  orderTotal: {
                                      $divide: [
                                          {$multiply: ['$orderQuantity', '$unitPrice']},
                                          `$exchangeRatesMap.${this.selectedCurrency}`
                                      ]
                                  }
                              }
                            : {orderTotal: {$multiply: ['$orderQuantity', '$unitPrice']}}),
                        ...(this.selectedCurrency !== company.currency.name
                            ? {
                                  remainingTotal: {
                                      $divide: [
                                          {$multiply: ['$remainingQuantity', '$unitPrice']},
                                          `$exchangeRatesMap.${this.selectedCurrency}`
                                      ]
                                  }
                              }
                            : {remainingTotal: {$multiply: ['$remainingQuantity', '$unitPrice']}}),
                        ...(this.selectedCurrency !== company.currency.name
                            ? {
                                  deliveredTotal: {
                                      $divide: [
                                          {
                                              $multiply: [
                                                  {$subtract: ['$orderQuantity', '$remainingQuantity']},
                                                  '$unitPrice'
                                              ]
                                          },
                                          `$exchangeRatesMap.${this.selectedCurrency}`
                                      ]
                                  }
                              }
                            : {
                                  deliveredTotal: {
                                      $multiply: [{$subtract: ['$orderQuantity', '$remainingQuantity']}, '$unitPrice']
                                  }
                              })
                    }
                },
                {
                    $match: {
                        ...query
                    }
                }
            ];

            let sortStage = {$sort: {date: -1}}; 

            if (tableQuery.$sort && Object.keys(tableQuery.$sort).length > 0) {
                sortStage = {$sort: tableQuery.$sort};
            }

            const report = await this.$collection('inventory.external-reservations').aggregate([
                ...pipeline,
                sortStage,
                {$skip: skip},
                {$limit: limit}
            ]);
            const totalReport = await this.$collection('inventory.external-reservations').aggregate([
                ...pipeline,
                {
                    $group: {
                        _id: null,
                        orderQuantity: {$sum: '$orderQuantity'},
                        remainingQuantity: {$sum: '$remainingQuantity'},
                        orderTotal: {$sum: '$orderTotal'},
                        remainingTotal: {$sum: '$remainingTotal'},
                        deliveredTotal: {$sum: '$deliveredTotal'},
                        count: {$sum: 1}
                    }
                }
            ]);
            this.totalReport =
                totalReport.length > 0
                    ? totalReport[0]
                    : {
                          orderQuantity: 0,
                          remainingQuantity: 0,
                          orderTotal: 0,
                          remainingTotal: 0,
                          deliveredTotal: 0,
                          count: 0
                      };

            const orderIds = [];
            const invoiceIds = [];
            for (const r of report) {
                if (r.documentCollection === 'sale.orders') {
                    orderIds.push(r.documentId);
                } else if (r.documentCollection === 'accounting.customer-invoices') {
                    invoiceIds.push(r.documentId);
                }
            }

            if (orderIds.length > 0) {
                const orders = await this.$collection('sale.orders').find({
                    _id: {$in: orderIds},
                    $select: ['_id', 'deliveryAddressId'],
                    $disableActiveCheck: true,
                    $disableSoftDelete: true
                });

                report.forEach(r => {
                    const order = orders.find(o => o._id === r.documentId);

                    if (order) {
                        r.deliveryAddressId = order.deliveryAddressId;
                    }
                });
            }
            if (invoiceIds.length > 0) {
                const invoices = await this.$collection('accounting.customer-invoices').find({
                    _id: {$in: invoiceIds},
                    $select: ['_id', 'deliveryAddressId'],
                    $disableActiveCheck: true,
                    $disableSoftDelete: true
                });

                report.forEach(r => {
                    const invoice = invoices.find(i => i._id === r.documentId);

                    if (invoice) {
                        r.deliveryAddressId = invoice.deliveryAddressId;
                    }
                });
            }

            const deliveryAddressIds = report.filter(r => !!r.deliveryAddressId).map(r => r.deliveryAddressId);
            const contacts = await this.$collection('kernel.contacts').find({
                _id: {$in: deliveryAddressIds},
                $select: ['_id', 'code', 'name'],
                $disableActiveCheck: true,
                $disableSoftDelete: true
            });

            report.forEach(r => {
                const contact = contacts.find(c => c._id === r.deliveryAddressId);

                if (contact) {
                    r.deliveryAddressInfo = contact.code ? `${contact.code} - ${contact.name}` : contact.name;
                }
            });

            result.data = report;
            result.total = this.totalReport.count;

            this.$nextTick(() => {
                this.$params('loading', false);
            });

            return result;
        },
        summaryRow() {
            const totalReport = this.totalReport;

            if (_.isPlainObject(totalReport)) {
                return {
                    rowCount: totalReport.count,
                    orderQuantity: totalReport.orderQuantity,
                    remainingQuantity: totalReport.remainingQuantity,
                    orderTotal: totalReport.orderTotal,
                    remainingTotal: totalReport.remainingTotal,
                    deliveredTotal: totalReport.deliveredTotal
                };
            }

            return {};
        }
    },

    async created() {
        const company = this.$store.getters['session/company'];

        this.currencies = await this.$collection('kernel.currencies').find({
            name: {$in: ['TL', 'USD', 'EUR', 'GBP', 'RUB']}
        });
        this.selectedCurrency = company.currency.name;
        this.isInitialized = true;
    }
};
</script>

<style lang="scss">
.sale-reports-delivery-analysis {
    .ui-view-top-panel {
        display: flex;

        .currency-select {
            display: flex;
            flex-flow: column nowrap;
            width: 120px;
            justify-content: center;
            align-items: flex-end;
        }
    }
}
</style>
