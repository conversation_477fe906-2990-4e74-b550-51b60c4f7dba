import _ from 'lodash';

export default {
    data: () => ({
        model: {},
        activeTab: 'items',
        moduleName: '',
        extraFields: [
            'status',
            'currencyRate',
            'subTotal',
            'taxTotal',
            'rounding',
            'grandTotal',
            'appliedTaxes',
            'discount',
            'discountAmount',
            'subTotalAfterDiscount',
            'paymentTerm',
            'paymentPlan',
            'paymentPlanBackup',
            'transferIds',
            'relatedDocuments',
            'partialDeliveries',
            'limitParams',
            'contractParams',
            'paidTotal',
            'plannedTotal',
            'receiptId',
            'workflowApprovalStatus',
            'campaigns',
            'freight',
            'additionalInformation',
            'additionalInformationId',
            'stageHistory',
            'stages'
        ],
        generatedCode: '',
        currencyFormat: {},
        itemsKey: _.uniqueId('orderItems_'),
        organizations: [],
        organizationTeam: [],
        organizationSettings: {},
        deliveryPolicyOptions: [
            {value: 'when-one-ready', label: 'When a product is ready'},
            {value: 'when-all-ready', label: 'When all products are ready'}
        ],
        deliveryPriorityOptions: [
            {value: 'not-urgent', label: 'Not urgent'},
            {value: 'normal', label: 'Normal'},
            {value: 'urgent', label: 'Urgent'},
            {value: 'very-urgent', label: 'Very urgent'}
        ],
        shippingPaymentTypeOptions: [
            {value: 'freight-prepaid', label: 'Freight prepaid'},
            {value: 'freight-collect', label: 'Freight collect'}
        ],
        productFields: [
            'code',
            'name',
            'displayName',
            'definition',
            'type',
            'baseUnitId',
            'salesUnitId',
            'barcode',
            'categoryPath',
            'groupIds',
            'unitRatios',
            'unitConversions',
            'salesPrice',
            'salesTaxId',
            'salesNote',
            'isSimple',
            'isKit',
            'brandId',
            'shippingUnitId',
            'typeOfGoodsId',
            'manufacturerId',
            'manufacturerProductCode',
            'countryOfManufactureId',
            'containerTypeId',
            'containerNo',
            'containerBrand',
            'hsCode',
            'classificationCode',
            'classificationVersion',
            'classificationValue',
            'countryOfOriginId',
            'unitMeasurements',
            'tracking'
        ],
        legalDocumentPreviewUrl: '',
        currentCurrencyRate: 1,
        canCreateInvoiceFromOrder: false,
        canCreateDeliveryFromOrder: false,
        canCreateReceiptFromOrder: false,
        deliveryMethodType: null,
        systemCurrencyId: null,
        hasReservations: false,
        currencies: [],
        isAdvancedDeliveryPlanningEnabled: false,
        showAdditionalInformationTab: false,
        stages: [],
        activeStageIndex: null,
        initialized: false
    })
};
