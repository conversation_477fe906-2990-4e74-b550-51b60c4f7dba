import _ from 'lodash';
import schema from './schema';
import saveAs from 'framework/save-as';
import fastCopy from 'fast-copy';
import TotalsCurrencyConversions from './_totals-currency-conversions';
import {toUpper} from '../../../../../../framework/helpers';

export default {
    computed: {
        title() {
            const model = this.model;

            if (this.$params('id')) {
                return model.code ? model.code : '';
            }

            return this.$t('New Order');
        },
        schema() {
            return schema(this);
        },
        statuses() {
            const model = this.model;
            const statuses = [{value: 'draft', label: 'Draft'}];

            if (_.isObject(model.paymentPlan) && !_.isEmpty(model.paymentPlan)) {
                statuses.push({
                    value: 'payment-planned',
                    label: 'Payment Planned'
                });
            }

            if (this.status === 'canceled') {
                statuses.push({value: 'canceled', label: 'Canceled'});
            } else {
                statuses.push({value: 'approved', label: 'Approved'});
            }

            if (this.status === 'invoiced') {
                statuses.push({value: 'invoiced', label: 'Invoiced'});
            } else {
                statuses.push({value: 'to-invoice', label: 'To Invoice'});
            }

            return statuses;
        },
        status() {
            const model = this.model;

            return model.status;
        },
        actions() {
            return this.$params('id') &&
                (this.isApproved ||
                    this.isToInvoice ||
                    this.isInvoiced ||
                    this.isCanceled ||
                    (!!this.model.workflowApprovalStatus &&
                        this.model.workflowApprovalStatus === 'waiting-for-approval'))
                ? 'edit:disabled,cancel'
                : 'edit,cancel';
        },
        extraActions() {
            const round = this.$app.roundNumber;
            const currencyPrecision = this.$setting('system.currencyPrecision');
            const self = this;

            return [
                {
                    name: 'create-invoice',
                    title: 'Create Invoice',
                    icon: 'fal fa-file-invoice',
                    handler: this.handleCreateInvoice,
                    disabled() {
                        return (
                            !self.$params('id') ||
                            self.status !== 'approved' ||
                            (!!self.model.workflowApprovalStatus &&
                                self.model.workflowApprovalStatus === 'waiting-for-approval')
                        );
                    },
                    hidden() {
                        return (
                            !self.canCreateInvoiceFromOrder ||
                            !self.$checkPermission({
                                type: 'permission',
                                name: 'sale.canCreateInvoiceFromOrder'
                            })
                        );
                    }
                },
                {
                    name: 'create-delivery',
                    title: 'Create Delivery',
                    icon: 'fal fa-truck-container',
                    handler: this.handleCreateDelivery,
                    disabled() {
                        let isDisabled =
                            !self.$params('id') ||
                            !(
                                self.status === 'approved' ||
                                self.status === 'to-invoice' ||
                                self.status === 'invoiced'
                            ) ||
                            !(self.model.items || []).some(item => item.productType !== 'service') ||
                            (!!self.model.workflowApprovalStatus &&
                                self.model.workflowApprovalStatus === 'waiting-for-approval');

                        if (!isDisabled) {
                            if (!!self.isAdvancedDeliveryPlanningEnabled) {
                                isDisabled = !self.hasReservations;
                            } else {
                                isDisabled = Array.isArray(self.model.transferIds) && self.model.transferIds.length > 0;
                            }
                        }

                        return isDisabled;
                    },
                    hidden() {
                        return !self.canCreateDeliveryFromOrder;
                    }
                },
                {
                    name: 'schedule-delivery',
                    title: 'Delivery Schedule',
                    icon: 'fal fa-calendar-edit',
                    handler: this.handleScheduleDelivery,
                    disabled() {
                        let isDisabled =
                            !self.$params('id') ||
                            !(
                                self.status === 'approved' ||
                                self.status === 'to-invoice' ||
                                self.status === 'invoiced'
                            ) ||
                            !(self.model.items || []).some(item => item.productType !== 'service') ||
                            (!!self.model.workflowApprovalStatus &&
                                self.model.workflowApprovalStatus === 'waiting-for-approval');

                        if (!isDisabled) {
                            if (!!self.isAdvancedDeliveryPlanningEnabled) {
                                isDisabled = !self.hasReservations;
                            } else {
                                isDisabled = Array.isArray(self.model.transferIds) && self.model.transferIds.length > 0;
                            }
                        }

                        return isDisabled;
                    },
                    hidden() {
                        return !self.isAdvancedDeliveryPlanningEnabled;
                    }
                },
                {
                    name: 'create-payment',
                    title: 'Create Payment',
                    icon: 'fal fa-coins',
                    handler: this.handleCreatePayment,
                    hidden() {
                        return (
                            !self.$params('id') ||
                            !self.model.paymentTermId ||
                            self.status === 'canceled' ||
                            !!self.model.receiptId ||
                            !self.canCreateReceiptFromOrder ||
                            round(self.model.grandTotal - self.model.plannedTotal || 0, currencyPrecision) === 0 ||
                            (!!self.model.workflowApprovalStatus &&
                                self.model.workflowApprovalStatus === 'waiting-for-approval')
                        );
                    }
                },
                {
                    name: 'supply',
                    title: 'Supply',
                    icon: 'fal fa-shopping-cart',
                    handler: this.handleSupply,
                    disabled() {
                        const relatedDocuments = self.model.relatedDocuments || [];
                        const index = relatedDocuments.findIndex(rd => rd.collection === 'purchase.requests');

                        return (
                            index !== -1 ||
                            (self.model.items || []).length < 1 ||
                            _.sumBy(self.model.items || [], 'quantity') < 1 ||
                            (!!self.model.workflowApprovalStatus &&
                                self.model.workflowApprovalStatus === 'waiting-for-approval')
                        );
                    }
                },
                ...(this.$app.hasModule('basic-production') || this.$app.hasModule('production')
                    ? [
                          {
                              name: 'create-production-order',
                              title: 'Create production order',
                              icon: 'fal fa-industry-alt',
                              handler: this.handleCreateProductionOrder,
                              disabled() {
                                  const relatedDocuments = self.model.relatedDocuments || [];
                                  const index = relatedDocuments.findIndex(
                                      rd =>
                                          rd.collection === 'basic-production.orders' ||
                                          rd.collection === 'production.orders'
                                  );

                                  return (
                                      !self.$params('isPreview') ||
                                      index !== -1 ||
                                      (self.model.items || []).length < 1 ||
                                      _.sumBy(self.model.items || [], 'quantity') < 1 ||
                                      (!!self.model.workflowApprovalStatus &&
                                          self.model.workflowApprovalStatus === 'waiting-for-approval')
                                  );
                              }
                          }
                      ]
                    : []),
                {
                    name: 'clear-payment-plan',
                    title: 'Clear Payment Plan',
                    icon: 'fal fa-broom',
                    handler: this.handleClearPaymentPlan,
                    disabled() {
                        return (
                            self.$params('isPreview') ||
                            self.status === 'draft' ||
                            self.status === 'approved' ||
                            self.status === 'invoiced' ||
                            self.status === 'to-invoice' ||
                            self.status === 'canceled' ||
                            (!!self.model.workflowApprovalStatus &&
                                self.model.workflowApprovalStatus === 'waiting-for-approval')
                        );
                    }
                },
                {
                    name: 'clear-campaigns',
                    title: 'Clear Campaigns',
                    icon: 'fal fa-broom',
                    handler: this.handleClearCampaigns,
                    disabled() {
                        return (
                            self.$params('isPreview') ||
                            (self.model.campaigns || []).length < 1 ||
                            self.status === 'approved' ||
                            self.status === 'invoiced' ||
                            self.status === 'to-invoice' ||
                            self.status === 'canceled' ||
                            (!!self.model.workflowApprovalStatus &&
                                self.model.workflowApprovalStatus === 'waiting-for-approval')
                        );
                    }
                },
                ...(this.$setting('crm.isDiscoveriesObservationsEnabled')
                    ? [
                          {
                              name: 'add-discovery-observation',
                              title: 'Add Discovery & Observation',
                              icon: 'fal fa-telescope',
                              handler: this.handleDiscoveryObservation,
                              disabled() {
                                  return (
                                      !self.$params('id') ||
                                      self.status === 'canceled' ||
                                      (!!self.model.workflowApprovalStatus &&
                                          self.model.workflowApprovalStatus === 'waiting-for-approval')
                                  );
                              }
                          }
                      ]
                    : []),
                {
                    name: 'cancel-order',
                    title: 'Cancel Order',
                    icon: 'fal fa-ban',
                    handler: this.handleCancel,
                    disabled() {
                        return (
                            !self.$params('id') ||
                            self.status === 'canceled' ||
                            (!!self.model.workflowApprovalStatus &&
                                self.model.workflowApprovalStatus === 'waiting-for-approval')
                        );
                    }
                },
                {
                    name: 'print-preliminary-information-form',
                    title: 'Print Preliminary Information Form',
                    icon: 'fal fa-print',
                    handler: () => this.printLegalDocuments({type: 'preliminary-information-form'}),
                    hidden() {
                        return !self.$params('id') || self.model.module !== 'ecommerce';
                    }
                },
                {
                    name: 'print-sales-contract-text',
                    title: 'Print Distance Sales Contract',
                    icon: 'fal fa-print',
                    handler: () => this.printLegalDocuments({type: 'sales-contract-text'}),
                    hidden() {
                        return !self.$params('id') || self.model.module !== 'ecommerce';
                    }
                }
            ];
        },
        isApproved() {
            const model = this.model;

            return model.status === 'approved';
        },
        isToInvoice() {
            const model = this.model;

            return model.status === 'to-invoice';
        },
        isInvoiced() {
            const model = this.model;

            return model.status === 'invoiced';
        },
        isCanceled() {
            const model = this.model;

            return model.status === 'canceled';
        },
        assignation() {
            const approvedDocumentsCanBeAssigned = !!this.$setting('system.approvedDocumentsCanBeAssigned');
            const disabled = approvedDocumentsCanBeAssigned
                ? this.status === 'canceled'
                : this.status === 'approved' ||
                  this.status === 'invoiced' ||
                  this.status === 'to-invoice' ||
                  this.status === 'canceled' ||
                  (!!this.model.workflowApprovalStatus && this.model.workflowApprovalStatus === 'waiting-for-approval');

            return {
                organizationScope: 'sale',
                managerField: 'salesManagerId',
                employeeField: 'salespersonId',
                disabled
            };
        },
        totalItems() {
            const self = this;
            const model = this.model;
            const items = [];

            // Sub total
            items.push({label: this.$t('Subtotal'), value: model.subTotal});

            // Discount
            if (this.$setting('sale.discounts')) {
                let discountLabel =
                    model.discount > 0
                        ? `${this.$t('Discount')} (%${this.$format(model.discount, 'percentage')})`
                        : this.$t('Discount');

                const item = {
                    label: discountLabel,
                    value: model.discountAmount > 0 ? -model.discountAmount : 0,
                    action: {
                        title: this.$t('Apply Discount'),
                        icon: 'fas fa-badge-percent text-danger',
                        disabled:
                            model.subTotal === 0 ||
                            (this.$params('id') && this.$params('isPreview')) ||
                            this.model.status === 'payment-planned',
                        handler: this.applyDiscount
                    }
                };

                if (model.discountAmount > 0) {
                    item.subItems = [
                        {
                            label: this.$t('Subtotal After Discount'),
                            value: model.subTotalAfterDiscount
                        }
                    ];
                }

                let totalRowDiscountAmount = 0;
                for (const row of model.items || []) {
                    const unDiscountedTotal = this.$app.round(row.quantity * row.unitPrice, 'total');
                    const rowDiscountAmount = this.$app.round(unDiscountedTotal - row.total, 'total');

                    if (rowDiscountAmount > 0) {
                        totalRowDiscountAmount += rowDiscountAmount;
                    }
                }
                if (totalRowDiscountAmount > 0) {
                    if (!Array.isArray(item.subItems)) item.subItems = [];

                    item.subItems.push({
                        label: this.$t('Total Row Discount Amount'),
                        value: totalRowDiscountAmount
                    });
                }

                items.push(item);
            }

            // Tax total.
            if (Array.isArray(model.appliedTaxes) && model.appliedTaxes.length > 0) {
                const groupedTaxes = _.groupBy(model.appliedTaxes, tax => (!!tax.label ? tax.label : tax.name));
                const item = {
                    label: this.$t('Taxes'),
                    value: model.taxTotal,
                    subItems: []
                };

                for (const label of Object.keys(groupedTaxes)) {
                    const appliedTaxes = groupedTaxes[label];
                    let total = 0;

                    for (const tax of appliedTaxes) {
                        total += tax.appliedAmount;
                    }

                    item.subItems.push({
                        label,
                        value: this.$app.round(total, 'total')
                    });
                }

                items.push(item);
            } else {
                items.push({label: this.$t('Taxes'), value: 0});
            }

            items.push({
                label: this.$t('Rounding'),
                value: model.rounding,
                action: {
                    title: this.$t('Apply Rounding'),
                    icon: 'far fa-badge',
                    disabled: model.subTotal === 0 || (this.$params('id') && this.$params('isPreview')),
                    handler: this.applyRounding
                }
            });

            // Grand total
            const grandTotal = {
                label: this.$t('Grand Total'),
                value: model.grandTotal,
                style: {fontWeight: 700},
                action: {
                    title: this.$t('Currency Conversions'),
                    icon: 'fas fa-sync-alt',
                    iconStyle: 'font-size: 12px;',
                    popupComponent: TotalsCurrencyConversions,
                    popupWidth: 320,
                    popupPayload: {
                        systemCurrencyId: this.systemCurrencyId,
                        currencies: this.currencies ?? [],
                        currencyRate: this.model.currencyRate ?? 1,
                        exchangeRates: this.model.exchangeRates ?? []
                    }
                }
            };
            if (model.paidTotal > 0) {
                grandTotal.subItems = [];

                grandTotal.subItems.push({
                    label: this.$t('Total Paid'),
                    value: model.paidTotal || 0
                });
                grandTotal.subItems.push({
                    label: this.$t('Total Remaining'),
                    value: model.grandTotal - (model.paidTotal || 0)
                });
            }
            items.push(grandTotal);

            if (!!this.$setting('system.freight')) {
                const freight = model.freight;
                let amount = 0;

                if (!!freight && freight.amount > 0) {
                    amount = freight.amount;
                }

                const item = {
                    label: this.$t('Freight'),
                    value: amount,
                    action: {
                        title: this.$t('Apply Freight'),
                        icon: 'fas fa-truck',
                        iconStyle: {fontSize: '12px'},
                        handler: this.applyFreight
                    }
                };

                if (!!freight && Array.isArray(freight.appliedTaxes) && freight.appliedTaxes.length > 0) {
                    item.subItems = [
                        {
                            label: this.$t('Freight'),
                            value: amount
                        }
                    ];

                    const groupedTaxes = _.groupBy(freight.appliedTaxes, tax => (!!tax.label ? tax.label : tax.name));

                    for (const label of Object.keys(groupedTaxes)) {
                        const appliedTaxes = groupedTaxes[label];
                        let total = 0;

                        for (const tax of appliedTaxes) {
                            total += tax.appliedAmount;
                        }

                        item.subItems.push({
                            label,
                            value: this.$app.round(total, 'total')
                        });
                    }
                }

                items.push(item);
            }

            return {items, format: this.currencyFormat};
        },
        documentTypeIdFilters() {
            const filters = {type: 'order'};

            if (this.model.module === 'service') {
                filters.module = 'service';
            }

            if (_.isObject(this.organizationSettings) && !_.isEmpty(this.organizationSettings)) {
                filters._id = {
                    $in: this.organizationSettings.documentTypeIds || []
                };
            }

            return filters;
        },
        partnerGroupIdFilters() {
            const filters = {type: 'customer'};

            if (_.isObject(this.organizationSettings) && !_.isEmpty(this.organizationSettings)) {
                filters._id = {
                    $in: this.organizationSettings.customerGroupIds || []
                };
            }

            return filters;
        },
        partnerIdFilters() {
            const filters = {type: 'customer'};

            if (this.model.partnerGroupId) {
                filters.groupId = this.model.partnerGroupId;
            } else if (_.isObject(this.organizationSettings) && !_.isEmpty(this.organizationSettings)) {
                filters.groupId = {
                    $in: this.organizationSettings.customerGroupIds || []
                };
            }

            return filters;
        },
        isPartnerIdDisabled() {
            // if (_.isObject(this.organizationSettings) && !_.isEmpty(this.organizationSettings)) {
            //     return !this.model.partnerGroupId || this.status === 'payment-planned';
            // }

            return this.status === 'payment-planned';
        },
        warehouseIdFilters() {
            const filters = {branchId: this.model.branchId};

            if (
                _.isObject(this.organizationSettings) &&
                !_.isEmpty(this.organizationSettings) &&
                Array.isArray(this.organizationSettings.warehouseIds) &&
                this.organizationSettings.warehouseIds.length > 0
            ) {
                filters._id = {
                    $in: this.organizationSettings.warehouseIds || []
                };
            }

            return filters;
        },
        paymentTermIdFilters() {
            const filters = {};

            if (_.isObject(this.organizationSettings) && !_.isEmpty(this.organizationSettings)) {
                filters._id = {
                    $in: this.organizationSettings.paymentTermIds || []
                };
            }

            filters.$or = [{scope: null}, {scope: {$exists: false}}, {scope: {$eq: ''}}, {scope: 'receipt'}];

            return filters;
        },
        priceListIdFilters() {
            const filters = {status: 'published'};

            if (this.model.module === 'service') {
                filters.module = 'service';
            }
            if (this.model.module === 'ecommerce') {
                filters.module = 'ecommerce';
            }

            if (_.isObject(this.organizationSettings) && !_.isEmpty(this.organizationSettings)) {
                filters._id = {
                    $in: this.organizationSettings.priceListIds || []
                };
            }

            return filters;
        },
        customerPriceListIdFilters() {
            const filters = {status: 'published', partnerId: this.model.partnerId};

            if (this.model.module === 'service') {
                filters.module = 'service';
            }
            if (this.model.module === 'ecommerce') {
                filters.module = 'ecommerce';
            }

            return filters;
        },
        isGrossProfitCalculatorDisabled() {
            if (
                this.$setting('sale.salesOrganizations') &&
                (!_.isObject(this.organizationSettings) || _.isEmpty(this.organizationSettings))
            ) {
                return true;
            }

            if (_.isObject(this.organizationSettings) && !_.isEmpty(this.organizationSettings)) {
                const settings = this.organizationSettings;

                if (!settings.defaultProfitBase) return true;
                else if ((settings.profitBases || []).length < 1) return true;
            }

            return (this.model.items || []).length < 1;
        },
        isUnitPriceAnalysisDisabled() {
            let allowedUnitPriceAnalysisTypes = this.$setting('sale.allowedUnitPriceAnalysisTypes');
            if (
                _.isObject(this.organizationSettings) &&
                Array.isArray(this.organizationSettings.allowedUnitPriceAnalysisTypes)
            ) {
                allowedUnitPriceAnalysisTypes = this.organizationSettings.allowedUnitPriceAnalysisTypes;
            } else if (this.$setting('sale.salesOrganizations')) {
                allowedUnitPriceAnalysisTypes = [];
            }

            return !(
                Array.isArray(this.model.items) &&
                this.model.items.length > 0 &&
                Array.isArray(allowedUnitPriceAnalysisTypes) &&
                allowedUnitPriceAnalysisTypes.length > 0
            );
        },
        activityPayload() {
            return {
                partnerType: 'customer',
                partnerId: this.model.partnerId
            };
        },
        itemsContextMenuActions() {
            return [
                {
                    title: this.$t('Stock Status'),
                    icon: 'inventory',
                    disabled: row => {
                        return !(!!row.product && !!row.warehouseId);
                    },
                    action: async row => {
                        this.$program.dialog({
                            component: 'inventory.reports.stock-status.master',
                            params: {
                                productId: row.productId,
                                warehouseId: row.warehouseId,
                                isADPGraphShown: true
                            }
                        });
                    }
                },
                ...(!!this.isAdvancedDeliveryPlanningEnabled
                    ? [
                          {
                              title: this.$t('Delivery Schedule'),
                              icon: 'calendar-edit',
                              disabled: row => {
                                  let isDisabled =
                                      !this.$params('id') ||
                                      this.status !== 'approved' ||
                                      !(this.model.items || []).some(item => item.productType !== 'service');

                                  if (!isDisabled) {
                                      isDisabled = !this.hasReservations;
                                  }

                                  return isDisabled || !row.productId;
                              },
                              action: async row => {
                                  this.handleScheduleDelivery(row.productId, row.productCode, row.productDefinition);
                              }
                          }
                      ]
                    : []),
                {
                    title: this.$t('Unit Price Analysis'),
                    icon: 'coins',
                    disabled: row => {
                        let allowedUnitPriceAnalysisTypes = this.$setting('sale.allowedUnitPriceAnalysisTypes');
                        if (
                            _.isObject(this.organizationSettings) &&
                            Array.isArray(this.organizationSettings.allowedUnitPriceAnalysisTypes)
                        ) {
                            allowedUnitPriceAnalysisTypes = this.organizationSettings.allowedUnitPriceAnalysisTypes;
                        } else if (this.$setting('sale.salesOrganizations')) {
                            allowedUnitPriceAnalysisTypes = [];
                        }

                        return !(
                            !!row.product &&
                            Array.isArray(allowedUnitPriceAnalysisTypes) &&
                            allowedUnitPriceAnalysisTypes.length > 0
                        );
                    },
                    action: async row => {
                        let allowedUnitPriceAnalysisTypes = this.$setting('sale.allowedUnitPriceAnalysisTypes');
                        if (
                            _.isObject(this.organizationSettings) &&
                            Array.isArray(this.organizationSettings.allowedUnitPriceAnalysisTypes)
                        ) {
                            allowedUnitPriceAnalysisTypes = this.organizationSettings.allowedUnitPriceAnalysisTypes;
                        } else if (this.$setting('sale.salesOrganizations')) {
                            allowedUnitPriceAnalysisTypes = [];
                        }

                        this.$program.dialog({
                            component: 'sale.components.unit-price-analysis',
                            params: {
                                documentCollection: 'sale.orders',
                                documentId: this.$params('id'),
                                documentCode: this.model.code,
                                partnerId: this.model.partnerId,
                                leadId: null,
                                productId: row.productId,
                                productCode: row.product.code,
                                productDefinition: row.product.definition,
                                unitPrice: row.unitPrice,
                                currencyId: this.model.currencyId,
                                currencyRate: this.model.currencyRate,
                                currencyFormat: this.currencyFormat,
                                allowedUnitPriceAnalysisTypes,
                                forcedPreview: this.$params('isPreview'),
                                handleApply: async unitPrice => {
                                    this.$params('loading', true);

                                    const itemIndex = this.model.items.findIndex(item => item.id === row.id);
                                    const updatedItems = await this.$rpc('sale.decorate-order-items', {
                                        items: [
                                            {
                                                ...this.model.items[itemIndex],
                                                unitId: row.baseUnitId,
                                                unitPrice
                                            }
                                        ],
                                        field: 'unitId',
                                        model: _.omit(this.model, 'items'),
                                        productFields: this.productFields,
                                        dontSetUnitPrice: true
                                    });

                                    const items = [...this.model.items];
                                    items[itemIndex] = updatedItems[0];

                                    this.$set(this.model, 'items', items);
                                    this.afterSaveItem();

                                    this.$params('loading', false);
                                }
                            }
                        });
                    }
                },
                {
                    title: this.$t('Expiration Analysis'),
                    icon: 'calendar-exclamation',
                    disabled: row => {
                        return !(!!row.product && row.product.tracking === 'serial');
                    },
                    action: async row => {
                        this.$program.dialog({
                            component: 'inventory.reports.expiration-analysis',
                            params: {
                                productId: row.productId
                            }
                        });
                    }
                },
                ...(this.$app.hasModule('pcm')
                    ? [
                          {
                              title: this.$t('Configure Product'),
                              icon: 'cog',
                              disabled: row => {
                                  return !(!!row._id && !!row.productId && !!row.pcmModelId);
                              },
                              action: async row => {
                                  this.$params('loading', true);

                                  try {
                                      const onSubmit = async ({
                                          configuratorType,
                                          description,
                                          configurationId,
                                          deliveryDate,
                                          quantity,
                                          price,
                                          additionalPrice,
                                          products,
                                          hash
                                      }) => {
                                          this.$params('loading', true);

                                          try {
                                              const items = fastCopy(this.model.items);
                                              const itemsIndex = items.findIndex(i => i.id === row.id);

                                              if (configuratorType !== 'product-finder') {
                                                  const item = (
                                                      await this.$rpc('sale.decorate-order-items', {
                                                          items: [
                                                              {
                                                                  ...row,
                                                                  description,
                                                                  pcmConfigurationId: configurationId,
                                                                  pcmHash: hash,
                                                                  scheduledDate: deliveryDate,
                                                                  quantity,
                                                                  baseQuantity:
                                                                      quantity * (row.baseQuantity / row.quantity),
                                                                  total: price
                                                              }
                                                          ],
                                                          field: 'total',
                                                          model: _.omit(this.model, 'items'),
                                                          productFields: this.productFields
                                                      })
                                                  )[0];

                                                  items[itemsIndex] = item;
                                              } else {
                                                  items.splice(itemsIndex, 1);
                                              }

                                              if (products.length > 0) {
                                                  const additionalItems = await this.$rpc('sale.decorate-order-items', {
                                                      items: products.map(row => {
                                                          return {
                                                              productId: row.productId,
                                                              scheduledDate: deliveryDate,
                                                              quantity: row.quantity,
                                                              baseQuantity: row.quantity,
                                                              unitPrice: row.unitPrice,
                                                              discount: 0,
                                                              unitPriceAfterDiscount: 0,
                                                              grossUnitPriceAfterDiscount: 0,
                                                              taxTotal: 0,
                                                              grossTotal: 0,
                                                              stockQuantity: 0,
                                                              orderedQuantity: 0,
                                                              assignedQuantity: 0,
                                                              availableQuantity: 0,
                                                              warehouseStockQuantity: 0,
                                                              warehouseOrderedQuantity: 0,
                                                              warehouseAssignedQuantity: 0,
                                                              warehouseAvailableQuantity: 0,
                                                              total: 0
                                                          };
                                                      }),
                                                      field: 'productId',
                                                      model: _.omit(this.model, 'items'),
                                                      productFields: this.productFields,
                                                      dontSetUnitPrice: true
                                                  });

                                                  items.push(...additionalItems);
                                              }

                                              this.model.items = items;

                                              let profitBase = null;
                                              if (
                                                  _.isObject(this.organizationSettings) &&
                                                  this.organizationSettings.defaultProfitBase
                                              ) {
                                                  profitBase =
                                                      this.organizationSettings.defaultProfitBase ||
                                                      this.$setting('sale.profitBase');
                                              } else {
                                                  profitBase = this.$setting('sale.profitBase');
                                              }
                                              if (
                                                  this.$params('id') &&
                                                  Array.isArray(this.model.items) &&
                                                  this.model.items.length > 0
                                              ) {
                                                  this.$rpc('sale.update-document-profits', {
                                                      profitBase,
                                                      documentId: this.$params('id'),
                                                      documentCollection: 'sale.orders',
                                                      currencyId: this.model.currencyId,
                                                      currencyRate: this.model.currencyRate,
                                                      date: this.model.orderDate,
                                                      exchangeRates: this.model.exchangeRates ?? [],
                                                      items: this.model.items.map(item => ({
                                                          productId: item.productId,
                                                          warehouseId: item.warehouseId,
                                                          quantity: item.quantity,
                                                          unitId: item.unitId,
                                                          freight: item.freight || 0,
                                                          totalSalesPrice: item.realTotal
                                                      }))
                                                  });
                                              }

                                              // Reset totals.
                                              this.model.discount = await this.calculateGeneralDiscount();
                                              this.model.paymentPlan = null;
                                              this.model.paymentPlanningDate = null;
                                              await this.calculateTotals();

                                              this.$nextTick(() => {
                                                  this.itemsKey = _.uniqueId('orderItems_');

                                                  this.$program.message(
                                                      'success',
                                                      this.$t('Product configuration applied successfully.')
                                                  );
                                              });
                                          } catch (error) {
                                              this.$program.message('error', error.message);
                                          }

                                          this.$params('loading', false);
                                      };
                                      const params = {
                                          title: row.productDefinition,
                                          forcedPreview: this.$params('isPreview'),
                                          modelId: row.pcmModelId,
                                          price: row.unitPrice,
                                          quantity: row.quantity,
                                          deliveryDate: row.scheduledDate,
                                          configurationId: row.pcmConfigurationId,
                                          currencyFormat: this.currencyFormat,
                                          currencyId: this.model.currencyId,
                                          currencyRate: this.model.currencyRate || 1,
                                          priceListId: this.model.priceListId,
                                          issueDate: this.model.orderDate,
                                          referenceCollection: 'sale.orders',
                                          referenceView: 'sale.sales.orders',
                                          referenceId: this.$params('id'),
                                          referenceCode: this.model.code,
                                          partnerId: this.model.partnerId,
                                          onSubmit
                                      };

                                      if (!!row.pcmConfigurationId) {
                                          const configuration = await this.$collection('pcm.configurations').findOne({
                                              _id: row.pcmConfigurationId,
                                              $disableSoftDelete: true,
                                              $disableActiveCheck: true
                                          });

                                          params.price = configuration.payload.price;
                                          params.quantity = configuration.payload.quantity;
                                          params.deliveryDate = configuration.payload.deliveryDate;
                                          params.values = configuration.payload.values;
                                          params.stepId = configuration.payload.stepId;
                                          params.finder = configuration.payload.finder;
                                      }

                                      this.$program.dialog({
                                          component: 'pcm.components.configuration',
                                          params
                                      });
                                  } catch (error) {
                                      this.$program.message('error', error.message);
                                  }
                              }
                          }
                      ]
                    : []),
                ...(this.$app.hasModule('health')
                    ? [
                          {
                              title: this.$t('Surplus Goods'),
                              icon: 'sign-out',
                              disabled: row => {
                                  return !row.productId;
                              },
                              action: async row => {
                                  this.$program.dialog({
                                      component: 'health.reports.surplus-good-analysis',
                                      params: {
                                          productId: row.productId
                                      }
                                  });
                              }
                          }
                      ]
                    : []),
                {
                    title: this.$t('Download Sample Data'),
                    icon: 'cloud-download',
                    action: async () => {
                        this.$params('loading', true);

                        const buffer = await this.$rpc('sale.download-order-items-sample');
                        const blob = new Blob([buffer], {
                            type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                        });

                        saveAs(blob, `${this.$t('Items')}.xlsx`);

                        this.$params('loading', false);
                    }
                }
            ];
        },
        financialProjectIdFilters() {
            return {
                $and: [
                    {
                        $or: [
                            {validFrom: {$exists: false}},
                            {validFrom: {$eq: null}},
                            {validFrom: {$eq: ''}},
                            {validFrom: {$lte: this.model.orderDate}}
                        ]
                    },
                    {
                        $or: [
                            {validTo: {$exists: false}},
                            {validTo: {$eq: null}},
                            {validTo: {$eq: ''}},
                            {validTo: {$gte: this.model.orderDate}}
                        ]
                    }
                ],
                $sort: {code: 1}
            };
        },
        campaignsColumns() {
            return [
                {field: 'campaignCode', label: 'Campaign code', width: 150},
                {field: 'campaignName', label: 'Campaign name', minWidth: 180},
                {
                    field: 'campaignType',
                    label: 'Campaign type',
                    width: 150,
                    translateLabels: true,
                    valueLabels: [
                        {value: 'order-discount', label: 'Order discount'},
                        {value: 'product-discount', label: 'Product discount'},
                        {value: 'buy-x-get-y', label: 'Buy X get Y'},
                        {value: 'shipping-discount', label: 'Shipping discount'}
                    ]
                },
                {
                    field: 'campaignApplicationType',
                    label: 'Campaign application type',
                    width: 150,
                    translateLabels: true,
                    valueLabels: [
                        {value: 'automatic', label: 'Automatic'},
                        {value: 'with-coupon-code', label: 'With coupon code'},
                        {value: 'manuel', label: 'Manuel'}
                    ],
                    visible: false
                },
                {
                    field: 'couponCode',
                    label: 'Coupon code',
                    width: 150,
                    visible: false
                },
                {
                    field: 'productCode',
                    label: 'Product code',
                    width: 150
                },
                {
                    field: 'productName',
                    label: 'Product name',
                    minWidth: 180,
                    visible: false
                },
                {
                    field: 'rewardValueType',
                    label: 'Reward value type',
                    width: 150,
                    translateLabels: true,
                    valueLabels: [
                        {value: 'fixed-amount', label: 'Fixed amount'},
                        {value: 'percentage', label: 'Percentage'}
                    ]
                },
                {field: 'rewardValue', label: 'Reward value', width: 120},
                {field: 'discount', label: 'Discount %', width: 120, format: 'percentage'},
                {
                    field: 'discountAmount',
                    label: 'Discount amount',
                    width: 150,
                    format: 'currency',
                    formatOptions: () => {
                        return this.currencyFormat;
                    }
                }
            ];
        },
        stageIdOptions() {
            return this.stages.map(stage => {
                return {
                    value: stage._id,
                    label: stage.name,
                    color: stage.color
                };
            });
        },
        stepStages() {
            return _.cloneDeep(this.stages).map(stage => {
                stage.name = toUpper(stage.name);

                return stage;
            });
        },
        stageHistoryColumns() {
            const self = this;

            return [
                {field: 'stageName', label: 'Stage name'},
                {
                    field: 'subTotal',
                    label: 'Subtotal',
                    format: 'currency',
                    formatOptions() {
                        return self.currencyFormat;
                    },
                    width: 150
                },
                {
                    field: 'grandTotal',
                    label: 'Grand total',
                    format: 'currency',
                    formatOptions() {
                        return self.currencyFormat;
                    },
                    width: 150
                },
                {
                    field: 'date',
                    label: 'Date',
                    format: 'datetime',
                    sort: 'desc',
                    width: 175
                },
                {
                    field: 'stageInterval',
                    label: 'Stage interval',
                    width: 150
                },
                {
                    field: 'username',
                    label: 'Updated by',
                    width: 180
                }
            ];
        }
    }
};
