import _ from 'lodash';
import StockStatusCell<PERSON>enderer from './_stock-status-cell-renderer';
import TaxCellRenderer from './_tax-cell-renderer';
import DiscountCellRenderer from './_discount-cell-renderer';
import SurplusGoodCellRenderer from './_surplus-good-cell-renderer';

export default function (vm) {
    const currenciesMap = {};
    for (const currency of vm.currencies || []) {
        currenciesMap[currency._id] = currency;
    }

    return {
        // General
        module: {
            type: 'string',
            label: 'Module',
            allowed: ['sale', 'ecommerce', 'service', 'project', 'rental'],
            default: 'sale'
        },
        status: {
            type: 'string',
            label: 'Status',
            allowed: ['draft', 'payment-planned', 'approved', 'to-invoice', 'invoiced', 'canceled'],
            default: 'draft'
        },
        documentTypeId: {
            type: 'string',
            label: 'Document type'
        },
        code: {
            type: 'string',
            label: 'Code'
        },
        stageId: {
            type: 'string',
            label: 'Stage',
            required: false
        },
        storeId: {
            type: 'string',
            label: 'Store',
            required: false
        },
        financialEntryIds: {
            type: ['string'],
            label: 'Financial entries',
            index: true,
            default: []
        },
        receiptId: {
            type: 'string',
            label: 'Payment',
            index: true,
            required: false
        },
        reference: {
            type: 'string',
            label: 'Reference',
            required: false
        },
        partnerGroupId: {
            type: 'string',
            label: 'Customer group',
            required: false
        },
        partnerId: {
            type: 'string',
            label: 'Customer'
        },
        currencyId: {
            type: 'string',
            label: 'Currency'
        },
        currencyRate: {
            type: 'decimal',
            label: 'Currency rate',
            default: 1
        },
        contactPersonId: {
            type: 'string',
            label: 'Contact person',
            required: false
        },
        recordDate: {
            type: 'date',
            label: 'Record date',
            default: 'date:now'
        },
        orderDate: {
            type: vm.moduleName === 'ecommerce' ? 'datetime' : 'date',
            label: 'Order date',
            default: 'date:now'
        },
        dueDate: {
            type: 'date',
            label: 'Due date',
            default: 'date:now'
        },
        scheduledDate: {
            type: 'date',
            label: 'Delivery date',
            default: 'date:now'
        },
        branchId: {
            type: 'string',
            label: 'Branch office'
        },
        note: {
            type: 'string',
            label: 'Note',
            required: false
        },

        // Totals
        subTotal: {
            type: 'decimal',
            label: 'Subtotal',
            default: 0
        },
        discount: {
            type: 'decimal',
            label: 'Discount %',
            default: 0
        },
        discountAmount: {
            type: 'decimal',
            label: 'Discount',
            default: 0
        },
        subTotalAfterDiscount: {
            type: 'decimal',
            label: 'Subtotal after discount',
            default: 0
        },
        taxTotal: {
            type: 'decimal',
            label: 'Tax total',
            default: 0
        },
        rounding: {
            type: 'decimal',
            label: 'Rounding',
            default: 0
        },
        grandTotal: {
            type: 'decimal',
            label: 'Grand total',
            default: 0
        },
        paidTotal: {
            type: 'decimal',
            label: 'Paid total',
            default: 0
        },
        plannedTotal: {
            type: 'decimal',
            label: 'Planned total',
            default: 0
        },
        appliedTaxes: {
            type: [
                {
                    type: 'object',
                    blackbox: true,
                    required: false
                }
            ],
            default: []
        },

        // Items
        items: [
            {
                id: {
                    type: 'string',
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                stockStatus: {
                    type: 'string',
                    required: false,
                    column: {
                        iconColumn: true,
                        pinned: 'left',
                        cellRendererFramework: StockStatusCellRenderer
                    }
                },
                productId: {
                    type: 'string',
                    label: 'Product',
                    column: {
                        populate: 'product',
                        pinned: 'left',
                        visible: false,
                        minWidth: 180
                    },
                    editor: {
                        collection: 'inventory.products',
                        view: 'inventory.catalog.products',
                        labelFrom: 'displayName',
                        filters() {
                            const filters = {
                                isSimple: true,
                                canBeSold: true
                            };

                            if (vm.model.module === 'service') {
                                filters.isServiceProduct = true;
                            }
                            if (vm.model.module === 'ecommerce') {
                                filters.isECommerceProduct = true;
                            }

                            if (_.isObject(vm.organizationSettings) && !_.isEmpty(vm.organizationSettings)) {
                                filters.groupIds = {
                                    $in: vm.organizationSettings.productGroupIds || []
                                };
                            }

                            return filters;
                        },
                        updateParams(params, type) {
                            if (type === 'create' || type === 'detail') {
                                params.model = {
                                    isSimple: true,
                                    canBeSold: true
                                };

                                if (vm.model.module === 'service') {
                                    params.model.isServiceProduct = true;
                                }
                                if (vm.model.module === 'ecommerce') {
                                    params.model.isECommerceProduct = true;
                                }

                                if (
                                    _.isObject(vm.organizationSettings) &&
                                    !_.isEmpty(vm.organizationSettings) &&
                                    vm.organizationSettings.defaultProductGroupId
                                ) {
                                    params.model.groupIds = [vm.organizationSettings.defaultProductGroupId];
                                }
                            }

                            return params;
                        }
                    }
                },
                productCode: {
                    type: 'string',
                    label: 'Product code',
                    column: {
                        populate: 'product',
                        pinned: 'left',
                        minWidth: 120
                    },
                    editor: {
                        collection: 'inventory.products',
                        view: 'inventory.catalog.products',
                        labelFrom: 'code',
                        valueFrom: 'code',
                        idFrom: 'productId',
                        extraFields: ['displayName'],
                        htmlTemplate(item) {
                            return item.displayName;
                        },
                        filters() {
                            const filters = {
                                isSimple: true,
                                canBeSold: true
                            };

                            if (vm.model.module === 'service') {
                                filters.isServiceProduct = true;
                            }
                            if (vm.model.module === 'ecommerce') {
                                filters.isECommerceProduct = true;
                            }

                            if (_.isObject(vm.organizationSettings) && !_.isEmpty(vm.organizationSettings)) {
                                filters.groupIds = {
                                    $in: vm.organizationSettings.productGroupIds || []
                                };
                            }

                            return filters;
                        },
                        updateParams(params, type) {
                            if (type === 'create' || type === 'detail') {
                                params.model = {
                                    isSimple: true,
                                    canBeSold: true
                                };

                                if (vm.model.module === 'service') {
                                    params.model.isServiceProduct = true;
                                }
                                if (vm.model.module === 'ecommerce') {
                                    params.model.isECommerceProduct = true;
                                }

                                if (
                                    _.isObject(vm.organizationSettings) &&
                                    !_.isEmpty(vm.organizationSettings) &&
                                    vm.organizationSettings.defaultProductGroupId
                                ) {
                                    params.model.groupIds = [vm.organizationSettings.defaultProductGroupId];
                                }
                            }

                            return params;
                        }
                    }
                },
                productDefinition: {
                    type: 'string',
                    label: 'Product definition',
                    column: {
                        populate: 'product',
                        // visible: false,
                        minWidth: 150
                    },
                    editor: {
                        collection: 'inventory.products',
                        view: 'inventory.catalog.products',
                        labelFrom: 'definition',
                        valueFrom: 'definition',
                        idFrom: 'productId',
                        extraFields: ['displayName'],
                        htmlTemplate(item) {
                            return item.displayName;
                        },
                        filters() {
                            const filters = {
                                isSimple: true,
                                canBeSold: true
                            };

                            if (vm.model.module === 'service') {
                                filters.isServiceProduct = true;
                            }
                            if (vm.model.module === 'ecommerce') {
                                filters.isECommerceProduct = true;
                            }

                            if (_.isObject(vm.organizationSettings) && !_.isEmpty(vm.organizationSettings)) {
                                filters.groupIds = {
                                    $in: vm.organizationSettings.productGroupIds || []
                                };
                            }

                            return filters;
                        },
                        updateParams(params, type) {
                            if (type === 'create' || type === 'detail') {
                                params.model = {
                                    isSimple: true,
                                    canBeSold: true
                                };

                                if (vm.model.module === 'service') {
                                    params.model.isServiceProduct = true;
                                }
                                if (vm.model.module === 'ecommerce') {
                                    params.model.isECommerceProduct = true;
                                }

                                if (
                                    _.isObject(vm.organizationSettings) &&
                                    !_.isEmpty(vm.organizationSettings) &&
                                    vm.organizationSettings.defaultProductGroupId
                                ) {
                                    params.model.groupIds = [vm.organizationSettings.defaultProductGroupId];
                                }
                            }

                            return params;
                        }
                    }
                },
                productCategoryId: {
                    type: 'string',
                    label: 'Product category',
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                productCategoryPath: {
                    type: 'string',
                    label: 'Product category path',
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                productType: {
                    type: 'string',
                    label: 'Product type',
                    required: false,
                    column: {
                        width: 150,
                        visible: false,
                        valueLabels: [
                            {value: 'stockable', label: 'Stockable product'},
                            {value: 'service', label: 'Service product'},
                            {value: 'kit', label: 'Kit product'}
                        ],
                        translateLabels: true
                    },
                    editor: {
                        disabled: true
                    }
                },
                barcode: {
                    type: 'string',
                    label: 'Barcode',
                    required: false,
                    column: {
                        width: 150,
                        visible: false
                    }
                },
                description: {
                    type: 'string',
                    label: 'Description',
                    required: false,
                    column: {
                        visible: false
                    }
                },
                scheduledDate: {
                    type: 'date',
                    label: 'Delivery Date',
                    default: 'date:now',
                    column: {
                        visible: false,
                        width: 120
                    }
                },
                customerCatalogNo: {
                    type: 'string',
                    label: 'Customer catalog no',
                    required: false,
                    column: {
                        visible: false,
                        width: 150
                    }
                },
                deliveryNote: {
                    type: 'string',
                    label: 'Delivery note',
                    required: false,
                    column: {
                        visible: false,
                        width: 210
                    }
                },
                branchId: {
                    type: 'string',
                    label: 'Branch Office',
                    required: false,
                    column: {
                        populate: 'branch',
                        width: 180,
                        visible: false,
                        hidden: !vm.$setting('system.multiBranch')
                    },
                    editor: {
                        collection: 'kernel.branches',
                        disabled: true
                    }
                },
                warehouseId: {
                    type: 'string',
                    label: 'Warehouse',
                    required: false,
                    column: {
                        populate: 'warehouse',
                        width: 180,
                        visible: false
                    },
                    editor: {
                        collection: 'inventory.warehouses',
                        view: 'inventory.configuration.warehouses',
                        filters() {
                            const filters = {branchId: vm.model.branchId};

                            if (
                                _.isObject(vm.organizationSettings) &&
                                !_.isEmpty(vm.organizationSettings) &&
                                Array.isArray(vm.organizationSettings.warehouseIds) &&
                                vm.organizationSettings.warehouseIds.length > 0
                            ) {
                                filters._id = {
                                    $in: vm.organizationSettings.warehouseIds || []
                                };
                            }

                            return filters;
                        },
                        extraFields: ['shortName'],
                        template: '{{shortName}} - {{name}}'
                    }
                },
                vendorId: {
                    type: 'string',
                    label: 'Vendor',
                    required: false,
                    column: {
                        populate: 'vendor',
                        width: 210,
                        visible: false
                    },
                    editor: {
                        collection: 'kernel.partners',
                        view: 'partners.partners',
                        filters(row) {
                            return {type: 'vendor'};
                        },
                        extraFields: ['code'],
                        template: '{{code}} - {{name}}'
                    }
                },
                quantity: {
                    type: 'decimal',
                    label: 'Quantity',
                    default: 1,
                    column: {
                        width: 75,
                        format: 'unit',
                        formatOptions(row) {
                            return {
                                number: {
                                    precision: vm.$setting('system.unitPrecision')
                                }
                            };
                        }
                    },
                    editor: {}
                },
                unitId: {
                    type: 'string',
                    label: 'Unit',
                    required: false,
                    column: {
                        populate: 'unit',
                        width: 60
                    },
                    editor: {
                        collection: 'kernel.units',
                        filters(data) {
                            let ids = [];

                            if (_.isObject(data.product)) {
                                if (_.isString(data.product.baseUnitId)) {
                                    ids.push(data.product.baseUnitId);
                                }

                                const unitConversions = data.product.unitConversions;
                                if (Array.isArray(unitConversions) && unitConversions.length > 0) {
                                    unitConversions.forEach(uc => {
                                        ids.push(uc.fromUnitId);
                                        ids.push(uc.toUnitId);
                                    });
                                }
                            }

                            return {_id: {$in: _.uniq(ids)}};
                        }
                    }
                },
                baseQuantity: {
                    type: 'decimal',
                    label: 'Base Quantity',
                    default: 1,
                    column: {
                        width: 75,
                        format: 'unit',
                        visible: false
                    },
                    editor: {
                        disabled: () => true
                    }
                },
                baseUnitId: {
                    type: 'string',
                    label: 'Base unit',
                    required: false,
                    column: {
                        populate: 'baseUnit',
                        width: 60,
                        visible: false
                    },
                    editor: {
                        collection: 'kernel.units',
                        disabled: true
                    }
                },
                priceListId: {
                    type: 'string',
                    label: 'Price list',
                    required: false,
                    column: {
                        populate: 'priceList',
                        width: 150,
                        visible: false,
                        hidden: !vm.$setting('sale.salesPriceList')
                    },
                    editor: {
                        collection: 'sale.price-lists',
                        view: 'sale.pricing.price-lists',
                        filters(row) {
                            return vm.priceListIdFilters;
                        },
                        disabled: () => {
                            return vm.status === 'payment-planned';
                        }
                    }
                },
                currencyId: {
                    type: 'string',
                    label: 'Currency',
                    required: false,
                    column: {
                        populate: 'currency',
                        width: 90,
                        visible: false
                    },
                    editor: {
                        collection: 'kernel.currencies',
                        disabled: () => {
                            return vm.status === 'payment-planned';
                        }
                    }
                },
                currencyRate: {
                    type: 'decimal',
                    label: 'Currency rate',
                    default: 1,
                    column: {
                        format: 'amount',
                        width: 95,
                        visible: false,
                        formatOptions(row) {
                            return {
                                number: {
                                    precision: vm.$setting('system.exchangeRatePrecision')
                                }
                            };
                        }
                    },
                    editor: {
                        precision: vm.$setting('system.exchangeRatePrecision')
                    }
                },
                unitPrice: {
                    type: 'decimal',
                    label: 'Unit Price',
                    default: 0,
                    column: {
                        format: 'unit-price',
                        formatOptions() {
                            return vm.currencyFormat;
                        },
                        width: 95
                    },
                    editor: {
                        disabled: () =>
                            !!vm.organizationSettings &&
                            !_.isEmpty(vm.organizationSettings) &&
                            !vm.organizationSettings.canUpdateUnitPrices
                    }
                },
                unitPriceFC: {
                    type: 'decimal',
                    label: 'Unit Price (FC)',
                    default: 0,
                    column: {
                        format: 'unit-price',
                        formatOptions(row) {
                            if (_.isObject(row) && row.currencyId) {
                                const currency = currenciesMap[row.currencyId];

                                if (!!currency) {
                                    let options = {currency: {}};

                                    options.currency.symbol = currency.symbol;
                                    options.currency.format = currency.symbolPosition === 'before' ? '%s %v' : '%v %s';

                                    return options;
                                }
                            }

                            return vm.currencyFormat;
                        },
                        visible: false,
                        width: 95
                    },
                    editor: {
                        disabled: () =>
                            !!vm.organizationSettings &&
                            !_.isEmpty(vm.organizationSettings) &&
                            !vm.organizationSettings.canUpdateUnitPrices
                    }
                },
                grossUnitPrice: {
                    type: 'decimal',
                    label: 'Gross Unit Price',
                    required: false,
                    column: {
                        format: 'unit-price',
                        formatOptions() {
                            return vm.currencyFormat;
                        },
                        visible: false,
                        width: 95
                    },
                    editor: {}
                },
                surplusGoodDescription: {
                    type: 'string',
                    label: 'Surplus good description',
                    column: {
                        width: 120,
                        cellRendererFramework: SurplusGoodCellRenderer,
                        hidden: !vm.$app.hasModule('health'),
                        visible: false
                    },
                    editor: {
                        disabled: () => true
                    },
                    required: false
                },
                hasSurplusGood: {
                    type: 'boolean',
                    label: 'Has surplus good',
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                selloutId: {
                    type: 'string',
                    label: 'Sellout',
                    required: false,
                    column: {
                        hidden: true,
                        visible: false
                    },
                    editor: {
                        disabled: () => true
                    }
                },
                selloutCode: {
                    type: 'string',
                    label: 'Sellout code',
                    required: false,
                    column: {
                        hidden: true,
                        visible: false
                    },
                    editor: {
                        disabled: () => true
                    }
                },
                selloutName: {
                    type: 'string',
                    label: 'Sellout name',
                    required: false,
                    column: {
                        hidden: true,
                        visible: false
                    },
                    editor: {
                        disabled: () => true
                    }
                },
                selloutAmount: {
                    type: 'decimal',
                    label: 'Sellout amount',
                    required: false,
                    column: {
                        hidden: true,
                        visible: false
                    },
                    editor: {
                        disabled: () => true
                    }
                },
                discount: {
                    type: 'decimal',
                    label: 'Discount %',
                    default: 0,
                    column: {
                        format: 'percentage',
                        width: 90,
                        formatOptions(row) {
                            return {
                                number: {
                                    precision: vm.$setting('system.percentagePrecision')
                                }
                            };
                        },
                        hidden: !vm.$setting('sale.lineDiscounts'),
                        cellRendererParams({data: row}) {
                            return {
                                handler: vm.handleApplyMultipleDiscounts,
                                disabled: vm.status !== 'draft',
                                hasDiscountPayload: !_.isEmpty(row.discountPayload)
                            };
                        },
                        cellRendererFramework: DiscountCellRenderer
                    },
                    editor: {
                        disabled: row => {
                            return !_.isEmpty(row.discountPayload);
                        }
                    }
                },
                discountPayload: {
                    type: 'object',
                    blackbox: true,
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                unitPriceAfterDiscount: {
                    type: 'decimal',
                    label: 'Unit Price After Discount',
                    default: 0,
                    column: {
                        format: 'unit-price',
                        formatOptions() {
                            return vm.currencyFormat;
                        },
                        visible: false,
                        width: 95
                    },
                    editor: {
                        disabled: () => true
                    }
                },
                grossUnitPriceAfterDiscount: {
                    type: 'decimal',
                    label: 'Gross Unit Price After Discount',
                    default: 0,
                    column: {
                        format: 'unit-price',
                        formatOptions() {
                            return vm.currencyFormat;
                        },
                        visible: false,
                        width: 95
                    },
                    editor: {
                        disabled: () => true
                    }
                },
                freight: {
                    type: 'decimal',
                    label: 'Freight',
                    default: 0,
                    column: {
                        format: 'unit-price',
                        formatOptions() {
                            return vm.currencyFormat;
                        },
                        hidden: !vm.$setting('system.freight'),
                        width: 95
                    },
                    editor: {
                        disabled: () => true
                    }
                },
                taxId: {
                    type: 'string',
                    label: 'Tax',
                    required: false,
                    column: {
                        populate: 'tax',
                        cellRendererParams({data: row}) {
                            return {
                                handler: vm.handleApplyMultipleTaxes,
                                disabled: vm.status !== 'draft',
                                hasTaxPayload: !_.isEmpty(row.taxPayload)
                            };
                        },
                        cellRendererFramework: TaxCellRenderer,
                        width: 100
                    },
                    editor: {
                        collection: 'kernel.taxes',
                        filters(data) {
                            return {scope: 'sale'};
                        },
                        disabled: row => {
                            return !_.isEmpty(row.taxPayload);
                        }
                    }
                },
                taxPayload: {
                    type: 'object',
                    blackbox: true,
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                taxDetail: {
                    type: 'object',
                    blackbox: true,
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                taxTotal: {
                    type: 'decimal',
                    label: 'Tax Total',
                    default: 0,
                    column: {
                        format: 'currency',
                        formatOptions() {
                            return vm.currencyFormat;
                        },
                        visible: false,
                        minWidth: 90
                    },
                    editor: {
                        disabled: () => true
                    }
                },
                grossTotal: {
                    type: 'decimal',
                    label: 'Gross Total',
                    default: 0,
                    column: {
                        format: 'currency',
                        formatOptions() {
                            return vm.currencyFormat;
                        },
                        visible: false,
                        width: 150
                    }
                },
                realTotal: {
                    type: 'decimal',
                    label: 'Real Total',
                    default: 0,
                    column: {
                        format: 'currency',
                        formatOptions() {
                            return vm.currencyFormat;
                        },
                        visible: false,
                        width: 150
                    },
                    editor: {
                        disabled: () => true
                    }
                },
                stockQuantity: {
                    type: 'decimal',
                    label: 'Stock on Hand',
                    default: 0,
                    column: {
                        width: 75,
                        format: 'unit',
                        visible: false
                    },
                    editor: {
                        disabled: () => true
                    }
                },
                orderedQuantity: {
                    type: 'decimal',
                    label: 'Ordered Quantity',
                    default: 0,
                    column: {
                        width: 75,
                        format: 'unit',
                        visible: false
                    },
                    editor: {
                        disabled: () => true
                    }
                },
                assignedQuantity: {
                    type: 'decimal',
                    label: 'Assigned Quantity',
                    default: 0,
                    column: {
                        width: 75,
                        format: 'unit',
                        visible: false
                    },
                    editor: {
                        disabled: () => true
                    }
                },
                availableQuantity: {
                    type: 'decimal',
                    label: 'Available Quantity',
                    default: 0,
                    column: {
                        width: 75,
                        format: 'unit',
                        visible: false
                    },
                    editor: {
                        disabled: () => true
                    }
                },
                warehouseStockQuantity: {
                    type: 'decimal',
                    label: 'Warehouse Stock on Hand',
                    default: 0,
                    column: {
                        width: 75,
                        format: 'unit',
                        visible: false
                    },
                    editor: {
                        disabled: () => true
                    }
                },
                warehouseOrderedQuantity: {
                    type: 'decimal',
                    label: 'Warehouse Ordered Quantity',
                    default: 0,
                    column: {
                        width: 75,
                        format: 'unit',
                        visible: false
                    },
                    editor: {
                        disabled: () => true
                    }
                },
                warehouseAssignedQuantity: {
                    type: 'decimal',
                    label: 'Warehouse Assigned Quantity',
                    default: 0,
                    column: {
                        width: 75,
                        format: 'unit',
                        visible: false
                    },
                    editor: {
                        disabled: () => true
                    }
                },
                warehouseAvailableQuantity: {
                    type: 'decimal',
                    label: 'Warehouse Available Quantity',
                    default: 0,
                    column: {
                        width: 75,
                        format: 'unit',
                        visible: false
                    },
                    editor: {
                        disabled: () => true
                    }
                },
                financialProjectId: {
                    type: 'string',
                    label: 'Project',
                    required: false,
                    column: {
                        populate: 'financialProject',
                        visible: false,
                        width: 150
                    },
                    editor: {
                        collection: 'kernel.financial-projects',
                        view: 'system.management.configuration.financial-projects',
                        disableCreate: true,
                        disableDetail: true,
                        extraFields: ['code'],
                        template: '{{code}} - {{name}}',
                        filters: () => ({
                            $and: [
                                {
                                    $or: [
                                        {validFrom: {$exists: false}},
                                        {validFrom: {$eq: null}},
                                        {validFrom: {$eq: ''}},
                                        {validFrom: {$lte: vm.model.orderDate}}
                                    ]
                                },
                                {
                                    $or: [
                                        {validTo: {$exists: false}},
                                        {validTo: {$eq: null}},
                                        {validTo: {$eq: ''}},
                                        {validTo: {$gte: vm.model.orderDate}}
                                    ]
                                }
                            ],
                            $sort: {code: 1}
                        })
                    }
                },
                total: {
                    type: 'decimal',
                    label: 'Total',
                    default: 0,
                    column: {
                        format: 'currency',
                        formatOptions() {
                            return vm.currencyFormat;
                        },
                        width: 150
                    },
                    editor: {
                        disabled: () =>
                            !!vm.organizationSettings &&
                            !_.isEmpty(vm.organizationSettings) &&
                            !vm.organizationSettings.canUpdateUnitPrices
                    }
                },
                totalFC: {
                    type: 'decimal',
                    label: 'Total (FC)',
                    default: 0,
                    column: {
                        format: 'currency',
                        formatOptions(row) {
                            if (_.isObject(row) && row.currencyId) {
                                const currency = currenciesMap[row.currencyId];

                                if (!!currency) {
                                    let options = {currency: {}};

                                    options.currency.symbol = currency.symbol;
                                    options.currency.format = currency.symbolPosition === 'before' ? '%s %v' : '%v %s';

                                    return options;
                                }
                            }

                            return vm.currencyFormat;
                        },
                        visible: false,
                        width: 150
                    },
                    editor: {
                        disabled: true
                    }
                },

                // Internal
                partnerId: {
                    type: 'string',
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                invoiceResponsibleId: {
                    type: 'string',
                    label: 'Invoice responsible',
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                invoiceAddressId: {
                    type: 'string',
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                invoiceAddress: {
                    type: 'object',
                    label: 'Invoice address',
                    blackbox: true,
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                deliveryReceiverId: {
                    type: 'string',
                    label: 'Delivery receiver',
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                deliveryAddressId: {
                    type: 'string',
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                deliveryAddressCode: {
                    type: 'string',
                    label: 'Delivery address code',
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                deliveryAddress: {
                    type: 'object',
                    label: 'Delivery address',
                    blackbox: true,
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                contractApplied: {
                    type: 'boolean',
                    default: false,
                    column: {
                        hidden: true
                    }
                },
                discountListApplied: {
                    type: 'boolean',
                    default: false,
                    column: {
                        hidden: true
                    }
                },
                brandId: {
                    type: 'string',
                    label: 'Brand',
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                brandName: {
                    type: 'string',
                    label: 'Brand name',
                    required: false,
                    column: {
                        width: 150,
                        visible: false
                    }
                },
                shippingUnitId: {
                    type: 'string',
                    label: 'Shipping unit',
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                typeOfGoodsId: {
                    type: 'string',
                    label: 'Type of goods',
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                hsCode: {
                    type: 'string',
                    label: 'HS code',
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                manufacturerId: {
                    type: 'string',
                    label: 'Manufacturer',
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                manufacturerProductCode: {
                    type: 'string',
                    label: 'Manufacturer product code',
                    required: false,
                    column: {
                        width: 150,
                        visible: false
                    }
                },
                countryOfManufactureId: {
                    type: 'string',
                    label: 'Country of manufacture',
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                classificationCode: {
                    type: 'string',
                    label: 'Classification code',
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                classificationVersion: {
                    type: 'string',
                    label: 'Classification version',
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                classificationValue: {
                    type: 'string',
                    label: 'Classification value',
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                countryOfOriginId: {
                    type: 'string',
                    label: 'Country of origin',
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                containerTypeId: {
                    type: 'string',
                    label: 'Container type',
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                containerNo: {
                    type: 'string',
                    label: 'Container no',
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                containerBrand: {
                    type: 'string',
                    label: 'Container brand',
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                containerQuantity: {
                    type: 'decimal',
                    label: 'Container quantity',
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                netWeight: {
                    type: 'decimal',
                    label: 'Net weight',
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                netWeightUnitId: {
                    type: 'string',
                    label: 'Net weight unit',
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                grossWeight: {
                    type: 'decimal',
                    label: 'Gross weight',
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                grossWeightUnitId: {
                    type: 'string',
                    label: 'Gross weight unit',
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                netVolume: {
                    type: 'decimal',
                    label: 'Net volume',
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                netVolumeUnitId: {
                    type: 'string',
                    label: 'Net volume unit',
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                grossVolume: {
                    type: 'decimal',
                    label: 'Gross volume',
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                grossVolumeUnitId: {
                    type: 'string',
                    label: 'Gross volume unit',
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                height: {
                    type: 'decimal',
                    label: 'Height',
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                heightUnitId: {
                    type: 'string',
                    label: 'Height unit',
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                width: {
                    type: 'decimal',
                    label: 'Width',
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                widthUnitId: {
                    type: 'string',
                    label: 'Width unit',
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                depth: {
                    type: 'decimal',
                    label: 'Depth',
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                depthUnitId: {
                    type: 'string',
                    label: 'Depth unit',
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                paymentResponsibleId: {
                    type: 'string',
                    label: 'Payment responsible',
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                paymentAddressId: {
                    type: 'string',
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                paymentAddress: {
                    type: 'object',
                    label: 'Payment address',
                    blackbox: true,
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                pcmModelId: {
                    type: 'string',
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                pcmConfigurationId: {
                    type: 'string',
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                pcmHash: {
                    type: 'string',
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                costSource: {
                    type: 'string',
                    label: 'Cost source',
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                unitCost: {
                    type: 'decimal',
                    label: 'Unit cost',
                    default: 0,
                    column: {
                        format: 'currency',
                        formatOptions() {
                            return vm.currencyFormat;
                        },
                        width: 150,
                        visible: false,
                        hidden: true
                    },
                    editor: {
                        disabled: true
                    }
                },
                unitProfit: {
                    type: 'decimal',
                    label: 'Unit profit',
                    default: 0,
                    column: {
                        format: 'currency',
                        formatOptions() {
                            return vm.currencyFormat;
                        },
                        width: 150,
                        visible: false,
                        hidden: true
                    },
                    editor: {
                        disabled: true
                    }
                },
                cost: {
                    type: 'decimal',
                    label: 'Cost',
                    default: 0,
                    column: {
                        format: 'currency',
                        formatOptions() {
                            return vm.currencyFormat;
                        },
                        width: 150,
                        visible: false,
                        hidden: true
                    },
                    editor: {
                        disabled: true
                    }
                },
                profitRate: {
                    type: 'decimal',
                    label: 'Profit rate %',
                    default: 0,
                    column: {
                        format: 'percentage',
                        width: 120,
                        hidden: true
                    },
                    editor: {
                        disabled: true
                    }
                },
                profitMargin: {
                    type: 'decimal',
                    label: 'Profit margin %',
                    default: 0,
                    column: {
                        format: 'percentage',
                        width: 120,
                        hidden: true
                    },
                    editor: {
                        disabled: true
                    }
                },
                profit: {
                    type: 'decimal',
                    label: 'Profit',
                    default: 0,
                    column: {
                        format: 'currency',
                        formatOptions() {
                            return vm.currencyFormat;
                        },
                        width: 150,
                        hidden: true
                    },
                    editor: {
                        disabled: true
                    }
                },
                subItems: {
                    type: [
                        {
                            productId: {
                                type: 'string',
                                label: 'Product ID'
                            },
                            productCode: {
                                type: 'string',
                                label: 'Product code'
                            },
                            productDefinition: {
                                type: 'string',
                                label: 'Product definition'
                            },
                            quantity: {
                                type: 'decimal',
                                label: 'Quantity',
                                default: 0
                            },
                            unitPrice: {
                                type: 'decimal',
                                label: 'Unit price',
                                default: 0
                            },
                            unitId: {
                                type: 'string',
                                label: 'Unit ID'
                            }
                        }
                    ],
                    default: [],
                    column: {
                        hidden: true
                    }
                },
                copyableItemId: {
                    type: 'string',
                    required: false,
                    column: {
                        hidden: true
                    }
                }
            }
        ],

        // Organization
        organizationId: {
            type: 'string',
            label: 'Organization',
            required: false
        },
        salesManagerId: {
            type: 'string',
            label: 'Sales manager',
            required: false
        },
        salespersonId: {
            type: 'string',
            label: 'Salesperson',
            required: false
        },

        // Exchange rates.
        exchangeRates: {
            type: [
                {
                    currencyName: {
                        type: 'string',
                        label: 'Currency',
                        required: false,
                        column: {
                            width: 150
                        },
                        editor: {
                            disabled: true
                        }
                    },
                    rate: {
                        type: 'decimal',
                        label: 'Rate',
                        default: 0,
                        column: {
                            format: 'amount',
                            formatOptions() {
                                return {
                                    number: {
                                        precision: vm.$setting('system.exchangeRatePrecision')
                                    }
                                };
                            }
                        },
                        editor: {}
                    },
                    grandTotal: {
                        type: 'decimal',
                        label: 'Grand total',
                        default: 0,
                        column: {
                            format: 'amount',
                            valueGetter: params => {
                                const {data} = params;

                                if (!!data && _.isNumber(data.rate) && data.rate > 0) {
                                    return (vm.model.grandTotal * vm.model.currencyRate) / data.rate;
                                }

                                return 0;
                            }
                        },
                        editor: {
                            disabled: () => true
                        }
                    }
                }
            ],
            default: []
        },
        exchangeRatesMap: {
            type: 'object',
            blackbox: true,
            required: false
        },

        // Details.
        sourceId: {
            type: 'string',
            label: 'Source',
            required: false
        },
        communicationChannelId: {
            type: 'string',
            label: 'Communication channel',
            required: false
        },
        referencePartnerId: {
            type: 'string',
            label: 'Reference partner',
            required: false
        },
        partnerOrderReference: {
            type: 'string',
            label: 'Partner order reference',
            required: false
        },
        partnerOrderDate: {
            type: 'date',
            label: 'Partner order date',
            required: false
        },

        // Logistic
        warehouseId: {
            type: 'string',
            label: 'Warehouse'
        },
        invoiceResponsibleId: {
            type: 'string',
            label: 'Invoice responsible',
            required: false
        },
        invoiceAddressId: {
            type: 'string',
            required: false
        },
        invoiceAddress: {
            type: 'object',
            label: 'Invoice address',
            blackbox: true,
            required: false
        },
        deliveryReceiverId: {
            type: 'string',
            label: 'Delivery receiver',
            required: false
        },
        deliveryAddressId: {
            type: 'string',
            required: false
        },
        deliveryAddressCode: {
            type: 'string',
            label: 'Delivery address code',
            required: false
        },
        deliveryAddress: {
            type: 'object',
            label: 'Delivery address',
            blackbox: true,
            required: false
        },
        deliveryPriority: {
            type: 'string',
            label: 'Delivery priority',
            allowed: ['not-urgent', 'normal', 'urgent', 'very-urgent'],
            default: 'normal'
        },
        deliveryPolicy: {
            type: 'string',
            label: 'Delivery policy',
            allowed: ['when-one-ready', 'when-all-ready'],
            default: 'when-one-ready'
        },
        deliveryConditionId: {
            type: 'string',
            label: 'Delivery condition',
            required: false
        },
        deliveryMethodId: {
            type: 'string',
            label: 'Delivery method',
            required: false
        },
        deliveryNote: {
            type: 'string',
            label: 'Delivery note',
            required: false
        },
        imoAndMmsiNo: {
            type: 'string',
            label: 'IMO and MMSI no',
            required: false
        },
        shipName: {
            type: 'string',
            label: 'Ship name',
            required: false
        },
        shipRadioCallName: {
            type: 'string',
            label: 'Ship radio call name',
            required: false
        },
        shipRegistrationName: {
            type: 'string',
            label: 'Ship registration name',
            required: false
        },
        shipNetWeight: {
            type: 'decimal',
            label: 'Ship net weight',
            required: false
        },
        shipGrossWeight: {
            type: 'decimal',
            label: 'Ship gross weight',
            required: false
        },
        shipRequirements: {
            type: 'string',
            label: 'Ship requirements',
            required: false
        },
        shipPortOfRegistration: {
            type: 'string',
            label: 'Ship port of registration',
            required: false
        },
        trainNo: {
            type: 'string',
            label: 'Train no',
            required: false
        },
        trainWagonNo: {
            type: 'string',
            label: 'Train wagon no',
            required: false
        },
        licensePlateNo: {
            type: 'string',
            label: 'License plate no',
            required: false
        },
        aircraftNo: {
            type: 'string',
            label: 'Aircraft no',
            required: false
        },
        carrierId: {
            type: 'string',
            label: 'Carrier',
            required: false
        },
        cargoTrackingCode: {
            type: 'string',
            label: 'Cargo tracking code',
            required: false
        },
        shippingPaymentType: {
            type: 'string',
            label: 'Shipment payment type',
            required: false
        },

        // Financial.
        financialProjectId: {
            type: 'string',
            label: 'Project',
            required: false,
            index: true
        },
        paymentTermId: {
            type: 'string',
            label: 'Payment term'
        },
        priceListId: {
            type: 'string',
            label: 'Price list',
            required: false
        },
        customerPriceListId: {
            type: 'string',
            label: 'Customer price list',
            required: false
        },
        guaranteeId: {
            type: 'string',
            label: 'Guarantee',
            required: false
        },
        paymentTerm: {
            type: 'object',
            blackbox: true,
            required: false
        },
        paymentPlan: {
            type: 'object',
            blackbox: true,
            required: false
        },
        paymentPlanningDate: {
            type: 'date',
            label: 'Payment planning date',
            required: false
        },
        guarantorIds: {
            type: ['string'],
            label: 'Guarantors',
            default: []
        },
        paymentResponsibleId: {
            type: 'string',
            label: 'Payment responsible',
            required: false
        },
        paymentAddressId: {
            type: 'string',
            required: false
        },
        paymentAddress: {
            type: 'object',
            label: 'Payment address',
            blackbox: true,
            required: false
        },

        content: {
            type: 'string',
            label: 'Content',
            required: false
        },

        // Attachments.
        attachments: {
            type: ['string'],
            default: []
        },

        // Internal.
        paymentPlanBackup: {
            type: 'object',
            blackbox: true,
            required: false
        },
        partialDeliveries: {
            type: [
                {
                    type: 'object',
                    blackbox: true,
                    required: false
                }
            ],
            default: []
        },
        limitParams: {
            type: 'object',
            blackbox: true,
            required: false
        },
        contractParams: {
            type: 'object',
            blackbox: true,
            required: false
        },
        campaigns: {
            type: [
                {
                    type: 'object',
                    blackbox: true,
                    required: false
                }
            ],
            default: []
        },
        freight: {
            type: 'object',
            blackbox: true,
            required: false
        },
        // Additional information
        additionalInformation: {
            type: 'object',
            blackbox: true,
            required: false
        },
        additionalInformationId: {
            type: 'string',
            label: 'Additional information',
            required: false
        },

        // Stages
        stages: {
            type: [
                {
                    type: 'object',
                    blackbox: true,
                    required: false
                }
            ],
            required: false
        },
        stageHistory: {
            type: [
                {
                    type: 'object',
                    blackbox: true,
                    required: false
                }
            ],
            required: false
        }
    };
}
