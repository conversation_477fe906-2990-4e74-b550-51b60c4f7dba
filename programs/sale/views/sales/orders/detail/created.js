import _ from 'lodash';

export default {
    async created() {
        this.$params('loading', true);

        const company = this.$store.getters['session/company'];
        this.systemCurrencyId = company.currencyId;

        // Default currency format.
        this.currencyFormat = {
            currency: {
                symbol: company.currency.symbol,
                format: company.currency.symbolPosition === 'before' ? '%s %v' : '%v %s'
            }
        };

        this.moduleName = this.$params('model.module');

        // Order stages.
        this.stages = await this.$collection('sale.order-stages').find();

        // Get currencies.
        this.currencies = await this.$collection('kernel.currencies').find({});

        // Organization.
        if (this.$setting('sale.salesOrganizations')) {
            let scope = 'sale';

            if (this.$params('model.module') === 'service') {
                scope = 'service';
            }

            this.organizations = await this.$collection('kernel.organizations').find({scope});
        }

        const additionalInformationCount = await this.$collection('kernel.additional-information').count({
            type: 'sale-order',
            'fields.0': {$exists: true}
        });
        if (additionalInformationCount > 0) {
            this.showAdditionalInformationTab = true;
        }

        this.$nextTick(() => {
            this.initialized = true;

            this.$nextTick(() => {
                this.$params('loading', false);
            });
        });
    }
};
