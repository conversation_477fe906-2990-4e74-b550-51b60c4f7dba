<template>
    <ui-view
        type="table"
        collection="sale.orders"
        :columns="columns"
        :filters="filters"
        :extra-fields="[
            'currencyId',
            'deliveryStatus',
            'deliveries',
            'shipments',
            'additionalInformation',
            'deliveryAddressId'
        ]"
        :remove-checks="removeChecks"
        actions="create,remove"
        v-if="initialized"
    >
        <template slot="top-panel">
            <ui-scope id="sale.sales.orders" :filters="scopeApplicableFilters" @changed="handleScopeChange" />
        </template>
    </ui-view>
</template>

<script>
import _ from 'lodash';
import DeliveryStatusCellRenderer from './master/_delivery-status-cell-renderer';
import ShipmentStatusCellRenderer from './master/_shipment-status-cell-renderer';
import {
    getAdditionalInformationColumns,
    getAdditionalInformationScopeFilters
} from '../../../../system/helpers/additional-information';

export default {
    data: () => ({
        currencies: [],
        scopeQuery: {},
        additionalInformationColumns: [],
        additionalInformationScopeFilters: [],
        stages: [],
        initialized: false
    }),

    computed: {
        filters() {
            let filters = _.cloneDeep(this.scopeQuery);

            return filters;
        },
        scopeApplicableFilters() {
            const self = this;
            const forECommerce = this.$params('module') === 'ecommerce';
            const additionalInformationScopeFilters = this.additionalInformationScopeFilters || [];

            return [
                {code: 'today', label: 'Today', query: 'orderDate|today'},
                {
                    code: 'yesterday',
                    label: 'Yesterday',
                    query: 'orderDate|yesterday'
                },
                {
                    code: 'thisWeek',
                    label: 'This week',
                    query: 'orderDate|thisWeek'
                },
                {
                    code: 'lastWeek',
                    label: 'Last week',
                    query: 'orderDate|lastWeek'
                },
                {
                    code: 'thisMonth',
                    label: 'This month',
                    query: 'orderDate|thisMonth'
                },
                {
                    code: 'lastMonth',
                    label: 'Last month',
                    query: 'orderDate|lastMonth'
                },
                {
                    code: 'thisQuarter',
                    label: 'This quarter',
                    query: 'orderDate|thisQuarter'
                },
                {
                    code: 'lastQuarter',
                    label: 'Last quarter',
                    query: 'orderDate|lastQuarter'
                },

                {field: 'code', label: 'Code'},
                {
                    field: 'orderDate',
                    code: 'orderDate',
                    label: 'Order date',
                    type: 'date'
                },
                {
                    field: 'partnerId',
                    label: 'Customer',
                    collection: 'kernel.partners',
                    extraFields: ['code'],
                    filters: {type: 'customer', $sort: {name: 1}},
                    template: '{{code}} - {{name}}'
                },
                {
                    field: 'stageId',
                    label: 'Stage',
                    collection: 'sale.order-stages',
                    extraFields: ['code'],
                    template: '{{code}} - {{name}}',
                    condition: () => this.stages.length > 0
                },
                ...(forECommerce
                    ? [
                          {
                              field: 'storeId',
                              label: 'Store',
                              collection: 'ecommerce.stores',
                              extraFields: ['code'],
                              filters: {$sort: {name: 1}},
                              template: '{{code}} - {{name}}'
                          },
                          {
                              field: 'carrierId',
                              label: 'Carrier',
                              collection: 'logistics.carriers',
                              extraFields: ['code'],
                              filters: {$sort: {name: 1}},
                              template: '{{code}} - {{name}}'
                          },
                          {
                              field: 'shipmentStatus',
                              label: 'Shipment status',
                              translateLabels: true,
                              valueLabels: [
                                  {value: 'none', label: 'None'},
                                  {value: 'order-created', label: 'Order created'},
                                  {value: 'shipment-prepared', label: 'Delivered to cargo'},
                                  {value: 'in-transfer-phase', label: 'In transfer phase'},
                                  {value: 'delivered', label: 'Delivered'},
                                  {value: 'failed-to-deliver', label: 'Failed to deliver'}
                              ]
                          }
                      ]
                    : []),
                {
                    field: 'scheduledDate',
                    label: 'Delivery date',
                    type: 'date'
                },
                {
                    field: 'branchId',
                    label: 'Branch office',
                    collection: 'kernel.branches',
                    filters: {
                        _id: {$in: this.$user.branchIds},
                        $sort: {name: 1}
                    },
                    condition() {
                        return self.$setting('system.multiBranch');
                    }
                },
                {
                    field: 'financialProjectId',
                    label: 'Project',
                    collection: 'kernel.financial-projects',
                    extraFields: ['code'],
                    filters: {$sort: {name: 1}},
                    template: '{{code}} - {{name}}'
                },
                {field: 'reference', label: 'Reference'},
                {
                    field: 'documentTypeId',
                    label: 'Document type',
                    collection: 'sale.document-types',
                    filters: {
                        $sort: {name: 1}
                    }
                },
                {field: 'subTotalAfterDiscount', label: 'Subtotal', type: 'integer'},
                {field: 'taxTotal', label: 'Tax total', type: 'integer'},
                {field: 'grandTotal', label: 'Total', type: 'integer'},
                {
                    field: 'communicationChannelId',
                    label: 'Communication channel',
                    collection: 'kernel.communication-channels'
                },
                {
                    field: 'sourceId',
                    label: 'Source',
                    collection: 'sale.sources'
                },
                {
                    field: 'organizationId',
                    label: 'Organization',
                    collection: 'kernel.organizations',
                    extraFields: ['code'],
                    filters: {scope: 'sale', $sort: {code: 1}},
                    template: '{{code}} - {{name}}'
                },
                {
                    field: 'salesManagerId',
                    label: 'Sales manager',
                    collection: 'kernel.partners',
                    extraFields: ['code'],
                    filters: {type: 'employee', $sort: {name: 1}},
                    template: '{{code}} - {{name}}'
                },
                {
                    field: 'salespersonId',
                    label: 'Salesperson',
                    collection: 'kernel.partners',
                    extraFields: ['code'],
                    filters: {type: 'employee', $sort: {name: 1}},
                    template: '{{code}} - {{name}}'
                },
                {
                    field: 'referencePartnerId',
                    label: 'Reference partner',
                    collection: 'kernel.partners',
                    extraFields: ['code'],
                    filters: {$sort: {name: 1}},
                    template: '{{code}} - {{name}}'
                },
                {
                    field: 'partnerOrderReference',
                    label: 'Partner order reference'
                },
                {
                    field: 'partnerOrderDate',
                    label: 'Partner order date',
                    type: 'date'
                },
                {
                    field: 'deliveryStatus',
                    label: 'Delivery status',
                    translateLabels: true,
                    valueLabels: [
                        {value: 'none', label: 'None'},
                        {value: 'waiting', label: 'Waiting'},
                        {value: 'partially-delivered', label: 'Partially delivered'},
                        {value: 'delivered', label: 'Delivered'}
                    ]
                },
                {
                    field: 'paymentStatus',
                    label: 'Payment status',
                    translateLabels: true,
                    items: [
                        {value: 'paid', label: 'Paid'},
                        {value: 'partially-paid', label: 'Partially paid'},
                        {value: 'not-paid', label: 'Not paid'}
                    ]
                },
                {
                    field: 'workflowApprovalStatus',
                    label: 'EnterFlow',
                    items: [
                        {value: 'approved', label: 'Approved'},
                        {value: 'rejected', label: 'Rejected'},
                        {value: 'waiting-for-approval', label: 'Waiting'}
                    ],
                    condition() {
                        return !!self.$app.hasModule('workflow');
                    }
                },
                {
                    field: 'content',
                    label: 'Note',
                    type: 'text'
                },
                {
                    field: 'status',
                    label: 'Status',
                    translateLabels: true,
                    items: [
                        {value: 'draft', label: 'Draft'},
                        {value: 'payment-planned', label: 'Payment Planned'},
                        {value: 'approved', label: 'Approved'},
                        {value: 'to-invoice', label: 'To Invoice'},
                        {value: 'invoiced', label: 'Invoiced'},
                        {value: 'canceled', label: 'Canceled'}
                    ]
                },
                ...additionalInformationScopeFilters
            ];
        },
        columns() {
            const self = this;
            const forECommerce = this.$params('module') === 'ecommerce';
            const additionalInformationColumns = this.additionalInformationColumns || [];

            return [
                {
                    field: 'code',
                    label: 'Code',
                    width: 120
                },
                {
                    field: 'orderDate',
                    label: 'Order Date',
                    format: forECommerce ? 'datetime' : 'date',
                    sort: 'desc',
                    width: forECommerce ? 155 : 120
                },
                {
                    field: 'partner',
                    label: 'Customer',
                    subSelect: ['code', 'name'],
                    width: 240,
                    relationParams(params) {
                        const data = params.data;
                        const relation = {};

                        relation.isVisible =
                            _.isObject(data.partner) && _.isString(data.partner.code) && _.isString(data.partner.name);

                        if (relation.isVisible) {
                            relation.view = 'partners.partners-detail';
                            relation.id = data.partnerId;
                            relation.template = '{{code}} - {{name}}';
                        }

                        return relation;
                    }
                },
                ...(forECommerce
                    ? [
                          {
                              field: 'store',
                              label: 'Store',
                              subSelect: ['code', 'name'],
                              minWidth: 150,
                              relationParams(params) {
                                  const data = params.data;
                                  const relation = {};

                                  relation.isVisible =
                                      _.isObject(data.store) &&
                                      _.isString(data.store.code) &&
                                      _.isString(data.store.name);

                                  if (relation.isVisible) {
                                      relation.view = 'ecommerce.sales.stores-detail';
                                      relation.id = data.storeId;
                                      relation.template = '{{name}}';
                                  }

                                  return relation;
                              }
                          },
                          {
                              field: 'carrier',
                              label: 'Carrier',
                              subSelect: ['code', 'name'],
                              minWidth: 150,
                              relationParams(params) {
                                  const data = params.data;
                                  const relation = {};

                                  relation.isVisible =
                                      _.isObject(data.carrier) &&
                                      _.isString(data.carrier.code) &&
                                      _.isString(data.carrier.name);

                                  if (relation.isVisible) {
                                      relation.view = 'logistics.configuration.carriers';
                                      relation.id = data.carrierId;
                                      relation.template = '{{name}}';
                                  }

                                  return relation;
                              }
                          }
                      ]
                    : []),
                {
                    field: 'scheduledDate',
                    label: 'Delivery Date',
                    format: 'date',
                    width: 120
                },
                {
                    field: 'branch.name',
                    label: 'Branch Office',
                    hidden: !this.$setting('system.multiBranch'),
                    width: 180,
                    visible: false
                },
                {
                    field: 'documentType.name',
                    label: 'Document type',
                    width: 150,
                    visible: false
                },
                {
                    field: 'financialProject.code',
                    label: 'Project code',
                    visible: false,
                    width: 90
                },
                {
                    field: 'financialProject.name',
                    label: 'Project name',
                    visible: false,
                    width: 150
                },
                {
                    field: 'reference',
                    label: 'Reference',
                    visible: false,
                    width: 150
                },
                {
                    field: 'partnerOrderReference',
                    label: 'Partner order reference',
                    visible: false,
                    width: 150
                },
                {
                    field: 'partnerOrderDate',
                    label: 'Partner order date',
                    format: 'date',
                    visible: false,
                    width: 120
                },
                {
                    field: 'subTotalAfterDiscount',
                    label: 'Subtotal',
                    format: 'currency',
                    formatOptions(row) {
                        if (_.isObject(row) && row.currencyId) {
                            const currency = self.currencies.find(c => c._id === row.currencyId);
                            let options = {currency: {}};

                            options.currency.symbol = currency.symbol;
                            options.currency.format = currency.symbolPosition === 'before' ? '%s %v' : '%v %s';

                            return options;
                        }

                        return {};
                    },
                    visible: false,
                    width: 120
                },
                {
                    field: 'taxTotal',
                    label: 'Tax Total',
                    format: 'currency',
                    formatOptions(row) {
                        if (_.isObject(row) && row.currencyId) {
                            const currency = self.currencies.find(c => c._id === row.currencyId);
                            let options = {currency: {}};

                            options.currency.symbol = currency.symbol;
                            options.currency.format = currency.symbolPosition === 'before' ? '%s %v' : '%v %s';

                            return options;
                        }

                        return {};
                    },
                    visible: false,
                    width: 120
                },
                {
                    field: 'grandTotal',
                    label: 'Total',
                    format: 'currency',
                    formatOptions(row) {
                        if (_.isObject(row) && row.currencyId) {
                            const currency = self.currencies.find(c => c._id === row.currencyId);
                            let options = {currency: {}};

                            options.currency.symbol = currency.symbol;
                            options.currency.format = currency.symbolPosition === 'before' ? '%s %v' : '%v %s';

                            return options;
                        }

                        return {};
                    },
                    width: 120
                },
                {field: 'communicationChannel.name', label: 'Communication channel', visible: false},
                {field: 'source.name', label: 'Source', visible: false},
                {
                    field: 'organization',
                    label: 'Organization',
                    subSelect: ['code', 'name'],
                    render(params) {
                        const data = params.data;

                        if (_.isObject(data) && _.isObject(data.organization)) {
                            return `${data.organization.code} - ${data.organization.name}`;
                        }

                        if (params.value) {
                            return params.value;
                        }

                        return '';
                    },
                    visible: false
                },
                {
                    field: 'salesManager',
                    label: 'Sales manager',
                    subSelect: ['code', 'name'],
                    relationParams(params) {
                        const data = params.data;
                        const relation = {};

                        relation.isVisible =
                            _.isObject(data.salesManager) &&
                            _.isString(data.salesManager.code) &&
                            _.isString(data.salesManager.name);

                        if (relation.isVisible) {
                            relation.view = 'partners.partners-detail';
                            relation.id = data.salesManagerId;
                            relation.template = '{{code}} - {{name}}';
                        }

                        return relation;
                    },
                    visible: false,
                    width: 180
                },
                {
                    field: 'salesperson',
                    label: 'Salesperson',
                    subSelect: ['code', 'name'],
                    relationParams(params) {
                        const data = params.data;
                        const relation = {};

                        relation.isVisible =
                            _.isObject(data.salesperson) &&
                            _.isString(data.salesperson.code) &&
                            _.isString(data.salesperson.name);

                        if (relation.isVisible) {
                            relation.view = 'partners.partners-detail';
                            relation.id = data.salespersonId;
                            relation.template = '{{code}} - {{name}}';
                        }

                        return relation;
                    },
                    visible: false,
                    width: 180
                },
                {
                    field: 'referencePartner',
                    label: 'Reference partner',
                    subSelect: ['code', 'name'],
                    relationParams(params) {
                        const data = params.data;
                        const relation = {};

                        relation.isVisible =
                            _.isObject(data.referencePartner) &&
                            _.isString(data.referencePartner.code) &&
                            _.isString(data.referencePartner.name);

                        if (relation.isVisible) {
                            relation.view = 'partners.partners-detail';
                            relation.id = data.referencePartnerId;
                            relation.template = '{{code}} - {{name}}';
                        }

                        return relation;
                    },
                    visible: false,
                    width: 180
                },
                {
                    field: 'deliveryAddress.name',
                    label: 'Delivery address',
                    view: 'partners.contacts',
                    visible: false,
                    width: 150,
                    relationParams(params) {
                        const data = params.data;

                        return {id: data.deliveryAddressId};
                    }
                },
                {
                    field: 'deliveryAddress.city',
                    label: 'Delivery City',
                    width: 120,
                    visible: false
                },
                {
                    field: 'deliveryAddress.district',
                    label: 'Delivery District',
                    width: 150,
                    visible: false
                },
                {
                    field: 'deliveryStatus',
                    label: 'Delivery status',
                    cellRendererFramework: DeliveryStatusCellRenderer,
                    width: 150
                },
                {
                    field: 'stage',
                    label: 'Stage',
                    subSelect: ['name', 'color'],
                    width: 150,
                    render: params => {
                        const data = params.data;

                        if (typeof data === 'object' && data !== null && !!data.stage) {
                            return `
                            <div style="display: flex; flex-flow: row nowrap; align-items: center;">
                                <div style="width: 10px; height: 10px; border-radius: 5px; background-color: ${data.stage.color};"></div>
                                <div style="margin-left: 5px;">
                                    ${data.stage.name}
                                </div>
                            </div>
                            `.trim();
                        }

                        return '';
                    },
                    hidden: this.stages.length < 1
                },
                {
                    field: 'paymentStatus',
                    label: 'Payment status',
                    render: ({data}) => {
                        if (!!data) {
                            if (data.paymentStatus === 'paid') {
                                return `<span class="text-success"><i class="fas fa-coins"></i> ${this.$t(
                                    'Paid'
                                )}</span>`;
                            } else if (data.paymentStatus === 'partially-paid') {
                                return `<span class="text-warning"><i class="fas fa-coins"></i> ${this.$t(
                                    'Partially paid'
                                )}</span>`;
                            } else {
                                return `<span class="text-danger"><i class="fas fa-coins"></i> ${this.$t(
                                    'Not paid'
                                )}</span>`;
                            }
                        }

                        return '';
                    },
                    width: 120
                },
                {
                    field: 'content',
                    label: 'Note',
                    type: 'text',
                    width: 150,
                    valueFormatter: params => {
                        if (!params.value) return '';
                        const div = document.createElement('div');
                        div.innerHTML = params.value;
                        return div.textContent || div.innerText || '';
                    }
                },
                ...(forECommerce
                    ? [
                          {
                              field: 'shipmentStatus',
                              label: 'Shipment status',
                              cellRendererFramework: ShipmentStatusCellRenderer,
                              width: 150
                          }
                      ]
                    : []),
                {
                    field: 'workflowApprovalStatus',
                    label: 'EnterFlow',
                    workflowApprovalStatusCell: true,
                    documentCollection: 'sale.orders',
                    width: 120,
                    hidden: !this.$app.hasModule('workflow')
                },
                {
                    field: 'status',
                    label: 'Status',
                    tagsCell: true,
                    translateLabels: true,
                    tagLabels: [
                        {value: 'draft', label: 'Draft', color: 'default'},
                        {value: 'payment-planned', label: 'Payment Planned', color: 'success'},
                        {value: 'approved', label: 'Approved', color: 'primary'},
                        {value: 'to-invoice', label: 'To Invoice', color: 'purple'},
                        {value: 'invoiced', label: 'Invoiced', color: 'teal'},
                        {value: 'canceled', label: 'Canceled', color: 'danger'}
                    ],
                    width: 150
                },
                ...additionalInformationColumns
            ];
        },
        removeChecks() {
            return [
                {filter: {status: 'to-invoice'}, message: 'You cannot delete approved records!'},
                {filter: {status: 'invoiced'}, message: 'You cannot delete approved records!'}
            ];
        }
    },

    methods: {
        handleScopeChange(model) {
            this.scopeQuery = model.query;
        }
    },

    async created() {
        this.$params('loading', true);

        this.currencies = await this.$collection('kernel.currencies').find();

        const ais = await this.$collection('kernel.additional-information').find({
            type: 'sale-order'
        });
        const additionalInformationColumns = [];
        const additionalInformationScopeFilters = [];
        for (const ai of ais) {
            const columns = getAdditionalInformationColumns(ai);
            for (const column of columns) {
                if (additionalInformationColumns.findIndex(c => c.field === column.field) === -1) {
                    additionalInformationColumns.push(column);
                }
            }

            const filters = getAdditionalInformationScopeFilters(ai);
            for (const filter of filters) {
                if (additionalInformationScopeFilters.findIndex(f => f.field === filter.field) === -1) {
                    additionalInformationScopeFilters.push(filter);
                }
            }
        }
        this.additionalInformationColumns = additionalInformationColumns;
        this.additionalInformationScopeFilters = additionalInformationScopeFilters;

        // Order stages.
        this.stages = await this.$collection('sale.order-stages').find();

        this.initialized = true;

        this.$nextTick(() => {
            this.$params('loading', false);
        });
    }
};
</script>
