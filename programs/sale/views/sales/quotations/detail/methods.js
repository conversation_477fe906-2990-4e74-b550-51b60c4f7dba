import _ from 'lodash';
import fastCopy from 'fast-copy';
import {trim} from 'framework/helpers';
import WonForm from './_won-form.vue';
import LostForm from './_lost-form.vue';
import CreateRevisionPopup from '../../../../../../kernel/frontend/components/ui/view/create-revision-popup.vue';

export default {
    methods: {
        async handleWon() {
            const self = this;

            this.$program.dialog({
                component: WonForm,
                params: {
                    title: 'Won',
                    leadId: this.model.leadId,
                    currencyFormat: this.currencyFormat,
                    organizationSettings: this.organizationSettings
                },
                async onSubmit(data) {
                    self.$params('isPreview', true);
                    self.$params('loading', true);

                    // Run approval workflow.
                    if (self.$app.hasModule('workflow')) {
                        try {
                            const flowResult = await self.$rpc('workflow.run-workflow', {
                                name: 'sale.quotations',
                                data: {
                                    ..._.omit(self.model, 'workflowApprovalStatus')
                                },
                                id: self.$params('id'),
                                operation: 'update',
                                actionTypes: ['approval'],
                                approvalMethod: 'sale.close-quotation',
                                approvalPayload: {
                                    type: 'won',
                                    quotation: {
                                        ..._.omit(self.model, 'workflowApprovalStatus'),
                                        _id: self.$params('id')
                                    },
                                    conversionType: data.conversionType,
                                    groupId: data.groupId,
                                    identity: data.identity,
                                    tin: data.tin,
                                    useCurrentExchangeRate: data.useCurrentExchangeRate
                                }
                            });
                            if (!!flowResult.approvalDefinitionFound) {
                                if (flowResult.approvalIsAlreadyInProgress === true) {
                                    self.$params('loading', false);
                                    self.$program.alert(
                                        'error',
                                        self.$t(
                                            'The document is currently in the process of approval. When the relevant person or persons complete the approval process, the document will be automatically approved.'
                                        )
                                    );
                                    return;
                                }

                                self.$set(self.model, 'workflowApprovalStatus', 'waiting-for-approval');
                                self.$params('loading', false);
                                self.$program.alert(
                                    'success',
                                    self.$t(
                                        'The document has been successfully submitted to the workflow approval process. When the relevant person or persons complete the approval process, the document will be automatically approved.'
                                    )
                                );
                                self.$program.emit('workflow-approval-params-changed', {
                                    documentId: self.$params('id'),
                                    documentCollection: 'sale.quotations',
                                    approvalStatus: 'waiting-for-approval'
                                });
                                return;
                            } else {
                                self.$params('loading', false);
                            }
                        } catch (error) {
                            self.$program.message('error', error.message);
                            self.$params('loading', false);
                            return;
                        }
                    }

                    if (
                        data.conversionType === 'to-invoice' &&
                        (!_.isPlainObject(self.model.paymentPlan) || _.isEmpty(self.model.paymentPlan))
                    ) {
                        const isPaymentPlanConfirmed = await new Promise(resolve => {
                            self.$program.alert(
                                'confirm',
                                self.$t(
                                    'You are about to approve the document without planning the payment. Do you want continue?'
                                ),
                                confirmed => {
                                    resolve(confirmed);
                                }
                            );
                        });

                        if (!isPaymentPlanConfirmed) {
                            self.$params('loading', false);
                            return;
                        }
                    }

                    try {
                        await self.$rpc('sale.close-quotation', {
                            type: 'won',
                            quotation: {
                                ...self.model,
                                _id: self.$params('id')
                            },
                            conversionType: data.conversionType,
                            groupId: data.groupId,
                            identity: data.identity,
                            tin: data.tin,
                            useCurrentExchangeRate: data.useCurrentExchangeRate
                        });
                    } catch (error) {
                        self.$program.message('error', error.message);
                    }

                    self.$params('loading', false);
                }
            });
        },
        handleLost() {
            const self = this;

            this.$program.dialog({
                component: LostForm,
                params: {
                    title: 'Lost'
                },
                async onSubmit(data) {
                    self.$params('isPreview', true);
                    self.$params('loading', true);

                    try {
                        await self.$rpc('sale.close-quotation', {
                            type: 'lost',
                            quotation: {
                                ...self.model,
                                _id: self.$params('id')
                            },
                            lostType: data.lostType,
                            competitorId: data.competitorId,
                            lostReasonId: data.lostReasonId
                        });

                        if (!!data.competitorId && self.model.competitorIds.indexOf(data.competitorId) === -1) {
                            self.model.competitorIds.push(data.competitorId);
                        }
                    } catch (error) {
                        self.$program.message('error', error.message);
                    }

                    self.$params('loading', false);
                }
            });
        },
        async handleSupply() {
            this.$program.dialog({
                component: 'sale.sales.quotations.detail.supply-method',
                onSubmit: async ({supplyMethod}) => {
                    const isConfirmed = await new Promise(resolve => {
                        this.$program.alert(
                            'confirm',
                            this.$t('A purchase request will be created for the document. Do you want continue?'),
                            confirmed => {
                                resolve(confirmed);
                            }
                        );
                    });

                    if (!isConfirmed) return;

                    this.$params('loading', true);

                    try {
                        let result = this.model;
                        let id = this.$params('id');

                        if (!this.$params('isPreview')) {
                            result = await this.$refs.view.$refs.component.submitForm();
                        }

                        if (!id) {
                            const document = await this.$collection('sale.quotations').findOne({
                                code: result.code,
                                $select: ['_id']
                            });

                            if (!document) {
                                throw Error('Document not found!');
                            }

                            id = document._id;
                        }

                        this.$nextTick(async () => {
                            try {
                                const requestId = await this.$rpc('sale.create-purchase-request-for-sale', {
                                    id,
                                    model: result,
                                    supplyMethod,
                                    collection: 'sale.quotations',
                                    view: 'sale.sales.quotations',
                                    title: 'Sale Quotations'
                                });

                                this.$params('loading', false);

                                this.$program.dialog({
                                    component: 'purchase.procurement-and-source.request-procurements.master',
                                    params: {requestId}
                                });
                            } catch (error) {
                                this.$program.message('error', error.message);
                                this.$params('loading', false);
                            }
                        });
                    } catch (error) {
                        this.$program.message('error', error.message);
                        this.$params('loading', false);
                    }
                }
            });
        },
        async handleClearPaymentPlan() {
            this.$params('loading', true);

            const backup = this.model.paymentPlanBackup;

            this.model.items = backup.items;
            this.model.status = 'draft';
            this.model.discount = 0;
            this.model.rounding = 0;
            this.model.paymentPlanningDate = null;
            this.model.paymentPlan = null;
            this.model.paymentPlanBackup = null;

            await this.calculateTotals();

            let profitBase = null;
            if (_.isObject(this.organizationSettings) && this.organizationSettings.defaultProfitBase) {
                profitBase = this.organizationSettings.defaultProfitBase || this.$setting('sale.profitBase');
            } else {
                profitBase = this.$setting('sale.profitBase');
            }
            if (this.$params('id') && Array.isArray(this.model.items) && this.model.items.length > 0) {
                this.$rpc('sale.update-document-profits', {
                    profitBase,
                    documentId: this.$params('id'),
                    documentCollection: 'sale.quotations',
                    currencyId: this.model.currencyId,
                    currencyRate: this.model.currencyRate,
                    date: this.model.quotationDate,
                    exchangeRates: this.model.exchangeRates ?? [],
                    items: this.model.items.map(item => ({
                        productId: item.productId,
                        warehouseId: item.warehouseId,
                        quantity: item.quantity,
                        unitId: item.unitId,
                        freight: item.freight || 0,
                        totalSalesPrice: item.realTotal
                    }))
                });
            }

            this.$params('loading', false);
        },
        async handleDiscoveryObservation() {
            this.$params('loading', true);

            const doTypes = await this.$collection('crm.discovery-observation-types').find({
                $select: ['_id']
            });
            let doType = null;

            if (doTypes.length > 1) {
                doType = await new Promise(resolve => {
                    this.$program.dialog({
                        component: 'crm.marketing.discoveries-observations.create',
                        onSubmit: data => {
                            resolve(doTypes.find(dt => dt._id === data.discoveryObservationTypeId));
                        }
                    });
                });
            } else if (doTypes.length === 1) {
                doType = doTypes[0];
            }

            if (_.isPlainObject(doType)) {
                const paramsModel = {};

                paramsModel.discoveryObservationTypeId = doType._id;
                paramsModel.linkedDocumentCollection = 'sale.quotations';
                paramsModel.linkedDocumentId = this.$params('id');
                paramsModel.linkedDocumentCode = this.model.code;
                paramsModel.communicationChannelId = this.model.communicationChannelId;
                paramsModel.sourceId = this.model.sourceId;
                paramsModel.branchId = this.model.branchId;
                paramsModel.organizationId = this.model.organizationId;
                paramsModel.salesManagerId = this.model.salesManagerId;
                paramsModel.salespersonId = this.model.salespersonId;
                paramsModel.address = this.model.deliveryAddress;

                if (this.model.partnerType === 'customer' && !!this.model.partnerId) {
                    const partner = await this.$collection('kernel.partners').findOne({
                        _id: this.model.partnerId,
                        $select: ['phoneNumbers']
                    });

                    paramsModel.phoneNumbers = partner.phoneNumbers;
                } else if (!!this.model.leadId) {
                    const lead = await this.$collection('crm.leads').findOne({
                        _id: this.model.leadId,
                        $select: ['phoneNumbers']
                    });

                    paramsModel.phoneNumbers = lead.phoneNumbers;
                }

                this.$program.dialog({
                    component: 'crm.marketing.discoveries-observations.detail',
                    params: {
                        discoveryObservationTypeId: doType._id,
                        model: paramsModel,
                        isPreview: false
                    }
                });
            } else {
                this.$program.message(
                    'error',
                    this.$t('Could not find any discovery & observation type for this document type!')
                );
            }

            this.$params('loading', false);
        },
        async handleRenewValidity() {
            const isConfirmed = await new Promise(resolve => {
                this.$program.alert(
                    'confirm',
                    this.$t(
                        'During this process, the payment plan will be deleted and document dates will be updated. Do you want continue?'
                    ),
                    confirmed => {
                        resolve(confirmed);
                    }
                );
            });

            if (!isConfirmed) return;

            const params = {
                title: this.$t('New Revision'),
                forcedPreview: false,
                collection: 'sale.quotations',
                documentId: this.$params('id'),
                revisionId: this.model.revisionId,
                model: {
                    reason: this.$t('Quotation validity renewed due to expiration.')
                }
            };
            this.$program.dialog({
                component: CreateRevisionPopup,
                params,
                onSubmit: async ({revision}) => {
                    this.$params('loading', true);

                    this.model.revisionId = revision._id;
                    this.model.revisionName = revision.name;
                    this.model.hasRevisions = true;
                    this.$params('title', `${this.title} (${revision.name})`);

                    try {
                        this.model.status = 'draft';
                        this.model.quotationDate = this.$app.datetime.local().toJSDate();
                        this.model.expiryDate = this.$datetime
                            .fromJSDate(this.model.quotationDate)
                            .plus({
                                days: this.$setting('sale.defaultQuotationValidityDayCount')
                            })
                            .toJSDate();
                        this.model.isExpired = false;

                        this.model.items = await this.$rpc('sale.decorate-quotation-items', {
                            items: this.model.items,
                            field: 'quantity',
                            model: _.omit(this.model, 'items'),
                            productFields: this.productFields
                        });

                        let profitBase = null;
                        if (_.isObject(this.organizationSettings) && this.organizationSettings.defaultProfitBase) {
                            profitBase =
                                this.organizationSettings.defaultProfitBase || this.$setting('sale.profitBase');
                        } else {
                            profitBase = this.$setting('sale.profitBase');
                        }
                        if (this.$params('id') && Array.isArray(this.model.items) && this.model.items.length > 0) {
                            this.$rpc('sale.update-document-profits', {
                                profitBase,
                                documentId: this.$params('id'),
                                documentCollection: 'sale.quotations',
                                currencyId: this.model.currencyId,
                                currencyRate: this.model.currencyRate,
                                date: this.model.quotationDate,
                                exchangeRates: this.model.exchangeRates ?? [],
                                items: this.model.items.map(item => ({
                                    productId: item.productId,
                                    warehouseId: item.warehouseId,
                                    quantity: item.quantity,
                                    unitId: item.unitId,
                                    freight: item.freight || 0,
                                    totalSalesPrice: item.realTotal
                                }))
                            });
                        }

                        // Reset totals.
                        this.model.discount = await this.calculateGeneralDiscount();
                        this.model.paymentPlan = null;
                        this.model.paymentPlanBackup = null;
                        this.model.paymentPlanningDate = null;
                        await this.calculateTotals();

                        await this.$refs.view.submitForm();

                        this.$nextTick(() => {
                            this.itemsKey = _.uniqueId('quotationItems_');

                            this.$program.message('success', this.$t('Quotation validity renewed successfully.'));

                            this.$params('loading', false);
                        });
                    } catch (error) {
                        this.$program.message('error', error.message);

                        this.$params('loading', false);
                    }
                }
            });
        },
        async handleCancel() {
            const isConfirmed = await new Promise(resolve => {
                this.$program.alert(
                    'confirm',
                    this.$t('You are about to cancel the document. Do you want continue?'),
                    confirmed => {
                        resolve(confirmed);
                    }
                );
            });

            if (!isConfirmed) return;

            this.model.status = 'canceled';

            this.$refs.view.submitForm({status: 'canceled'});
        },
        handleOpenNearbyDocuments() {
            this.$program.dialog({
                component: 'sale.components.nearby-documents',
                params: {
                    documentCollection: 'sale.quotations',
                    documentView: 'sale.sales.quotations-detail',
                    documentId: this.$params('id'),
                    markerIcon: this.$app.absoluteUrl('static/images/markers/quotation.png'),
                    selectedMarkerIcon: this.$app.absoluteUrl('static/images/markers/quotation-s.png'),
                    address: this.model.deliveryAddress.address
                }
            });
        },
        async beforeInit(model) {
            const company = this.$store.getters['session/company'];
            const user = this.$user;

            // Get stages.
            if (!!this.$params('id') && Array.isArray(model.stages) && model.stages.length > 0) {
                this.stages = model.stages;
            }
            if (
                Array.isArray(this.stages) &&
                this.stages.length > 0 &&
                (!Array.isArray(model.stages) || model.stages.length < 1)
            ) {
                model.stages = this.stages;
            }

            // When creating generate code.
            if (!this.$params('id')) {
                model.code = await this.generateCode(false);
            }
            this.generatedCode = model.code;

            // Default currency.
            if (!model.currencyId) model.currencyId = company.currencyId;

            // Currency format.
            if (company.currencyId !== model.currencyId) {
                const currency = await this.$collection('kernel.currencies').findOne({
                    _id: model.currencyId,
                    $select: ['name', 'symbol', 'symbolPosition']
                });

                if (_.isObject(currency)) {
                    this.currencyFormat = {
                        currency: {
                            symbol: currency.symbol,
                            format: currency.symbolPosition === 'before' ? '%s %v' : '%v %s'
                        }
                    };
                }
            }

            // Default organization.
            if (user.partnerId && this.$setting('sale.salesOrganizations')) {
                const organization = this.organizations.find(
                    o =>
                        Array.isArray(o.team) &&
                        o.team.some(m => m.partnerId === user.partnerId) &&
                        o.scope.indexOf(model.module === 'service' ? 'service' : 'sale') !== -1
                );

                if (_.isObject(organization)) {
                    const team = organization.team || [];
                    const currentMember = team.find(m => m.partnerId === user.partnerId);

                    this.organizationSettings = organization.settings || {};
                    this.organizationTeam = team;

                    if (!this.$params('id')) {
                        if (!model.organizationId) {
                            model.organizationId = organization._id;
                        }

                        if (_.isObject(currentMember) && !model.salesManagerId) {
                            if (currentMember.managerId) {
                                model.salesManagerId = currentMember.managerId;
                            } else {
                                model.salesManagerId = currentMember.partnerId;
                            }
                        }

                        if (_.isObject(currentMember) && !model.salespersonId) {
                            model.salespersonId = currentMember.partnerId;
                        }
                    }

                    if (
                        _.isObject(currentMember) &&
                        _.isObject(currentMember.settings) &&
                        !_.isEmpty(currentMember.settings)
                    ) {
                        this.organizationSettings = _.assign(this.organizationSettings, currentMember.settings);
                    }
                }
            }
            if (_.isEmpty(this.organizationSettings) && model.organizationId) {
                const organization = this.organizations.find(o => o._id === model.organizationId);

                if (_.isPlainObject(organization)) {
                    const team = organization.team || [];
                    const currentMember = team.find(m => m.partnerId === user.partnerId);

                    this.organizationSettings = organization.settings || {};
                    this.organizationTeam = team;

                    if (
                        _.isObject(currentMember) &&
                        _.isObject(currentMember.settings) &&
                        !_.isEmpty(currentMember.settings)
                    ) {
                        this.organizationSettings = _.assign(this.organizationSettings, currentMember.settings);
                    }
                }
            }

            // Organization defaults.
            if (!this.$params('id') && _.isObject(this.organizationSettings) && !_.isEmpty(this.organizationSettings)) {
                const settings = this.organizationSettings;

                // Document type.
                if (settings.defaultDocumentTypeId && !model.documentTypeId)
                    model.documentTypeId = settings.defaultDocumentTypeId;

                // Customer group.
                if (settings.defaultCustomerGroupId && !model.partnerGroupId)
                    model.partnerGroupId = settings.defaultCustomerGroupId;

                // Price list.
                if (settings.defaultPriceListId && !model.priceListId) model.priceListId = settings.defaultPriceListId;

                // Payment term.
                if (settings.defaultPaymentTermId && !model.paymentTermId) {
                    model.paymentTermId = settings.defaultPaymentTermId;
                    model.paymentTerm = await this.$collection('finance.payment-terms').findOne({
                        _id: model.paymentTermId
                    });
                    model.paymentPlan = null;
                    model.paymentPlanningDate = null;
                }
            }

            // Default sales type.
            let documentType = null;
            if (!model.documentTypeId) {
                const filters = {};

                if (model.module === 'service') {
                    filters.module = 'service';
                } else {
                    filters.code = 'domesticSales';
                }

                documentType = await this.$collection('sale.document-types').findOne(filters);

                model.documentTypeId = documentType._id;
            } else {
                documentType = await this.$collection('sale.document-types').findOne({_id: model.documentTypeId});

                model.documentTypeId = documentType._id;
            }
            this.documentType = documentType;

            if (!!model.deliveryMethodId) {
                const deliveryMethod = await this.$collection('logistics.delivery-methods').findOne({
                    _id: model.deliveryMethodId,
                    $select: ['type'],
                    $disableActiveCheck: true,
                    $disableSoftDelete: true
                });

                if (!!deliveryMethod) {
                    this.deliveryMethodType = deliveryMethod.type;
                } else {
                    this.deliveryMethodType = null;
                }
            } else {
                this.deliveryMethodType = null;
            }

            // Default stage index.
            if (!this.$params('id') && this.stages.length > 0 && !model.stageId) {
                model.stageId = this.stages[0]._id;
            }
            if (model.stageId && this.stages.length > 0) {
                this.activeStageIndex = _.findIndex(this.stages, stage => stage._id === model.stageId);
            }

            // Default quotation validity day count.
            if (!this.$params('id')) {
                model.expiryDate = this.$datetime
                    .fromJSDate(model.recordDate)
                    .plus({
                        days: this.$setting('sale.defaultQuotationValidityDayCount')
                    })
                    .toJSDate();
            }

            if (!Array.isArray(model.exchangeRates) || model.exchangeRates.length < 1) {
                const currencies = await this.$collection('kernel.currencies').find({
                    $select: ['name']
                });
                const exchangeRates = [];
                const payloads = [];

                for (const currency of currencies) {
                    if (currency.name === company.currency.name) {
                        continue;
                    }

                    payloads.push({
                        from: currency.name,
                        to: company.currency.name,
                        value: 1,
                        options: {
                            date: model.quotationDate
                        }
                    });
                }

                for (const payload of await this.$rpc('kernel.common.convert-currencies', payloads)) {
                    exchangeRates.push({
                        currencyName: payload.from,
                        rate: payload.rate
                    });
                }

                model.exchangeRates = exchangeRates;
            }

            // Competitors.
            model.competitorIds = (model.competitors || []).map(c => c.id);
            this.selectedCompetitorId = null;

            // Get nearby documents count.
            this.getNearbyDocumentsCount();

            // Save current currency rate.
            this.currentCurrencyRate = model.currencyRate;

            // Is expired.isExpired
            if (!!this.$params('id')) {
                const expiryDate = this.$app.datetime.fromJSDate(model.expiryDate);
                const now = this.$app.datetime.local();

                model.isExpired = now.diff(expiryDate).as('days') > 0;
            }

            this.isModelLoaded = true;

            // Additional items fields.
            if (Array.isArray(documentType.additionalItemFields) && documentType.additionalItemFields.length > 0) {
                model.items = model.items.map(item => {
                    const additionalItemFields = item.additionalItemFields || {};

                    for (const key of Object.keys(additionalItemFields)) {
                        item[`additionalFields_${key}`] = additionalItemFields[key] || '';
                    }

                    return item;
                });

                this.additionalItemFields = fastCopy(documentType.additionalItemFields);

                this.isItemsShown = false;
                this.$nextTick(() => {
                    this.isItemsShown = true;
                });
            }

            return model;
        },
        async beforeValidate(model) {
            if (model.partnerType === 'customer' && !model.partnerId) {
                throw new this.$app.errors.Unprocessable({
                    message: this.$t('{{label}} is required', {
                        label: this.$t('Customer')
                    }),
                    field: 'partnerId'
                });
            }

            if (model.partnerType === 'lead' && !model.leadId) {
                throw new this.$app.errors.Unprocessable({
                    message: this.$t('{{label}} is required', {
                        label: this.$t('Lead')
                    }),
                    field: 'leadId'
                });
            }

            if (!model.stageId && this.stages.length > 0) {
                throw new this.$app.errors.Unprocessable({
                    message: this.$t('{{label}} is required', {
                        label: this.$t('Stage')
                    }),
                    field: 'stageId'
                });
            }

            const documentType = this.documentType;
            if (
                !!documentType &&
                Array.isArray(documentType.additionalItemFields) &&
                documentType.additionalItemFields.length > 0
            ) {
                this.additionalItemsFieldValues = {};
                model.items = model.items.map(item => {
                    this.additionalItemsFieldValues[item.id] = {};

                    for (const key of Object.keys(item)) {
                        if (key.includes('additionalFields_')) {
                            this.additionalItemsFieldValues[item.id][key.replaceAll('additionalFields_', '')] =
                                item[key];
                        }
                    }

                    return item;
                });
            }

            return model;
        },
        async beforeSubmit(model) {
            this.selectedCompetitorId = null;

            // if (this.model.code === this.generatedCode && !this.$params('id')) {
            //     model.code = await this.generateCode(true);
            // }

            if (!!this.$refs && !!this.$refs.additionalInformationForm) {
                model.additionalInformation = await this.$refs.additionalInformationForm.submitForm();

                if (!!this.$refs.additionalInformationForm.additionalInformation?._id) {
                    model.additionalInformationId = this.$refs.additionalInformationForm.additionalInformation._id;
                }
            }

            const documentType = this.documentType;
            if (
                !!documentType &&
                Array.isArray(documentType.additionalItemFields) &&
                documentType.additionalItemFields.length > 0
            ) {
                model.items = model.items.map(item => {
                    item.additionalItemFields = this.additionalItemsFieldValues[item.id];

                    return item;
                });
            }

            return model;
        },
        async afterSubmit(result) {
            const model = this.model;
            const id = result._id;
            let profitBase = null;

            if (_.isObject(this.organizationSettings) && this.organizationSettings.defaultProfitBase) {
                profitBase = this.organizationSettings.defaultProfitBase || this.$setting('sale.profitBase');
            } else {
                profitBase = this.$setting('sale.profitBase');
            }

            const existingCount = await this.$collection('sale.document-profits').count({
                documentId: id,
                documentCollection: 'sale.quotations'
            });

            if (id && Array.isArray(model.items) && model.items.length > 0 && existingCount < 1) {
                this.$rpc('sale.update-document-profits', {
                    profitBase,
                    documentId: id,
                    documentCollection: 'sale.quotations',
                    currencyId: this.model.currencyId,
                    currencyRate: this.model.currencyRate,
                    date: this.model.quotationDate,
                    exchangeRates: this.model.exchangeRates ?? [],
                    items: model.items.map(item => ({
                        productId: item.productId,
                        warehouseId: item.warehouseId,
                        quantity: item.quantity,
                        unitId: item.unitId,
                        freight: item.freight || 0,
                        totalSalesPrice: item.realTotal
                    }))
                });
            }

            // Additional items fields.
            setTimeout(() => {
                const documentType = this.documentType;
                if (
                    !!documentType &&
                    Array.isArray(documentType.additionalItemFields) &&
                    documentType.additionalItemFields.length > 0
                ) {
                    this.model.items = fastCopy(this.model.items).map(item => {
                        const additionalItemFieldValues = this.additionalItemsFieldValues[item.id] || {};

                        for (const key of Object.keys(additionalItemFieldValues)) {
                            item[`additionalFields_${key}`] = additionalItemFieldValues[key] || '';
                        }

                        return item;
                    });

                    this.additionalItemFields = fastCopy(documentType.additionalItemFields);

                    this.isItemsShown = false;
                    this.$nextTick(() => {
                        this.isItemsShown = true;
                    });
                }
            }, 250);
        },
        onError() {
            // Additional items fields.
            setTimeout(() => {
                const documentType = this.documentType;
                if (
                    !!documentType &&
                    Array.isArray(documentType.additionalItemFields) &&
                    documentType.additionalItemFields.length > 0
                ) {
                    this.model.items = fastCopy(this.model.items).map(item => {
                        const additionalItemFieldValues = this.additionalItemsFieldValues[item.id] || {};

                        for (const key of Object.keys(additionalItemFieldValues)) {
                            item[`additionalFields_${key}`] = additionalItemFieldValues[key] || '';
                        }

                        return item;
                    });

                    this.additionalItemFields = fastCopy(documentType.additionalItemFields);

                    this.isItemsShown = false;
                    this.$nextTick(() => {
                        this.isItemsShown = true;
                    });
                }
            }, 250);
        },
        async handleChange(model, field) {
            const company = this.$store.getters['session/company'];
            const user = this.$user;

            if (field === 'documentTypeId') {
                const documentType = await this.$collection('sale.document-types').findOne({_id: model.documentTypeId});

                if (Array.isArray(documentType.additionalItemFields) && documentType.additionalItemFields.length > 0) {
                    this.additionalItemFields = documentType.additionalItemFields;

                    this.isItemsShown = false;
                    this.$nextTick(() => {
                        this.isItemsShown = true;
                    });
                }

                this.documentType = documentType;
            } else if (field === 'partnerType') {
                // Lead
                this.model.leadId = '';

                // Partner group
                this.model.partnerGroupId = '';

                await this.handleChange(this.model, 'partnerGroupId');
            } else if (field === 'partnerGroupId') {
                // Partner
                this.model.partnerId = '';

                // Branch
                this.model.branchId = user.branchIds[0];

                // Currency
                this.model.currencyId = company.currencyId;
                this.currencyFormat = {
                    currency: {
                        symbol: company.currency.symbol,
                        format: company.currency.symbolPosition === 'before' ? '%s %v' : '%v %s'
                    }
                };

                // Branch default warehouse.
                let warehouse = null;
                if (
                    _.isObject(this.organizationSettings) &&
                    !_.isEmpty(this.organizationSettings) &&
                    !!this.organizationSettings.defaultWarehouseId
                ) {
                    warehouse = await this.$collection('inventory.warehouses').findOne({
                        _id: this.organizationSettings.defaultWarehouseId,
                        $select: ['_id']
                    });
                    if (_.isObject(warehouse)) {
                        this.model.warehouseId = warehouse._id;
                    } else {
                        this.$program.message(
                            'error',
                            this.$t('No warehouse that is belongs to the selected branch is found.')
                        );
                    }
                } else {
                    warehouse = await this.$collection('inventory.warehouses').findOne({
                        branchId: this.model.branchId,
                        $select: ['_id']
                    });
                    if (_.isObject(warehouse)) {
                        this.model.warehouseId = warehouse._id;
                    } else {
                        this.$program.message(
                            'error',
                            this.$t('No warehouse that is belongs to the selected branch is found.')
                        );
                    }
                }

                // Branch journal.
                this.model.journalId = '';
                this.model.journalDescription = '';

                // Addresses.
                this.model.invoiceAddress = {};
                this.model.deliveryAddress = {};

                // // Items
                // this.$set(this.model, 'items', []);
                //
                // // Reset totals.
                // this.model.subTotal = 0;
                // this.model.discount = 0;
                // this.model.discountAmount = 0;
                // this.model.subTotalAfterDiscount = 0;
                // this.model.taxTotal = 0;
                // this.model.rounding = 0;
                // this.model.grandTotal = 0;
                // this.model.appliedTaxes = [];
                //
                // // Payment plan.
                // this.model.paymentPlan = null;
                // this.model.paymentPlanningDate = null;
                this.model.partialDeliveries = [];
            } else if (field === 'partnerId') {
                // this.model.items = [];
                //
                // await this.calculateTotals();

                if (model.partnerId) {
                    this.$params('loading', true);

                    const partner = await this.$collection('kernel.partners').findOne({
                        _id: model.partnerId,
                        $select: [
                            'code',
                            'groupId',
                            'name',
                            'currencyId',
                            'priceListId',
                            'address',
                            'branchIds',
                            'paymentTermId'
                        ]
                    });

                    if (_.isObject(partner)) {
                        if (!model.partnerGroupId) {
                            this.model.partnerGroupId = partner.groupId;
                        }

                        // Partner currency.
                        if (!!partner.currencyId && this.model.currencyId !== partner.currencyId) {
                            this.model.currencyId = partner.currencyId;

                            await this.handleChange(this.model, 'currencyId');
                        }

                        // Partner branch.
                        this.model.branchId = partner.branchIds[0];

                        // Branch default warehouse.
                        let warehouse = null;
                        if (
                            _.isObject(this.organizationSettings) &&
                            !_.isEmpty(this.organizationSettings) &&
                            !!this.organizationSettings.defaultWarehouseId
                        ) {
                            warehouse = await this.$collection('inventory.warehouses').findOne({
                                _id: this.organizationSettings.defaultWarehouseId,
                                $select: ['_id']
                            });
                            if (_.isObject(warehouse)) {
                                this.model.warehouseId = warehouse._id;
                            } else {
                                this.$program.message(
                                    'error',
                                    this.$t('No warehouse that is belongs to the selected branch is found.')
                                );
                            }
                        } else {
                            const partnerWarehouse = await this.$collection('inventory.warehouses').findOne({
                                branchId: this.model.branchId,
                                partnerId: this.model.partnerId,
                                $select: ['_id', 'address', 'advancedDeliveryPlanning']
                            });
                            if (!!partnerWarehouse) {
                                warehouse = partnerWarehouse;
                            } else {
                                warehouse = await this.$collection('inventory.warehouses').findOne({
                                    branchId: this.model.branchId,
                                    $select: ['_id', 'address', 'advancedDeliveryPlanning']
                                });
                            }

                            if (_.isObject(warehouse)) {
                                this.model.warehouseId = warehouse._id;
                            } else {
                                this.$program.message(
                                    'error',
                                    this.$t('No warehouse that is belongs to the selected branch is found.')
                                );
                            }
                        }

                        // Partner payment term.
                        if (partner.paymentTermId) {
                            this.model.paymentTermId = partner.paymentTermId;
                            this.model.paymentTerm = await this.$collection('finance.payment-terms').findOne({
                                _id: this.model.paymentTermId
                            });
                        } else if (!this.model.paymentTermId) {
                            this.model.paymentTerm = await this.$collection('finance.payment-terms').findOne({
                                system: true
                            });
                            if (_.isObject(this.model.paymentTerm) && !_.isEmpty(this.model.paymentTerm)) {
                                this.model.paymentTermId = this.model.paymentTerm._id;
                            }
                        }

                        // Price list.
                        if (partner.priceListId) {
                            this.model.customerPriceListId = partner.priceListId;
                        }

                        // Partner addresses.
                        if (partner.address) {
                            this.model.invoiceAddress = partner.address;
                            this.model.deliveryAddress = partner.address;

                            this.getNearbyDocumentsCount();
                        }

                        // Document type.
                        const isForeignPartner =
                            !!partner.address &&
                            !!partner.address.countryId &&
                            partner.address.countryId !== company.address.countryId;
                        if (isForeignPartner) {
                            const dt = await this.$collection('sale.document-types').findOne({
                                code: 'foreignSales',
                                $select: ['_id']
                            });

                            if (!!dt) {
                                this.model.documentTypeId = dt._id;
                            }
                        }

                        // this.$set(this.model, 'items', []);
                        //
                        // // Reset totals.
                        // this.model.subTotal = 0;
                        // this.model.discount = await this.calculateGeneralDiscount();
                        // this.model.discountAmount = 0;
                        // this.model.subTotalAfterDiscount = 0;
                        // this.model.taxTotal = 0;
                        // this.model.rounding = 0;
                        // this.model.grandTotal = 0;
                        // this.model.appliedTaxes = [];
                        //
                        // this.model.paymentPlan = null;
                        // this.model.paymentPlanningDate = null;
                        this.model.partialDeliveries = [];

                        await this.resetItems();
                    }

                    this.$params('loading', false);
                }
            } else if (field === 'leadId') {
                this.model.items = [];

                await this.calculateTotals();

                if (model.leadId) {
                    this.$params('loading', true);

                    const lead = await this.$collection('crm.leads').findOne({
                        _id: model.leadId,
                        $select: ['code', 'name', 'currencyId', 'address', 'branchId']
                    });

                    if (_.isObject(lead)) {
                        // Lead currency.
                        if (lead.currencyId) {
                            this.model.currencyId = lead.currencyId;

                            await this.handleChange(this.model, 'currencyId');
                        }

                        // Lead branch.
                        this.model.branchId = lead.branchId;

                        // Branch default warehouse.
                        let warehouse = null;
                        if (
                            _.isObject(this.organizationSettings) &&
                            !_.isEmpty(this.organizationSettings) &&
                            !!this.organizationSettings.defaultWarehouseId
                        ) {
                            warehouse = await this.$collection('inventory.warehouses').findOne({
                                _id: this.organizationSettings.defaultWarehouseId,
                                $select: ['_id']
                            });
                            if (_.isObject(warehouse)) {
                                this.model.warehouseId = warehouse._id;
                            } else {
                                this.$program.message(
                                    'error',
                                    this.$t('No warehouse that is belongs to the selected branch is found.')
                                );
                            }
                        } else {
                            warehouse = await this.$collection('inventory.warehouses').findOne({
                                branchId: this.model.branchId,
                                $select: ['_id']
                            });
                            if (_.isObject(warehouse)) {
                                this.model.warehouseId = warehouse._id;
                            } else {
                                this.$program.message(
                                    'error',
                                    this.$t('No warehouse that is belongs to the selected branch is found.')
                                );
                            }
                        }

                        // Payment term.
                        if (!this.model.paymentTermId) {
                            this.model.paymentTerm = await this.$collection('finance.payment-terms').findOne({
                                system: true
                            });
                            if (_.isObject(this.model.paymentTerm) && !_.isEmpty(this.model.paymentTerm)) {
                                this.model.paymentTermId = this.model.paymentTerm._id;
                            }
                        }

                        // Lead addresses.
                        if (lead.address) {
                            this.model.invoiceAddress = lead.address;
                            this.model.deliveryAddress = lead.address;

                            this.getNearbyDocumentsCount();
                        }

                        // this.$set(this.model, 'items', []);
                        //
                        // // Reset totals.
                        // this.model.subTotal = 0;
                        // this.model.discount = await this.calculateGeneralDiscount();
                        // this.model.discountAmount = 0;
                        // this.model.subTotalAfterDiscount = 0;
                        // this.model.taxTotal = 0;
                        // this.model.rounding = 0;
                        // this.model.grandTotal = 0;
                        // this.model.appliedTaxes = [];
                        //
                        // this.model.paymentPlan = null;
                        // this.model.paymentPlanningDate = null;
                        this.model.partialDeliveries = [];

                        await this.resetItems();
                    }

                    this.$params('loading', false);
                }
            } else if (field === 'currencyId') {
                if (model.currencyId) {
                    const currency = await this.$collection('kernel.currencies').findOne({
                        _id: model.currencyId,
                        $select: ['name', 'symbol', 'symbolPosition']
                    });

                    if (_.isObject(currency)) {
                        this.currencyFormat = {
                            currency: {
                                symbol: currency.symbol,
                                format: currency.symbolPosition === 'before' ? '%s %v' : '%v %s'
                            }
                        };

                        if (company.currencyId !== model.currencyId) {
                            this.model.currencyRate = await this.$convertCurrency({
                                from: currency.name,
                                to: company.currency.name,
                                value: 1,
                                options: {
                                    date: model.quotationDate
                                }
                            });
                        } else {
                            this.model.currencyRate = 1;
                        }
                    }
                } else {
                    this.model.currencyId = company.currencyId;
                    this.model.currencyRate = 1;
                }

                this.$params('loading', true);
                const items = await this.$rpc('sale.decorate-quotation-items', {
                    items: model.items.map(item => {
                        item.unitPrice = this.$app.round(
                            (this.currentCurrencyRate * item.unitPrice) / this.model.currencyRate,
                            'unit-price'
                        );

                        return item;
                    }),
                    field: 'unitPrice',
                    model: _.omit(this.model, 'items'),
                    productFields: this.productFields
                });
                this.$set(this.model, 'items', items);

                let profitBase = null;
                if (_.isObject(this.organizationSettings) && this.organizationSettings.defaultProfitBase) {
                    profitBase = this.organizationSettings.defaultProfitBase || this.$setting('sale.profitBase');
                } else {
                    profitBase = this.$setting('sale.profitBase');
                }
                if (this.$params('id') && Array.isArray(model.items) && model.items.length > 0) {
                    this.$rpc('sale.update-document-profits', {
                        profitBase,
                        documentId: this.$params('id'),
                        documentCollection: 'sale.quotations',
                        currencyId: model.currencyId,
                        currencyRate: model.currencyRate,
                        date: model.quotationDate,
                        exchangeRates: model.exchangeRates ?? [],
                        items: model.items.map(item => ({
                            productId: item.productId,
                            warehouseId: item.warehouseId,
                            quantity: item.quantity,
                            unitId: item.unitId,
                            freight: item.freight || 0,
                            totalSalesPrice: item.realTotal
                        }))
                    });
                }

                // Reset totals.
                this.model.discount = await this.calculateGeneralDiscount(this.model.discount);
                this.model.paymentPlan = null;
                this.model.paymentPlanningDate = null;
                await this.calculateTotals();

                this.$nextTick(() => {
                    this.itemsKey = _.uniqueId('quotationItems_');
                    this.currentCurrencyRate = this.model.currencyRate;

                    this.$params('loading', false);
                });
            } else if (field === 'currencyRate') {
                this.currentCurrencyRate = model.currencyRate || 1;
            } else if (field === 'quotationDate') {
                if (company.currencyId !== model.currencyId) {
                    const currency = await this.$collection('kernel.currencies').findOne({
                        _id: model.currencyId,
                        $select: ['name']
                    });

                    this.model.currencyRate = await this.$convertCurrency({
                        from: currency.name,
                        to: company.currency.name,
                        value: 1,
                        options: {
                            date: model.quotationDate
                        }
                    });
                }

                this.$params('loading', true);
                const items = await this.$rpc('sale.decorate-quotation-items', {
                    items: model.items,
                    field: 'unitPrice',
                    model: _.omit(this.model, 'items'),
                    productFields: this.productFields
                });
                this.$set(this.model, 'items', items);

                let profitBase = null;
                if (_.isObject(this.organizationSettings) && this.organizationSettings.defaultProfitBase) {
                    profitBase = this.organizationSettings.defaultProfitBase || this.$setting('sale.profitBase');
                } else {
                    profitBase = this.$setting('sale.profitBase');
                }
                if (this.$params('id') && Array.isArray(model.items) && model.items.length > 0) {
                    this.$rpc('sale.update-document-profits', {
                        profitBase,
                        documentId: this.$params('id'),
                        documentCollection: 'sale.quotations',
                        currencyId: model.currencyId,
                        currencyRate: model.currencyRate,
                        date: model.quotationDate,
                        exchangeRates: model.exchangeRates ?? [],
                        items: model.items.map(item => ({
                            productId: item.productId,
                            warehouseId: item.warehouseId,
                            quantity: item.quantity,
                            unitId: item.unitId,
                            freight: item.freight || 0,
                            totalSalesPrice: item.realTotal
                        }))
                    });
                }

                // Reset totals.
                this.model.discount = await this.calculateGeneralDiscount(this.model.discount);
                this.model.paymentPlan = null;
                this.model.paymentPlanningDate = null;
                await this.calculateTotals();

                await (async () => {
                    const currencies = await this.$collection('kernel.currencies').find({
                        $select: ['name']
                    });
                    const exchangeRates = [];
                    const payloads = [];

                    for (const currency of currencies) {
                        if (currency.name === company.currency.name) {
                            continue;
                        }

                        payloads.push({
                            from: currency.name,
                            to: company.currency.name,
                            value: 1,
                            options: {
                                date: model.quotationDate
                            }
                        });
                    }

                    for (const payload of await this.$rpc('kernel.common.convert-currencies', payloads)) {
                        exchangeRates.push({
                            currencyName: payload.from,
                            rate: payload.rate
                        });
                    }

                    this.model.exchangeRates = exchangeRates;
                })();

                this.$nextTick(() => {
                    this.itemsKey = _.uniqueId('quotationItems_');
                    this.currentCurrencyRate = this.model.currencyRate;

                    this.$params('loading', false);
                });
            } else if (field === 'branchId') {
                // Branch default warehouse.
                let warehouse = null;
                if (
                    _.isObject(this.organizationSettings) &&
                    !_.isEmpty(this.organizationSettings) &&
                    !!this.organizationSettings.defaultWarehouseId
                ) {
                    warehouse = await this.$collection('inventory.warehouses').findOne({
                        _id: this.organizationSettings.defaultWarehouseId,
                        $select: ['_id']
                    });
                    if (_.isObject(warehouse)) {
                        this.model.warehouseId = warehouse._id;
                    } else {
                        this.$program.message(
                            'error',
                            this.$t('No warehouse that is belongs to the selected branch is found.')
                        );
                    }
                } else {
                    const partnerWarehouse = await this.$collection('inventory.warehouses').findOne({
                        branchId: this.model.branchId,
                        partnerId: this.model.partnerId,
                        $select: ['_id', 'address', 'advancedDeliveryPlanning']
                    });
                    if (!!partnerWarehouse) {
                        warehouse = partnerWarehouse;
                    } else {
                        warehouse = await this.$collection('inventory.warehouses').findOne({
                            branchId: this.model.branchId,
                            $select: ['_id', 'address', 'advancedDeliveryPlanning']
                        });
                    }

                    if (_.isObject(warehouse)) {
                        this.model.warehouseId = warehouse._id;
                    } else {
                        this.$program.message(
                            'error',
                            this.$t('No warehouse that is belongs to the selected branch is found.')
                        );
                    }
                }

                // this.$set(this.model, 'items', []);
                //
                // // Reset totals.
                // this.model.subTotal = 0;
                // this.model.discount = await this.calculateGeneralDiscount();
                // this.model.discountAmount = 0;
                // this.model.subTotalAfterDiscount = 0;
                // this.model.taxTotal = 0;
                // this.model.rounding = 0;
                // this.model.grandTotal = 0;
                // this.model.appliedTaxes = [];
                //
                // this.model.paymentPlan = null;
                // this.model.paymentPlanningDate = null;
                this.model.partialDeliveries = [];

                await this.resetItems();
            } else if (field === 'expiryDate') {
                const expiryDate = this.$app.datetime.fromJSDate(model.expiryDate);
                const now = this.$app.datetime.local();

                this.model.isExpired = now.diff(expiryDate).as('days') > 0;
            } else if (field === 'items') {
                let profitBase = null;

                if (_.isObject(this.organizationSettings) && this.organizationSettings.defaultProfitBase) {
                    profitBase = this.organizationSettings.defaultProfitBase || this.$setting('sale.profitBase');
                } else {
                    profitBase = this.$setting('sale.profitBase');
                }

                if (this.$params('id') && Array.isArray(model.items) && model.items.length > 0) {
                    this.$rpc('sale.update-document-profits', {
                        profitBase,
                        documentId: this.$params('id'),
                        documentCollection: 'sale.quotations',
                        currencyId: model.currencyId,
                        currencyRate: model.currencyRate,
                        date: model.quotationDate,
                        exchangeRates: model.exchangeRates ?? [],
                        items: model.items.map(item => ({
                            productId: item.productId,
                            warehouseId: item.warehouseId,
                            quantity: item.quantity,
                            unitId: item.unitId,
                            freight: item.freight || 0,
                            totalSalesPrice: item.realTotal
                        }))
                    });
                }

                this.model.partialDeliveries = [];
            } else if (field === 'invoiceResponsibleId') {
                const invoiceContact = await this.$collection('kernel.contacts').findOne({
                    relevantContactId: model.invoiceResponsibleId,
                    type: 'invoice-address',
                    $select: ['address']
                });

                if (_.isObject(invoiceContact)) {
                    this.model.invoiceAddressId = invoiceContact._id;
                    this.model.invoiceAddress = invoiceContact.address;
                }
            } else if (field === 'deliveryReceiverId') {
                const deliveryContact = await this.$collection('kernel.contacts').findOne({
                    relevantContactId: model.deliveryReceiverId,
                    type: 'delivery-address',
                    $select: ['code', 'address']
                });

                if (_.isObject(deliveryContact)) {
                    this.model.deliveryAddressId = deliveryContact._id;
                    this.model.deliveryAddressCode = deliveryContact.code;
                    this.model.deliveryAddress = deliveryContact.address;
                }
            } else if (field === 'invoiceAddressId') {
                const contact = await this.$collection('kernel.contacts').findOne({
                    _id: model.invoiceAddressId,
                    $select: ['address']
                });

                this.model.invoiceAddress = contact.address;
            } else if (field === 'paymentAddressId') {
                const contact = await this.$collection('kernel.contacts').findOne({
                    _id: model.paymentAddressId,
                    $select: ['address']
                });

                this.model.paymentAddress = contact.address;
            } else if (field === 'deliveryAddressId') {
                const contact = await this.$collection('kernel.contacts').findOne({
                    _id: model.deliveryAddressId,
                    $select: ['code', 'address']
                });

                if (!!contact) {
                    this.model.deliveryAddressCode = contact.code;
                    this.model.deliveryAddress = contact.address;
                }

                this.getNearbyDocumentsCount();
            } else if (field === 'deliveryAddress') {
                this.getNearbyDocumentsCount();
            } else if (field === 'deliveryMethodId') {
                if (!!model.deliveryMethodId) {
                    const deliveryMethod = await this.$collection('logistics.delivery-methods').findOne({
                        _id: model.deliveryMethodId,
                        $select: ['type'],
                        $disableActiveCheck: true,
                        $disableSoftDelete: true
                    });

                    if (!!deliveryMethod) {
                        this.deliveryMethodType = deliveryMethod.type;
                    } else {
                        this.deliveryMethodType = null;
                    }
                } else {
                    this.deliveryMethodType = null;
                }
            } else if (field === 'paymentTermId') {
                this.model.paymentTerm = await this.$collection('finance.payment-terms').findOne({
                    _id: this.model.paymentTermId
                });
                this.model.paymentPlan = null;
                this.model.paymentPlanningDate = null;
            } else if (field === 'discount') {
                this.model.paymentPlan = null;
                this.model.paymentPlanningDate = null;
            } else if (field === 'stageId') {
                const currentStage = this.stages[this.activeStageIndex];
                const nextStageIndex = _.findIndex(this.stages, stage => stage._id === model.stageId);
                const nextStage = this.stages[nextStageIndex];

                if (_.isObject(currentStage)) {
                    // Prepare history.
                    const history = {};
                    history.date = this.$datetime.local().toJSDate();
                    history.userId = user._id;
                    history.username = user.name;
                    history.subTotal = this.model.subTotal;
                    history.grandTotal = this.model.grandTotal;
                    history.probability = currentStage.probability;
                    history.stageId = currentStage._id;
                    history.stageName = currentStage.name;

                    // Stage interval.
                    let interval = '';
                    if (this.model.stageHistory.length > 0) {
                        const prevStageHistory = this.model.stageHistory[this.model.stageHistory.length - 1];
                        const now = this.$datetime.local();
                        const prevDate = this.$datetime.fromJSDate(prevStageHistory.date);
                        const diff = now.diff(prevDate).as('milliseconds');

                        let seconds = Math.abs(Math.round(diff / 1000));
                        let minutes = Math.abs(Math.round(diff / (1000 * 60)));
                        let hours = Math.abs(Math.round(diff / (1000 * 60 * 60)));
                        let days = Math.abs(Math.round(diff / (1000 * 60 * 60 * 24)));
                        let months = Math.abs(Math.round(diff / (1000 * 60 * 60 * 24 * 30)));

                        if (months) interval += `${months} ${this.$t('month(s)')}|`;
                        if (days) interval += `${days} ${this.$t('day(s)')}|`;
                        if (hours) interval += `${hours} ${this.$t('hour(s)')}|`;
                        if (minutes) interval += `${minutes} ${this.$t('minute(s)')}|`;
                        if (seconds) interval += `${seconds} ${this.$t('second(s)')}|`;
                        interval = trim(interval, '|').split('|').join(' ');

                        history.stageInterval = interval;
                    }

                    this.model.stageHistory = (this.model.stageHistory || []).concat([history]);
                }

                // Set probability.
                this.model.probability = nextStage.probability;

                // Set active stage.
                this.activeStageIndex = nextStageIndex;
            }

            if (
                (field === 'currencyId' || field === 'currencyRate' || field === 'quotationDate') &&
                Array.isArray(this.model.exchangeRates) &&
                this.model.exchangeRates.length > 0 &&
                company.currencyId !== this.model.currencyId
            ) {
                const currency = await this.$collection('kernel.currencies').findOne({
                    _id: this.model.currencyId,
                    $select: ['name'],
                    $disableActiveCheck: true,
                    $disableSoftDelete: true
                });
                const index = this.model.exchangeRates.findIndex(er => er.currencyName === currency.name);

                if (index !== -1) {
                    const exchangeRates = fastCopy(this.model.exchangeRates);

                    exchangeRates[index].rate = this.model.currencyRate;

                    this.model.exchangeRates = exchangeRates;
                }
            }

            if (!!this.$params('id') && (field === 'currencyId' || field === 'currencyRate' || field === 'issueDate')) {
                const profit = await this.$collection('sale.document-profits').findOne({
                    documentId: this.$params('id')
                });

                if (!!profit) {
                    this.model.items = (fastCopy(this.model.items) ?? []).map(item => {
                        if (!item.costSource && !!profit.defaultProfitBase) {
                            item.costSource = profit.defaultProfitBase;
                        }

                        if (!!item.costSource) {
                            const profitItem = (profit.items ?? []).find(
                                profitItem =>
                                    profitItem.profitBase === item.costSource &&
                                    profitItem.productId === item.productId &&
                                    profitItem.unitId === item.unitId &&
                                    profitItem.quantity === item.quantity &&
                                    profitItem.warehouseId === item.warehouseId
                            );

                            if (!!profitItem) {
                                item.unitCost =
                                    (profitItem.basePrice ?? 0) / (model.currencyRate !== 0 ? model.currencyRate : 1);
                                item.cost = item.unitCost * item.quantity;
                                item.profitRate =
                                    ((item.realTotal - (item.cost + (item.freight ?? 0))) / (item.cost || 1)) * 100;
                                item.profitMargin =
                                    ((item.realTotal - (item.cost + (item.freight ?? 0))) / (item.realTotal || 1)) *
                                    100;
                                item.profit = item.realTotal - (item.cost + (item.freight ?? 0));
                                item.unitProfit = item.profit / item.quantity;
                            }
                        }

                        return item;
                    });
                }
            }
        },
        async beforeItemsInit(items) {
            let rows = await this.$rpc('sale.init-quotation-items', {
                items,
                productFields: this.productFields
            });

            const documentType = this.documentType;
            if (
                !!documentType &&
                Array.isArray(documentType.additionalItemFields) &&
                documentType.additionalItemFields.length > 0
            ) {
                rows = rows.map(item => {
                    const additionalItemFields = item.additionalItemFields || {};

                    for (const key of Object.keys(additionalItemFields)) {
                        item[`additionalFields_${key}`] = additionalItemFields[key] || '';
                    }

                    return item;
                });

                this.additionalItemFields = fastCopy(documentType.additionalItemFields);
            }

            return rows;
        },
        async beforeSaveItem({row, originalRow, params}) {
            let field = params.colDef.field;
            let result = row;

            if (
                !result.itemGroup &&
                this.model.items.length > 0 &&
                !!this.model.items[this.model.items.length - 1].itemGroup
            ) {
                result.itemGroupColor = this.model.items[this.model.items.length - 1].itemGroupColor;
                result.itemGroup = this.model.items[this.model.items.length - 1].itemGroup;
            }
            if (field === 'itemGroup' && originalRow.itemGroup !== result.itemGroup && !!result.itemGroup) {
                const existing = this.model.items.find(
                    item => item.itemGroup === result.itemGroup && item.id !== result.id
                );
                if (!!existing && !!existing.itemGroupColor) {
                    result.itemGroupColor = existing.itemGroupColor;
                } else {
                    let testCount = 0;
                    while (true) {
                        testCount++;

                        const colors = Object.values(this.$app.palateMap);
                        const colorMap = colors[_.random(colors.length - 1)];

                        const color = colorMap[600].color;

                        if (this.model.items.some(item => item.itemGroupColor === color)) {
                            if (testCount > 50) {
                                break;
                            } else {
                                continue;
                            }
                        }

                        result.itemGroupColor = color;

                        break;
                    }
                }
            }

            try {
                result = (
                    await this.$rpc('sale.decorate-quotation-items', {
                        items: [row],
                        field,
                        model: _.omit(this.model, 'items'),
                        productFields: this.productFields
                    })
                )[0];
            } catch (error) {
                this.$program.message('error', error.message);

                result = originalRow;
            }

            return result;
        },
        async afterSaveItem() {
            this.model.paymentPlan = null;
            this.model.paymentPlanningDate = null;
            this.model.distributions = [];

            await this.handleChange(this.model, 'items');

            this.$nextTick(() => {
                this.calculateTotals();
            });
        },
        async afterRemoveItem() {
            if (this.model.items.length === 0) {
                this.model.discount = 0;
                this.model.discountAmount = 0;
            }

            await this.handleChange(this.model, 'items');

            this.calculateTotals();
        },
        async handleCheckItems(command) {
            this.$params('loading', true);

            try {
                if (command === 'check-availability') {
                    const items = await this.$rpc('sale.check-items-availability', this.model.items);

                    this.$set(this.model, 'items', items);
                    this.afterSaveItem();
                } else if (command === 'check-prices') {
                    const items = await this.$rpc('sale.decorate-quotation-items', {
                        items: this.model.items,
                        field: 'priceListId',
                        model: _.omit(this.model, 'items'),
                        productFields: this.productFields
                    });

                    this.$set(this.model, 'items', items);
                    this.afterSaveItem();
                } else if (command === 'update-exchange-rates') {
                    const model = this.model;
                    let items = fastCopy(this.model.items);
                    const ratesMap = {};

                    for (const item of items) {
                        if (!item.currencyId) {
                            continue;
                        }

                        const ratesKey = `${model.currencyId}-${item.currencyId}`;
                        let rate = 0;
                        if (!_.isFinite(ratesMap[ratesKey])) {
                            const company = this.$store.getters['session/company'];
                            const currencies = await this.$collection('kernel.currencies').find({
                                _id: {
                                    $in: [model.currencyId, item.currencyId, company.currencyId]
                                },
                                $select: ['name'],
                                $disableSoftDelete: true,
                                $disableActiveCheck: true
                            });
                            const formCurrency = currencies.find(currency => currency._id === item.currencyId);
                            const toCurrency = currencies.find(currency => currency._id === company.currencyId);

                            rate = await this.$convertCurrency({
                                from: formCurrency.name,
                                to: toCurrency.name,
                                value: 1,
                                options: {
                                    date: model.quotationDate
                                }
                            });
                            ratesMap[ratesKey] = rate;
                        } else {
                            rate = ratesMap[ratesKey];
                        }

                        if (rate > 0) {
                            item.currencyRate = rate;
                        } else {
                            item.currencyRate = 0;

                            continue;
                        }

                        item.unitPrice = item.unitPriceFC * rate;
                    }

                    items = await this.$rpc('sale.decorate-quotation-items', {
                        items,
                        field: 'unitPrice',
                        model: _.omit(this.model, 'items'),
                        productFields: this.productFields
                    });

                    this.$set(this.model, 'items', items);
                    this.afterSaveItem();
                }
            } catch (error) {
                this.$program.message('error', error.message);
            }

            this.$params('loading', false);
        },
        async handleUpdateItems(command) {
            try {
                if (command === 'update-item-prices') {
                    await this.handleUpdateItemsPrices();
                } else if (command === 'update-item-discounts') {
                    this.$program.dialog({
                        component: 'sale.sales.quotations.detail.apply-multiple-discounts',
                        params: {
                            discountPayload: {},
                            discount: 0,
                            forcedPreview: this.$params('isPreview')
                        },
                        onSubmit: async data => {
                            this.$params('loading', true);

                            try {
                                let items = fastCopy(this.model.items).map(item => {
                                    item.discountPayload = data;
                                    item.discount = data.discount;

                                    return item;
                                });

                                items = await this.$rpc('sale.decorate-quotation-items', {
                                    items,
                                    field: 'discount',
                                    model: this.model,
                                    productFields: this.productFields
                                });

                                this.$set(this.model, 'items', items);
                                this.afterSaveItem();
                            } catch (error) {
                                this.$program.message('error', error.message);
                            }

                            this.$params('loading', false);
                        }
                    });
                } else if (command === 'update-item-taxes') {
                    this.$program.dialog({
                        component: 'sale.sales.quotations.detail.apply-multiple-taxes',
                        params: {
                            taxPayload: {},
                            isReturn: this.isReturn,
                            forcedPreview: this.$params('isPreview')
                        },
                        onSubmit: async data => {
                            this.$params('loading', true);

                            try {
                                let items = fastCopy(this.model.items).map(item => {
                                    item.taxPayload =
                                        data.taxes.length > 0
                                            ? {
                                                  taxIds: data.taxes.map(tax => tax.taxId),
                                                  description: data.description
                                              }
                                            : null;

                                    return item;
                                });

                                items = await this.$rpc('sale.decorate-quotation-items', {
                                    items,
                                    field: 'taxPayload',
                                    model: this.model,
                                    productFields: this.productFields
                                });

                                this.$set(this.model, 'items', items);
                                this.afterSaveItem();
                            } catch (error) {
                                this.$program.message('error', error.message);
                            }

                            this.$params('loading', false);
                        }
                    });
                }
            } catch (error) {
                this.$program.message('error', error.message);
            }
        },
        handleUpdateItemsPrices() {
            this.$program.dialog({
                component: 'sale.sales.quotations.detail.mass-item-price-change',
                params: {
                    title: this.$t('Update Prices')
                },
                onSubmit: async ({changeType, operation, value}) => {
                    this.$params('loading', true);

                    const items = await this.$rpc('sale.decorate-quotation-items', {
                        items: this.model.items.map(item => {
                            if (changeType === 'linear') {
                                if (operation === 'multiply') {
                                    item.unitPrice = item.unitPrice * value;
                                } else if (operation === 'divide' && value > 0) {
                                    item.unitPrice = item.unitPrice / value;
                                } else if (operation === 'add') {
                                    item.unitPrice = item.unitPrice + value;
                                } else if (operation === 'subtract') {
                                    item.unitPrice = item.unitPrice - value;
                                }
                            } else if (changeType === 'percentage') {
                                if (operation === 'add') {
                                    item.unitPrice = item.unitPrice + item.unitPrice * (value / 100);
                                } else if (operation === 'subtract') {
                                    item.unitPrice = item.unitPrice - item.unitPrice * (value / 100);
                                }
                            }

                            return item;
                        }),
                        field: 'unitPrice',
                        model: _.omit(this.model, 'items'),
                        productFields: this.productFields
                    });

                    this.$set(this.model, 'items', items);
                    this.afterSaveItem();

                    this.$params('loading', false);
                }
            });
        },
        handleOpenCampaigns() {
            const items = [];
            for (const mi of this.model.items || []) {
                const item = {
                    id: mi.id,
                    productId: mi.productId,
                    productCode: mi.productCode,
                    productDefinition: mi.productDefinition,
                    quantity: mi.quantity
                };

                item.total = mi.unitPrice * mi.quantity;
                item.grossTotal = mi.total > 0 ? (item.total * mi.grossTotal) / mi.total : 0;

                items.push(item);
            }
            const subTotal = _.sumBy(items, 'total');
            const grandTotal = _.sumBy(items, 'grossTotal');

            this.$program.dialog({
                component: 'sale.components.campaigns',
                params: {
                    title: this.$t('Campaigns'),
                    scope: 'quotation',
                    date: this.model.quotationDate,
                    documentId: this.$params('id'),
                    partnerId: this.model.partnerId,
                    grandTotal,
                    items
                },
                onSelect: async campaigns => {
                    this.$params('loading', true);

                    const campaignIds = _.uniq(
                        (this.model.campaigns || [])
                            .map(ec => ec.campaignId)
                            .concat(campaigns.map(campaign => campaign._id))
                    );
                    const result = await this.$rpc('sale.apply-campaigns', {
                        campaignIds,
                        subTotal,
                        grandTotal,
                        items
                    });

                    const newItems = await this.$rpc('sale.decorate-quotation-items', {
                        items: fastCopy(this.model.items).map(item => {
                            const ri = result.items.find(ri => ri.id === item.id);

                            if ((this.model.campaigns || []).some(c => c.itemId === item.id)) {
                                item.discount = 0;
                            }

                            if (!!ri) {
                                item.discount = ri.discount || 0;
                            }

                            return item;
                        }),
                        field: 'discount',
                        model: _.omit(this.model, 'items'),
                        productFields: this.productFields
                    });

                    this.model.campaigns = result.campaigns;
                    this.model.discount = Math.min(result.discount, 100);
                    this.$set(this.model, 'items', newItems);

                    await this.afterSaveItem();

                    this.$params('loading', false);
                }
            });
        },
        handleApplyCouponCode() {
            const items = [];
            for (const mi of this.model.items || []) {
                const item = {
                    id: mi.id,
                    productId: mi.productId,
                    productCode: mi.productCode,
                    productDefinition: mi.productDefinition,
                    quantity: mi.quantity
                };

                item.total = mi.unitPrice * mi.quantity;
                item.grossTotal = mi.total > 0 ? (item.total * mi.grossTotal) / mi.total : 0;

                items.push(item);
            }
            const subTotal = _.sumBy(items, 'total');
            const grandTotal = _.sumBy(items, 'grossTotal');

            this.$program.dialog({
                component: 'sale.components.apply-coupon-code',
                params: {
                    title: this.$t('Apply Coupon Code'),
                    scope: 'quotation',
                    date: this.model.quotationDate,
                    documentId: this.$params('id'),
                    partnerId: this.model.partnerId,
                    grandTotal,
                    items
                },
                onSubmit: async ({couponCode, campaigns}) => {
                    this.$params('loading', true);

                    const campaignIds = _.uniq(
                        (this.model.campaigns || [])
                            .map(ec => ec.campaignId)
                            .concat(campaigns.map(campaign => campaign._id))
                    );
                    const result = await this.$rpc('sale.apply-campaigns', {
                        campaignIds,
                        couponCode,
                        subTotal,
                        grandTotal,
                        items
                    });

                    const newItems = await this.$rpc('sale.decorate-quotation-items', {
                        items: fastCopy(this.model.items).map(item => {
                            const ri = result.items.find(ri => ri.id === item.id);

                            if ((this.model.campaigns || []).some(c => c.itemId === item.id)) {
                                item.discount = 0;
                            }

                            if (!!ri) {
                                item.discount = ri.discount || 0;
                            }

                            return item;
                        }),
                        field: 'discount',
                        model: _.omit(this.model, 'items'),
                        productFields: this.productFields
                    });

                    this.model.campaigns = result.campaigns;
                    this.model.discount = Math.min(result.discount, 100);
                    this.$set(this.model, 'items', newItems);

                    await this.afterSaveItem();

                    this.$params('loading', false);
                }
            });
        },
        async handleClearCampaigns() {
            this.$params('loading', true);

            const newItems = await this.$rpc('sale.decorate-quotation-items', {
                items: fastCopy(this.model.items).map(item => {
                    item.discount = 0;

                    return item;
                }),
                field: 'discount',
                model: _.omit(this.model, 'items'),
                productFields: this.productFields
            });
            this.model.campaigns = [];
            this.model.discount = 0;
            this.$set(this.model, 'items', newItems);

            await this.afterSaveItem();

            this.$params('loading', false);
        },
        handleAddMultipleProducts() {
            const self = this;

            const filters = {
                isSimple: true,
                canBeSold: true
            };

            if (this.model.module === 'service') {
                filters.isServiceProduct = true;
            }

            if (_.isObject(this.organizationSettings) && !_.isEmpty(this.organizationSettings)) {
                filters.groupIds = {
                    $in: this.organizationSettings.productGroupIds || []
                };
            }

            let allowedPriceListIds = null;
            if (_.isObject(this.organizationSettings) && !_.isEmpty(this.organizationSettings)) {
                allowedPriceListIds = this.organizationSettings.priceListIds || [];
            }
            this.$program.dialog({
                component: 'inventory.components.product-finder',
                params: {
                    module: 'sale',
                    priceListId: this.model.priceListId,
                    warehouseId: this.model.warehouseId,
                    issueDate: this.model.quotationDate,
                    allowedPriceListIds,
                    filters,
                    onSelect: async selected => {
                        self.$params('loading', true);

                        const items = await self.$rpc('sale.decorate-quotation-items', {
                            items: selected.map(s => {
                                return {
                                    productId: s._id,
                                    scheduledDate: new Date(),
                                    quantity: s.quantity,
                                    baseQuantity: 1,
                                    unitPrice: 0,
                                    discount: 0,
                                    unitPriceAfterDiscount: 0,
                                    grossUnitPriceAfterDiscount: 0,
                                    taxTotal: 0,
                                    grossTotal: 0,
                                    stockQuantity: 0,
                                    orderedQuantity: 0,
                                    assignedQuantity: 0,
                                    availableQuantity: 0,
                                    warehouseStockQuantity: 0,
                                    warehouseOrderedQuantity: 0,
                                    warehouseAssignedQuantity: 0,
                                    warehouseAvailableQuantity: 0,
                                    total: 0
                                };
                            }),
                            field: 'productId',
                            model: _.omit(self.model, 'items'),
                            productFields: self.productFields
                        });

                        self.$set(self.model, 'items', fastCopy(self.model.items).concat(items));
                        self.afterSaveItem();
                        self.$params('loading', false);
                    }
                }
            });

            // this.$program.dialog({
            //     component: 'inventory.catalog.products.master',
            //     params: {filters},
            //     async onSelect(products) {
            //         self.$params('loading', true);
            //
            //         const items = await self.$rpc('sale.decorate-quotation-items', {
            //             items: products.map(product => {
            //                 return {
            //                     productId: product._id,
            //                     scheduledDate: new Date(),
            //                     quantity: 1,
            //                     baseQuantity: 1,
            //                     unitPrice: 0,
            //                     discount: 0,
            //                     unitPriceAfterDiscount: 0,
            //                     grossUnitPriceAfterDiscount: 0,
            //                     taxTotal: 0,
            //                     grossTotal: 0,
            //                     stockQuantity: 0,
            //                     orderedQuantity: 0,
            //                     assignedQuantity: 0,
            //                     availableQuantity: 0,
            //                     warehouseStockQuantity: 0,
            //                     warehouseOrderedQuantity: 0,
            //                     warehouseAssignedQuantity: 0,
            //                     warehouseAvailableQuantity: 0,
            //                     total: 0
            //                 };
            //             }),
            //             field: 'productId',
            //             model: _.omit(self.model, 'items'),
            //             productFields: self.productFields
            //         });
            //
            //         self.$set(self.model, 'items', fastCopy(self.model.items).concat(items));
            //         self.afterSaveItem();
            //         self.$params('loading', false);
            //     }
            // });
        },
        handleAddKitProducts() {
            const self = this;

            const filters = {
                isKit: true,
                canBeSold: true
            };

            if (this.model.module === 'service') {
                filters.isServiceProduct = true;
            }

            if (_.isObject(this.organizationSettings) && !_.isEmpty(this.organizationSettings)) {
                filters.groupIds = {
                    $in: this.organizationSettings.productGroupIds || []
                };
            }

            this.$program.dialog({
                component: 'inventory.catalog.products.master',
                params: {filters},
                async onSelect(selected) {
                    self.$params('loading', true);

                    const kits = await self.$collection('inventory.products').find({
                        _id: {$in: selected.map(s => s._id)},
                        $select: ['_id', 'subProducts', 'useSubProducts'],
                        $disableActiveCheck: true,
                        $disableSoftDelete: true
                    });
                    const rows = [];

                    for (let kit of kits.filter(k => Array.isArray(k.subProducts))) {
                        if (kit.useSubProducts) {
                            for (const subProduct of kit.subProducts.filter(sp => !!sp.canBeSold)) {
                                rows.push({
                                    productId: subProduct.productId,
                                    scheduledDate: new Date(),
                                    quantity: subProduct.quantity || 1,
                                    baseQuantity: 1,
                                    unitPrice: 0,
                                    discount: 0,
                                    unitPriceAfterDiscount: 0,
                                    grossUnitPriceAfterDiscount: 0,
                                    taxTotal: 0,
                                    grossTotal: 0,
                                    stockQuantity: 0,
                                    orderedQuantity: 0,
                                    assignedQuantity: 0,
                                    availableQuantity: 0,
                                    warehouseStockQuantity: 0,
                                    warehouseOrderedQuantity: 0,
                                    warehouseAssignedQuantity: 0,
                                    warehouseAvailableQuantity: 0,
                                    total: 0
                                });
                            }
                        } else {
                            rows.push({
                                productId: kit._id,
                                scheduledDate: new Date(),
                                quantity: 1,
                                baseQuantity: 1,
                                unitPrice: 0,
                                discount: 0,
                                unitPriceAfterDiscount: 0,
                                grossUnitPriceAfterDiscount: 0,
                                taxTotal: 0,
                                grossTotal: 0,
                                stockQuantity: 0,
                                orderedQuantity: 0,
                                assignedQuantity: 0,
                                availableQuantity: 0,
                                warehouseStockQuantity: 0,
                                warehouseOrderedQuantity: 0,
                                warehouseAssignedQuantity: 0,
                                warehouseAvailableQuantity: 0,
                                total: 0
                            });
                        }
                    }

                    const items = await self.$rpc('sale.decorate-quotation-items', {
                        items: rows,
                        field: 'productId',
                        model: _.omit(self.model, 'items'),
                        productFields: self.productFields
                    });

                    self.$set(self.model, 'items', fastCopy(self.model.items).concat(items));
                    self.afterSaveItem();
                    self.$params('loading', false);
                }
            });
        },
        handleClusterProducts() {
            const filters = {};

            if (_.isObject(this.organizationSettings) && !_.isEmpty(this.organizationSettings)) {
                filters._id = {
                    $in: this.organizationSettings.productClusterIds || []
                };
            }

            this.$program.dialog({
                component: 'inventory.configuration.product-clusters.master',
                params: {filters},
                onSelect: async selected => {
                    this.$params('loading', true);

                    const products = await this.$collection('inventory.cluster-products').find({
                        clusterId: {$in: selected.map(s => s._id)},
                        canBeSold: true
                    });
                    const simpleProducts = products.filter(p => !p.isKit);
                    const kitProducts = products.filter(p => !!p.isKit);

                    // Add products.
                    if (simpleProducts.length > 0) {
                        const items = await this.$rpc('sale.decorate-quotation-items', {
                            items: simpleProducts.map(product => {
                                return {
                                    productId: product.productId,
                                    scheduledDate: new Date(),
                                    unitId: product.unitId,
                                    quantity: product.quantity,
                                    baseQuantity: 1,
                                    unitPrice: 0,
                                    discount: 0,
                                    unitPriceAfterDiscount: 0,
                                    grossUnitPriceAfterDiscount: 0,
                                    taxTotal: 0,
                                    grossTotal: 0,
                                    stockQuantity: 0,
                                    orderedQuantity: 0,
                                    assignedQuantity: 0,
                                    availableQuantity: 0,
                                    warehouseStockQuantity: 0,
                                    warehouseOrderedQuantity: 0,
                                    warehouseAssignedQuantity: 0,
                                    warehouseAvailableQuantity: 0,
                                    total: 0
                                };
                            }),
                            field: 'productId',
                            model: _.omit(this.model, 'items'),
                            productFields: this.productFields
                        });

                        this.$set(this.model, 'items', fastCopy(this.model.items).concat(items));
                        this.afterSaveItem();
                    }

                    if (kitProducts.length > 0) {
                        const kits = await this.$collection('inventory.products').find({
                            _id: {$in: kitProducts.map(s => s.productId)},
                            $select: ['_id', 'subProducts', 'useSubProducts'],
                            $disableActiveCheck: true,
                            $disableSoftDelete: true
                        });
                        const rows = [];

                        for (let kit of kits.filter(k => Array.isArray(k.subProducts))) {
                            const kitProduct = kitProducts.find(kp => kp.productId === kit._id);

                            if (kit.useSubProducts) {
                                for (const subProduct of kit.subProducts.filter(sp => !!sp.canBeSold)) {
                                    rows.push({
                                        productId: subProduct.productId,
                                        scheduledDate: new Date(),
                                        quantity: (subProduct.quantity || 1) * kitProduct.quantity,
                                        baseQuantity: 1,
                                        unitPrice: 0,
                                        discount: 0,
                                        unitPriceAfterDiscount: 0,
                                        grossUnitPriceAfterDiscount: 0,
                                        taxTotal: 0,
                                        grossTotal: 0,
                                        stockQuantity: 0,
                                        orderedQuantity: 0,
                                        assignedQuantity: 0,
                                        availableQuantity: 0,
                                        warehouseStockQuantity: 0,
                                        warehouseOrderedQuantity: 0,
                                        warehouseAssignedQuantity: 0,
                                        warehouseAvailableQuantity: 0,
                                        total: 0
                                    });
                                }
                            } else {
                                rows.push({
                                    productId: kit._id,
                                    scheduledDate: new Date(),
                                    unitId: kitProduct.unitId,
                                    quantity: kitProduct.quantity,
                                    baseQuantity: 1,
                                    unitPrice: 0,
                                    discount: 0,
                                    unitPriceAfterDiscount: 0,
                                    grossUnitPriceAfterDiscount: 0,
                                    taxTotal: 0,
                                    grossTotal: 0,
                                    stockQuantity: 0,
                                    orderedQuantity: 0,
                                    assignedQuantity: 0,
                                    availableQuantity: 0,
                                    warehouseStockQuantity: 0,
                                    warehouseOrderedQuantity: 0,
                                    warehouseAssignedQuantity: 0,
                                    warehouseAvailableQuantity: 0,
                                    total: 0
                                });
                            }
                        }

                        const items = await this.$rpc('sale.decorate-quotation-items', {
                            items: rows,
                            field: 'productId',
                            model: _.omit(this.model, 'items'),
                            productFields: this.productFields
                        });

                        this.$set(this.model, 'items', fastCopy(this.model.items).concat(items));
                        this.afterSaveItem();
                    }

                    this.$params('loading', false);
                }
            });
        },
        handleAddProductWithBarcode() {
            this.$program.dialog({
                component: 'inventory.components.barcode-finder',
                params: {
                    title: this.$t('Add Product With Barcode')
                },
                onSubmit: async ({items}) => {
                    this.$params('loading', true);

                    const rows = await this.$rpc('sale.decorate-quotation-items', {
                        items: items.map(item => {
                            return {
                                productId: item.productId,
                                scheduledDate: new Date(),
                                quantity: item.quantity,
                                baseQuantity: item.baseQuantity,
                                unitPrice: 0,
                                discount: 0,
                                unitPriceAfterDiscount: 0,
                                grossUnitPriceAfterDiscount: 0,
                                taxTotal: 0,
                                grossTotal: 0,
                                stockQuantity: 0,
                                orderedQuantity: 0,
                                assignedQuantity: 0,
                                availableQuantity: 0,
                                warehouseStockQuantity: 0,
                                warehouseOrderedQuantity: 0,
                                warehouseAssignedQuantity: 0,
                                warehouseAvailableQuantity: 0,
                                total: 0
                            };
                        }),
                        field: 'productId',
                        model: _.omit(this.model, 'items'),
                        productFields: this.productFields
                    });

                    this.$set(this.model, 'items', fastCopy(this.model.items).concat(rows));
                    this.afterSaveItem();
                    this.$params('loading', false);
                }
            });
        },
        async handleImportItems(payload) {
            this.$params('loading', true);

            try {
                const items = await this.$rpc('sale.import-quotation-items', {
                    fileId: payload._id,
                    model: _.omit(this.model, 'items'),
                    productFields: this.productFields
                });

                if (items.length > 0) {
                    this.$set(this.model, 'items', fastCopy(this.model.items).concat(items));
                    this.afterSaveItem();
                }
            } catch (error) {
                this.$program.message('error', error.message);
            }

            this.$params('loading', false);
        },
        handleRemoveAllItems() {
            this.$set(this.model, 'items', []);
            this.afterSaveItem();
            this.model.partialDeliveries = [];
        },
        handleCalculateGrossProfit() {
            this.$program.dialog({
                component: 'sale.components.gross-profit-calculator',
                params: {
                    id: this.$params('id'),
                    collection: 'sale.quotations',
                    forcedPreview: false,
                    currencyId: this.model.currencyId,
                    currencyRate: this.model.currencyRate,
                    date: this.model.quotationDate,
                    currencyFormat: this.currencyFormat,
                    exchangeRates: this.model.exchangeRates ?? [],
                    items: this.model.items || []
                }
            });
        },
        async handleUnitPriceAnalysis() {
            if (!this.$params('id')) {
                this.$program.message('error', this.$t('The document must be saved for unit price analysis!'));

                return;
            }

            let allowedUnitPriceAnalysisTypes = this.$setting('sale.allowedUnitPriceAnalysisTypes');
            if (
                _.isObject(this.organizationSettings) &&
                Array.isArray(this.organizationSettings.allowedUnitPriceAnalysisTypes)
            ) {
                allowedUnitPriceAnalysisTypes = this.organizationSettings.allowedUnitPriceAnalysisTypes;
            } else if (this.$setting('sale.salesOrganizations')) {
                allowedUnitPriceAnalysisTypes = [];
            }

            const organizationSettings = this.organizationSettings ?? {};
            let allowedCostSourceOptions = [];
            const priceLists = (
                await this.$collection('sale.price-lists').find({
                    status: 'published',
                    isBasePriceList: true,
                    validFrom: {
                        $lte: this.model.quotationDate
                    },
                    validTo: {
                        $gte: this.model.quotationDate
                    },
                    $select: ['code', 'name']
                })
            ).map(priceList => {
                const item = {};

                item._id = priceList._id;
                item.name = `${priceList.code} - ${priceList.name}`;

                return item;
            });
            let profitBases = [];
            profitBases.push({
                _id: 'manuel',
                name: this.$t('Manuel')
            });
            profitBases.push({
                _id: 'product-cost',
                name: this.$t('Product cost')
            });
            profitBases.push({
                _id: 'last-purchase-price',
                name: this.$t('Last purchase price')
            });
            profitBases.push({
                _id: 'forecasted-purchase-price',
                name: this.$t('Forecasted purchase price')
            });
            profitBases = profitBases.concat(priceLists);
            allowedCostSourceOptions = profitBases.map(profitBase => ({value: profitBase._id, label: profitBase.name}));
            if (!!this.$setting('sale.salesOrganizations') && !organizationSettings.canSeeAllProfitBases) {
                allowedCostSourceOptions = allowedCostSourceOptions.filter(
                    o => (organizationSettings.profitBases || []).indexOf(o.value) !== -1
                );
            }

            const profit = await this.$collection('sale.document-profits').findOne({
                documentId: this.$params('id')
            });
            let items = fastCopy(this.model.items) ?? [];
            if (!!profit) {
                items = items.map(item => {
                    if (!item.costSource && !!profit.defaultProfitBase) {
                        item.costSource = profit.defaultProfitBase;
                    }

                    if (!!item.costSource) {
                        const profitItem = (profit.items ?? []).find(
                            profitItem =>
                                profitItem.profitBase === item.costSource &&
                                profitItem.productId === item.productId &&
                                profitItem.unitId === item.unitId &&
                                profitItem.quantity === item.quantity &&
                                profitItem.warehouseId === item.warehouseId
                        );

                        if (!!profitItem) {
                            if (!_.isFinite(item.unitCost) || item.unitCost === 0) {
                                item.unitCost =
                                    (profitItem.basePrice ?? 0) /
                                    (this.model.currencyRate !== 0 ? this.model.currencyRate : 1);
                            }
                            item.cost = item.unitCost * item.quantity;
                            item.profitRate =
                                ((item.realTotal - (item.cost + (item.freight ?? 0))) / (item.cost || 1)) * 100;
                            item.profitMargin =
                                ((item.realTotal - (item.cost + (item.freight ?? 0))) / (item.realTotal || 1)) * 100;
                            item.profit = item.realTotal - (item.cost + (item.freight ?? 0));
                            item.unitProfit = item.profit / item.quantity;
                        }
                    }

                    return item;
                });
            }
            if (
                items.some(
                    item => !!item.costSource && !allowedCostSourceOptions.some(o => o.value === item.costSource)
                )
            ) {
                this.$program.message(
                    'error',
                    this.$t('You do not allowed to see the necessary cost sources for unit price analysis!')
                );

                return;
            }

            this.$program.dialog({
                component: 'sale.components.bulk-unit-price-analysis',
                params: {
                    documentCollection: 'sale.quotations',
                    documentId: this.$params('id'),
                    documentCode: this.model.code,
                    partnerId: this.model.partnerId,
                    leadId: this.model.leadId,
                    items,
                    currencyId: this.model.currencyId,
                    currencyRate: this.model.currencyRate,
                    currencyFormat: this.currencyFormat,
                    allowedUnitPriceAnalysisTypes,
                    allowedCostSourceOptions,
                    forcedPreview: this.$params('isPreview'),
                    handleApply: async row => {
                        const items = fastCopy(this.model.items);
                        const rowIndex = items.findIndex(item => item.id === row.id);

                        items[rowIndex].costSource = row.costSource;
                        items[rowIndex].unitCost = row.unitCost;
                        items[rowIndex].unitPrice = row.unitPrice;
                        items[rowIndex].unitProfit = row.unitProfit;
                        items[rowIndex].cost = row.cost;
                        items[rowIndex].profitRate = row.profitRate;
                        items[rowIndex].profitMargin = row.profitMargin;
                        items[rowIndex].profit = row.profit;

                        items[rowIndex] = (
                            await this.$rpc('sale.decorate-quotation-items', {
                                items: [items[rowIndex]],
                                field: 'unitPrice',
                                model: this.model,
                                productFields: this.productFields
                            })
                        )[0];

                        this.$set(this.model, 'items', items);
                        this.afterSaveItem();
                    }
                }
            });
        },
        handlePartialDelivery() {
            this.$program.dialog({
                component: 'sale.components.partial-delivery',
                params: {
                    partnerId: this.model.partnerId,
                    deliveryAddress: this.model.deliveryAddress,
                    items: this.model.items || [],
                    partialDeliveries: this.model.partialDeliveries || [],
                    forcedPreview:
                        this.$params('isPreview') ||
                        this.model.status === 'approved' ||
                        this.model.status === 'canceled' ||
                        this.$params('isPreview')
                },
                onSubmit: ({partialDeliveries}) => {
                    this.model.partialDeliveries = partialDeliveries;
                    this.model.deliveryAddress = partialDeliveries.find(
                        pd => pd.address.type === 'document-delivery-address'
                    ).address;
                }
            });
        },
        async handleOpenAutomaticPaymentPlan() {
            const self = this;
            this.$params('loading', true);

            const grandTotal = await this.calculateOriginalGrandTotal();
            let paymentTermIds = null;
            if (_.isObject(this.organizationSettings) && !_.isEmpty(this.organizationSettings)) {
                paymentTermIds = this.organizationSettings.paymentTermIds || null;
            }

            this.$program.dialog({
                component: 'finance.components.pp-automatic',
                params: {
                    paymentTermIds,
                    currencyId: this.model.currencyId,
                    branchId: this.model.branchId,
                    currencyFormat: this.currencyFormat,
                    recordDate: this.model.recordDate,
                    issueDate: this.model.quotationDate,
                    amount: grandTotal,
                    discount: this.model.discount,
                    isReceipt: true,
                    isPayment: false,
                    paymentTermId: this.model.paymentTermId,
                    enableSelection: !(
                        this.status === 'won' ||
                        this.status === 'lost' ||
                        this.status === 'lost-to-competitor' ||
                        this.status === 'payment-planned' ||
                        this.status === 'canceled' ||
                        this.$params('isPreview')
                    ),
                    onSelectPayment({paymentTermId}) {
                        self.model.paymentTermId = paymentTermId;

                        self.handleChange(self.model, 'paymentTermId');
                    }
                }
            });
        },
        handleApplyScheduledDate() {
            if (this.model.items.length > 0) {
                this.$program.alert('confirm', this.$t('Do you want to apply this to all rows?'), confirmed => {
                    if (confirmed) {
                        this.model.items = fastCopy(this.model.items).map(item => {
                            item.scheduledDate = this.model.scheduledDate;

                            return item;
                        });
                    }
                });
            }
        },
        handleApplyBranch() {
            if (this.model.items.length > 0) {
                this.$program.alert('confirm', this.$t('Do you want to apply this to all rows?'), async confirmed => {
                    if (confirmed) {
                        const items = [];

                        for (const item of fastCopy(this.model.items)) {
                            item.branchId = this.model.branchId;

                            const warehouse = await this.$collection('inventory.warehouses').findOne({
                                branchId: item.branchId,
                                $select: ['_id'],
                                $disableActiveCheck: true,
                                $disableSoftDelete: true
                            });

                            if (_.isObject(warehouse)) {
                                item.warehouseId = warehouse._id;
                            } else {
                                item.warehouseId = '';
                            }

                            items.push(item);
                        }

                        this.model.items = items;
                    }
                });
            }
        },
        handleApplyWarehouse() {
            if (this.model.items.length > 0) {
                this.$program.alert('confirm', this.$t('Do you want to apply this to all rows?'), async confirmed => {
                    if (confirmed) {
                        this.model.items = await this.$rpc('sale.decorate-quotation-items', {
                            items: this.model.items.map(item => {
                                item.warehouseId = this.model.warehouseId;

                                return item;
                            }),
                            field: 'warehouseId',
                            model: this.model,
                            productFields: this.productFields
                        });
                    }
                });
            }
        },
        handleApplyDeliveryAddress() {
            if (this.model.items.length > 0) {
                this.$program.alert('confirm', this.$t('Do you want to apply this to all rows?'), confirmed => {
                    if (confirmed) {
                        this.model.items = fastCopy(this.model.items).map(item => {
                            item.deliveryAddressId = this.model.deliveryAddressId;
                            item.deliveryAddressCode = this.model.deliveryAddressCode;
                            item.deliveryAddress = this.model.deliveryAddress;

                            return item;
                        });
                    }
                });
            }
        },
        handleApplyPaymentAddress() {
            if (this.model.items.length > 0) {
                this.$program.alert('confirm', this.$t('Do you want to apply this to all rows?'), confirmed => {
                    if (confirmed) {
                        this.model.items = fastCopy(this.model.items).map(item => {
                            item.paymentAddressId = this.model.paymentAddressId;
                            item.paymentAddress = this.model.paymentAddress;

                            return item;
                        });
                    }
                });
            }
        },
        handleApplyInvoiceAddress() {
            if (this.model.items.length > 0) {
                this.$program.alert('confirm', this.$t('Do you want to apply this to all rows?'), confirmed => {
                    if (confirmed) {
                        this.model.items = fastCopy(this.model.items).map(item => {
                            item.invoiceAddressId = this.model.invoiceAddressId;
                            item.invoiceAddress = this.model.invoiceAddress;

                            return item;
                        });
                    }
                });
            }
        },
        async handleApplyMultipleDiscounts({rowId, discountPayload}) {
            const items = fastCopy(this.model.items);
            const rowIndex = items.findIndex(item => item.id === rowId);

            items[rowIndex].discountPayload = discountPayload;
            items[rowIndex].discount = discountPayload.discount;

            items[rowIndex] = (
                await this.$rpc('sale.decorate-quotation-items', {
                    items: [items[rowIndex]],
                    field: 'discount',
                    model: this.model,
                    productFields: this.productFields
                })
            )[0];

            this.$set(this.model, 'items', items);
            this.afterSaveItem();
        },
        async handleApplyMultipleTaxes({rowId, taxPayload}) {
            const items = fastCopy(this.model.items);
            const rowIndex = items.findIndex(item => item.id === rowId);

            items[rowIndex].taxPayload = taxPayload;

            items[rowIndex] = (
                await this.$rpc('sale.decorate-quotation-items', {
                    items: [items[rowIndex]],
                    field: 'taxPayload',
                    model: this.model,
                    productFields: this.productFields
                })
            )[0];

            this.$set(this.model, 'items', items);
            this.afterSaveItem();
        },
        async handlePaymentPlanned() {
            const self = this;
            const grandTotal = await this.calculateOriginalGrandTotal();

            // Prepare params.
            const params = {};
            params.amount = grandTotal;
            params.discount = this.model.discount;
            // params.rounding = this.model.rounding;
            params.partnerId = this.model.partnerId;
            params.currencyId = this.model.currencyId;
            params.currencyRate = this.model.currencyRate;
            params.currencyFormat = this.currencyFormat;
            params.checkOpenAmount = true;
            params.isReceipt = true;
            params.isPayment = false;
            params.guaranteeId = this.model.guaranteeId;
            params.paymentTermId = this.model.paymentTermId;
            params.paymentTerm = this.model.paymentTerm;
            params.branchId = this.model.branchId;
            params.recordDate = this.model.recordDate;
            params.issueDate = this.model.quotationDate;
            params.reference = this.model.code;
            params.description = this.$t('Receipts');
            params.items =
                _.isObject(this.model.paymentPlan) && Array.isArray(this.model.paymentPlan.items)
                    ? this.model.paymentPlan.items
                    : [];
            if (_.isObject(this.model.paymentPlan) && !_.isEmpty(this.model.paymentPlan)) {
                params.manuelPlanned = !!this.model.paymentPlan.manuelPlanned;
                params.total = this.model.paymentPlan.total;
                params.discount = this.model.paymentPlan.discount;
                params.rounding = this.model.paymentPlan.rounding;
                params.openAmount = this.model.paymentPlan.openAmount;
                params.baseDate = this.model.paymentPlan.baseDate;
                params.report = this.model.paymentPlan.report;
                params.sectionReport = this.model.paymentPlan.sectionReport;
            }
            params.forcedPreview =
                this.$params('isPreview') ||
                this.model.status === 'approved' ||
                this.model.status === 'canceled' ||
                this.$params('isPreview');

            if (this.model.salespersonId) {
                params.issuedBy = this.model.salespersonId;
            }

            async function handleSubmit(data) {
                self.$params('loading', true);

                // Make backup.
                let backup = self.model.paymentPlanBackup;
                if (!_.isObject(backup) || _.isEmpty(backup)) {
                    backup = _.pick(self.model, ['items', 'subTotal']);
                    backup.items = fastCopy(backup.items);

                    self.model.paymentPlanBackup = backup;
                }

                // Get model.
                const model = _.assign(fastCopy(self.model), fastCopy(backup));

                // Get totals.
                let rawTotal = data.total - data.rounding;
                let taxTotal = 0;
                let fixedTaxAmount = 0;
                if (data.discount > 0) {
                    rawTotal = rawTotal / (1 - data.discount / 100);
                }
                model.items.forEach(row => {
                    row.taxDetail.applied.forEach(tax => {
                        if (tax.computation === 'fixed') {
                            fixedTaxAmount += tax.appliedAmount;
                        }

                        taxTotal += tax.appliedAmount;
                    });
                });

                // Get new sub total.
                const taxTotalWithoutFixedTaxes = taxTotal - fixedTaxAmount;
                const grandTotalWithoutFixedTaxes = rawTotal - fixedTaxAmount;
                const ratio = taxTotalWithoutFixedTaxes / model.subTotal;
                const newSubtotal = grandTotalWithoutFixedTaxes / (1 + ratio);

                // Update unit prices.
                const diff = newSubtotal - model.subTotal;
                const diffRatio = diff / model.subTotal;
                const newItems = [];
                for (let item of fastCopy(model.items)) {
                    item.unitPrice += item.unitPrice * diffRatio;

                    item = await self.calculateRowTotals(item, 'unitPrice', {
                        subTotal: self.$app.round(newSubtotal, 'total'),
                        discountAmount: self.$app.round((newSubtotal * data.discount) / 100, 'total')
                    });

                    newItems.push(item);
                }
                self.$set(self.model, 'items', newItems);

                // Update rounding.
                // self.$set(self.model, 'rounding', data.rounding);

                // Update totals.
                self.$set(self.model, 'subTotal', self.$app.round(newSubtotal, 'total'));
                self.$nextTick(async () => {
                    // Find result.
                    const result = await self.calculateTotals(true, data.discount);

                    // Check rounding.
                    if (data.total !== result.grandTotal) {
                        const diff = data.total - result.grandTotal;
                        // const rounding = data.rounding + diff;
                        const rounding = diff;

                        result.grandTotal += diff;

                        self.$set(self.model, 'rounding', rounding);
                    }

                    self.$set(self.model, 'status', 'payment-planned');
                    self.$set(self.model, 'discount', data.discount);
                    self.$set(self.model, 'discountAmount', result.discountAmount);
                    self.$set(self.model, 'subTotalAfterDiscount', result.subTotalAfterDiscount);
                    self.$set(self.model, 'taxTotal', result.taxTotal);
                    self.$set(self.model, 'grandTotal', result.grandTotal);
                    self.$set(self.model, 'appliedTaxes', result.appliedTaxes);

                    // Prepare payment plan.
                    const paymentPlan = fastCopy(_.omit(data, 'paymentTerm'));
                    self.$set(self.model, 'paymentPlanningDate', data.baseDate);
                    self.$set(self.model, 'paymentTerm', data.paymentTerm);
                    self.$set(self.model, 'paymentPlan', paymentPlan);

                    // Calculate profit
                    let profitBase = null;
                    if (_.isObject(self.organizationSettings) && self.organizationSettings.defaultProfitBase) {
                        profitBase = self.organizationSettings.defaultProfitBase || this.$setting('sale.profitBase');
                    } else {
                        profitBase = self.$setting('sale.profitBase');
                    }
                    if (self.$params('id') && Array.isArray(self.model.items) && self.model.items.length > 0) {
                        self.$rpc('sale.update-document-profits', {
                            profitBase,
                            documentId: self.$params('id'),
                            documentCollection: 'sale.quotations',
                            currencyId: self.model.currencyId,
                            currencyRate: self.model.currencyRate,
                            date: self.model.quotationDate,
                            exchangeRates: self.model.exchangeRates ?? [],
                            items: self.model.items.map(item => ({
                                productId: item.productId,
                                warehouseId: item.warehouseId,
                                quantity: item.quantity,
                                unitId: item.unitId,
                                freight: item.freight || 0,
                                totalSalesPrice: item.realTotal
                            }))
                        });
                    }

                    self.$params('loading', false);
                });
            }

            // Open payment plan.
            self.$program.dialog({
                component: 'finance.components.pp',
                params,
                onSubmit(data) {
                    self.$nextTick(async () => {
                        await handleSubmit(data);
                    });
                }
            });
        },
        handleStageClick(index) {
            if (this.activeStageIndex !== index && !this.$params('isPreview')) {
                const nextStage = this.stages[index];

                this.model.stageId = nextStage._id;
                this.handleChange(this.model, 'stageId');
            }
        },
        handleCompetitorAdd(ids) {
            let competitors = fastCopy(this.model.competitors);

            competitors = competitors.concat(ids.map(id => ({id, productIds: []})));

            this.model.competitors = competitors;
        },
        handleCompetitorRemove(ids) {
            let competitors = fastCopy(this.model.competitors);

            ids.forEach(id => {
                competitors = competitors.filter(c => c.id !== id);
            });

            if (ids.indexOf(this.selectedCompetitorId) !== -1) {
                this.selectedCompetitorId = null;
            }

            this.model.competitors = competitors;
        },
        handleCompetitorCreate(id) {
            this.model.competitors.push({id, productIds: []});
        },
        handleCompetitorSelect(selected) {
            if (selected.length === 1) {
                this.selectedCompetitorId = selected[0]._id;

                const existing = this.model.competitors.find(c => c.id === this.selectedCompetitorId);

                this.model.competitorProductIds = existing.productIds || [];
            } else {
                this.selectedCompetitorId = null;
            }
        },
        handleCompetitorProductAdd(ids) {
            const competitors = fastCopy(this.model.competitors);
            const index = _.findIndex(competitors, c => c.id === this.selectedCompetitorId);

            competitors[index].productIds = competitors[index].productIds.concat(ids);

            this.model.competitors = competitors;
        },
        handleCompetitorProductRemove(ids) {
            const competitors = fastCopy(this.model.competitors);
            const index = _.findIndex(competitors, c => c.id === this.selectedCompetitorId);

            ids.forEach(id => {
                competitors[index].productIds = competitors[index].productIds.filter(pId => pId !== id);
            });

            this.model.competitors = competitors;
        },
        handleCompetitorProductCreate(id) {
            const competitors = fastCopy(this.model.competitors);
            const index = _.findIndex(competitors, c => c.id === this.selectedCompetitorId);

            competitors[index].productIds.push(id);

            this.model.competitors = competitors;
        },

        async generateCode(save = false) {
            const numbering = await this.$collection('kernel.numbering').findOne({
                code: 'salesQuotationNumbering',
                $select: ['_id'],
                $disableInUseCheck: true,
                $disableActiveCheck: true,
                $disableSoftDelete: true
            });

            return await this.$rpc('kernel.common.request-number', {
                numberingId: numbering._id,
                save
            });
        },
        async calculateOriginalGrandTotal() {
            let grandTotal = 0;

            // Calculate original grand total.
            if (_.isObject(this.model.paymentPlanBackup) && !_.isEmpty(this.model.paymentPlanBackup)) {
                let backup = this.model.paymentPlanBackup;

                let subTotal = 0;
                let taxTotal = 0;

                const payload = {items: []};
                backup.items.forEach(row => {
                    subTotal += this.$app.round(row.total, 'total');

                    const key = row.taxDetail.applied.map(t => t._id).join('');
                    const existingIndex = _.findIndex(payload.items, i => i.key === key);
                    if (existingIndex === -1) {
                        payload.items.push({
                            taxId: row.taxId,
                            amount: this.$app.round(row.total, 'total'),
                            quantity: row.quantity || 1,
                            taxPayload: row.taxPayload,
                            key
                        });
                    } else {
                        payload.items[existingIndex].amount += this.$app.round(row.total, 'total');
                        payload.items[existingIndex].quantity += row.quantity || 1;
                    }
                });
                const result = await this.$rpc(
                    'kernel.common.calculate-taxes',
                    {...payload, currencyRate: this.model.currencyRate || 1},
                    {
                        currencyRate: this.model.currencyRate || 1
                    }
                );
                taxTotal = 0;
                for (const r of result) {
                    taxTotal += this.$app.round(r.amount, 'total');
                }

                grandTotal = this.$app.round(subTotal + taxTotal, 'total');
            } else {
                let subTotal = 0;
                let taxTotal = 0;

                const payload = {items: []};
                this.model.items.forEach(row => {
                    subTotal += this.$app.round(row.total, 'total');

                    const key = row.taxDetail.applied.map(t => t._id).join('');
                    const existingIndex = _.findIndex(payload.items, i => i.key === key);
                    if (existingIndex === -1) {
                        payload.items.push({
                            taxId: row.taxId,
                            amount: this.$app.round(row.total, 'total'),
                            quantity: row.quantity || 1,
                            taxPayload: row.taxPayload,
                            key
                        });
                    } else {
                        payload.items[existingIndex].amount += this.$app.round(row.total, 'total');
                        payload.items[existingIndex].quantity += row.quantity || 1;
                    }
                });
                const result = await this.$rpc(
                    'kernel.common.calculate-taxes',
                    {...payload, currencyRate: this.model.currencyRate || 1},
                    {
                        currencyRate: this.model.currencyRate || 1
                    }
                );
                taxTotal = 0;
                for (const r of result) {
                    taxTotal += this.$app.round(r.amount, 'total');
                }

                grandTotal = this.$app.round(subTotal + taxTotal, 'total');
            }

            return grandTotal;
        },
        updatePartnerIdParams(params) {
            params.model = {type: 'customer'};

            if (this.model.partnerGroupId) {
                params.model.groupId = this.model.partnerGroupId;
            }

            return params;
        },
        updateContactIdParams(params, type) {
            if (type === 'create' || type === 'detail') {
                params.model = {type: 'contact'};
                params.partnerId = this.model.partnerId;
            } else if (type === 'list') {
                params.filters = {
                    partnerId: this.model.partnerId,
                    type: 'contact'
                };
            }

            return params;
        },
        updateItemsParams(params) {
            params.type = 'sale';
            params.collection = 'sale.quotations';
            params.itemDecorationMethod = 'sale.decorate-quotation-items';
            params.productFields = fastCopy(this.productFields);
            params.parentModel = fastCopy(this.model);
            params.organizationSettings = fastCopy(this.organizationSettings);

            return params;
        },
        updatePaymentAddressSelectParams(params, type) {
            if (type === 'create' || type === 'detail') {
                params.model = {type: 'other-address'};
                params.partnerId = this.model.partnerId;
            } else if (type === 'list') {
                params.filters = {
                    partnerId: this.model.partnerId,
                    type: 'other-address'
                };
            }

            return params;
        },
        updateInvoiceAddressSelectParams(params, type) {
            if (type === 'create' || type === 'detail') {
                params.model = {type: 'invoice-address'};
                params.partnerId = this.model.partnerId;
            } else if (type === 'list') {
                params.filters = {
                    partnerId: this.model.partnerId,
                    type: 'invoice-address'
                };
            }

            return params;
        },
        updateDeliveryAddressSelectParams(params, type) {
            if (type === 'create' || type === 'detail') {
                params.model = {type: 'delivery-address'};
                params.partnerId = this.model.partnerId;
            } else if (type === 'list') {
                params.filters = {
                    partnerId: this.model.partnerId,
                    type: 'delivery-address'
                };
            }

            return params;
        },
        async calculateRowTotals(row, field, extra = {}) {
            try {
                row = await this.$rpc('sale.calculate-quotation-row-totals', {
                    row: row,
                    field,
                    model: {
                        ...this.model,
                        ...extra
                    }
                });
            } catch (error) {
                this.$program.message('error', error.message);
            }

            return row;
        },
        async calculateGeneralDiscount(discount) {
            if (this.model.partnerType === 'lead') return 0;

            const partner = await this.$collection('kernel.partners').findOne({
                _id: this.model.partnerId,
                $select: ['_id', 'groupId', 'tags']
            });
            const discountList = await this.$collection('sale.general-discount-lists').findOne({
                startDate: {
                    $lte: this.model.quotationDate
                },
                endDate: {
                    $gte: this.model.quotationDate
                },
                $or: [
                    {type: 'all-customers'},
                    {type: 'customer', partnerId: partner._id},
                    {type: 'customer-groups', partnerGroupIds: partner.groupId},
                    {
                        type: 'customer-tags',
                        partnerTags: {
                            $in: (partner.tags || []).map(tag => tag.label)
                        }
                    }
                ]
            });

            if (_.isPlainObject(discountList) && _.isNumber(discountList.discount)) {
                discount = discountList.discount;
            }

            return discount;
        },
        async applyDiscount() {
            const self = this;
            const result = await this.calculateTotals(true, 0);

            this.$program.dialog({
                component: 'sale.sales.quotations.detail.apply-discount',
                params: {
                    items: fastCopy(this.model.items),
                    subTotal: result.subTotal,
                    discount: result.discount,
                    taxTotal: result.taxTotal,
                    appliedTaxes: result.appliedTaxes,
                    isPreview: this.$params('id') && this.$params('isPreview')
                },
                async onSubmit(result) {
                    self.model.discount = result.discount;
                    self.model.rounding = result.rounding;

                    await self.calculateTotals();

                    self.$nextTick(async () => {
                        const items = [];

                        for (const item of fastCopy(self.model.items)) {
                            items.push(await self.calculateRowTotals(item));
                        }

                        self.model.items = items;
                    });
                }
            });
        },
        applyRounding() {
            const self = this;

            this.$program.dialog({
                component: 'sale.sales.quotations.detail.apply-rounding',
                params: {
                    rounding: this.model.rounding,
                    isPreview: this.$params('id') && this.$params('isPreview')
                },
                onSubmit(result) {
                    self.model.rounding = result.rounding;

                    self.calculateTotals();
                }
            });
        },
        async calculateTotals(getResult = false, forcedDiscount = null) {
            const model = fastCopy(this.model);
            const discount = _.isNumber(forcedDiscount) ? forcedDiscount : this.model.discount;
            const rounding = this.model.rounding;
            let discountAmount = 0;
            let subTotal = 0;
            let subTotalAfterDiscount = 0;
            let taxTotal = 0;
            let grandTotal = 0;
            let appliedTaxes = [];

            if (Array.isArray(model.items)) {
                model.items.forEach(row => {
                    subTotal += this.$app.round(row.total, 'total');

                    if (_.isObject(row.taxDetail)) {
                        taxTotal += this.$app.round(row.taxDetail.taxTotal, 'total');

                        row.taxDetail.applied.forEach(tax => {
                            const taxIndex = _.findIndex(appliedTaxes, t => t._id === tax._id);

                            if (taxIndex !== -1) {
                                appliedTaxes[taxIndex].unAppliedAmount += this.$app.round(
                                    tax.unAppliedAmount || 0,
                                    'total'
                                );
                                appliedTaxes[taxIndex].appliedAmount += this.$app.round(
                                    tax.appliedAmount || 0,
                                    'total'
                                );
                            } else {
                                tax.unAppliedAmount = this.$app.round(tax.unAppliedAmount || 0, 'total');
                                tax.appliedAmount = this.$app.round(tax.appliedAmount || 0, 'total');
                                appliedTaxes.push(fastCopy(tax));
                            }
                        });
                    }
                });
                grandTotal = this.$app.round(subTotal + taxTotal, 'total');

                if (_.isNumber(discount) && subTotal > 0) {
                    // Get discount amount.
                    discountAmount = this.$app.round((subTotal * discount) / 100, 'total');

                    // Calculate new taxes and tax total.
                    const ratio = discountAmount / subTotal;
                    const payload = {items: []};
                    let taxRounding = 0;
                    model.items.forEach(row => {
                        const rowDiscountAmount = this.$app.round(ratio * row.total, 'total');
                        const newRowTotal = this.$app.round(row.total - rowDiscountAmount, 'total');
                        const key = row.taxDetail.applied.map(t => t._id).join('');
                        const existingIndex = _.findIndex(payload.items, i => i.key === key);
                        if (existingIndex === -1) {
                            payload.items.push({
                                taxId: row.taxId,
                                amount: newRowTotal,
                                quantity: row.quantity || 1,
                                taxPayload: row.taxPayload,
                                key
                            });
                        } else {
                            payload.items[existingIndex].amount += newRowTotal;
                            payload.items[existingIndex].quantity += row.quantity || 1;
                        }

                        taxRounding += row.taxDetail.rounding || 0;
                    });
                    const result = await this.$rpc(
                        'kernel.common.calculate-taxes',
                        {...payload, currencyRate: this.model.currencyRate || 1},
                        {
                            currencyRate: this.model.currencyRate || 1
                        }
                    );
                    taxTotal = 0;
                    for (const r of result) {
                        taxTotal += this.$app.round(r.amount, 'total');
                    }
                    appliedTaxes = appliedTaxes.map(tax => {
                        const newTaxResult = result.find(r => r.taxId === tax._id);

                        tax.appliedAmount = this.$app.round(newTaxResult.amount, 'total');

                        return tax;
                    });
                    if (taxRounding !== 0) {
                        appliedTaxes[0].appliedAmount -= taxRounding;
                        taxTotal -= taxRounding;
                    }

                    // Calculate subtotal after discount.
                    subTotalAfterDiscount = this.$app.round(subTotal - discountAmount, 'total');

                    // Calculate new grand total.
                    grandTotal = this.$app.round(subTotal + taxTotal - discountAmount, 'total');
                } else {
                    subTotalAfterDiscount = this.$app.round(subTotal, 'total');
                }

                if (_.isNumber(rounding) && rounding !== 0) {
                    grandTotal += rounding;
                }

                setTimeout(async () => {
                    await this.initializeContractParams();
                }, 50);

                if (getResult) {
                    return {
                        subTotal: this.$app.round(subTotal, 'total'),
                        discount: discount,
                        discountAmount: this.$app.round(discountAmount, 'total'),
                        subTotalAfterDiscount,
                        taxTotal: this.$app.round(taxTotal, 'total'),
                        grandTotal: this.$app.round(grandTotal, 'total'),
                        appliedTaxes: appliedTaxes
                    };
                } else {
                    this.model.subTotal = this.$app.round(subTotal, 'total');
                    this.model.discountAmount = this.$app.round(discountAmount, 'totoal');
                    this.model.subTotalAfterDiscount = subTotalAfterDiscount;
                    this.model.taxTotal = this.$app.round(taxTotal, 'total');
                    this.model.grandTotal = this.$app.round(grandTotal, 'total');
                    this.model.appliedTaxes = appliedTaxes;
                }
            }
        },
        async getNearbyDocumentsCount() {
            const nearbyDocumentsSetting = (this.$setting('sale.nearbyDocuments') || []).find(
                s => s.document === 'sale.quotations'
            );

            if (
                !!nearbyDocumentsSetting &&
                !!nearbyDocumentsSetting.isActive &&
                !!this.model.deliveryAddress &&
                !_.isEmpty(this.model.deliveryAddress)
            ) {
                this.nearbyDocumentsCount = await this.$rpc('sale.get-nearby-documents-count', {
                    documentCollection: 'sale.quotations',
                    address: this.model.deliveryAddress.address,
                    query: {
                        documentId: {$ne: this.$params('id')}
                    }
                });
            }
        },
        async initializeContractParams() {
            // const contractIds = _.uniq(this.model.items.map(item => item.contractId)).filter(id => !!id);
            //
            // if (contractIds.length > 0) {
            //     const contracts = await this.$collection('sale.contracts').find({
            //         _id: {$in: contractIds},
            //         $select: ['paymentTermId', 'guaranteeIds']
            //     });
            //     const currentPaymentTermId = this.model.paymentTermId;
            //     const params = {paymentTermIds: [], guaranteeIds: []};
            //
            //     for (const contract of contracts) {
            //         if (!!contract.paymentTermId) {
            //             params.paymentTermIds.push(contract.paymentTermId);
            //         }
            //
            //         if ((contract.guaranteeIds || []).length > 0) {
            //             params.guaranteeIds.push(...contract.guaranteeIds);
            //         }
            //     }
            //
            //     params.paymentTermIds = _.uniq(params.paymentTermIds);
            //     params.guaranteeIds = _.uniq(params.guaranteeIds);
            //
            //     if (params.paymentTermIds.length > 0) {
            //         this.model.paymentTermId = params.paymentTermIds[0];
            //     }
            //
            //     if (params.guaranteeIds.length > 0) {
            //         this.model.guaranteeId = params.guaranteeIds[0];
            //     }
            //
            //     if (currentPaymentTermId !== this.model.paymentTermId) {
            //         this.model.paymentTerm = await this.$collection('finance.payment-terms').findOne({
            //             _id: this.model.paymentTermId
            //         });
            //     }
            //
            //     this.model.contractParams = params;
            // }
        },
        organizationMemberTemplate(item) {
            if (item.position) {
                return `<span style="float: left">${item.label}</span><span style="float: right; color: #8492a6;">${item.position}</span>`;
            }

            return item.label;
        },
        stageIdOptionsTemplate(item) {
            if (_.isNumber(item.probability)) {
                return `<span style="float: left">${
                    item.label
                }</span><span style="float: right; color: #8492a6;">%${parseInt(item.probability)}</span>`;
            }

            return item.name;
        },
        async getMailPayload() {
            let partner = null;
            let language = null;

            if (this.model.partnerType === 'customer') {
                partner = await this.$collection('kernel.partners').findOne({
                    _id: this.model.partnerId,
                    $select: ['code', 'name', 'email', 'languageId']
                });
                language = await this.$collection('kernel.languages').findOne({
                    _id: partner.languageId,
                    $select: ['isoCode'],
                    $disableSoftDelete: true,
                    $disableActiveCheck: true
                });
            } else {
                partner = await this.$collection('crm.leads').findOne({
                    _id: this.model.leadId,
                    $select: ['code', 'name', 'email', 'languageId']
                });
                language = await this.$collection('kernel.languages').findOne({
                    _id: partner.languageId,
                    $select: ['isoCode'],
                    $disableSoftDelete: true,
                    $disableActiveCheck: true
                });
            }

            const payload = {
                documentId: this.$params('id'),
                locale: language.isoCode,
                receiver: partner.email || '',
                subject: 'Sales Quotation - {{code}}',
                subjectTranslationPayload: {code: this.model.code},
                message: `
<p>${this.$t('Hello, {{name}}', {name: partner.name})}</p>
<p style="margin-bottom: 0px;">${this.$t(
                    'A copy of the sales quotation that has {{code}} code is attached to this email. You can download the related document from the attachments of this email.',
                    {code: this.model.code}
                )}</p>
                `.trim(),
                messageTranslationPayload: {},
                printMethod: 'sale.print-quotation',
                printPayload: {
                    source: 'sale.quotation'
                }
            };

            if (!!this.documentType && this.documentType.defaultPrintingTemplateId) {
                payload.printPayload.templateId = this.documentType.defaultPrintingTemplateId;
            }

            return payload;
        },
        async printPayload() {
            const payload = {
                source: 'sale.quotation',
                title: this.$t('Sale Quotation')
            };

            if (this.model.partnerType === 'lead') {
                const lead = await this.$collection('crm.leads').findOne({
                    _id: this.model.leadId,
                    $select: ['code', 'name'],
                    $disableActiveCheck: true,
                    $disableSoftDelete: true
                });
                payload.title = `${lead.name} - ${this.$t('Sale Quotation')} - ${this.model.code}`;
            } else {
                const partner = await this.$collection('kernel.partners').findOne({
                    _id: this.model.partnerId,
                    $select: ['code', 'name'],
                    $disableActiveCheck: true,
                    $disableSoftDelete: true
                });
                payload.title = `${partner.name} - ${this.$t('Sale Quotation')} - ${this.model.code}`;
            }

            if (!!this.documentType && this.documentType.defaultPrintingTemplateId) {
                payload.templateId = this.documentType.defaultPrintingTemplateId;
            }

            if (
                _.isObject(this.organizationSettings) &&
                Array.isArray(this.organizationSettings.templateIds) &&
                this.organizationSettings.templateIds.length > 0 &&
                !!this.organizationSettings.enableTemplateRestrictions
            ) {
                payload.allowedTemplateIds = this.organizationSettings.templateIds;
            }

            return payload;
        },
        handleApplyFinancialProject() {
            if (this.model.items.length > 0) {
                this.$program.alert('confirm', this.$t('Do you want to apply this to all rows?'), confirmed => {
                    if (confirmed) {
                        this.model.items = fastCopy(this.model.items).map(item => {
                            item.financialProjectId = this.model.financialProjectId;

                            return item;
                        });
                    }
                });
            }
        },
        async resetItems() {
            this.$params('loading', true);
            const model = this.model;
            const company = this.$store.getters['session/company'];

            const items = await this.$rpc('sale.decorate-quotation-items', {
                items: model.items,
                field: 'unitPrice',
                model: _.omit(this.model, 'items'),
                productFields: this.productFields
            });
            this.$set(this.model, 'items', items);

            let profitBase = null;
            if (_.isObject(this.organizationSettings) && this.organizationSettings.defaultProfitBase) {
                profitBase = this.organizationSettings.defaultProfitBase || this.$setting('sale.profitBase');
            } else {
                profitBase = this.$setting('sale.profitBase');
            }
            if (this.$params('id') && Array.isArray(model.items) && model.items.length > 0) {
                this.$rpc('sale.update-document-profits', {
                    profitBase,
                    documentId: this.$params('id'),
                    documentCollection: 'sale.quotations',
                    currencyId: model.currencyId,
                    currencyRate: model.currencyRate,
                    date: model.quotationDate,
                    exchangeRates: model.exchangeRates ?? [],
                    items: model.items.map(item => ({
                        productId: item.productId,
                        warehouseId: item.warehouseId,
                        quantity: item.quantity,
                        unitId: item.unitId,
                        freight: item.freight || 0,
                        totalSalesPrice: item.realTotal
                    }))
                });
            }

            // Reset totals.
            this.model.discount = await this.calculateGeneralDiscount(this.model.discount);
            this.model.paymentPlan = null;
            this.model.paymentPlanningDate = null;
            await this.calculateTotals();

            await (async () => {
                const currencies = await this.$collection('kernel.currencies').find({
                    $select: ['name']
                });
                const exchangeRates = [];
                const payloads = [];

                for (const currency of currencies) {
                    if (currency.name === company.currency.name) {
                        continue;
                    }

                    payloads.push({
                        from: currency.name,
                        to: company.currency.name,
                        value: 1,
                        options: {
                            date: model.quotationDate
                        }
                    });
                }

                for (const payload of await this.$rpc('kernel.common.convert-currencies', payloads)) {
                    exchangeRates.push({
                        currencyName: payload.from,
                        rate: payload.rate
                    });
                }

                this.model.exchangeRates = exchangeRates;
            })();

            this.$nextTick(() => {
                this.itemsKey = _.uniqueId('quotationItems_');
                this.currentCurrencyRate = this.model.currencyRate;

                this.$params('loading', false);
            });
        },
        async applyFreight() {
            const freightItems = await new Promise(resolve => {
                this.$program.dialog({
                    component: 'system.components.freight-calculator',
                    params: {
                        title: this.$t('Freight'),
                        ...(!!this.model.freight && Array.isArray(this.model.freight.items)
                            ? {
                                  model: {
                                      items: this.model.freight.items
                                  }
                              }
                            : {}),
                        forcedPreview: this.$params('isPreview') || (this.model.items ?? []).length < 1
                    },
                    onSubmit: ({items}) => resolve(items),
                    onClose: () => resolve(null)
                });
            });
            if (freightItems === null) {
                return;
            }

            this.$params('loading', true);

            const company = this.$store.getters['session/company'];
            const currencies = await this.$collection('kernel.currencies').find({
                $select: ['_id', 'name'],
                $disableSoftDelete: true,
                $disableActiveCheck: true
            });

            let exchangeRates = [];
            if (Array.isArray(this.model.exchangeRates) && this.model.exchangeRates.length > 0) {
                exchangeRates = this.model.exchangeRates;
            } else {
                const exchangeRatesPayloads = [];

                for (const currency of currencies) {
                    if (currency.name === company.currency.name) {
                        continue;
                    }

                    exchangeRatesPayloads.push({
                        from: currency.name,
                        to: company.currency.name,
                        value: 1,
                        options: {
                            date: new Date()
                        }
                    });
                }
                for (const payload of await this.$rpc('kernel.common.convert-currencies', exchangeRatesPayloads)) {
                    exchangeRates.push({
                        currencyName: payload.from,
                        rate: payload.rate
                    });
                }
            }

            let modelItems = fastCopy(this.model.items).map(item => {
                item.freight = 0;

                return item;
            });
            const freight = {
                items: freightItems,
                amount: 0,
                taxTotal: 0,
                grossAmount: 0,
                appliedTaxes: []
            };

            for (const freightItem of freightItems) {
                const freightItemCurrency = currencies.find(currency => currency._id === freightItem.currencyId);
                const freightItemExchangeRate = exchangeRates.find(er => er.currencyName === freightItemCurrency.name);
                const freightItemRate = (freightItemExchangeRate ?? {}).rate ?? 1;
                const modelRate = this.model.currencyRate ?? 1;
                const rate = freightItemRate / modelRate;
                let amount = this.$app.round((freightItem.amount ?? 0) * rate, 'currency');

                freight.amount += amount;
                freight.taxTotal += this.$app.round((freightItem.taxTotal ?? 0) * rate, 'currency');
                freight.grossAmount += this.$app.round((freightItem.grossAmount ?? 0) * rate, 'currency');

                freight.taxTotal += freightItem.taxTotal ?? 0;
                freight.grossAmount += freightItem.grossAmount ?? 0;

                for (const appliedTax of freightItem.appliedTaxes) {
                    const appliedTaxIndex = freight.appliedTaxes.findIndex(at => at._id === appliedTax._id);

                    if (appliedTaxIndex === -1) {
                        appliedTax.appliedAmount = this.$app.round((appliedTax.appliedAmount ?? 0) * rate, 'currency');
                        appliedTax.unAppliedAmount = this.$app.round(
                            (appliedTax.unAppliedAmount ?? 0) * rate,
                            'currency'
                        );

                        freight.appliedTaxes.push(appliedTax);
                    } else {
                        freight.appliedTaxes[appliedTaxIndex].appliedAmount += this.$app.round(
                            (appliedTax.appliedAmount ?? 0) * rate,
                            'currency'
                        );
                        freight.appliedTaxes[appliedTaxIndex].unAppliedAmount += this.$app.round(
                            (appliedTax.unAppliedAmount ?? 0) * rate,
                            'currency'
                        );
                    }
                }

                if (freightItem.distributionMethod === 'equal') {
                    modelItems = modelItems.map(item => {
                        item.freight += this.$app.round(amount / modelItems.length, 'currency');

                        return item;
                    });
                } else if (freightItem.distributionMethod === 'quantity') {
                    const total = _.sumBy(modelItems, 'quantity');

                    if (total > 0) {
                        modelItems = modelItems.map(item => {
                            item.freight += this.$app.round((amount / total) * item.quantity, 'currency');

                            return item;
                        });
                    }
                } else if (freightItem.distributionMethod === 'net-weight') {
                    let total = 0;
                    for (const modelItem of modelItems) {
                        if (_.isFinite(modelItem.netWeight)) {
                            total += modelItem.netWeight ?? 0;
                        }
                    }

                    if (total > 0) {
                        modelItems = modelItems.map(item => {
                            item.freight += this.$app.round((amount / total) * (item.netWeight ?? 1), 'currency');

                            return item;
                        });
                    }
                } else if (freightItem.distributionMethod === 'gross-weight') {
                    let total = 0;
                    for (const modelItem of modelItems) {
                        if (_.isFinite(modelItem.grossWeight)) {
                            total += modelItem.grossWeight ?? 0;
                        }
                    }

                    if (total > 0) {
                        modelItems = modelItems.map(item => {
                            item.freight += this.$app.round((amount / total) * (item.grossWeight ?? 1), 'currency');

                            return item;
                        });
                    }
                } else if (freightItem.distributionMethod === 'net-volume') {
                    let total = 0;
                    for (const modelItem of modelItems) {
                        if (_.isFinite(modelItem.netVolume)) {
                            total += modelItem.netVolume ?? 0;
                        }
                    }

                    if (total > 0) {
                        modelItems = modelItems.map(item => {
                            item.freight += this.$app.round((amount / total) * (item.netVolume ?? 1), 'currency');

                            return item;
                        });
                    }
                } else if (freightItem.distributionMethod === 'gross-volume') {
                    let total = 0;
                    for (const modelItem of modelItems) {
                        if (_.isFinite(modelItem.grossVolume)) {
                            total += modelItem.grossVolume ?? 0;
                        }
                    }

                    if (total > 0) {
                        modelItems = modelItems.map(item => {
                            item.freight += this.$app.round((amount / total) * (item.grossVolume ?? 1), 'currency');

                            return item;
                        });
                    }
                } else if (freightItem.distributionMethod === 'amount') {
                    const total = _.sumBy(modelItems, 'total');

                    if (total > 0) {
                        modelItems = modelItems.map(item => {
                            item.freight += this.$app.round((amount / total) * (item.total ?? 1), 'currency');

                            return item;
                        });
                    }
                }
            }

            freight.amount = this.$app.round(freight.amount, 'currency');
            freight.taxTotal = this.$app.round(freight.taxTotal, 'currency');
            freight.grossAmount = this.$app.round(freight.grossAmount, 'currency');
            freight.appliedTaxes = freight.appliedTaxes.map(appliedTax => {
                appliedTax.appliedAmount = this.$app.round(appliedTax.appliedAmount, 'currency');
                appliedTax.unAppliedAmount = this.$app.round(appliedTax.unAppliedAmount, 'currency');

                return appliedTax;
            });
            this.model.freight = freight;

            modelItems = modelItems.map(item => {
                item.freight = this.$app.round(item.freight, 'currency');

                return item;
            });
            modelItems = await this.$rpc('sale.decorate-quotation-items', {
                items: modelItems,
                field: 'freight',
                model: _.omit(this.model, 'items'),
                productFields: this.productFields
            });
            this.$set(this.model, 'items', modelItems);

            let profitBase = null;
            if (_.isObject(this.organizationSettings) && this.organizationSettings.defaultProfitBase) {
                profitBase = this.organizationSettings.defaultProfitBase || this.$setting('sale.profitBase');
            } else {
                profitBase = this.$setting('sale.profitBase');
            }
            if (this.$params('id') && Array.isArray(this.model.items) && this.model.items.length > 0) {
                this.$rpc('sale.update-document-profits', {
                    profitBase,
                    documentId: this.$params('id'),
                    documentCollection: 'sale.quotations',
                    currencyId: this.model.currencyId,
                    currencyRate: this.model.currencyRate,
                    date: this.model.quotationDate,
                    exchangeRates: this.model.exchangeRates ?? [],
                    items: modelItems.map(item => ({
                        productId: item.productId,
                        warehouseId: item.warehouseId,
                        quantity: item.quantity,
                        unitId: item.unitId,
                        freight: item.freight || 0,
                        totalSalesPrice: item.realTotal
                    }))
                });
            }

            this.$params('loading', false);
        },
        handleAdditionalInformationChange(info) {
            this.model.additionalInformation = info;
        }
    }
};
