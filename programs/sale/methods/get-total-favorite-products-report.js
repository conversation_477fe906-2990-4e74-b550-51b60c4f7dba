import _ from 'lodash';

export default {
    name: 'get-total-favorite-products-report',
    async action(payload, params) {
        const app = this.app;
        const user = params.user;

        // Check user
        if (!_.isObject(user)) {
            throw new Error('User must be provided!');
        }

        // Get collection
        const collection = app.collection('store.products');

        // Get start and end date
        let endDate = app.datetime.local().toJSDate();
        let startDate = app.datetime.fromJSDate(endDate).minus({years: 1}).toJSDate();
        if (_.isDate(payload.endDate)) endDate = payload.endDate;
        if (_.isDate(payload.startDate)) startDate = payload.startDate;

        // Get query
        let query = {
            favoritesCount: { $gt: 0 }
        };
        if (_.isObject(payload.query)) {
            query = { ...query, ...payload.query };
        }

        // Prepare match state
        const $match = {
            ...query,
            createdAt: { $gte: startDate, $lte: endDate }
        };

        // Apply category filters if provided
        if (
            _.isPlainObject(payload.categoryFilters) &&
            Array.isArray(payload.categoryFilters.$or) &&
            payload.categoryFilters.$or.length > 0
        ) {
            if (!Array.isArray($match.$and)) $match.$and = [];
            $match.$and.push(payload.categoryFilters);
        }

        const pipeline = [
            { $match },
            {
                $group: {
                    _id: null,
                    totalFavorites: { $sum: '$favoritesCount' }
                }
            }
        ];

        const result = await collection.aggregate(pipeline);

        return {
            totalFavorites: result.length > 0 ? result[0].totalFavorites : 0
        };
    }
};
