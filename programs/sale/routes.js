export default [
    {
        name: 'panel',
        title: 'Panel',
        icon: 'desktop'
    },

    // Sales
    {
        name: 'sales',
        title: 'Sales',
        icon: 'chart-line',
        items: [
            {
                name: 'quotations',
                title: 'Quotations',
                params: {
                    filters: {
                        $or: [{module: 'sale'}, {module: {$eq: null}}, {module: {$eq: ''}}, {module: {$exists: false}}]
                    }
                }
            },
            {
                name: 'quotations-detail',
                params: {model: {module: 'sale'}}
            },
            {
                name: 'orders',
                title: 'Orders',
                params: {
                    filters: {
                        $or: [{module: 'sale'}, {module: {$eq: null}}, {module: {$eq: ''}}, {module: {$exists: false}}]
                    }
                }
            },
            {
                name: 'orders-detail',
                params: {model: {module: 'sale'}}
            },
            {
                name: 'invoices',
                title: 'Invoices',
                view: 'accounting.sales.customer-invoices.master'
            },
            {
                name: 'invoices-detail',
                view: 'accounting.sales.customer-invoices.detail'
            },
            {name: 'cancellations', title: 'Cancellations'},
            {
                name: 'returns',
                title: 'Returns',
                params: {
                    filters: {
                        $or: [{module: 'sale'}, {module: {$eq: null}}, {module: {$eq: ''}}, {module: {$exists: false}}]
                    }
                }
            },
            {
                name: 'returns-detail',
                params: {model: {module: 'sale'}}
            },
            {
                name: 'to-invoice',
                title: 'To Invoice',
                async condition(app) {
                    return app.checkPermission({
                        type: 'permission',
                        name: 'sale.canCreateInvoiceFromOrder'
                    });
                }
            },
            {name: 'deliveries', title: 'Deliveries'}
        ]
    },

    // Customer Relations
    {
        name: 'customer-relations',
        title: 'Customer Relations',
        icon: 'handshake',
        items: [
            {
                name: 'leads',
                title: 'Leads',
                view: 'crm.customer-relations.leads.master'
            },
            {
                name: 'leads-detail',
                view: 'crm.customer-relations.leads.detail'
            },
            {
                name: 'opportunities',
                title: 'Opportunities',
                view: 'crm.customer-relations.opportunities.master'
            },
            {
                name: 'opportunities-detail',
                view: 'crm.customer-relations.opportunities.detail'
            },
            {
                name: 'customers',
                title: 'Customers',
                view: 'partners.partners.master',
                params: {filters: {type: 'customer'}}
            },
            {
                name: 'customers-detail',
                view: 'partners.partners.detail',
                params: {model: {type: 'customer'}}
            },
            {
                name: 'activities',
                title: 'Activities',
                view: 'calendar.activities.master'
            },
            {
                name: 'activities-detail',
                view: 'calendar.activities.detail'
            },
            {
                name: 'competitors',
                title: 'Competitors',
                view: 'crm.customer-relations.competitors.master'
            },
            {
                name: 'competitors-detail',
                view: 'crm.customer-relations.competitors.detail'
            }
        ]
    },

    // Catalog
    {
        name: 'catalog',
        title: 'Catalog',
        icon: 'books',
        items: [
            {
                name: 'products',
                title: 'Products',
                view: 'inventory.catalog.products.master',
                params: {filters: {canBeSold: true}}
            },
            {
                name: 'products-detail',
                view: 'inventory.catalog.products.detail',
                params: {model: {canBeSold: true}}
            },
            {
                name: 'configurable-products',
                title: 'Configurable Products',
                view: 'inventory.catalog.products.master',
                params: {filters: {isConfigurable: true, canBeSold: true}}
            },
            {
                name: 'configurable-products-detail',
                view: 'inventory.catalog.products.detail',
                params: {
                    model: {
                        isConfigurable: true,
                        isSimple: false,
                        canBeSold: true
                    }
                }
            },
            {
                name: 'kit-products',
                title: 'Kit Products',
                view: 'inventory.catalog.products.master',
                params: {filters: {isKit: true, canBeSold: true}}
            },
            {
                name: 'kit-products-detail',
                view: 'inventory.catalog.products.detail',
                params: {model: {isKit: true, isSimple: false, canBeSold: true}}
            },
            {name: 'customer-catalog', title: 'Customer Catalog'},
            {
                name: 'competitor-products',
                title: 'Competitor Products',
                view: 'crm.customer-relations.competitor-products.master'
            },
            {
                name: 'competitor-products-detail',
                view: 'crm.customer-relations.competitor-products.detail'
            }
            // {name: 'alternate-products', title: 'Alternate Products'},
            // {name: 'serial-numbers', title: 'Serial Numbers'},
            // {name: 'lot-numbers', title: 'Lot Numbers'}
        ]
    },

    // Marketing
    {
        name: 'marketing',
        title: 'Marketing',
        icon: 'business-time',
        items: [
            {name: 'quotas', title: 'Quotas'},
            {name: 'quotas-detail'}
            // {name: 'goals', title: 'Goals'},
            // {name: 'campaigns', title: 'Campaigns'},
            // {name: 'marketing-lists', title: 'Marketing Lists'},
            // {name: 'bonuses', title: 'Bonuses'},
            // {name: 'advertisements', title: 'Advertisements'},
            // {name: 'marketing-calendar', title: 'Marketing Calendar'},
            // {name: 'forecasts', title: 'Forecasts'}
        ]
    },

    // Prices.
    {
        name: 'pricing',
        title: 'Pricing',
        icon: 'coins',
        items: [
            {
                name: 'price-lists',
                title: 'Price Lists',
                async condition(app) {
                    return app.setting('sale.salesPriceList');
                },
                params: {
                    filters: {
                        $or: [{module: 'sale'}, {module: {$eq: null}}, {module: {$eq: ''}}, {module: {$exists: false}}]
                    },
                    module: 'sale'
                }
            },
            {
                name: 'price-lists-detail',
                params: {model: {module: 'sale'}}
            },
            {
                name: 'campaigns',
                title: 'Campaigns',
                params: {
                    filters: {
                        $or: [{module: 'sale'}, {module: {$eq: null}}, {module: {$eq: ''}}, {module: {$exists: false}}]
                    }
                }
            },
            {name: 'campaigns-detail', params: {model: {module: 'sale'}}},
            {
                name: 'campaign-groups',
                title: 'Campaign Groups'
            },
            {name: 'campaign-groups-detail'},
            {
                name: 'customer-price-lists',
                title: 'Customer Price Lists',
                async condition(app) {
                    return app.setting('sale.customerPriceList');
                }
            },
            {name: 'customer-price-lists-detail'},
            {
                name: 'discount-lists',
                title: 'Discount Lists',
                async condition(app) {
                    return app.setting('sale.lineDiscounts') && app.setting('sale.discountList');
                },
                params: {
                    filters: {
                        $or: [{module: 'sale'}, {module: {$eq: null}}, {module: {$eq: ''}}, {module: {$exists: false}}]
                    }
                }
            },
            {name: 'discount-lists-detail', params: {model: {module: 'sale'}}},
            {
                name: 'general-discount-lists',
                title: 'General Discount Lists',
                async condition(app) {
                    return app.setting('sale.discounts');
                },
                params: {
                    filters: {
                        $or: [{module: 'sale'}, {module: {$eq: null}}, {module: {$eq: ''}}, {module: {$exists: false}}]
                    }
                }
            },
            {
                name: 'sellout',
                title: 'Sellout',
                async condition(app) {
                    return app.setting('sale.sellout');
                },
                params: {
                    module: 'sale',
                    filters: {
                        $or: [{module: 'sale'}, {module: {$eq: null}}, {module: {$eq: ''}}, {module: {$exists: false}}]
                    }
                }
            },
            {name: 'sellout-detail', params: {model: {module: 'sale'}}},
            {
                name: 'general-discount-lists-detail',
                params: {model: {module: 'sale'}}
            },
            {
                name: 'pricing-wizard',
                title: 'Pricing Wizard',
                async condition(app) {
                    return !!app.setting('sale.salesPriceList');
                },
                params: {
                    filters: {
                        $or: [{module: 'sale'}, {module: {$eq: null}}, {module: {$eq: ''}}, {module: {$exists: false}}]
                    },
                    module: 'sale'
                }
            }
        ]
    },

    // Reports
    {
        name: 'reports',
        title: 'Reports',
        icon: 'chart-bar',
        items: [
            {
                name: 'sales-map',
                title: 'Sales Map',
                async condition(app) {
                    return !!app.setting('system.googleMapsApiKey');
                },
                params: {module: 'sale'}
            },
            {name: 'open-documents-list', title: 'Open Documents List', params: {module: 'sale'}},
            {name: 'open-items-list', title: 'Open Items List', params: {module: 'sale'}},
            {name: 'delivery-analysis', title: 'Delivery Analysis', params: {module: 'sale'}},
            {name: 'customer-sales-analysis', title: 'Customer Sales Analysis', params: {module: 'sale'}},
            {name: 'product-sales-analysis', title: 'Product Sales Analysis', params: {module: 'sale'}},
            {
                name: 'organization-sales-analysis',
                title: 'Organization Sales Analysis',
                params: {module: 'sale'}
            },
            // {name: 'gross-profit-analysis', title: 'Gross Profit Analysis', params: {module: 'sale'}},
            {name: 'detailed-sales-analysis', title: 'Detailed Sales Analysis', params: {module: 'sale'}},
            {
                name: 'quotation-analysis',
                title: 'Quotation Analysis',
                view: 'crm.reports.quotation-analysis'
            },
            {name: 'price-analysis', title: 'Price Analysis', params: {module: 'sale'}},
            {name: 'kit-price-analysis', title: 'Kit Price Analysis', params: {module: 'sale'}},
            {name: 'conversion-analysis', title: 'Conversion Analysis'},
            {
                name: 'customer-limit-analysis',
                title: 'Customer Limit Analysis',
                view: 'finance.reports.partner-limit-analysis',
                params: {type: 'customer'}
            },
            {
                name: 'customer-ledger',
                title: 'Customer Ledger',
                view: 'accounting.reports.partner-ledger',
                params: {type: 'customer'}
            },
            {name: 'sellable-product-analysis', title: 'Sellable Product Analysis'},
            {name: 'best-selling-products', title: 'Best Selling Products'}
        ]
    },

    // Configuration
    {
        name: 'configuration',
        title: 'Configuration',
        icon: 'wrench',
        items: [
            {
                name: 'organizations',
                title: 'Organizations',
                async condition(app) {
                    return app.setting('sale.salesOrganizations');
                }
            },
            {
                name: 'organizations-detail',
                params: {model: {scope: ['sale']}},
                async condition(app) {
                    return app.setting('sale.salesOrganizations');
                }
            },
            {
                name: 'document-types',
                title: 'Document Types',
                params: {
                    filters: {
                        $or: [{module: 'sale'}, {module: {$eq: null}}, {module: {$eq: ''}}, {module: {$exists: false}}]
                    }
                }
            },
            {
                name: 'document-types-detail',
                params: {model: {module: 'sale'}}
            },
            {name: 'discount-groups', title: 'Discount Groups'},
            {
                name: 'communication-channels',
                title: 'Communication Channels',
                view: 'system.management.configuration.communication-channels.master'
            },
            {name: 'sources', title: 'Sources'},
            {name: 'quotation-stages', title: 'Quotation Stages'},
            {name: 'quotation-stages-detail'},
            {name: 'order-stages', title: 'Order Stages'},
            {name: 'order-stages-detail'},
            {
                name: 'product-categories',
                title: 'Product Categories',
                view: 'inventory.configuration.product-categories.master'
            },
            {
                name: 'product-groups',
                title: 'Product Groups',
                view: 'inventory.configuration.product-groups.master'
            },
            {
                name: 'product-groups-detail',
                view: 'inventory.configuration.product-groups.detail'
            },
            {
                name: 'product-attribute-sets',
                title: 'Product Attribute Sets',
                view: 'inventory.configuration.product-attribute-sets.master',
                async condition(app) {
                    return app.setting('inventory.configurableProducts');
                }
            },
            {
                name: 'product-attribute-sets-detail',
                view: 'inventory.configuration.product-attribute-sets.detail'
            },
            {
                name: 'customer-groups',
                title: 'Customer Groups',
                view: 'system.management.configuration.partner-groups.master',
                params: {filters: {type: 'customer'}}
            },
            {
                name: 'customer-groups-detail',
                view: 'system.management.configuration.partner-groups.detail',
                params: {model: {type: 'customer'}}
            },
            {
                name: 'customer-additional-information',
                title: 'Customer Additional Information',
                view: 'system.management.configuration.additional-information.master',
                params: {filters: {type: 'customer'}}
            },
            {
                name: 'customer-additional-information-detail',
                view: 'system.management.configuration.additional-information.detail',
                params: {model: {type: 'customer'}}
            },
            {name: 'customer-catalog-categories', title: 'Customer Catalog Categories'},
            {
                name: 'lost-reasons',
                title: 'Lost Reasons',
                view: 'crm.configuration.lost-reasons.master'
            },
            {
                name: 'sectors-of-activity',
                title: 'Sectors Of Activity',
                view: 'system.management.configuration.sectors-of-activity.master'
            },
            {
                name: 'return-reasons',
                title: 'Return Reasons',
                view: 'system.management.configuration.return-reasons.master'
            }
        ]
    },

    // Settings
    {
        name: 'settings',
        title: 'Settings',
        icon: 'cog',
        params: {isPreview: true}
    }
];
