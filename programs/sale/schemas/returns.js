import _ from 'lodash';

export default {
    name: 'returns',
    title: 'Returns',
    collection: 'sale.returns',
    programName: 'sale',
    programTitle: 'Sale',
    icon: 'undo',
    fields(app) {
        return [
            {type: 'string', name: 'module', label: 'Module'},
            {type: 'string', name: 'status', label: 'Status'},
            {type: 'string', name: 'code', label: 'Code'},
            {type: 'string', name: 'returnReason', label: 'Return reason' },
            {type: 'string', name: 'customerGroup', label: 'Customer group' },
            {type: 'string', name: 'customerName', label: 'Customer' },
            {type: 'string', name: 'storeId', label: 'Store'},
            {type: 'string', name: 'currencyId', label: 'Currency'},
            {type: 'decimal', name: 'currencyRate', label: 'Currency rate'},
            {type: 'string', name: 'contactPersonId', label: 'Contact person'},
            {type: 'date', name: 'recordDate', label: 'Record date'},
            {type: 'date', name: 'issueDate', label: 'Issue date'},
            {type: 'date', name: 'scheduledDate', label: 'Delivery date'},
            {type: 'string', name: 'requests', label: 'Requests'},
            {type: 'string', name: 'note', label: 'Note'},
            {type: 'string', name: 'orderId', label: 'Order'},
            {
                type: 'array',
                name: 'items',
                label: 'Items',
                fields: [
                    {type: 'string', name: 'productId', label: 'Product'},
                    {type: 'string', name: 'productCode', label: 'Product code'},
                    {type: 'string', name: 'returnCode', label: 'Return code'},
                    {type: 'string', name: 'productDefinition', label: 'Product definition'},
                    {type: 'string', name: 'productType', label: 'Product type'},
                    {type: 'string', name: 'barcode', label: 'Barcode'},
                    {type: 'string', name: 'unit', label: 'Unit'},
                    {type: 'string', name: 'description', label: 'Description'},
                    {type: 'string', name: 'serialNumber', label: 'Serial number'},
                    {type: 'string', name: 'lotNumber', label: 'Lot number'},
                    {type: 'date', name: 'scheduledDate', label: 'Delivery Date'},
                    {type: 'string', name: 'branchId', label: 'Branch Office'},
                    {type: 'string', name: 'warehouseId', label: 'Warehouse'},
                    {type: 'decimal', name: 'quantity', label: 'Quantity'},
                    {type: 'string', name: 'unitId', label: 'Unit'},
                    {type: 'string', name: 'baseUnitId', label: 'Base unit'},
                    {type: 'decimal', name: 'baseQuantity', label: 'Base quantity'},
                    {type: 'decimal', name: 'unitCost', label: 'Unit cost'},
                    {type: 'decimal', name: 'stockQuantity', label: 'Stock on hand'},
                    {type: 'decimal', name: 'orderedQuantity', label: 'Ordered quantity'},
                    {type: 'decimal', name: 'assignedQuantity', label: 'Assigned quantity'},
                    {type: 'decimal', name: 'availableQuantity', label: 'Available quantity'},
                    {type: 'decimal', name: 'warehouseStockQuantity', label: 'Warehouse stock on hand'},
                    {type: 'decimal', name: 'warehouseOrderedQuantity', label: 'Warehouse ordered quantity'},
                    {type: 'decimal', name: 'warehouseAssignedQuantity', label: 'Warehouse assigned quantity'},
                    {type: 'decimal', name: 'warehouseAvailableQuantity', label: 'Warehouse available quantity'},
                    {type: 'string', name: 'financialProjectId', label: 'Project'},
                    {type: 'decimal', name: 'cost', label: 'Cost'},
                    {type: 'string', name: 'partnerId', label: 'Customer'},
                    {type: 'string', name: 'deliveryReceiverId', label: 'Delivery receiver'}
                ]
            }
        ];
    },
    async bulkDocumentExtra(app, schema, documents) {
        const company = await app.collection('kernel.company').findOne({ $select: ['address'] });
        let companyCountry = null;
        if (!!company && !!company.address && !!company.address.countryId) {
            companyCountry = await app.collection('kernel.countries').findOne({ _id: company.address.countryId });
        }

        const productsMap = {};
        const warehousesMap = {};
        const unitsMap = {};
        const taxesMap = {};
        const financialProjectsMap = {};
        const contactMap = {};
        const carriersMap = {};
        const returnReasonsMap = {};
        const partnerGroupsMap = {};
        const partnersMap = {};

        let productIds = [];
        let warehouseIds = [];
        let unitIds = [];
        let taxIds = [];
        let financialProjectIds = [];
        let addressIds = [];
        let carrierIds = [];
        let returnReasonIds = [];
        let partnerGroupIds = [];
        let partnerIds = [];

        for (const document of documents || []) {
            if (!!document.financialProjectId) financialProjectIds.push(document.financialProjectId);
            if (!!document.deliveryAddressId) addressIds.push(document.deliveryAddressId);
            if (!!document.invoiceAddressId) addressIds.push(document.invoiceAddressId);
            if (!!document.carrierId) carrierIds.push(document.carrierId);
            if (!!document.returnReasonId) returnReasonIds.push(document.returnReasonId);
            if (!!document.partnerGroupId) partnerGroupIds.push(document.partnerGroupId);
            if (!!document.partnerId) partnerIds.push(document.partnerId);

            for (const item of document.items || []) {
                if (!!item.productId) productIds.push(item.productId);
                if (!!item.warehouseId) warehouseIds.push(item.warehouseId);
                if (!!item.unitId) unitIds.push(item.unitId);
                if (!!item.baseUnitId) unitIds.push(item.baseUnitId);
                if (!!item.taxId) taxIds.push(item.taxId);
                if (!!item.financialProjectId) financialProjectIds.push(item.financialProjectId);
            }
        }
        productIds = _.uniq(productIds);
        warehouseIds = _.uniq(warehouseIds);
        unitIds = _.uniq(unitIds);
        taxIds = _.uniq(taxIds);
        financialProjectIds = _.uniq(financialProjectIds);
        addressIds = _.uniq(addressIds);
        carrierIds = _.uniq(carrierIds);
        returnReasonIds = _.uniq(returnReasonIds);
        partnerGroupIds = _.uniq(partnerGroupIds);
        partnerIds = _.uniq(partnerIds);

        if (productIds.length > 0) {
            const products = await app.collection('inventory.products').find({
                _id: {$in: productIds},
                $select: ['code', 'definition', 'barcode', 'type', 'groupIds', 'categoryId', 'categoryPath', 'brandId'],
                $populate: ['groups', 'category', 'brand'],
                $disableSoftDelete: true,
                $disableActiveCheck: true
            });
            for (const product of products) {
                productsMap[product._id] = product;
            }
        }
        if (warehouseIds.length > 0) {
            const warehouses = await app.collection('inventory.warehouses').find({
                _id: {$in: warehouseIds},
                $select: ['shortName', 'name'],
                $disableSoftDelete: true,
                $disableActiveCheck: true
            });
            for (const warehouse of warehouses) {
                warehousesMap[warehouse._id] = warehouse;
            }
        }
        if (unitIds.length > 0) {
            const units = await app.collection('kernel.units').find({
                _id: {$in: unitIds},
                $select: ['name'],
                $disableSoftDelete: true,
                $disableActiveCheck: true
            });
            for (const unit of units) {
                unitsMap[unit._id] = unit;
            }
        }
        if (taxIds.length > 0) {
            const taxes = await app.collection('kernel.taxes').find({
                _id: {$in: taxIds},
                $select: ['name'],
                $disableSoftDelete: true,
                $disableActiveCheck: true
            });
            for (const tax of taxes) {
                taxesMap[tax._id] = tax;
            }
        }
        if (financialProjectIds.length > 0) {
            const financialProjects = await app.collection('kernel.financial-projects').find({
                _id: {$in: financialProjectIds},
                $select: ['code', 'name'],
                $disableSoftDelete: true,
                $disableActiveCheck: true
            });
            for (const financialProject of financialProjects) {
                financialProjectsMap[financialProject._id] = financialProject;
            }
        }
        if (addressIds.length > 0) {
            const contacts = await app.collection('kernel.contacts').find({
                _id: {$in: addressIds},
                $select: ['_id', 'code', 'name'],
                $disableSoftDelete: true,
                $disableActiveCheck: true
            });
            for (const contact of contacts) {
                contactMap[contact._id] = contact;
            }
        }
        if (carrierIds.length > 0) {
            const carriers = await app.collection('logistics.carriers').find({
                _id: {$in: carrierIds},
                $select: ['_id', 'code', 'name'],
                $disableSoftDelete: true,
                $disableActiveCheck: true
            });
            for (const carrier of carriers) {
                carriersMap[carrier._id] = carrier;
            }
        }
        if (returnReasonIds.length > 0) {
            const returnReasons = await app.collection('kernel.return-reasons').find({
                _id: {$in: returnReasonIds},
                $select: ['_id', 'name'],
                $disableSoftDelete: true,
                $disableActiveCheck: true
            });
            for (const reason of returnReasons) {
                returnReasonsMap[reason._id] = reason;
            }
        }
        if (partnerGroupIds.length > 0) {
            const partnerGroups = await app.collection('kernel.partner-groups').find({
                _id: {$in: partnerGroupIds},
                $select: ['_id', 'name'],
                $disableSoftDelete: true,
                $disableActiveCheck: true
            });
            for (const group of partnerGroups) {
                partnerGroupsMap[group._id] = group;
            }
        }
        if (partnerIds.length > 0) {
            const partners = await app.collection('kernel.partners').find({
                _id: {$in: partnerIds},
                $select: ['_id', 'name'],
                $disableSoftDelete: true,
                $disableActiveCheck: true
            });
            for (const partner of partners) {
                partnersMap[partner._id] = partner;
            }
        }

        return {
            companyCountry: companyCountry || {},
            productsMap,
            warehousesMap,
            unitsMap,
            taxesMap,
            financialProjectsMap,
            contactMap,
            carriersMap,
            returnReasonsMap,
            partnerGroupsMap,
            partnersMap
        };
    },
    async data(
        app,
        schema,
        document,
        {
            productsMap,
            warehousesMap,
            unitsMap,
            taxesMap,
            financialProjectsMap,
            returnReasonsMap,
            partnerGroupsMap,
            partnersMap
        }
    ) {
        const data = {};
        const moduleOptions = [
            {value: 'sale', label: 'Sale'},
            {value: 'ecommerce', label: 'E-Commerce'}
        ];
        const moduleLabel = (moduleOptions.find(option => option.value === document.module) || {}).label || '';
        if (!!moduleLabel) {
            data.module = app.translate(moduleLabel);
        } else {
            data.module = app.translate('Sale');
        }
        const statusOptions = [
            {value: 'draft', label: 'Draft'},
            {value: 'payment-planned', label: 'Payment Planned'},
            {value: 'approved', label: 'Approved'},
            {value: 'to-invoice', label: 'To Invoice'},
            {value: 'invoiced', label: 'Invoiced'},
            {value: 'canceled', label: 'Canceled'}
        ];
        const statusLabel = (statusOptions.find(option => option.value === document.status) || {}).label || '';
        if (!!statusLabel) {
            data.status = app.translate(statusLabel);
        } else {
            data.status = app.translate('Draft');
        }
        data.code = document.code;
        data.storeId = document.storeId;
        data.returnReasonId = document.returnReasonId;
        data.partnerGroupId = document.partnerGroupId;
        data.partnerId = document.partnerId;

        data.returnReason = (returnReasonsMap[document.returnReasonId] || {}).name || '';
        data.customerGroup = (partnerGroupsMap[document.partnerGroupId] || {}).name || '';
        data.customerName = (partnersMap[document.partnerId] || {}).name || '';
        data.currencyId = document.currencyId;
        data.currencyRate = document.currencyRate;
        data.contactPersonId = document.contactPersonId;
        data.recordDate = document.recordDate;
        data.issueDate = document.issueDate;
        data.scheduledDate = document.scheduledDate;
        data.requests = document.requests;
        data.note = document.note;
        data.orderId = document.orderId;
        // Items
        data.items = [];
        if (Array.isArray(document.items)) {
            for (const item of document.items) {
                const product = productsMap[item.productId];
                const warehouse = warehousesMap[item.warehouseId];
                const unit = unitsMap[item.unitId];
                const baseUnit = unitsMap[item.baseUnitId];
                const tax = taxesMap[item.taxId];
                const financialProject = financialProjectsMap[item.financialProjectId];
                const i = {};

                i.returnStatus = data.status;
                i.returnCode = data.code;
                i.returnReason = data.returnReason;
                i.customerGroup = data.customerGroup;
                i.customerName = data.customerName;
                i.currencyId = data.currencyId;
                i.currencyRate = data.currencyRate;
                i.recordDate = data.recordDate;
                i.issueDate = data.issueDate;
                i.scheduledDate = data.scheduledDate;
                i.note = data.note;
                i.orderId = data.orderId;

                i.productId = item.productId;
                i.productCode = item.productCode;
                i.productDefinition = item.productDefinition;
                const productTypeOptions = [
                    {value: 'stockable', label: 'Stockable product'},
                    {value: 'service', label: 'Service product'},
                    {value: 'kit', label: 'Kit product'}
                ];
                const productTypeLabel = (productTypeOptions.find(option => option.value === item.productType) || {}).label || '';
                if (!!productTypeLabel) {
                    i.productType = app.translate(productTypeLabel);
                } else {
                    i.productType = app.translate('Stockable product');
                }

                i.barcode = item.barcode;
                i.description = item.description;
                i.serialNumber = item.serialNumber;
                i.lotNumber = item.lotNumber;
                i.scheduledDate = item.scheduledDate;
                i.branchId = item.branchId;
                i.warehouseId = item.warehouseId;
                i.quantity = item.quantity;
                i.unitId = item.unitId;
                if (!!unit) i.unit = unit.name;
                i.baseUnitId = item.baseUnitId;
                i.baseQuantity = item.baseQuantity;
                i.unitCost = item.unitCost;
                i.stockQuantity = item.stockQuantity;
                i.orderedQuantity = item.orderedQuantity;
                i.assignedQuantity = item.assignedQuantity;
                i.availableQuantity = item.availableQuantity;
                i.warehouseStockQuantity = item.warehouseStockQuantity;
                i.warehouseOrderedQuantity = item.warehouseOrderedQuantity;
                i.warehouseAssignedQuantity = item.warehouseAssignedQuantity;
                i.warehouseAvailableQuantity = item.warehouseAvailableQuantity;
                i.financialProjectId = item.financialProjectId;
                i.cost = item.cost;
                i.partnerId = item.partnerId;
                i.deliveryReceiverId = item.deliveryReceiverId;

                if (product) {
                    i.productName = product.definition;
                    i.productCategory = (product.category || {}).name;
                    i.productBrand = (product.brand || {}).name;
                }
                if (warehouse) {
                    i.warehouseName = warehouse.name;
                }
                if (unit) {
                    i.unitName = unit.name;
                }
                if (baseUnit) {
                    i.baseUnitName = baseUnit.name;
                }
                if (financialProject) {
                    i.projectName = financialProject.name;
                }

                data.items.push(i);
            }
        }
        return data;
    }
};