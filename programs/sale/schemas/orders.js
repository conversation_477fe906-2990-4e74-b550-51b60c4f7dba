import _ from 'lodash';

export default {
    name: 'orders',
    title: 'Orders',
    collection: 'sale.orders',
    workflow: {
        allowedOperations: ['create', 'update', 'remove'],
        allowedActions: ['approval', 'assignation', 'notification', 'validation'],
        documentView: 'sale.sales.orders-detail'
    },
    fields(app) {
        return [
            {type: 'string', name: 'module', label: 'Module'},
            {type: 'string', name: 'status', label: 'Status'},
            {type: 'string', name: 'code', label: 'Code'},
            {type: 'string', name: 'storeName', label: 'Store name'},
            {type: 'string', name: 'stageName', label: 'Stage name'},
            {type: 'string', name: 'documentType', label: 'Document type'},
            {type: 'string', name: 'customerType', label: 'Customer type'},
            {type: 'string', name: 'customerGroup', label: 'Customer group'},
            {type: 'string', name: 'customerCode', label: 'Customer code'},
            {type: 'string', name: 'customerName', label: 'Customer name'},
            {type: 'string', name: 'customerEmail', label: 'Customer e-mail'},
            {type: 'string', name: 'customerPhone', label: 'Customer phone'},
            {type: 'string', name: 'customerTinIdentity', label: 'Customer tin / identity'},
            {type: 'string', name: 'currency', label: 'Currency'},
            {type: 'decimal', name: 'currencyRate', label: 'Currency rate'},
            {type: 'string', name: 'contactPerson', label: 'Contact person'},
            {type: 'string', name: 'reference', label: 'Reference'},
            {type: 'string', name: 'referencePartnerCode', label: 'Reference partner code'},
            {type: 'string', name: 'referencePartnerName', label: 'Reference partner name'},
            {type: 'datetime', name: 'recordDate', label: 'Record date'},
            {type: 'datetime', name: 'orderDate', label: 'Order date'},
            {type: 'datetime', name: 'deliveryDate', label: 'Delivery date'},
            {type: 'string', name: 'branchCode', label: 'Branch code'},
            {type: 'string', name: 'branchName', label: 'Branch name'},
            {type: 'string', name: 'projectCode', label: 'Project code'},
            {type: 'string', name: 'projectName', label: 'Project name'},
            {type: 'string', name: 'communicationChannel', label: 'Communication channel'},
            {type: 'string', name: 'source', label: 'Source'},
            {type: 'string', name: 'organization', label: 'Organization'},
            {type: 'string', name: 'salesManager', label: 'Sales manager'},
            {type: 'string', name: 'salesperson', label: 'Salesperson'},
            {type: 'string', name: 'warehouseCode', label: 'Warehouse code'},
            {type: 'string', name: 'warehouseName', label: 'Warehouse name'},
            {type: 'string', name: 'invoiceAddressAddress', label: 'Invoice address address'},
            {type: 'string', name: 'invoiceAddressCountry', label: 'Invoice address country'},
            {type: 'string', name: 'invoiceAddressState', label: 'Invoice address state'},
            {type: 'string', name: 'invoiceAddressCity', label: 'Invoice address city'},
            {type: 'string', name: 'invoiceAddressDistrict', label: 'Invoice address district'},
            {type: 'string', name: 'invoiceAddressSubDistrict', label: 'Invoice address sub-district'},
            {type: 'string', name: 'invoiceAddressStreet', label: 'Invoice address street'},
            {type: 'string', name: 'invoiceAddressDoorNumber', label: 'Invoice address door number'},
            {type: 'string', name: 'invoiceAddressApartmentNumber', label: 'Invoice address apartment number'},
            {type: 'string', name: 'invoiceAddressPostalCode', label: 'Invoice address postal code'},
            {type: 'string', name: 'invoiceAddressCode', label: 'Invoice address code'},
            {type: 'string', name: 'invoiceAddressName', label: 'Invoice address name'},
            {type: 'string', name: 'deliveryAddressAddress', label: 'Delivery address address'},
            {type: 'string', name: 'deliveryAddressCountry', label: 'Delivery address country'},
            {type: 'string', name: 'deliveryAddressState', label: 'Delivery address state'},
            {type: 'string', name: 'deliveryAddressCity', label: 'Delivery address city'},
            {type: 'string', name: 'deliveryAddressDistrict', label: 'Delivery address district'},
            {type: 'string', name: 'deliveryAddressSubDistrict', label: 'Delivery address sub-district'},
            {type: 'string', name: 'deliveryAddressStreet', label: 'Delivery address street'},
            {type: 'string', name: 'deliveryAddressDoorNumber', label: 'Delivery address door number'},
            {type: 'string', name: 'deliveryAddressApartmentNumber', label: 'Delivery address apartment number'},
            {type: 'string', name: 'deliveryAddressPostalCode', label: 'Delivery address postal code'},
            {type: 'string', name: 'deliveryAddressCode', label: 'Delivery address code'},
            {type: 'string', name: 'deliveryAddressName', label: 'Delivery address name'},
            {type: 'string', name: 'deliveryPriority', label: 'Delivery priority'},
            {type: 'string', name: 'cargoTrackingCode', label: 'Cargo tracking code'},
            {type: 'string', name: 'paymentTerm', label: 'Payment term'},
            {type: 'string', name: 'priceList', label: 'Price list'},
            {type: 'string', name: 'customerPriceList', label: 'Customer price list'},
            {type: 'string', name: 'guarantee', label: 'Guarantee'},
            {type: 'datetime', name: 'paymentPlanningDate', label: 'Payment planning date'},
            {type: 'text', name: 'note', label: 'Note'},
            {type: 'string', name: 'carrierCode', label: 'Carrier Code'},
            {type: 'string', name: 'carrierName', label: 'Carrier Name'},

            {type: 'decimal', name: 'subTotal', label: 'Subtotal'},
            {type: 'decimal', name: 'discount', label: 'Discount'},
            {type: 'decimal', name: 'discountAmount', label: 'Discount amount'},
            {type: 'decimal', name: 'subTotalAfterDiscount', label: 'Subtotal after discount'},
            {type: 'decimal', name: 'taxTotal', label: 'Tax total'},
            {type: 'decimal', name: 'rounding', label: 'Rounding'},
            {type: 'decimal', name: 'grandTotal', label: 'Grand total'},
            {type: 'decimal', name: 'basePrice', label: 'Base price'},
            {type: 'decimal', name: 'grossProfit', label: 'Gross profit'},
            {type: 'decimal', name: 'profitRate', label: 'Profit rate'},
            {type: 'decimal', name: 'profitMargin', label: 'Profit margin'},
            {type: 'decimal', name: 'subTotalSC', label: 'Subtotal (SC)'},
            {type: 'decimal', name: 'discountAmountSC', label: 'Discount amount (SC)'},
            {type: 'decimal', name: 'subTotalAfterDiscountSC', label: 'Subtotal after discount (SC)'},
            {type: 'decimal', name: 'taxTotalSC', label: 'Tax total (SC)'},
            {type: 'decimal', name: 'roundingSC', label: 'Rounding (SC)'},
            {type: 'decimal', name: 'grandTotalSC', label: 'Grand total (SC)'},
            {type: 'decimal', name: 'basePriceSC', label: 'Base price (SC)'},
            {type: 'decimal', name: 'grossProfitSC', label: 'Gross profit (SC)'},
            {type: 'decimal', name: 'cashAmount', label: 'Cash amount'},
            {type: 'decimal', name: 'moneyTransferAmount', label: 'Money transfer amount'},
            {type: 'decimal', name: 'chequeAmount', label: 'Cheque amount'},
            {type: 'decimal', name: 'promissoryNoteAmount', label: 'Promissory note amount'},
            {type: 'decimal', name: 'posAmount', label: 'Pos amount'},
            {type: 'decimal', name: 'cashInstallmentCount', label: 'Cash installment count'},
            {type: 'decimal', name: 'moneyTransferInstallmentCount', label: 'Money transfer installment count'},
            {type: 'decimal', name: 'chequeInstallmentCount', label: 'Cheque amount'},
            {type: 'decimal', name: 'promissoryNoteInstallmentCount', label: 'Promissory note installment count'},
            {type: 'decimal', name: 'posInstallmentCount', label: 'Pos installment count'},
            {type: 'decimal', name: 'installmentCount', label: 'Installment count'},

            {type: 'decimal', name: 'exchangeRateUSD', label: 'Exchange rate USD'},
            {type: 'decimal', name: 'exchangeRateEUR', label: 'Exchange rate EUR'},
            {type: 'decimal', name: 'exchangeRateGBP', label: 'Exchange rate GBP'},
            {type: 'decimal', name: 'exchangeRateRUB', label: 'Exchange rate RUB'},

            // IDS.
            {type: 'string', name: 'storeId', label: 'Store ID'},
            {type: 'string', name: 'stageId', label: 'Stage ID'},
            {type: 'string', name: 'documentTypeId', label: 'Document type ID'},
            {type: 'string', name: 'customerGroupId', label: 'Customer group ID'},
            {type: 'string', name: 'customerId', label: 'Customer ID'},
            {type: 'string', name: 'currencyId', label: 'Currency ID'},
            {type: 'string', name: 'contactPersonId', label: 'Contact person ID'},
            {type: 'string', name: 'referencePartnerId', label: 'Reference partner ID'},
            {type: 'string', name: 'branchId', label: 'Branch ID'},
            {type: 'string', name: 'projectId', label: 'Project ID'},
            {type: 'string', name: 'communicationChannelId', label: 'Communication channel ID'},
            {type: 'string', name: 'sourceId', label: 'Source ID'},
            {type: 'string', name: 'organizationId', label: 'Organization ID'},
            {type: 'string', name: 'salesManagerId', label: 'Sales manager ID'},
            {type: 'string', name: 'salespersonId', label: 'Salesperson ID'},
            {type: 'string', name: 'warehouseId', label: 'Warehouse ID'},
            {type: 'string', name: 'paymentTermId', label: 'Payment term ID'},
            {type: 'string', name: 'priceListId', label: 'Price list ID'},
            {type: 'string', name: 'customerPriceListId', label: 'Customer price list ID'},
            {type: 'string', name: 'guaranteeId', label: 'Guarantee ID'},
            {type: 'string', name: 'deliveryAddressId', label: 'Delivery address ID'},
            {type: 'string', name: 'invoiceAddressId', label: 'Invoice address ID'},
            {type: 'string', name: 'carrierId', label: 'Carrier ID'},

            {
                type: 'array',
                name: 'items',
                label: 'Items',
                fields: [
                    {type: 'string', name: 'orderStatus', label: 'Order status'},
                    {type: 'string', name: 'orderCode', label: 'Order code'},
                    {type: 'string', name: 'documentType', label: 'Document type'},
                    {type: 'string', name: 'customerGroup', label: 'Customer group'},
                    {type: 'string', name: 'customerCode', label: 'Customer code'},
                    {type: 'string', name: 'customerName', label: 'Customer name'},
                    {type: 'string', name: 'currency', label: 'Currency'},
                    {type: 'decimal', name: 'currencyRate', label: 'Currency rate'},
                    {type: 'datetime', name: 'recordDate', label: 'Record date'},
                    {type: 'datetime', name: 'orderDate', label: 'Order date'},
                    {type: 'datetime', name: 'deliveryDate', label: 'Delivery date'},
                    {type: 'string', name: 'branchCode', label: 'Branch code'},
                    {type: 'string', name: 'branchName', label: 'Branch name'},
                    {type: 'string', name: 'warehouseCode', label: 'Warehouse code'},
                    {type: 'string', name: 'warehouseName', label: 'Warehouse name'},
                    {type: 'string', name: 'projectCode', label: 'Project code'},
                    {type: 'string', name: 'projectName', label: 'Project name'},
                    {type: 'string', name: 'communicationChannel', label: 'Communication channel'},
                    {type: 'string', name: 'source', label: 'Source'},
                    {type: 'string', name: 'organization', label: 'Organization'},
                    {type: 'string', name: 'salesManager', label: 'Sales manager'},
                    {type: 'string', name: 'salesperson', label: 'Salesperson'},

                    {type: 'string', name: 'productCode', label: 'Product code'},
                    {type: 'string', name: 'productDefinition', label: 'Product definition'},
                    {type: 'string', name: 'productType', label: 'Product type'},
                    {type: 'string', name: 'productCategory', label: 'Product category'},
                    {type: 'string', name: 'productCategoryPath', label: 'Product category path'},
                    {type: 'string', name: 'productGroup', label: 'Product group'},
                    {type: 'string', name: 'productBrand', label: 'Product brand'},
                    {type: 'string', name: 'productBarcode', label: 'Product barcode'},
                    {type: 'text', name: 'description', label: 'Description'},

                    {type: 'decimal', name: 'quantity', label: 'Quantity'},
                    {type: 'string', name: 'unit', label: 'Unit'},
                    {type: 'decimal', name: 'baseQuantity', label: 'Base quantity'},
                    {type: 'string', name: 'baseUnit', label: 'Base unit'},
                    {type: 'decimal', name: 'stockQuantity', label: 'Stock on hand'},
                    {type: 'decimal', name: 'orderedQuantity', label: 'Ordered quantity'},
                    {type: 'decimal', name: 'assignedQuantity', label: 'Assigned quantity'},
                    {type: 'decimal', name: 'availableQuantity', label: 'Available quantity'},
                    {type: 'decimal', name: 'warehouseStockQuantity', label: 'Warehouse stock on hand'},
                    {type: 'decimal', name: 'warehouseOrderedQuantity', label: 'Warehouse ordered quantity'},
                    {type: 'decimal', name: 'warehouseAssignedQuantity', label: 'Warehouse assigned quantity'},
                    {type: 'decimal', name: 'warehouseAvailableQuantity', label: 'Warehouse available quantity'},

                    {type: 'decimal', name: 'unitPrice', label: 'Unit price'},
                    {type: 'decimal', name: 'grossUnitPrice', label: 'Gross unit price'},
                    {type: 'decimal', name: 'discount', label: 'Discount'},
                    {type: 'decimal', name: 'unitPriceAfterDiscount', label: 'Unit price after discount'},
                    {type: 'decimal', name: 'grossUnitPriceAfterDiscount', label: 'Gross unit price after discount'},
                    {type: 'string', name: 'tax', label: 'Tax'},
                    {type: 'decimal', name: 'taxTotal', label: 'Tax total'},
                    {type: 'decimal', name: 'grossTotal', label: 'Gross total'},
                    {type: 'decimal', name: 'realTotal', label: 'Real total'},
                    {type: 'decimal', name: 'total', label: 'Total'},
                    {type: 'decimal', name: 'unitPriceSC', label: 'Unit price (SC)'},
                    {type: 'decimal', name: 'grossUnitPriceSC', label: 'Gross unit price (SC)'},
                    {type: 'decimal', name: 'unitPriceAfterDiscountSC', label: 'Unit price after discount (SC)'},
                    {
                        type: 'decimal',
                        name: 'grossUnitPriceAfterDiscountSC',
                        label: 'Gross unit price after discount (SC)'
                    },
                    {type: 'decimal', name: 'taxTotalSC', label: 'Tax total (SC)'},
                    {type: 'decimal', name: 'grossTotalSC', label: 'Gross total (SC)'},
                    {type: 'decimal', name: 'realTotalSC', label: 'Real total (SC)'},
                    {type: 'decimal', name: 'totalSC', label: 'Total (SC)'},

                    {type: 'string', name: 'costSource', label: 'Cost source'},
                    {type: 'decimal', name: 'unitCost', label: 'Unit cost'},
                    {type: 'decimal', name: 'unitProfit', label: 'Unit profit'},
                    {type: 'decimal', name: 'cost', label: 'Cost'},
                    {type: 'decimal', name: 'profitRate', label: 'Profit rate'},
                    {type: 'decimal', name: 'profitMargin', label: 'Profit margin'},
                    {type: 'decimal', name: 'profit', label: 'Profit'},

                    {type: 'decimal', name: 'exchangeRateUSD', label: 'Exchange rate USD'},
                    {type: 'decimal', name: 'exchangeRateEUR', label: 'Exchange rate EUR'},
                    {type: 'decimal', name: 'exchangeRateGBP', label: 'Exchange rate GBP'},
                    {type: 'decimal', name: 'exchangeRateRUB', label: 'Exchange rate RUB'},

                    // IDS.
                    {type: 'string', name: 'documentTypeId', label: 'Document type ID'},
                    {type: 'string', name: 'customerGroupId', label: 'Customer group ID'},
                    {type: 'string', name: 'customerId', label: 'Customer ID'},
                    {type: 'string', name: 'currencyId', label: 'Currency ID'},
                    {type: 'string', name: 'branchId', label: 'Branch ID'},
                    {type: 'string', name: 'projectId', label: 'Project ID'},
                    {type: 'string', name: 'communicationChannelId', label: 'Communication channel ID'},
                    {type: 'string', name: 'sourceId', label: 'Source ID'},
                    {type: 'string', name: 'organizationId', label: 'Organization ID'},
                    {type: 'string', name: 'salesManagerId', label: 'Sales manager ID'},
                    {type: 'string', name: 'salespersonId', label: 'Salesperson ID'},
                    {type: 'string', name: 'warehouseId', label: 'Warehouse ID'},
                    {type: 'string', name: 'productId', label: 'Product ID'},
                    {type: 'string', name: 'baseUnitId', label: 'Base unit ID'},
                    {type: 'string', name: 'unitId', label: 'Unit ID'},
                    {type: 'string', name: 'taxId', label: 'Tax ID'}
                ]
            },

            {
                type: 'array',
                name: 'profits',
                label: 'Profits',
                fields: [
                    {type: 'string', name: 'orderStatus', label: 'Order status'},
                    {type: 'string', name: 'orderCode', label: 'Order code'},
                    {type: 'string', name: 'documentType', label: 'Document type'},
                    {type: 'string', name: 'customerGroup', label: 'Customer group'},
                    {type: 'string', name: 'customerCode', label: 'Customer code'},
                    {type: 'string', name: 'customerName', label: 'Customer name'},
                    {type: 'string', name: 'currency', label: 'Currency'},
                    {type: 'decimal', name: 'currencyRate', label: 'Currency rate'},
                    {type: 'datetime', name: 'recordDate', label: 'Record date'},
                    {type: 'datetime', name: 'orderDate', label: 'Order date'},
                    {type: 'datetime', name: 'deliveryDate', label: 'Delivery date'},
                    {type: 'string', name: 'branchCode', label: 'Branch code'},
                    {type: 'string', name: 'branchName', label: 'Branch name'},
                    {type: 'string', name: 'projectCode', label: 'Project code'},
                    {type: 'string', name: 'projectName', label: 'Project name'},
                    {type: 'string', name: 'warehouseCode', label: 'Warehouse code'},
                    {type: 'string', name: 'warehouseName', label: 'Warehouse name'},
                    {type: 'string', name: 'communicationChannel', label: 'Communication channel'},
                    {type: 'string', name: 'source', label: 'Source'},
                    {type: 'string', name: 'organization', label: 'Organization'},
                    {type: 'string', name: 'salesManager', label: 'Sales manager'},
                    {type: 'string', name: 'salesperson', label: 'Salesperson'},

                    {type: 'string', name: 'productCode', label: 'Product code'},
                    {type: 'string', name: 'productDefinition', label: 'Product definition'},
                    {type: 'string', name: 'productType', label: 'Product type'},
                    {type: 'string', name: 'productCategory', label: 'Product category'},
                    {type: 'string', name: 'productCategoryPath', label: 'Product category path'},
                    {type: 'string', name: 'productGroup', label: 'Product group'},
                    {type: 'string', name: 'productBrand', label: 'Product brand'},
                    {type: 'string', name: 'productBarcode', label: 'Product barcode'},

                    {type: 'string', name: 'type', label: 'Type'},
                    {type: 'boolean', name: 'isDefault', label: 'Is default'},

                    {type: 'decimal', name: 'quantity', label: 'Quantity'},
                    {type: 'string', name: 'unit', label: 'Unit'},
                    {type: 'decimal', name: 'salesPrice', label: 'Sales price'},
                    {type: 'decimal', name: 'totalSalesPrice', label: 'Total sales price'},
                    ...(!!app.setting('system.freight')
                        ? [
                              {type: 'decimal', name: 'totalFreight', label: 'Freight'},
                              {type: 'decimal', name: 'totalFreightSC', label: 'Freight (SC)'}
                          ]
                        : []),
                    {type: 'decimal', name: 'basePrice', label: 'Base price'},
                    {type: 'decimal', name: 'totalBasePrice', label: 'Total base price'},
                    {type: 'decimal', name: 'grossProfit', label: 'Gross profit'},
                    {type: 'decimal', name: 'totalGrossProfit', label: 'Total gross profit'},
                    {type: 'decimal', name: 'salesPriceSC', label: 'Sales price (SC)'},
                    {type: 'decimal', name: 'totalSalesPriceSC', label: 'Total sales price (SC)'},
                    {type: 'decimal', name: 'basePriceSC', label: 'Base price (SC)'},
                    {type: 'decimal', name: 'totalBasePriceSC', label: 'Total base price (SC)'},
                    {type: 'decimal', name: 'grossProfitSC', label: 'Gross profit (SC)'},
                    {type: 'decimal', name: 'totalGrossProfitSC', label: 'Total gross profit (SC)'},
                    {type: 'decimal', name: 'profitRate', label: 'Profit rate'},
                    {type: 'decimal', name: 'profitMargin', label: 'Profit margin'},

                    {type: 'decimal', name: 'exchangeRateUSD', label: 'Exchange rate USD'},
                    {type: 'decimal', name: 'exchangeRateEUR', label: 'Exchange rate EUR'},
                    {type: 'decimal', name: 'exchangeRateGBP', label: 'Exchange rate GBP'},
                    {type: 'decimal', name: 'exchangeRateRUB', label: 'Exchange rate RUB'},

                    // IDS.
                    {type: 'string', name: 'documentTypeId', label: 'Document type ID'},
                    {type: 'string', name: 'customerGroupId', label: 'Customer group ID'},
                    {type: 'string', name: 'customerId', label: 'Customer ID'},
                    {type: 'string', name: 'currencyId', label: 'Currency ID'},
                    {type: 'string', name: 'branchId', label: 'Branch ID'},
                    {type: 'string', name: 'projectId', label: 'Project ID'},
                    {type: 'string', name: 'communicationChannelId', label: 'Communication channel ID'},
                    {type: 'string', name: 'sourceId', label: 'Source ID'},
                    {type: 'string', name: 'organizationId', label: 'Organization ID'},
                    {type: 'string', name: 'salesManagerId', label: 'Sales manager ID'},
                    {type: 'string', name: 'salespersonId', label: 'Salesperson ID'},
                    {type: 'string', name: 'productId', label: 'Product ID'},
                    {type: 'string', name: 'unitId', label: 'Unit ID'},
                    {type: 'string', name: 'typeId', label: 'Type ID'}
                ]
            },

            {
                type: 'array',
                name: 'relatedDocuments',
                label: 'Related Documents',
                fields: [
                    {type: 'string', name: 'collection', label: 'Collection'},
                    {type: 'string', name: 'view', label: 'View'},
                    {type: 'string', name: 'title', label: 'Title'},
                    {type: ['string'], name: 'ids', label: 'IDs'}
                ]
            }
        ];
    },
    async bulkDocumentExtra(app, schema, documents) {
        const company = await app.collection('kernel.company').findOne({
            $select: ['address']
        });
        let companyCountry = null;
        if (!!company && !!company.address && !!company.address.countryId) {
            companyCountry = await app.collection('kernel.countries').findOne({
                _id: company.address.countryId
            });
        }

        const basePriceLists = await app.collection('sale.price-lists').find({
            status: 'published',
            isBasePriceList: true,
            $select: ['_id', 'code', 'name']
        });

        const profits = await app.collection('sale.document-profits').find({
            documentId: {$in: _.uniq(documents.map(document => document._id))},
            documentCollection: 'sale.orders'
        });
        const profitsMap = {};
        for (const profit of profits) {
            profitsMap[profit.documentId] = profit;
        }

        const productsMap = {};
        const warehousesMap = {};
        const unitsMap = {};
        const taxesMap = {};
        const financialProjectsMap = {};
        const contactMap = {};
        const carriersMap = {};

        let productIds = [];
        let warehouseIds = [];
        let unitIds = [];
        let taxIds = [];
        let financialProjectIds = [];
        let addressIds = [];
        let carrierIds = [];

        for (const document of documents || []) {
            if (!!document.financialProjectId) financialProjectIds.push(document.financialProjectId);
            if (!!document.deliveryAddressId) addressIds.push(document.deliveryAddressId);
            if (!!document.invoiceAddressId) addressIds.push(document.invoiceAddressId);
            if (!!document.carrierId) carrierIds.push(document.carrierId);

            for (const item of document.items || []) {
                if (!!item.productId) productIds.push(item.productId);
                if (!!item.warehouseId) warehouseIds.push(item.warehouseId);
                if (!!item.unitId) unitIds.push(item.unitId);
                if (!!item.baseUnitId) unitIds.push(item.baseUnitId);
                if (!!item.taxId) taxIds.push(item.taxId);
                if (!!item.financialProjectId) financialProjectIds.push(item.financialProjectId);
            }
        }
        productIds = _.uniq(productIds);
        warehouseIds = _.uniq(warehouseIds);
        unitIds = _.uniq(unitIds);
        taxIds = _.uniq(taxIds);
        financialProjectIds = _.uniq(financialProjectIds);
        addressIds = _.uniq(addressIds);
        carrierIds = _.uniq(carrierIds);

        if (productIds.length > 0) {
            const products = await app.collection('inventory.products').find({
                _id: {$in: productIds},
                $select: ['code', 'definition', 'barcode', 'type', 'groupIds', 'categoryId', 'categoryPath', 'brandId'],
                $populate: ['groups', 'category', 'brand'],
                $disableSoftDelete: true,
                $disableActiveCheck: true
            });
            for (const product of products) {
                productsMap[product._id] = product;
            }
        }
        if (warehouseIds.length > 0) {
            const warehouses = await app.collection('inventory.warehouses').find({
                _id: {$in: warehouseIds},
                $select: ['shortName', 'name'],
                $disableSoftDelete: true,
                $disableActiveCheck: true
            });
            for (const warehouse of warehouses) {
                warehousesMap[warehouse._id] = warehouse;
            }
        }
        if (unitIds.length > 0) {
            const units = await app.collection('kernel.units').find({
                _id: {$in: unitIds},
                $select: ['name'],
                $disableSoftDelete: true,
                $disableActiveCheck: true
            });
            for (const unit of units) {
                unitsMap[unit._id] = unit;
            }
        }
        if (taxIds.length > 0) {
            const taxes = await app.collection('kernel.taxes').find({
                _id: {$in: taxIds},
                $select: ['name'],
                $disableSoftDelete: true,
                $disableActiveCheck: true
            });
            for (const tax of taxes) {
                taxesMap[tax._id] = tax;
            }
        }
        if (financialProjectIds.length > 0) {
            const financialProjects = await app.collection('kernel.financial-projects').find({
                _id: {$in: financialProjectIds},
                $select: ['code', 'name'],
                $disableSoftDelete: true,
                $disableActiveCheck: true
            });
            for (const financialProject of financialProjects) {
                financialProjectsMap[financialProject._id] = financialProject;
            }
        }
        if (addressIds.length > 0) {
            const contacts = await app.collection('kernel.contacts').find({
                _id: {$in: addressIds},
                $select: ['_id', 'code', 'name'],
                $disableSoftDelete: true,
                $disableActiveCheck: true
            });
            for (const contact of contacts) {
                contactMap[contact._id] = contact;
            }
        }
        if (carrierIds.length > 0) {
            const carriers = await app.collection('logistics.carriers').find({
                _id: {$in: carrierIds},
                $select: ['_id', 'code', 'name'],
                $disableSoftDelete: true,
                $disableActiveCheck: true
            });
            for (const carrier of carriers) {
                carriersMap[carrier._id] = carrier;
            }
        }

        return {
            companyCountry: companyCountry || {},
            basePriceLists,
            profitsMap,
            productsMap,
            warehousesMap,
            unitsMap,
            taxesMap,
            financialProjectsMap,
            contactMap,
            carriersMap
        };
    },
    async data(
        app,
        schema,
        document,
        {
            companyCountry,
            profitsMap,
            basePriceLists,
            productsMap,
            warehousesMap,
            unitsMap,
            taxesMap,
            financialProjectsMap,
            contactMap,
            carriersMap
        }
    ) {
        const data = {};

        // General.
        const moduleOptions = [
            {value: 'sale', label: 'Sale'},
            {value: 'service', label: 'Service'},
            {value: 'project', label: 'Project'},
            {value: 'rental', label: 'Rental'}
        ];
        const moduleLabel = (moduleOptions.find(option => option.value === document.module) || {}).label || '';
        if (!!moduleLabel) {
            data.module = app.translate(moduleLabel);
        } else {
            data.module = app.translate('Sale');
        }
        const statusOptions = [
            {value: 'draft', label: 'Draft'},
            {value: 'payment-planned', label: 'Payment Planned'},
            {value: 'approved', label: 'Approved'},
            {value: 'to-invoice', label: 'To Invoice'},
            {value: 'invoiced', label: 'Invoiced'},
            {value: 'canceled', label: 'Canceled'}
        ];
        const statusLabel = (statusOptions.find(option => option.value === document.status) || {}).label || '';
        if (!!statusLabel) {
            data.status = app.translate(statusLabel);
        } else {
            data.status = app.translate('Draft');
        }
        data.code = document.code;
        if (!!document.store) {
            data.storeId = document.store._id;
            data.storeName = document.store.name;
        }
        if (!!document.stage) {
            data.stageId = document.stage._id;
            data.stageName = document.stage.name;
        }
        if (!!document.documentType) data.documentType = document.documentType.name;
        if (!!document.partnerGroup) data.customerGroup = document.partnerGroup.name;
        if (_.isPlainObject(document.partner) && !_.isEmpty(document.partner)) {
            data.customerCode = document.partner.code;
            data.customerName = document.partner.name;
            data.customerEmail = document.partner.email;
            data.customerTinIdentity = document.partner.isCompany ? document.partner.tin : document.partner.identity;
            data.customerPhone = document.partner.phone;
        }
        if (!!document.currency) data.currency = document.currency.name;
        data.currencyRate = document.currencyRate;
        if (!!document.contactPerson) data.contactPerson = document.contactPerson.name;
        data.reference = document.reference;
        if (!!document.referencePartner) {
            data.referencePartnerCode = document.referencePartner.code;
            data.referencePartnerName = document.referencePartner.name;
        }
        data.recordDate = document.recordDate;
        data.orderDate = document.orderDate;
        data.deliveryDate = document.deliveryDate;
        if (!!document.branch) {
            data.branchCode = document.branch.code;
            data.branchName = document.branch.name;
        }
        if (!!document.financialProject) {
            data.projectCode = document.financialProject.code;
            data.projectName = document.financialProject.name;
        }
        if (!!document.communicationChannel) data.communicationChannel = document.communicationChannel.name;
        if (!!document.source) data.source = document.source.name;
        if (!!document.organization) data.organization = document.organization.name;
        if (!!document.salesManager) data.salesManager = document.salesManager.name;
        if (!!document.salesperson) data.salesperson = document.salesperson.name;
        if (!!document.warehouse) {
            data.warehouseCode = document.warehouse.shortName;
            data.warehouseName = document.warehouse.name;
        }
        if (_.isPlainObject(document.invoiceAddress) && !_.isEmpty(document.invoiceAddress)) {
            data.invoiceAddressAddress = document.invoiceAddress.address;
            if (document.invoiceAddress.countryId) {
                const country =
                    document.invoiceAddress.countryId === companyCountry._id
                        ? companyCountry
                        : await app
                              .collection('kernel.countries')
                              .findOne({_id: document.invoiceAddress.countryId, $select: ['name']});

                if (!!country) {
                    data.invoiceAddressCountry = country.name;
                }
            }
            data.invoiceAddressState = document.invoiceAddress.state;
            data.invoiceAddressCity = document.invoiceAddress.city;
            data.invoiceAddressDistrict = document.invoiceAddress.district;
            data.invoiceAddressSubDistrict = document.invoiceAddress.subDistrict;
            data.invoiceAddressStreet = document.invoiceAddress.street;
            data.invoiceAddressDoorNumber = document.invoiceAddress.doorNumber;
            data.invoiceAddressApartmentNumber = document.invoiceAddress.apartmentNumber;
            data.invoiceAddressPostalCode = document.invoiceAddress.postalCode;
        }
        if (_.isPlainObject(document.deliveryAddress) && !_.isEmpty(document.deliveryAddress)) {
            data.deliveryAddressAddress = document.deliveryAddress.address;
            if (document.deliveryAddress.countryId) {
                const country =
                    document.deliveryAddress.countryId === companyCountry._id
                        ? companyCountry
                        : await app
                              .collection('kernel.countries')
                              .findOne({_id: document.deliveryAddress.countryId, $select: ['name']});

                if (!!country) {
                    data.deliveryAddressCountry = country.name;
                }
            }
            data.deliveryAddressState = document.deliveryAddress.state;
            data.deliveryAddressCity = document.deliveryAddress.city;
            data.deliveryAddressDistrict = document.deliveryAddress.district;
            data.deliveryAddressSubDistrict = document.deliveryAddress.subDistrict;
            data.deliveryAddressStreet = document.deliveryAddress.street;
            data.deliveryAddressDoorNumber = document.deliveryAddress.doorNumber;
            data.deliveryAddressApartmentNumber = document.deliveryAddress.apartmentNumber;
            data.deliveryAddressPostalCode = document.deliveryAddress.postalCode;
        }
        const priorityOptions = [
            {value: 'not-urgent', label: 'Not urgent'},
            {value: 'normal', label: 'Normal'},
            {value: 'urgent', label: 'Urgent'},
            {value: 'very-urgent', label: 'Very urgent'}
        ];
        const deliveryPriorityLabel =
            (priorityOptions.find(option => option.value === document.priority) || {}).label || '';
        if (!!deliveryPriorityLabel) {
            data.deliveryPriority = app.translate(deliveryPriorityLabel);
        } else {
            data.deliveryPriority = app.translate('Normal');
        }
        data.cargoTrackingCode = document.cargoTrackingCode;
        if (!!document.paymentTerm) data.paymentTerm = document.paymentTerm.name;
        if (!!document.priceList) data.priceList = document.priceList.name;
        if (!!document.customerPriceList) data.customerPriceList = document.customerPriceList.name;
        if (!!document.guarantee) data.guarantee = document.guarantee.name;
        data.paymentPlanningDate = document.paymentPlanningDate;
        data.note = document.note;
        const deliveryAddress = contactMap[document.deliveryAddressId];
        if (deliveryAddress) {
            data.deliveryAddressCode = deliveryAddress.code;
            data.deliveryAddressName = deliveryAddress.name;
        }
        const invoiceAddress = contactMap[document.invoiceAddressId];
        if (invoiceAddress) {
            data.invoiceAddressCode = invoiceAddress.code;
            data.invoiceAddressName = invoiceAddress.name;
        }
        if (document.carrierId) {
            const carrier = carriersMap[document.carrierId];
            if (carrier) {
                data.carrierCode = carrier.code;
                data.carrierName = carrier.name;
            }
        }

        // Totals.
        data.subTotal = document.subTotal;
        data.discount = document.discount;
        data.discountAmount = document.discountAmount;
        data.subTotalAfterDiscount = document.subTotalAfterDiscount;
        data.taxTotal = document.taxTotal;
        data.rounding = document.rounding;
        data.grandTotal = document.grandTotal;
        if (_.isPlainObject(document.extra) && !_.isEmpty(document.extra)) {
            data.cashAmount = document.extra.cashAmount;
            data.moneyTransferAmount = document.extra.moneyTransferAmount;
            data.chequeAmount = document.extra.chequeAmount;
            data.promissoryNoteAmount = document.extra.promissoryNoteAmount;
            data.posAmount = document.extra.posAmount;
            data.basePrice = app.round((document.extra.basePrice || 0) / (document.currencyRate || 1), 'total');
            data.basePriceSC = document.extra.basePrice || 0;
            data.grossProfit = app.round((document.extra.grossProfit || 0) / (document.currencyRate || 1), 'total');
            data.grossProfitSC = document.extra.grossProfit || 0;
            data.profitRate = document.extra.profitRate;
            data.profitMargin = document.extra.profitMargin;
            data.cashInstallmentCount = document.extra.cashInstallmentCount;
            data.moneyTransferInstallmentCount = document.extra.moneyTransferInstallmentCount;
            data.chequeInstallmentCount = document.extra.chequeInstallmentCount;
            data.promissoryNoteInstallmentCount = document.extra.promissoryNoteInstallmentCount;
            data.posInstallmentCount = document.extra.posInstallmentCount;
            data.installmentCount = document.extra.installmentCount;
        }
        data.subTotalSC = app.round((document.subTotal || 0) * (document.currencyRate || 1), 'total');
        data.discountAmountSC = app.round((document.discountAmount || 0) * (document.currencyRate || 1), 'total');
        data.subTotalAfterDiscountSC = app.round(
            (document.subTotalAfterDiscount || 0) * (document.currencyRate || 1),
            'total'
        );
        data.taxTotalSC = app.round((document.taxTotal || 0) * (document.currencyRate || 1), 'total');
        data.roundingSC = app.round((document.rounding || 0) * (document.currencyRate || 1), 'total');
        data.grandTotalSC = app.round((document.grandTotal || 0) * (document.currencyRate || 1), 'total');

        // Exchange rates.
        if (Array.isArray(document.exchangeRates) && document.exchangeRates.length > 0) {
            for (const r of document.exchangeRates.filter(r => ['USD', 'EUR', 'GBP', 'RUB'].includes(r.currencyName))) {
                data[`exchangeRate${r.currencyName}`] = r.rate;
            }
        }

        // IDS.
        data.documentTypeId = document.documentTypeId;
        data.customerGroupId = document.partnerGroupId;
        data.customerId = document.partnerId;
        data.currencyId = document.currencyId;
        data.contactPersonId = document.contactPersonId;
        data.referencePartnerId = document.referencePartnerId;
        data.branchId = document.branchId;
        data.projectId = document.financialProjectId;
        data.communicationChannelId = document.communicationChannelId;
        data.sourceId = document.sourceId;
        data.organizationId = document.organizationId;
        data.salesManagerId = document.salesManagerId;
        data.salespersonId = document.salespersonId;
        data.warehouseId = document.warehouseId;
        data.paymentTermId = document.paymentTermId;
        data.priceListId = document.priceListId;
        data.customerPriceListId = document.customerPriceListId;
        data.guaranteeId = document.guaranteeId;
        data.deliveryAddressId = document.deliveryAddressId;
        data.carrierId = document.carrierId;

        // Items
        data.items = [];
        if (document.items.length > 0) {
            for (const item of document.items || []) {
                const product = productsMap[item.productId];
                const warehouse = warehousesMap[item.warehouseId];
                const unit = unitsMap[item.unitId];
                const baseUnit = unitsMap[item.baseUnitId];
                const tax = taxesMap[item.taxId];
                const financialProject = financialProjectsMap[item.financialProjectId];
                const i = {};

                // General.
                i.orderStatus = data.status;
                i.orderCode = data.code;
                i.documentType = data.documentType;
                i.customerGroup = data.customerGroup;
                i.customerCode = data.customerCode;
                i.customerName = data.customerName;
                i.currency = data.currency;
                i.currencyRate = data.currencyRate;
                i.recordDate = data.recordDate;
                i.orderDate = data.orderDate;
                i.branchCode = data.branchCode;
                i.branchName = data.branchName;
                i.communicationChannel = data.communicationChannel;
                i.source = data.source;
                i.organization = data.organization;
                i.salesManager = data.salesManager;
                i.salesperson = data.salesperson;

                // Product.
                i.productCode = item.productCode;
                i.productDefinition = item.productDefinition;
                const productTypeOptions = [
                    {value: 'stockable', label: 'Stockable product'},
                    {value: 'service', label: 'Service product'},
                    {value: 'kit', label: 'Kit product'}
                ];
                const productTypeLabel =
                    (productTypeOptions.find(option => option.value === item.productType) || {}).label || '';
                if (!!productTypeLabel) {
                    i.productType = app.translate(productTypeLabel);
                } else {
                    i.productType = app.translate('Stockable product');
                }
                if (!!product) {
                    i.productCategory = (product.category || {}).name;
                    i.productCategoryPath = product.categoryPath;
                    i.productGroup = (product.groups || []).map(group => group.name).join(', ');
                    i.productBrand = (product.brand || {}).name;
                }
                i.productBarcode = item.barcode;
                i.description = item.description;

                // Quantity
                if (!!unit) i.unit = unit.name;
                if (!!baseUnit) i.baseUnit = baseUnit.name;
                i.quantity = item.quantity;
                i.baseQuantity = item.baseQuantity;
                i.stockQuantity = item.stockQuantity;
                i.orderedQuantity = item.orderedQuantity;
                i.assignedQuantity = item.assignedQuantity;
                i.availableQuantity = item.availableQuantity;
                i.warehouseStockQuantity = item.warehouseStockQuantity;
                i.warehouseOrderedQuantity = item.warehouseOrderedQuantity;
                i.warehouseAssignedQuantity = item.warehouseAssignedQuantity;
                i.warehouseAvailableQuantity = item.warehouseAvailableQuantity;

                // Prices.
                i.unitPrice = item.unitPrice;
                i.grossUnitPrice = item.grossUnitPrice;
                i.discount = item.discount;
                i.unitPriceAfterDiscount = item.unitPriceAfterDiscount;
                i.grossUnitPriceAfterDiscount = item.grossUnitPriceAfterDiscount;
                if (!!tax) i.tax = tax.name;
                i.taxTotal = item.taxTotal;
                i.grossTotal = item.grossTotal;
                i.realTotal = item.realTotal;
                i.total = item.total;
                i.unitPriceSC = app.round((item.unitPrice || 0) * (document.currencyRate || 1), 'total');
                i.grossUnitPriceSC = app.round((item.grossUnitPrice || 0) * (document.currencyRate || 1), 'total');
                i.unitPriceAfterDiscountSC = app.round(
                    (item.unitPriceAfterDiscount || 0) * (document.currencyRate || 1),
                    'total'
                );
                i.grossUnitPriceAfterDiscountSC = app.round(
                    (item.grossUnitPriceAfterDiscount || 0) * (document.currencyRate || 1),
                    'total'
                );
                i.taxTotalSC = app.round((item.taxTotal || 0) * (document.currencyRate || 1), 'total');
                i.grossTotalSC = app.round((item.grossTotal || 0) * (document.currencyRate || 1), 'total');
                i.realTotalSC = app.round((item.realTotal || 0) * (document.currencyRate || 1), 'total');
                i.totalSC = app.round((item.total || 0) * (document.currencyRate || 1), 'total');

                // Mixed.
                if (!!warehouse) {
                    i.warehouseCode = warehouse.shortName;
                    i.warehouseName = warehouse.name;
                }
                if (!!financialProject) {
                    i.projectCode = financialProject.code;
                    i.projectName = financialProject.name;
                }
                i.deliveryDate = item.scheduledDate;

                if (!!item.costSource) {
                    i.costSource = item.costSource;
                    i.unitCost = item.unitCost ?? 0;
                    i.unitProfit = item.unitProfit ?? 0;
                    i.cost = item.cost ?? 0;
                    i.profitRate = item.profitRate ?? 0;
                    i.profitMargin = item.profitMargin ?? 0;
                    i.profit = item.profit ?? 0;
                }

                i.exchangeRateUSD = data.exchangeRateUSD;
                i.exchangeRateEUR = data.exchangeRateEUR;
                i.exchangeRateGBP = data.exchangeRateGBP;
                i.exchangeRateRUB = data.exchangeRateRUB;

                // IDS.
                i.documentTypeId = document.documentTypeId;
                i.customerGroupId = document.partnerGroupId;
                i.customerId = document.partnerId;
                i.currencyId = document.currencyId;
                i.branchId = item.branchId;
                i.projectId = item.financialProjectId;
                i.communicationChannelId = document.communicationChannelId;
                i.sourceId = document.sourceId;
                i.organizationId = document.organizationId;
                i.salesManagerId = document.salesManagerId;
                i.salespersonId = document.salespersonId;
                i.warehouseId = item.warehouseId;
                i.productId = item.productId;
                i.baseUnitId = item.baseUnitId;
                i.unitId = item.unitId;
                i.taxId = item.taxId;

                data.items.push(i);
            }
        }

        const profit = profitsMap[document._id];
        data.profits = [];
        if (!!profit && Array.isArray(profit.items) && profit.items.length > 0) {
            const typeOptions = [
                {value: 'manuel', label: 'Manuel'},
                {value: 'product-cost', label: 'Product cost'},
                {value: 'last-purchase-price', label: 'Last purchase price'},
                {value: 'forecasted-purchase-price', label: 'Forecasted purchase price'}
            ].concat(
                basePriceLists.map(pl => ({
                    value: pl._id,
                    label: pl.name
                }))
            );

            for (const item of profit.items) {
                const product = productsMap[item.productId];
                const warehouse = warehousesMap[item.warehouseId];
                const unit = unitsMap[item.unitId];
                const i = {};

                if (!product) {
                    continue;
                }

                // General.
                i.orderStatus = data.status;
                i.orderCode = data.code;
                i.documentType = data.documentType;
                i.customerGroup = data.customerGroup;
                i.customerCode = data.customerCode;
                i.customerName = data.customerName;
                i.currency = data.currency;
                i.currencyRate = data.currencyRate;
                i.recordDate = data.recordDate;
                i.orderDate = data.orderDate;
                i.branchCode = data.branchCode;
                i.branchName = data.branchName;
                i.communicationChannel = data.communicationChannel;
                i.source = data.source;
                i.organization = data.organization;
                i.salesManager = data.salesManager;
                i.salesperson = data.salesperson;
                i.projectCode = data.projectCode;
                i.projectName = data.projectName;

                // Product.
                i.productCode = product.code;
                i.productDefinition = product.definition;
                const productTypeOptions = [
                    {value: 'stockable', label: 'Stockable product'},
                    {value: 'service', label: 'Service product'},
                    {value: 'kit', label: 'Kit product'}
                ];
                const productTypeLabel =
                    (productTypeOptions.find(option => option.value === product.type) || {}).label || '';
                if (!!productTypeLabel) {
                    i.productType = app.translate(productTypeLabel);
                } else {
                    i.productType = app.translate('Stockable product');
                }
                if (!!product) {
                    i.productCategory = (product.category || {}).name;
                    i.productCategoryPath = product.categoryPath;
                    i.productGroup = (product.groups || []).map(group => group.name).join(', ');
                    i.productBrand = (product.brand || {}).name;
                }
                i.productBarcode = product.barcode;

                const typeLabel = (typeOptions.find(option => option.value === item.profitBase) || {}).label || '';
                if (!!typeLabel) {
                    i.type = app.translate(typeLabel);
                }
                i.isDefault = !!item.isDefault;

                if (!!unit) i.unit = unit.name;
                i.quantity = item.quantity;
                i.salesPrice = app.round(
                    (item.totalSalesPrice || 0) / (item.quantity || 1) / (document.currencyRate || 1),
                    'unit-price'
                );
                i.totalSalesPrice = app.round((item.totalSalesPrice || 0) / (document.currencyRate || 1), 'total');
                i.basePrice = app.round((item.basePrice || 0) / (document.currencyRate || 1), 'unit-price');
                i.totalBasePrice = app.round((item.totalBasePrice || 0) / (document.currencyRate || 1), 'total');
                i.grossProfit = app.round((item.grossProfit || 0) / (document.currencyRate || 1), 'unit-price');
                i.totalGrossProfit = app.round((item.totalGrossProfit || 0) / (document.currencyRate || 1), 'total');
                i.salesPriceSC = app.round((item.totalSalesPrice || 0) / (item.quantity || 1), 'unit-price');
                i.totalSalesPriceSC = app.round(item.totalSalesPrice || 0, 'total');
                i.basePriceSC = app.round(item.basePrice || 0, 'unit-price');
                i.totalBasePriceSC = app.round(item.totalBasePrice || 0, 'total');
                i.grossProfitSC = app.round(item.grossProfit || 0, 'unit-price');
                i.totalGrossProfitSC = app.round(item.totalGrossProfit || 0, 'total');
                i.profitRate = item.profitRate;
                i.profitMargin = item.profitMargin;
                if (!_.isNumber(i.profitRate) || isNaN(i.profitRate) || i.profitRate < 0) {
                    delete i.profitRate;
                }
                if (
                    !_.isNumber(i.profitMargin) ||
                    isNaN(i.profitMargin) ||
                    i.profitMargin < 0 ||
                    i.profitMargin > 100
                ) {
                    delete i.profitMargin;
                }

                // Mixed.
                if (!!warehouse) {
                    i.warehouseCode = warehouse.shortName;
                    i.warehouseName = warehouse.name;
                }

                if (!!app.setting('system.freight')) {
                    i.totalFreightSC = app.round(item.totalFreight || 0, 'total');
                    i.totalFreight = app.round((item.totalFreight || 0) / (document.currencyRate || 1), 'total');
                }

                i.exchangeRateUSD = data.exchangeRateUSD;
                i.exchangeRateEUR = data.exchangeRateEUR;
                i.exchangeRateGBP = data.exchangeRateGBP;
                i.exchangeRateRUB = data.exchangeRateRUB;

                // IDS.
                i.documentTypeId = document.documentTypeId;
                i.customerGroupId = document.partnerGroupId;
                i.customerId = document.partnerId;
                i.currencyId = document.currencyId;
                i.branchId = document.branchId;
                i.projectId = document.financialProjectId;
                i.communicationChannelId = document.communicationChannelId;
                i.sourceId = document.sourceId;
                i.organizationId = document.organizationId;
                i.salesManagerId = document.salesManagerId;
                i.salespersonId = document.salespersonId;
                i.productId = item.productId;
                i.unitId = item.unitId;
                i.typeId = item.profitBase;

                data.profits.push(i);
            }

            data.items = data.items.map(item => {
                if (!item.costSource && !!profit.defaultProfitBase) {
                    item.costSource = profit.defaultProfitBase;

                    const profitItem = (profit.items ?? []).find(
                        profitItem =>
                            profitItem.profitBase === item.costSource &&
                            profitItem.productId === item.productId &&
                            profitItem.unitId === item.unitId &&
                            profitItem.quantity === item.quantity &&
                            profitItem.warehouseId === item.warehouseId
                    );

                    if (!!profitItem) {
                        if (!_.isFinite(item.unitCost) || item.unitCost === 0) {
                            item.unitCost =
                                (profitItem.basePrice ?? 0) / (document.currencyRate !== 0 ? document.currencyRate : 1);
                        }
                        item.cost = item.unitCost * item.quantity;
                        item.profitRate =
                            ((item.realTotal - (item.cost + (item.freight ?? 0))) / (item.cost || 1)) * 100;
                        item.profitMargin =
                            ((item.realTotal - (item.cost + (item.freight ?? 0))) / (item.realTotal || 1)) * 100;
                        item.profit = item.realTotal - (item.cost + (item.freight ?? 0));
                        item.unitProfit = item.profit / item.quantity;
                    }
                }

                return item;
            });
        }

        return data;
    }
};
