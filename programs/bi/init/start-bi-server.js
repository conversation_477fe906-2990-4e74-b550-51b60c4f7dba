import fs from 'fs/promises';
import path from 'path';
import _ from 'lodash';
import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import CubejsServerCore from '@cubejs-backend/server-core';
import generateCubes from './generate-cubes';

const API_SECRET = 'oRfzaDHsy8lCN0OW2iqzWrMYgVeP0JUT';

export default async function (app) {
    process.env.CUBEJS_DB_TYPE = 'postgres';
    process.env.CUBEJS_DB_HOST = app.config('database.postgresql.host');
    process.env.CUBEJS_DB_PORT = app.config('database.postgresql.port');
    process.env.CUBEJS_DB_USER = app.config('database.postgresql.user');
    process.env.CUBEJS_DB_NAME = 'entererp';
    process.env.CUBEJS_API_SECRET = API_SECRET;
    process.env.CUBEJS_LOG_LEVEL = 'error';
    process.env.CUBEJS_TELEMETRY = 'false';

    const nativeLog = global.console.log;
    global.console.log = function (message) {
        let showLog = true;

        if (typeof message === 'string') {
            if (
                message.indexOf('Using undefined as an external database is deprecated.') !== -1 ||
                message.indexOf('Refresh Scheduler Error') !== -1 ||
                message.indexOf('At least one context should be returned by scheduledRefreshContexts') !== -1
            ) {
                showLog = false;
            }
        }

        if (showLog) {
            nativeLog.apply(global.console, arguments);
        }
    };

    const subApp = express();
    subApp.use(cors());
    if (!app.get('isDevelopment')) {
        subApp.use(
            helmet({
                contentSecurityPolicy: false
            })
        );
    }
    subApp.use(express.urlencoded({extended: true}));
    subApp.use(express.json({limit: '50mb'}));

    const serverCore = new CubejsServerCore({
        contextToAppId: context => {
            return (context.securityContext || {}).workspaceId;
        },
        dbType: 'postgres',
        basePath: '/api',
        // continueWaitTimeout: 15,
        devServer: false,
        telemetry: false,
        allowUngroupedWithoutPrimaryKey: true,
        cacheAndQueueDriver: 'redis',
        checkAuth: async request => {
            let workspaceId = request.headers['workspace-id'] ?? '';

            if (!workspaceId) {
                if (!_.isEmpty(request.query)) {
                    const payload = JSON.parse(request.query.query);

                    if (Array.isArray(payload)) {
                        workspaceId = payload[0].workspaceId ?? '';

                        request.query.query = JSON.stringify(
                            payload.map(p => {
                                delete p.workspaceId;

                                return p;
                            })
                        );
                    } else {
                        workspaceId = payload.workspaceId ?? '';
                        delete payload.workspaceId;
                        request.query.query = JSON.stringify(payload);
                    }
                } else if (!_.isEmpty(request.body)) {
                    const payload = request.body.query;

                    if (Array.isArray(payload)) {
                        workspaceId = payload[0].workspaceId ?? '';

                        request.query.query = payload.map(p => {
                            delete p.workspaceId;

                            return p;
                        });
                    } else {
                        workspaceId = payload.workspaceId ?? '';
                        delete payload.workspaceId;
                        request.body.query = payload;
                    }
                }
            }

            if (!workspaceId) {
                throw new Error(`No workspace id is supplied!`);
            }

            // let user = null;
            //
            // if (!workspaceId) {
            //     throw new Error(`No workspace id is supplied!`);
            // }
            //
            // if (!!request.headers['api-secret-key']) {
            //     if (request.headers['api-secret-key'] !== API_SECRET) {
            //         throw new Error(`Unauthorized`);
            //     }
            //
            //     user = await app.collection('kernel.users').findOne({isRoot: true});
            //
            //     if (!user) {
            //         throw new Error(`Unauthorized`);
            //     }
            // } else {
            //     try {
            //         user = await authenticate(app, request);
            //     } catch (e) {
            //         throw new Error(`Unauthorized`);
            //     }
            // }

            request.securityContext = {workspaceId};
        },
        repositoryFactory: ({securityContext}) => {
            return {
                dataSchemaFiles: async () => {
                    const workspaceId = securityContext.workspaceId;

                    if (!workspaceId) {
                        return [];
                    }

                    return [
                        {
                            fileName: `bi-schema-${workspaceId}.js`,
                            content: await generateCubes(app, workspaceId, false)
                        }
                    ];
                }
            };
        },
        schemaVersion: async ({securityContext}) => {
            const workspaceId = securityContext.workspaceId;
            let cacheKey = 'initial';

            if (!!workspaceId) {
                let directoryPath = app.get('isPackaged')
                    ? app.setting('system.uploadDirectoryPath')
                    : app.config('paths.files');
                if (directoryPath[0] !== '/') {
                    directoryPath = path.join(process.cwd(), directoryPath);
                }

                try {
                    const schemaPath = path.join(directoryPath, `bi-schema-${workspaceId}.js`);
                    const stats = await fs.stat(schemaPath);

                    cacheKey = stats.mtime.getTime().toString();
                } catch (error) {
                    cacheKey = 'initial';
                }
            }

            return cacheKey;

            // return Date.now();
        },
        scheduledRefreshContexts: async () => {
            const workspaces = await app.collection('bi.workspaces').find({
                $select: ['_id']
            });

            return workspaces.map(workspace => ({securityContext: {workspaceId: workspace._id}}));
        }
    });

    await serverCore.initApp(subApp);

    app.use(
        '/bi',
        async (request, response, next) => {
            if (request.url === '/') {
                return response.status(404).send('Not found!');
            }

            next();
        },
        subApp
    );
}
