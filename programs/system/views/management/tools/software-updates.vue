<template>
    <div class="system-management-tools-software-updates" v-if="initialized">
        <ui-card
            :title="title"
            :sub-title="subTitle"
            icon="far fa-spin fa-spinner"
            class="updating-box"
            v-if="isUpdating"
        />
        <ui-card
            :title="title"
            :sub-title="subTitle"
            icon="far fa-shield-check"
            class="no-update-box"
            v-else-if="!hasUpdate"
        />
        <ui-card
            :title="title"
            :sub-title="subTitle"
            image="/static/images/default-logo.png?width=128&height=128"
            class="new-update-box"
            v-else
        >
            <template slot="header-right">
                <el-button
                    :disabled="$params('loading')"
                    type="success"
                    plain
                    icon="far fa-cloud-download-alt"
                    size="small"
                    @click="handleUpdate"
                >
                    {{ 'Download and Install Update' | t }}
                </el-button>
            </template>

            <div class="update-content">
                <div class="update-content-separator"></div>

                <div class="article" v-html="description"></div>
            </div>
        </ui-card>
    </div>
</template>

<script>
export default {
    data: () => ({
        hasUpdate: false,
        title: '',
        subTitle: '',
        description: '',
        updateId: '',
        isUpdating: false,
        initialized: false
    }),

    methods: {
        async init() {
            const result = await this.$rpc('system.software-updates-check-update');

            if (!!result && !!result.isUpdating) {
                this.title = this.$t('Installing Updates..');
                this.subTitle = this.$t(
                    'New updates are being applied. This process will take between one and ten minutes. The system will reboot when the update process is finished.'
                );
                this.isUpdating = true;
                this.hasUpdate = false;
            } else {
                if (!!result && !!result.id) {
                    this.title = this.$t('New Update Available');
                    this.subTitle = `EnterERP Elegance v${result.version}`;
                    this.description = result.description;
                    this.hasUpdate = true;
                    this.updateId = result.id;
                } else {
                    this.title = this.$t('System Is Up To Date');
                    this.subTitle = 'EnterERP Elegance v1.0.0';
                    this.hasUpdate = false;
                }
            }

            this.initialized = true;
        },
        async handleUpdate() {
            if (this.isUpdating) {
                return;
            }

            this.$params('loading', true);

            let currentClientVersion = this.$app.get('clientVersion');
            // try {
            //     const response = await fetch(this.$app.absoluteUrl('/client-version'), {
            //         signal: AbortSignal.timeout(2500)
            //     });
            //     if (!response.ok) {
            //         this.$program.message('error', 'Could not get the current client version.');
            //         this.$params('loading', false);
            //         return;
            //     }
            //     currentClientVersion = await response.text();
            //     if (typeof currentClientVersion !== 'string' || !(currentClientVersion.trim().length > 0)) {
            //         this.$program.message('error', 'Could not get the current client version.');
            //         this.$params('loading', false);
            //         return;
            //     }
            //     currentClientVersion = currentClientVersion.trim();
            // } catch (error) {
            //     this.$program.message('error', error.message);
            //     this.$params('loading', false);
            //     return;
            // }

            this.title = this.$t('Installing Updates..');
            this.subTitle = this.$t(
                'New updates are being applied. This process will take between one and ten minutes. The system will reboot when the update process is finished.'
            );
            this.isUpdating = true;
            this.hasUpdate = false;

            await this.$rpc('system.software-updates-update', this.updateId);

            const idx = setInterval(async () => {
                try {
                    const response = await fetch(this.$app.absoluteUrl('/client-version'), {
                        signal: AbortSignal.timeout(2500)
                    });
                    if (!response.ok) {
                        return;
                    }

                    let clientVersion = await response.text();
                    if (
                        typeof clientVersion !== 'string' ||
                        !(clientVersion.trim().length > 0) ||
                        clientVersion.includes('html') ||
                        clientVersion.length > 25
                    ) {
                        return;
                    }
                    clientVersion = clientVersion.trim();

                    if (currentClientVersion !== clientVersion) {
                        clearInterval(idx);

                        window.location.reload();
                    }
                } catch (error) {}
            }, 5000);

            this.$params('loading', false);
        }
    },

    async created() {
        this.$params('loading', true);

        await this.init();

        this.$params('loading', false);
    }
};
</script>

<style lang="scss">
//noinspection CssUnknownTarget
@import 'core';

.system-management-tools-software-updates {
    padding: 20px;

    .updating-box {
        .ui-card-header {
            .ui-card-icon {
                color: $primary;
            }
        }
    }

    .no-update-box {
        .ui-card-header {
            .ui-card-icon {
                color: $success;
            }
        }
    }

    .new-update-box {
        .update-content {
            padding: 0 20px 20px;

            .update-content-separator {
                border-top: 1px solid $border-color;
                margin-bottom: 20px;
            }
        }
    }
}
</style>
