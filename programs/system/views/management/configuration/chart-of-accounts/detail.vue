<template>
    <ui-view
        type="form"
        collection="kernel.accounts"
        method="system.save-account"
        :model="model"
        :extend-schema="extendSchema"
        :before-init="beforeInit"
        :before-validate="beforeValidate"
        :before-submit="beforeSubmit"
        :extra-fields="extraFields"
        :title="title"
        :dialog-height="255"
        @changed="handleChange"
        v-if="initialized"
    >
        <div class="columns">
            <div class="column is-half">
                <ui-field
                    name="parentPath"
                    v-bind="parentPathSettings"
                    :disabled="!isSuperRoot && (isSystemAccount || hasTransactions)"
                />
                <ui-field name="code" :disabled="isSystemAccount || !model.parentPath || hasTransactions">
                    <template slot="prepend" v-if="!$params('id')">{{ parentAccountCode }}</template>
                </ui-field>
                <ui-field name="name" :disabled="!model.parentPath" />
                <ui-field name="type" :options="typeOptions" :disabled="isTypeDisabled" translate-labels />
            </div>

            <div class="column is-half">
                <ui-field
                    name="currencyId"
                    collection="kernel.currencies"
                    :disabled="!model.parentPath || hasTransactions"
                />
                <ui-field
                    name="scope"
                    :options="scopeOptions"
                    :disabled="!model.parentPath"
                    v-show="$setting('system.scopes')"
                />
                <ui-field name="reconciliation" :disabled="!model.parentPath" />
                <ui-field name="regulatory" :disabled="!model.parentPath" />
                <ui-field
                    name="inverseExpense"
                    :disabled="!model.parentPath"
                    v-show="model.type === 'financialExpenses'"
                />
                <ui-field name="isPartnerAccount" :disabled="!model.parentPath" />
            </div>
        </div>
    </ui-view>
</template>

<script>
import _ from 'lodash';
import {leadingZeros} from 'framework/helpers';

export default {
    data: () => ({
        model: {},
        parentAccountCode: '',
        typeOptions: [
            {value: 'assets', label: 'Assets'},
            {value: 'liabilities', label: 'Liabilities'},
            {value: 'equity', label: 'Equity'},
            {value: 'sales', label: 'Sales'},
            {value: 'costOfSales', label: 'Cost of Sales'},
            {value: 'operatingExpenses', label: 'Operating Expenses'},
            {value: 'financialExpenses', label: 'Financial Expenses'},
            {value: 'extraordinaryRevenuesAndExpenses', label: 'Extraordinary Revenues and Expenses'}
        ],
        parentPathSettings: {
            fieldType: 'cascader',
            collection: 'kernel.accounts',
            extraFields: ['name', 'code'],
            filters: {$sort: {code: 1}},
            template: '{{ code }} - {{ name }}',
            valueFrom: 'tree.path',
            showAllLevels: false
        },
        extendSchema: {
            parentPath: {
                type: 'string',
                label: 'Parent account'
            }
        },
        extraFields: ['system', 'tree'],
        hasTransactions: false,
        initialized: false
    }),

    computed: {
        title() {
            const model = this.model;

            if (this.$params('id') && !(model.name && model.code)) return '';

            return model.name && model.code ? `${model.code} - ${model.name}` : this.$t('New Account');
        },
        isSystemAccount() {
            return !_.isEmpty(this.model) ? this.model.system : false;
        },
        scopeOptions() {
            return [
                {value: '1', label: this.$setting('system.scope1Label')},
                {value: '2', label: this.$setting('system.scope2Label')},
                {value: '1+2', label: `${this.$setting('system.scope1Label')} + ${this.$setting('system.scope2Label')}`}
            ];
        },
        isSuperRoot() {
            const user = this.$user;

            return (
                user.email === '<EMAIL>' ||
                user.email === '<EMAIL>' ||
                user.email === '<EMAIL>'
            );
        },
        isTypeDisabled() {
            return !this.isSuperRoot && (this.isSystemAccount || !this.model.parentPath || this.hasTransactions);
        }
    },

    methods: {
        async beforeInit(model) {
            if (!model.currencyId) {
                model.currencyId = this.$store.getters['session/company'].currency._id;
            }

            if (!!model.tree.path) {
                const parentItem = await this.$collection('kernel.accounts').parent(model.tree.path, {
                    $select: ['tree'],
                    $disableLocalCache: true
                });

                if (_.isPlainObject(parentItem)) {
                    model.parentPath = parentItem.tree.path;
                }
            }

            if (!this.$params('id') && !!this.$params('parentPath')) {
                model.parentPath = this.$params('parentPath');

                this.$nextTick(() => {
                    this.handleChange(model, 'parentPath');
                });
            }

            return model;
        },
        async beforeValidate(model) {
            // Check code!
            if (isNaN(Number(model.code.replace(/[-._|]*/g, '')))) {
                throw new this.$app.errors.Unprocessable({
                    message: this.$t('Invalid account code!'),
                    field: 'code'
                });
            }

            // Check scope.
            if (!!this.$setting('system.scopes') && !model.scope) {
                throw new this.$app.errors.Unprocessable({
                    message: this.$t('{{label}} is required', {label: this.$t('Scope')}),
                    field: 'scope'
                });
            }

            if (!!this.$params('id')) {
                const count = await this.$collection('kernel.accounts').count({
                    _id: {$ne: this.$params('id')},
                    code: model.code,
                    $disableActiveCheck: true,
                    $disableSoftDelete: true
                });

                if (count > 0) {
                    throw new this.$app.errors.Unprocessable({
                        message: this.$t('Account code must be unique!'),
                        field: 'code'
                    });
                }
            }

            if (
                !!this.$params('id') &&
                _.isFunction(this.$params('checkAccount')) &&
                !(await this.$params('checkAccount')({_id: this.$params('id')}))
            ) {
                throw new this.$app.errors.Unprocessable({
                    message: this.$t('Operation is canceled!')
                });
            }

            return model;
        },
        async beforeSubmit(model) {
            const user = this.$user;
            const isSuperRoot =
                user.email === '<EMAIL>' ||
                user.email === '<EMAIL>' ||
                user.email === '<EMAIL>';

            if (!model.code.startsWith('.') && !this.$params('id')) {
                model.code = `.${model.code}`;
            }

            // Adjust code.
            if (!!model.parentPath) {
                const parentAccount = await this.$collection('kernel.accounts').findOne({
                    'tree.path': model.parentPath
                });

                if (_.isObject(parentAccount) && model.code && !this.$params('id')) {
                    model.code = parentAccount.code + model.code;
                }

                // Check if parent account is active account.
                if (!isSuperRoot) {
                    const parentTransactionCount = await this.$collection('accounting.transactions').count({
                        accountId: parentAccount._id
                    });

                    if (parentTransactionCount > 0) {
                        throw new this.$app.errors.Unprocessable(
                            this.$t('Subaccount can not be created under active accounts!')
                        );
                    }
                }
            }

            // Check if account has transaction.
            if (!isSuperRoot) {
                const transactionCount = await this.$collection('accounting.transactions').count({code: model.code});

                if (transactionCount > 0) {
                    throw new this.$app.errors.Unprocessable(
                        this.$t('Accounts that have accounting entries can not be updated!')
                    );
                }
            }

            return model;
        },
        async handleChange(model, field) {
            if (field === 'parentPath' && model.parentPath) {
                const parentAccount = await this.$collection('kernel.accounts').findOne({
                    'tree.path': model.parentPath
                });

                if (_.isObject(parentAccount)) {
                    let model = _.cloneDeep(this.model);

                    this.parentAccountCode = `${parentAccount.code}.`;

                    if (!model.type) {
                        model.type = parentAccount.type;
                    }

                    if (parentAccount.tree.depth >= 2 && parentAccount.regulatory) {
                        model.regulatory = true;
                    }

                    this.model = model;
                }
            }
        }
    },

    async created() {
        this.$params('loading', true);

        if (!!this.$params('id')) {
            const transactionCount = await this.$collection('accounting.transactions').count({
                accountId: this.$params('id')
            });

            this.hasTransactions = transactionCount > 0;
        }

        this.initialized = true;

        this.$nextTick(() => {
            this.$params('loading', false);
        });
    }
};
</script>
