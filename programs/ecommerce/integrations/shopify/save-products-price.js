import _ from 'lodash';
import {initShopifyClient} from './utils';
import {sleep} from 'framework/helpers';

export default async function (app, store, items) {
    const log = payload => {
        payload.collection = 'ecommerce.stores';
        payload.documentId = store._id;
        payload.documentIdentifier = store.code;
        payload.method = 'update';
        payload.collectionName = `${app.translate('E-Commerce')} / ${app.translate('Stores')}`;

        return app.log(payload);
    };

    try {
        const integrationParams = JSON.parse(store.integrationParams);
        const {shopName, accessToken} = integrationParams;
        const client = initShopifyClient(shopName, accessToken);

        for (const item of items.filter(item => !item.isConfigurable)) {
            const storeProduct = item.storeProduct;
            if (!storeProduct.integrationId || !_.isPlainObject(storeProduct.integrationParams)) {
                continue;
            }
            let salePrice = 0;
            let listPrice = 0;

            if (typeof item.discountedSalesPrice === 'number') {
                salePrice = item.discountedSalesPrice;
                listPrice = storeProduct.salesPrice;
            } else {
                salePrice = item.salesPrice;
                listPrice = item.salesPrice;
            }

            // On sale.
            if (storeProduct.onSale === false) {
                listPrice = 0;
                salePrice = 0;
            }

            if (!_.isFinite(salePrice)) {
                continue;
            }

            salePrice = app.roundNumber(salePrice, 2);
            if (_.isFinite(listPrice)) {
                listPrice = app.roundNumber(listPrice, 2);
            }

            const p = {
                price: salePrice.toString()
            };
            if (_.isFinite(listPrice) && salePrice < listPrice) {
                p.compare_at_price = listPrice.toString();
            }

            await client.productVariant.update(storeProduct.integrationParams.variantId, p);

            await sleep(100);
        }
    } catch (error) {
        log({
            level: 'error',
            message: `An error occurred while updating integration product prices! Error: ${error.message}`
        });
    }
}
