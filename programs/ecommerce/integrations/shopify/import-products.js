import _ from 'lodash';
import {initShopifyClient} from './utils';
import {removeUnmatchedIntegrationProducts} from '../utils';

export default async function (app, store, onProgress) {
    const log = payload => {
        payload.collection = 'ecommerce.stores';
        payload.documentId = store._id;
        payload.documentIdentifier = store.code;
        payload.method = 'update';
        payload.collectionName = `${app.translate('E-Commerce')} / ${app.translate('Stores')}`;

        return app.log(payload);
    };

    try {
        const integrationParams = JSON.parse(store.integrationParams);
        const {shopName, accessToken} = integrationParams;
        const now = app.datetime.local().toJSDate();
        const client = initShopifyClient(shopName, accessToken);
        const matchedProducts = [];
        const totalCount = await client.product.count();
        let currentCount = 0;

        // Delete store product that does not have barcode.
        // await app.collection('ecommerce.store-products').remove({
        //     storeId: store._id,
        //     $or: [{productBarcode: {$eq: null}}, {productBarcode: {$eq: ''}}, {productBarcode: {$exists: false}}],
        //     $disableSoftDelete: true
        // });

        let params = {limit: 100};
        do {
            const products = await client.product.list(params);

            const items = [];
            for (const product of products) {
                for (const variant of product.variants ?? []) {
                    const code = (variant.sku ?? '').trim();
                    const barcode = (variant.barcode ?? '').trim();

                    if (!code && !barcode) {
                        continue;
                    }

                    const query = {$or: []};
                    if (!!code) {
                        query.$or.push({code});
                    }
                    if (!!barcode) {
                        query.$or.push(...[{barcode}, {'barcodes.barcode': barcode}]);
                    }
                    const erpProduct = await app.collection('inventory.products').findOne(query);
                    if (!erpProduct) {
                        log({
                            level: 'error',
                            message: app.translate(
                                'The {{productName}} integration product could not be matched. No ERP product found for the relevant product! Barcode: {{barcode}}',
                                {
                                    productName: product.title,
                                    barcode
                                }
                            )
                        });

                        continue;
                    }

                    items.push({erpProduct, integrationProduct: {product, variant}});
                }
            }

            if (items.length > 0) {
                const storeProductOperations = [];

                // Get product ids.
                const productIds = items.map(item => item.erpProduct._id);

                // Get brands.
                const brands = await app.collection('inventory.product-brands').find({
                    _id: {$in: items.map(item => item.erpProduct.brandId).filter(brandId => !!brandId)},
                    $disableSoftDelete: true,
                    $disableActiveCheck: true
                });
                const brandsMap = {};
                for (const brand of brands) {
                    brandsMap[brand._id] = brand;
                }

                // Get product prices.
                const productPricesMap = {};
                if (store.pricingPolicy === 'use-price-list' && !!store.priceListId) {
                    const productPrices = (
                        await app.collection('sale.product-prices').find({
                            productId: {$in: productIds},
                            priceListId: store.priceListId,
                            currencyId: store.currencyId,
                            validFrom: {
                                $lte: now
                            },
                            validTo: {
                                $gte: now
                            }
                        })
                    ).filter(p => p.min <= 1 || _.isUndefined(p.min));

                    for (const productPrice of productPrices) {
                        productPricesMap[`${store.priceListId}${productPrice.productId}`] = productPrice.price;
                    }
                }

                // Get discounted product prices.
                const discountedProductPricesMap = {};
                if (store.pricingPolicy === 'use-price-list' && !!store.discountedPriceListId) {
                    const productPrices = (
                        await app.collection('sale.product-prices').find({
                            productId: {$in: productIds},
                            priceListId: store.discountedPriceListId,
                            currencyId: store.currencyId,
                            validFrom: {
                                $lte: now
                            },
                            validTo: {
                                $gte: now
                            }
                        })
                    ).filter(p => p.min <= 1 || _.isUndefined(p.min));

                    for (const productPrice of productPrices) {
                        discountedProductPricesMap[`${store.discountedPriceListId}${productPrice.productId}`] =
                            productPrice.price;
                    }
                }

                // Get stock report.
                const productStocksMap = {};
                if (store.stockPolicy !== 'manuel') {
                    const stockResult = await app.rpc('inventory.get-stock-report', {
                        productId: productIds,
                        warehouseId: store.warehouseId
                    });

                    if (!!stockResult && stockResult.length > 0) {
                        for (const sr of stockResult) {
                            productStocksMap[sr.productId] = sr;
                        }
                    }
                }

                for (const item of items) {
                    const product = item.erpProduct;
                    const code = (item.integrationProduct.variant.sku ?? '').toString().trim();
                    const barcode = (item.integrationProduct.variant.barcode ?? '').toString().trim();
                    const matchedBarcodeInfo = product.barcodes.find(pb => pb.barcode === barcode) ?? {};
                    const existingStoreProduct = await app.collection('ecommerce.store-products').findOne({
                        storeId: store._id,
                        $or: [{productCode: code}, {productBarcode: barcode}]
                    });

                    if (!!existingStoreProduct) {
                        const sp = {};

                        sp.integrationId = item.integrationProduct.variant.id.toString();
                        sp.integrationParams = {
                            productId: item.integrationProduct.product.id,
                            variantId: item.integrationProduct.variant.id,
                            inventoryItemId: item.integrationProduct.variant.inventory_item_id
                        };
                        sp.unitId = matchedBarcodeInfo.unitId || product.salesUnitId;
                        sp.isPublished = true;
                        sp.isSynchronized = true;

                        sp.integrationStatus = 'approved';
                        if (sp.integrationId) {
                            matchedProducts.push({
                                productId: product._id,
                                productName: product.definition,
                                barcode: matchedBarcodeInfo.barcode || product.barcode
                            });
                        }

                        storeProductOperations.push({
                            updateOne: {
                                filter: {_id: existingStoreProduct._id},
                                update: {$set: sp}
                            }
                        });
                    } else {
                        const sp = {};
                        sp.storeId = store._id;
                        sp.integrationId = item.integrationProduct.variant.id.toString();
                        sp.integrationParams = {
                            productId: item.integrationProduct.product.id,
                            variantId: item.integrationProduct.variant.id,
                            inventoryItemId: item.integrationProduct.variant.inventory_item_id
                        };
                        sp.productId = product._id;
                        sp.productImage = product.image;
                        sp.productCode = product.code;
                        sp.productDefinition = product.definition;
                        sp.productBarcode = matchedBarcodeInfo.barcode || product.barcode;
                        sp.productCategoryId = product.categoryId;
                        sp.productCategoryPath = product.categoryPath;
                        sp.productGroupIds = product.groupIds;
                        sp.unitId = matchedBarcodeInfo.unitId || product.salesUnitId;
                        sp.isPublished = true;
                        sp.isSynchronized = true;
                        sp.currencyId = store.currencyId;
                        sp.taxApplication = store.taxApplication;
                        sp.taxId = product.salesTaxId;
                        sp.salesPrice = product.salesPrice;
                        sp.quantity = 0;
                        sp.createdAt = app.datetime.local().toJSDate();
                        sp.updatedAt = sp.createdAt;

                        sp.integrationStatus = 'approved';
                        if (!!product.brandId) {
                            const brand = brandsMap[product.brandId];

                            sp.productBrandId = product.brandId;
                            sp.productBrandName = brand.name;
                            sp.productBrandSlug = brand.slug;
                        }

                        if (store.pricingPolicy === 'use-price-list' && !!store.priceListId) {
                            const price = productPricesMap[`${store.priceListId}${product._id}`];

                            if (typeof price === 'number') {
                                sp.salesPrice = price;
                            }
                        }
                        if (store.pricingPolicy === 'use-price-list' && !!store.discountedPriceListId) {
                            const price = discountedProductPricesMap[`${store.discountedPriceListId}${product._id}`];

                            if (typeof price === 'number') {
                                sp.discountedSalesPrice = price;

                                if (sp.salesPrice > 0) {
                                    sp.discount = app.round(
                                        ((sp.salesPrice - sp.discountedSalesPrice) / sp.salesPrice) * 100,
                                        'percentage'
                                    );
                                } else {
                                    delete sp.discountedSalesPrice;
                                }
                            }
                        }

                        if (store.taxApplication === 'tax-included') {
                            if (sp.salesPrice > 0 && !!sp.taxId) {
                                const taxResult = await app.rpc('kernel.common.calculate-tax', {
                                    taxId: sp.taxId,
                                    amount: sp.salesPrice
                                });
                                const taxTotal = taxResult.taxTotal;

                                const price = app.round(sp.salesPrice + taxTotal, 'unit-price');

                                if (typeof sp.discountedSalesPrice === 'number') {
                                    const discountedPriceTaxTotal = app.round(
                                        (taxTotal * sp.discountedSalesPrice) / sp.salesPrice,
                                        'unit-price'
                                    );

                                    sp.discountedSalesPrice = app.round(
                                        sp.discountedSalesPrice + discountedPriceTaxTotal,
                                        'unit-price'
                                    );
                                }

                                sp.salesPrice = price;
                            }
                        }

                        if (store.stockPolicy !== 'manuel') {
                            const r = productStocksMap[product._id];
                            const unitRatios = product.unitRatios;
                            let qty = 0;

                            if (!!r) {
                                if (store.stockPolicy === 'use-stock-on-hand') {
                                    qty = r.stockQuantity || 0;
                                } else if (store.stockPolicy === 'use-available-stock') {
                                    qty = r.availableQuantity || 0;
                                } else if (store.stockPolicy === 'use-available-stock-without-ordered-reservations') {
                                    qty = (r.availableQuantity || 0) - (r.orderedQuantity || 0);
                                } else {
                                    qty = 0;
                                }
                            }

                            if (!!unitRatios && _.isNumber(unitRatios[sp.unitId]) && unitRatios[sp.unitId] !== 0) {
                                const ratio = unitRatios[sp.unitId] || 1;

                                qty = Math.floor(qty / ratio);
                            }

                            sp.quantity = qty;
                        }

                        if (!!store.useProductImportStocks) {
                            let totalVendorStocks = 0;

                            const pcItems = await app.collection('purchase.procurement-catalog').find({
                                productId: product._id,
                                $select: ['qty']
                            });
                            for (const pcItem of pcItems) {
                                totalVendorStocks += pcItem.qty || 0;
                            }

                            sp.quantity += totalVendorStocks;
                        }

                        if (!sp.integrationId) {
                            log({
                                level: 'error',
                                message: app.translate(
                                    'The {{productName}} integration product could not be matched. Integration identifier was not found! Barcode: {{barcode}}',
                                    {productName: sp.productDefinition, barcode: sp.productBarcode}
                                )
                            });
                        } else {
                            storeProductOperations.push({
                                insertOne: {
                                    document: sp
                                }
                            });
                        }
                    }
                }

                if (storeProductOperations.length > 0) {
                    await app.collection('ecommerce.store-products').bulkWrite(storeProductOperations);
                }
            }

            currentCount += products.length;

            if (!!onProgress && totalCount > 0) {
                onProgress((currentCount / totalCount) * 100);
            }

            params = products.nextPageParameters;
        } while (params !== undefined && params !== null);

        await removeUnmatchedIntegrationProducts({app, store, products: matchedProducts});
    } catch (error) {
        log({
            level: 'error',
            message: error.message
        });
    }
}
