import _ from 'lodash';
import {initShopifyClient} from './utils';
import {sleep} from 'framework/helpers';

export default async function (app, store, items) {
    const log = payload => {
        payload.collection = 'ecommerce.stores';
        payload.documentId = store._id;
        payload.documentIdentifier = store.code;
        payload.method = 'update';
        payload.collectionName = `${app.translate('E-Commerce')} / ${app.translate('Stores')}`;

        return app.log(payload);
    };

    try {
        const integrationParams = JSON.parse(store.integrationParams);
        const {shopName, accessToken} = integrationParams;
        const client = initShopifyClient(shopName, accessToken);

        const locations = await client.location.list();
        if (Array.isArray(locations) && locations.length < 1) {
            log({
                level: 'error',
                message: 'No store location found for this shop!'
            });
            return;
        }
        const firstLocation = locations[0];

        for (const item of items.filter(item => !item.isConfigurable)) {
            const storeProduct = item.storeProduct;
            if (!storeProduct.integrationId || !_.isPlainObject(storeProduct.integrationParams)) {
                continue;
            }
            const product = item.product;

            const criticalStockQuantity = storeProduct.criticalStockQuantity;
            let quantity = Math.max(parseInt(item.quantity), 0);
            if (
                typeof criticalStockQuantity === 'number' &&
                criticalStockQuantity > 0 &&
                criticalStockQuantity >= quantity
            ) {
                quantity = 0;
            }
            if (storeProduct.onSale === false) {
                // On sale.
                quantity = 0;
            }
            if (!!product && !!product.ecommerceDropshipping) {
                // Dropshipping.
                quantity = 9999;
            }

            await client.inventoryLevel.set({
                available: quantity,
                inventory_item_id: storeProduct.integrationParams.inventoryItemId,
                location_id: firstLocation.id
            });

            await sleep(100);
        }
    } catch (error) {
        log({
            level: 'error',
            message: `An error occurred while updating integration product stocks! Error: ${error.message}`
        });
    }
}
