import _ from 'lodash';
import axios from 'axios';
import {initShopifyClient} from './utils';
import {sleep} from '../../../../framework/helpers';

const KNOWN_CARGO_COMPANIES = [
    '4PX',
    'AGS',
    'Amazon Logistics UK',
    'Amazon Logistics US',
    'An Post',
    'Anjun Logistics',
    'APC',
    'Asendia USA',
    'Australia Post',
    'Bonshaw',
    'BPost',
    'BPost International',
    'Canada Post',
    'Canpar',
    'CDL Last Mile',
    'China Post',
    'Chronopost',
    'Chukou1',
    'Colissimo',
    'Comingle',
    'Coordinadora',
    'Correios',
    'Correos',
    'CTT',
    'CTT Express',
    'Cyprus Post',
    'Delnext',
    'Deutsche Post',
    'DHL eCommerce',
    'DHL eCommerce Asia',
    'DHL Express',
    'DPD',
    'DPD Local',
    'DPD UK',
    'DTD Express',
    'DX',
    'Eagle',
    'Estes',
    'Evri',
    'FedEx',
    'First Global Logistics',
    'First Line',
    'FSC',
    'Fulfilla',
    'GLS',
    'Guangdong Weisuyi Information Technology (WSE)',
    'Heppner Internationale Spedition GmbH & Co.',
    'Iceland Post',
    'IDEX',
    'Israel Post',
    'Japan Post (EN)',
    'Japan Post (JA)',
    'La Poste',
    'Lasership',
    'Latvia Post',
    'Lietuvos Paštas',
    'Logisters',
    'Lone Star Overnight',
    'M3 Logistics',
    'Meteor Space',
    'Mondial Relay',
    'New Zealand Post',
    'NinjaVan',
    'North Russia Supply Chain (Shenzhen) Co.',
    'OnTrac',
    'Packeta',
    'Pago Logistics',
    'Ping An Da Tengfei Express',
    'Pitney Bowes',
    'Portal PostNord',
    'Poste Italiane',
    'PostNL',
    'PostNord DK',
    'PostNord NO',
    'PostNord SE',
    'Purolator',
    'Qxpress',
    'Qyun Express',
    'Royal Mail',
    'Royal Shipments',
    'Sagawa (EN)',
    'Sagawa (JA)',
    'Sendle',
    'SF Express',
    'SFC Fulfillment',
    'SHREE NANDAN COURIER',
    'Singapore Post',
    'Southwest Air Cargo',
    'StarTrack',
    'Step Forward Freight',
    'Swiss Post',
    'TForce Final Mile',
    'Tinghao',
    'TNT',
    'Toll IPEC',
    'United Delivery Service',
    'UPS',
    'USPS',
    'Venipak',
    'We Post',
    'Whistl',
    'Wizmo',
    'WMYC',
    'Xpedigo',
    'XPO Logistics',
    'Yamato (EN)',
    'Yamato (JA)',
    'YiFan Express',
    'YunExpress',
    'Aramex Australia',
    'TNT Australia',
    'Hunter Express',
    'Couriers Please',
    'Bonds',
    'Allied Express',
    'Direct Couriers',
    'Northline',
    'GO Logistics',
    'Österreichische Post',
    'Speedy',
    'Intelcom',
    'BoxKnight',
    'Loomis',
    'WanbExpress',
    'Zásilkovna',
    'Deutsche Post (DE)',
    'Deutsche Post (EN)',
    'DHL',
    'Swiship',
    'Hermes',
    'SEUR',
    'Colis Privé',
    'Parcelforce',
    'Yodel',
    'DHL Parcel',
    'Tuffnells',
    'ACS Courier',
    'Fastway',
    'DPD Ireland',
    'DTDC',
    'India Post',
    'Delhivery',
    'Gati KWE',
    'Professional Couriers',
    'XpressBees',
    'Ecom Express',
    'Ekart',
    'Shadowfax',
    'Bluedart',
    'BRT',
    'GLS Italy',
    'エコ配',
    '西濃運輸',
    '西濃スーパーエキスプレス',
    '福山通運',
    '日本通運',
    '名鉄運輸',
    '第一貨物',
    'Bring',
    'Inpost',
    'PTT',
    'Yurtiçi Kargo',
    'Aras Kargo',
    'Sürat Kargo',
    'Alliance Air Freight',
    'Pilot Freight',
    'LSO',
    'Old Dominion',
    'R+L Carriers',
    'Skynet'
];

export default async function (app, store, payload) {
    const log = payload => {
        payload.collection = 'ecommerce.stores';
        payload.documentId = store._id;
        payload.documentIdentifier = store.code;
        payload.method = 'update';
        payload.collectionName = `${app.translate('E-Commerce')} / ${app.translate('Stores')}`;

        return app.log(payload);
    };

    try {
        const integrationParams = JSON.parse(store.integrationParams);
        const {shopName, accessToken} = integrationParams;
        const client = initShopifyClient(shopName, accessToken);
        const {orderId, carrierId, cargoTrackingCode} = payload;
        const order = await app.collection('sale.orders').findOne({
            _id: orderId,
            $select: ['_id', 'integrationId']
        });
        const carrier = await app.collection('logistics.carriers').findOne({
            _id: carrierId,
            $select: ['_id', 'name'],
            $disableSoftDelete: true,
            $disableActiveCheck: true
        });
        if (!carrier) {
            return;
        }

        const fulfillmentOrders =
            (
                (
                    await axios.request({
                        method: 'get',
                        maxBodyLength: Infinity,
                        url: `${client.baseUrl.protocol}${client.baseUrl.hostname}/admin/orders/${order.integrationId}/fulfillment_orders.json`,
                        headers: client.baseHeaders
                    })
                ).data ?? {}
            ).fulfillment_orders ?? [];

        for (const fulfillmentOrder of fulfillmentOrders) {
            const payload = {
                line_items_by_fulfillment_order: [{fulfillment_order_id: fulfillmentOrder.id}],
                tracking_info: {
                    number: cargoTrackingCode
                }
            };

            if (KNOWN_CARGO_COMPANIES.includes(carrier.name)) {
                payload.tracking_info.company = carrier.name;
            } else {
                const trackingUrl = await app.rpc('logistics.shipping-orders-get-tracking-url', {
                    carrierId,
                    cargoTrackingCode
                });

                if (typeof trackingUrl === 'string' && trackingUrl.length > 0) {
                    payload.tracking_info.url = trackingUrl;
                }
            }

            await client.fulfillment.createV2(payload);

            await sleep(100);
        }
    } catch (error) {
        log({
            level: 'error',
            message: `An error occurred while updating integration order delivery status! Error: ${error.message}`
        });
    }
}
