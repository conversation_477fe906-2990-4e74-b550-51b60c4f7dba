import _ from 'lodash';
import {initShopifyClient} from './utils';
import {sleep} from 'framework/helpers';
import Random from 'framework/random';

export default async function (app, store, onProgress, startDate, endDate) {
    const log = payload => {
        payload.collection = 'ecommerce.stores';
        payload.documentId = store._id;
        payload.documentIdentifier = store.code;
        payload.method = 'update';
        payload.collectionName = `${app.translate('E-Commerce')} / ${app.translate('Stores')}`;

        return app.log(payload);
    };

    try {
        const integrationParams = JSON.parse(store.integrationParams);
        const {shopName, accessToken} = integrationParams;
        const now = app.datetime.local().toJSDate();
        const client = initShopifyClient(shopName, accessToken);
        const user = await app.collection('kernel.users').findOne({isRoot: true});
        const company = await app.collection('kernel.company').findOne({});

        let start = app.datetime.local().minus({days: 5}).startOf('day').toISO();
        let end = app.datetime.local().endOf('day').toISO();
        if (_.isDate(startDate) && _.isDate(endDate)) {
            start = app.datetime.fromJSDate(startDate).startOf('day').toISO();
            end = app.datetime.fromJSDate(endDate).endOf('day').toISO();
        }

        let params = {
            limit: 10,
            status: 'any',
            fields: 'id, order_number, refunds',
            financial_status: 'refunded,partially_refunded',
            updated_at_min: start,
            updated_at_max: end
        };
        do {
            await sleep(1000);

            const orders = await client.order.list(params);

            for (const integrationOrder of orders) {
                const code = `${store.code}/${integrationOrder.order_number}`;
                const order = await app.collection('sale.orders').findOne({
                    integrationId: integrationOrder.id.toString(),
                    status: {$ne: 'canceled'},
                    $disableActiveCheck: true,
                    $disableSoftDelete: true
                });
                if (!order) continue;

                try {
                    for (const integrationRefund of integrationOrder.refunds ?? []) {
                        if (
                            (await app
                                .collection('sale.returns')
                                .count({integrationId: integrationRefund.id.toString()})) > 0
                        ) {
                            continue;
                        }

                        await getReturn({
                            app,
                            log,
                            company,
                            store,
                            now,
                            user,
                            order,
                            integrationOrder,
                            integrationRefund
                        });
                    }
                } catch (error) {
                    log({
                        level: 'error',
                        message: `An error occurred while importing integration return of the order with the code ${code}! Error: ${error.message}`
                    });
                }
            }

            params = orders.nextPageParameters;
        } while (params !== undefined && params !== null);
    } catch (error) {
        log({
            level: 'error',
            message: `An error occurred while importing integration returns! Error: ${error.message}`
        });
    }
}

async function getReturn({app, log, company, store, now, user, order, integrationOrder, integrationRefund}) {
    const numbering = await app.collection('kernel.numbering').findOne({
        code: 'salesReturnNumbering',
        $select: ['_id'],
        $disableInUseCheck: true,
        $disableActiveCheck: true,
        $disableSoftDelete: true
    });
    const r = {};

    let returnReason = await app.collection('kernel.return-reasons').findOne({
        name: app.translate('Other')
    });
    if (!returnReason) {
        returnReason = await app.collection('kernel.return-reasons').create({
            code: Random.id(4).toString().toUpperCase(),
            name: app.translate('Other')
        });
    }

    let returnWarehouse = await app.collection('inventory.warehouses').findOne({
        _id: store.returnWarehouseId,
        $select: ['_id', 'address'],
        $disableActiveCheck: true,
        $disableSoftDelete: true
    });
    if (!returnWarehouse) {
        returnWarehouse = await app.collection('inventory.warehouses').findOne({
            _id: store.warehouseId,
            $select: ['_id', 'address'],
            $disableActiveCheck: true,
            $disableSoftDelete: true
        });
    }

    const defaultDeliveryOption = store.deliveryOptions.find(
        deliveryOption => deliveryOption.id === store.defaultDeliveryOptionId
    );
    if (!defaultDeliveryOption) {
        throw new Error('Default delivery option is not found!');
    }

    const carrier = await app.collection('logistics.carriers').findOne({
        _id: defaultDeliveryOption.carrierId,
        $disableActiveCheck: true,
        $disableSoftDelete: true
    });
    if (!carrier) {
        throw new Error('Carrier is not found!');
    }

    let currencyRate = 1;
    if (order.currencyId !== company.currencyId) {
        const fromCurrency = await app.collection('kernel.currencies').findOne({
            _id: order.currencyId,
            $select: ['name']
        });
        const toCurrency = company.currency;

        currencyRate = await app.rpc('kernel.common.convert-currency', {
            from: fromCurrency.name,
            to: toCurrency.name,
            value: 1,
            options: {
                date: app.datetime.local().toJSDate()
            }
        });
    }

    r.integrationId = integrationRefund.id.toString();
    r.module = 'ecommerce';
    r.status = 'draft';
    r.code = await app.rpc('kernel.common.request-number', {
        numberingId: numbering._id,
        save: true
    });
    r.branchId = order.branchId;
    r.storeId = store._id;
    r.partnerGroupId = order.partnerGroupId;
    r.partnerId = order.partnerId;
    r.currencyId = order.currencyId;
    r.currencyRate = currencyRate;
    r.contactPersonId = order.contactPersonId;
    r.orderId = order._id;
    r.returnReasonId = returnReason._id;
    r.recordDate = now;
    r.issueDate = now;
    r.scheduledDate = now;
    r.warehouseId = returnWarehouse._id;
    r.deliveryAddress = returnWarehouse.address;
    r.carrierId = carrier._id;
    r.transferIds = order.transferIds;
    r.relatedDocuments = [
        {
            collection: 'sale.orders',
            view: 'sale.sales.orders',
            title: 'Source Sales Orders',
            ids: [order._id]
        },
        {
            collection: 'inventory.transfers',
            view: 'inventory.operations.transfers',
            title: 'Source Deliveries',
            ids: order.transferIds
        }
    ];

    let items = [];
    for (const integrationRefundLine of integrationRefund.refund_line_items ?? []) {
        const storeProduct = await app.collection('ecommerce.store-products').findOne({
            integrationId: (integrationRefundLine.line_item.variant_id ?? '').toString(),
            $select: ['_id', 'productId', 'productBarcode']
        });
        if (!storeProduct) {
            throw new Error(
                app.translate(
                    'The {{productName}} integration product could not be matched. No ERP product found for the relevant product!',
                    {
                        productName: integrationRefundLine.line_item.title
                    }
                )
            );
        }
        const product = await app.collection('inventory.products').findOne({
            _id: storeProduct.productId
        });
        if (!product) {
            throw new Error(
                app.translate(
                    'The {{productName}} integration product could not be matched. No ERP product found for the relevant product!',
                    {
                        productName: integrationRefundLine.line_item.title
                    }
                )
            );
        }

        items.push({
            reference: order.code,
            referenceId: order._id,
            referenceCollection: 'sale.orders',
            productId: product._id,
            description:
                integrationRefundLine.line_item.title || integrationRefundLine.line_item.name || product.displayName,
            scheduledDate: now,
            quantity: integrationRefundLine.quantity,
            stockQuantity: 0,
            orderedQuantity: 0,
            assignedQuantity: 0,
            availableQuantity: 0,
            warehouseStockQuantity: 0,
            warehouseOrderedQuantity: 0,
            warehouseAssignedQuantity: 0,
            warehouseAvailableQuantity: 0
        });
    }
    r.items = await app.rpc(
        'sale.decorate-return-items',
        {
            items,
            field: 'productId',
            model: _.omit(r, 'items'),
            productFields: [
                'code',
                'name',
                'displayName',
                'definition',
                'type',
                'baseUnitId',
                'salesUnitId',
                'barcode',
                'unitRatios',
                'unitConversions',
                'salesNote',
                'isSimple'
            ]
        },
        {user}
    );

    let returnOrder = await app.collection('sale.returns').create(r, {user});
    await app.rpc(
        'sale.returns-save',
        {
            id: returnOrder._id,
            data: {
                ..._.omit(returnOrder, '_id'),
                status: 'approved'
            }
        },
        {user}
    );
    returnOrder = await app.collection('sale.returns').get(returnOrder._id);

    await (async () => {
        const partner = await app.collection('kernel.partners').findOne({
            _id: order.partnerId,
            $select: ['_id', 'accountingAccountId']
        });
        if (!partner || !partner.accountingAccountId) {
            return;
        }

        const financialEntryIds = order.financialEntryIds ?? [];
        if (financialEntryIds.length < 1) {
            return;
        }

        const financialEntry = await app.collection('finance.entries').findOne({
            _id: financialEntryIds[0],
            $select: ['_id', 'journalId']
        });
        if (!financialEntry || !financialEntry.journalId) {
            return;
        }

        const journal = await app.collection('accounting.journals').findOne({
            _id: financialEntry.journalId,
            $select: ['_id', 'creditAccountId']
        });
        if (!journal || !journal.creditAccountId) {
            return;
        }

        let amount = 0;
        for (const transaction of integrationRefund.transactions ?? []) {
            const a = parseFloat(transaction.amount);

            if (!isNaN(a)) {
                amount += a;
            }
        }
        if (!(amount > 0)) {
            return;
        }

        const entry = {items: []};
        entry.documentNo = '';
        entry.reference = returnOrder.code;
        entry.description = app.translate('Refund of order {{orderCode}}', {orderCode: order.code});
        entry.branchId = order.branchId;
        entry.recordDate = now;
        entry.issueDate = now;
        entry.dueDate = now;
        entry.journalId = financialEntry.journalId;
        entry.relatedDocuments = [
            {
                collection: 'sale.returns',
                view: 'ecommerce.sales.returns',
                title: 'Returns',
                ids: [returnOrder._id]
            }
        ];

        // First item.
        const firstItem = {};
        firstItem.accountId = partner.accountingAccountId;
        firstItem.partnerId = partner._id;
        firstItem.description = entry.description;
        firstItem.branchId = entry.branchId;
        firstItem.currencyId = order.currencyId;
        firstItem.scope = '1';
        if (order.currencyId !== company.currencyId) {
            firstItem.debitFC = amount;
            firstItem.debit = amount * currencyRate;
        } else {
            firstItem.debit = amount;
        }
        entry.items.push(firstItem);

        // Second item.
        const secondItem = {};
        secondItem.accountId = journal.creditAccountId;
        secondItem.partnerId = partner._id;
        secondItem.description = entry.description;
        secondItem.branchId = entry.branchId;
        secondItem.currencyId = order.currencyId;
        secondItem.scope = '1';
        if (order.currencyId !== company.currencyId) {
            secondItem.creditFC = amount;
            secondItem.credit = amount * currencyRate;
        } else {
            secondItem.credit = amount;
        }
        entry.items.push(secondItem);

        const journalEntry = await app.rpc('accounting.save-journal-entry', {data: entry}, {user});
        await app.rpc('accounting.post-journal-entry', journalEntry._id, {user});

        await app.collection('sale.returns').patch(
            {_id: returnOrder._id},
            {
                relatedDocuments: (returnOrder.relatedDocuments ?? []).concat([
                    {
                        collection: 'accounting.journal-entries',
                        view: 'accounting.adviser.journal-entries',
                        title: 'Journal Entries',
                        ids: [journalEntry._id]
                    }
                ])
            }
        );
    })();
}
