import _ from 'lodash';
import microtime from 'microtime';
import {initShopifyClient} from './utils';
import {arabicToEnName, isArabic, sleep, template, toUpper, trim} from 'framework/helpers';
import Random from 'framework/random';
import {updateProductsSalesCount} from '../utils';
import {calculateReport} from '../../../finance/components/pp/utils';

export default async function (app, store, onProgress, startDate, endDate) {
    const log = payload => {
        payload.collection = 'ecommerce.stores';
        payload.documentId = store._id;
        payload.documentIdentifier = store.code;
        payload.method = 'update';
        payload.collectionName = `${app.translate('E-Commerce')} / ${app.translate('Stores')}`;

        return app.log(payload);
    };

    try {
        const integrationParams = JSON.parse(store.integrationParams);
        const {shopName, accessToken} = integrationParams;
        const now = app.datetime.local().toJSDate();
        const client = initShopifyClient(shopName, accessToken);
        const user = await app.collection('kernel.users').findOne({isRoot: true});
        const company = await app.collection('kernel.company').findOne({});
        const stages = await app.collection('sale.order-stages').find();

        let start = app.datetime.local().minus({days: 5}).startOf('day').toISO();
        let end = app.datetime.local().endOf('day').toISO();
        if (_.isDate(startDate) && _.isDate(endDate)) {
            start = app.datetime.fromJSDate(startDate).startOf('day').toISO();
            end = app.datetime.fromJSDate(endDate).endOf('day').toISO();
        }

        let params = {
            limit: 10,
            status: 'any',
            financial_status: 'paid,refunded,partially_refunded',
            updated_at_min: start,
            updated_at_max: end
        };
        do {
            await sleep(1000);

            const orders = await client.order.list(params);

            for (const integrationOrder of orders) {
                const code = `${store.code}/${integrationOrder.order_number}`;
                if ((await app.collection('sale.orders').count({integrationId: integrationOrder.id.toString()})) > 0) {
                    continue;
                }

                try {
                    // Get customer, deliveryAddressId, deliveryAddress, invoiceAddressId, invoiceAddress
                    const {customer, invoiceAddressId, invoiceAddress, deliveryAddressId, deliveryAddress} =
                        await getCustomer(app, company, user, store, integrationOrder);

                    // Get order.
                    await getOrder({
                        app,
                        log,
                        company,
                        store,
                        now,
                        user,
                        integrationOrder,
                        customer,
                        invoiceAddressId,
                        invoiceAddress,
                        deliveryAddressId,
                        deliveryAddress,
                        stages
                    });
                } catch (error) {
                    log({
                        level: 'error',
                        message: `An error occurred while importing integration order with the code ${code}! Error: ${error.message}`
                    });
                }
            }

            params = orders.nextPageParameters;
        } while (params !== undefined && params !== null);
    } catch (error) {
        log({
            level: 'error',
            message: error.message
        });
    }
}

async function getOrder({
    app,
    log,
    company,
    store,
    now,
    user,
    integrationOrder,
    customer,
    invoiceAddressId,
    invoiceAddress,
    deliveryAddressId,
    deliveryAddress,
    stages
}) {
    let order = {};

    // General.
    order.module = 'ecommerce';
    order.storeId = store._id;
    order.status = store.automaticOrderApproval ? 'approved' : 'draft';
    order.code = `${store.code}/${integrationOrder.order_number}`;
    order.documentTypeId = store.salesDocumentTypeId;
    order.partnerId = customer._id;
    order.partnerGroupId = customer.groupId;
    order.branchId = store.branchId;
    order.currencyId = store.currencyId;
    order.currencyRate = 1;
    order.recordDate = now;
    order.orderDate = now;
    order.scheduledDate = now;
    order.subTotal = 0;
    order.discount = 0;
    order.discountAmount = 0;
    order.subTotalAfterDiscount = 0;
    order.taxTotal = 0;
    order.rounding = 0;
    order.grandTotal = 0;
    order.paidTotal = 0;
    order.plannedTotal = 0;
    order.appliedTaxes = [];
    order.sourceId = store.sourceId;
    order.communicationChannelId = store.communicationChannelId;
    order.invoiceAddressId = invoiceAddressId;
    order.invoiceAddress = invoiceAddress;
    order.deliveryAddressId = deliveryAddressId;
    order.deliveryAddress = deliveryAddress;
    // order.cargoTrackingCode = integrationOrder.cargoTrackingNumber;
    order.warehouseId = store.warehouseId;
    // order.paymentTermId = paymentTerm._id;
    // order.paymentTerm = paymentTerm;
    order.priceListId = store.priceListId || '';

    let currency = await app.collection('kernel.currencies').findOne({
        name: integrationOrder.currency
    });
    if (!!currency) {
        order.currencyId = currency._id;
    }
    if (order.currencyId !== company.currencyId) {
        if (order.currencyId !== currency._id) {
            currency = await app.collection('kernel.currencies').findOne({
                _id: order.currencyId
            });
        }

        order.currencyRate = await app.rpc('kernel.common.convert-currency', {
            from: currency.name,
            to: company.currency.name,
            value: 1,
            options: {
                date: now
            }
        });
    }

    order.note = integrationOrder.note ?? '';
    for (const attr of integrationOrder.note_attributes ?? []) {
        order.note += `\n${attr.name}: ${attr.value}`;
    }
    order.note = order.note.trim();

    if (Array.isArray(stages) && stages.length > 0) {
        order.stages = stages;
        order.stageId = stages[0]._id;
    }

    // Integration.
    order.integrationId = integrationOrder.id.toString();

    // Delivery note.
    // let deliveryNotes = [];
    // if (!!integrationOrder.cargoProviderName) {
    //     deliveryNotes.push(`${app.translate('Cargo provider name')}: ${integrationOrder.cargoProviderName}`);
    // }
    // if (!!integrationOrder.cargoTrackingNumber) {
    //     deliveryNotes.push(`${app.translate('Cargo tracking number')}: ${integrationOrder.cargoTrackingNumber}`);
    // }
    // if (!!integrationOrder.cargoTrackingLink) {
    //     deliveryNotes.push(`${app.translate('Cargo tracking link')}: ${integrationOrder.cargoTrackingLink}`);
    // }
    // order.deliveryNote = deliveryNotes.join('\n');

    const contact = await app.collection('kernel.contacts').findOne({
        _id: deliveryAddressId,
        $select: ['defaultDeliveryOptionId']
    });

    // if (Array.isArray(integrationCarriers) && integrationCarriers.length > 0) {
    //     const integrationCarrier = integrationCarriers.find(ic => ic.name === integrationOrder?.cargoProviderName);
    //     const deliveryOption = store.deliveryOptions.find(sdo => sdo.integrationId === integrationCarrier?.id);
    //     order.carrierId = deliveryOption?.carrierId;
    // }
    if (!_.isEmpty(customer.defaultDeliveryOptionId)) {
        order.carrierId = customer.defaultDeliveryOptionId;
    }
    if (_.isPlainObject(contact) && !_.isEmpty(contact.defaultDeliveryOptionId)) {
        order.carrierId = contact.defaultDeliveryOptionId;
    }
    if (!order.carrierId) {
        const storeDefaultDeliveryOption = store.deliveryOptions.find(sdo => sdo.id === store.defaultDeliveryOptionId);

        if (storeDefaultDeliveryOption) {
            order.carrierId = storeDefaultDeliveryOption.carrierId;
        }
    }

    // Calculate final discount.
    const totalIntegrationDiscountAmount = parseFloat(integrationOrder.total_discounts); // with taxes
    const totalIntegrationLineTotal = parseFloat(integrationOrder.total_line_items_price); // with taxes
    let discount = 0;
    if (!isNaN(totalIntegrationDiscountAmount) && !isNaN(totalIntegrationLineTotal) && totalIntegrationLineTotal > 0) {
        discount = (totalIntegrationDiscountAmount / totalIntegrationLineTotal) * 100;
    }

    // Get items.
    order.items = await getOrderItems({
        app,
        log,
        company,
        store,
        now,
        user,
        integrationOrder,
        customer,
        invoiceAddressId,
        invoiceAddress,
        deliveryAddressId,
        deliveryAddress,
        order,
        discount
    });

    // Totals.
    let discountAmount = 0;
    let subTotal = 0;
    let subTotalAfterDiscount = 0;
    let taxTotal = 0;
    let grandTotal = 0;
    let appliedTaxes = [];
    order.items.forEach(row => {
        subTotal += app.round(row.total, 'total');

        if (_.isPlainObject(row.taxDetail)) {
            taxTotal += app.round(row.taxDetail.taxTotal, 'total');

            row.taxDetail.applied.forEach(tax => {
                const taxIndex = _.findIndex(appliedTaxes, t => t._id === tax._id);

                if (taxIndex !== -1) {
                    appliedTaxes[taxIndex].unAppliedAmount += app.round(tax.unAppliedAmount || 0, 'total');
                    appliedTaxes[taxIndex].appliedAmount += app.round(tax.appliedAmount || 0, 'total');
                } else {
                    tax.unAppliedAmount = app.round(tax.unAppliedAmount || 0, 'total');
                    tax.appliedAmount = app.round(tax.appliedAmount || 0, 'total');
                    appliedTaxes.push(_.cloneDeep(tax));
                }
            });
        }
    });
    grandTotal = app.round(subTotal + taxTotal, 'total');
    if (_.isNumber(discount) && subTotal > 0) {
        // Get discount amount.
        discountAmount = app.round((subTotal * discount) / 100, 'total');

        // Calculate new taxes and tax total.
        const ratio = discountAmount / subTotal;
        const payload = {items: []};
        order.items.forEach(row => {
            const rowDiscountAmount = app.round(ratio * row.total, 'total');
            const newRowTotal = app.round(row.total - rowDiscountAmount, 'total');
            const key = row.taxDetail.applied.map(t => t._id).join('');
            const existingIndex = _.findIndex(payload.items, i => i.key === key);
            if (existingIndex === -1) {
                payload.items.push({
                    taxId: row.taxId,
                    quantity: row.quantity || 1,
                    taxPayload: row.taxPayload,
                    amount: newRowTotal,
                    key
                });
            } else {
                payload.items[existingIndex].amount += newRowTotal;
                payload.items[existingIndex].quantity += row.quantity || 1;
            }
        });
        const result = await app.rpc('kernel.common.calculate-taxes', payload);
        taxTotal = 0;
        for (const r of result) {
            taxTotal += app.round(r.amount, 'total');
        }
        appliedTaxes = appliedTaxes.map(tax => {
            const newTaxResult = result.find(r => r.taxId === tax._id);

            tax.appliedAmount = app.round(newTaxResult.amount, 'total');

            return tax;
        });

        // Calculate subtotal after discount.
        subTotalAfterDiscount = app.round(subTotal - discountAmount, 'total');

        // Calculate new grand total.
        grandTotal = app.round(subTotal + taxTotal - discountAmount, 'total');
    }
    order.subTotal = app.round(subTotal, 'total');
    order.discount = discount;
    order.discountAmount = app.round(discountAmount, 'total');
    order.subTotalAfterDiscount = subTotalAfterDiscount;
    order.taxTotal = app.round(taxTotal, 'total');
    order.grandTotal = app.round(grandTotal, 'total');
    order.appliedTaxes = appliedTaxes;

    // Fix grand total.
    const orderPayloadGrandTotal = parseFloat(integrationOrder.total_price);
    if (orderPayloadGrandTotal !== order.grandTotal) {
        const diff = orderPayloadGrandTotal - order.grandTotal;

        order.rounding = app.round(diff, 'total');
        order.grandTotal = app.round(order.grandTotal + order.rounding, 'total');
    }

    // Initialize exchange rates.
    await (async () => {
        const currencies = await app.collection('kernel.currencies').find({
            $select: ['name']
        });
        const exchangeRates = [];
        const payloads = [];

        for (const currency of currencies) {
            if (currency.name === company.currency.name) {
                continue;
            }

            payloads.push({
                from: currency.name,
                to: company.currency.name,
                value: 1,
                options: {
                    date: order.orderDate
                }
            });
        }

        for (const payload of await app.rpc('kernel.common.convert-currencies', payloads)) {
            exchangeRates.push({
                currencyName: payload.from,
                rate: payload.rate
            });
        }

        order.exchangeRates = exchangeRates;

        const exchangeRatesMap = {};
        for (const exchangeRate of order.exchangeRates || []) {
            exchangeRatesMap[exchangeRate.currencyName] = exchangeRate.rate;
        }
        order.exchangeRatesMap = exchangeRatesMap;
    })();

    // Get payment plan.
    let paymentMethod = null;
    if (!!store.defaultPaymentMethodId) {
        paymentMethod = store.paymentMethods.find(paymentMethod => paymentMethod.id === store.defaultPaymentMethodId);
    }
    if (!!paymentMethod) {
        try {
            const {paymentTerm, paymentPlan} = await getPaymentPlan({
                app,
                company,
                store,
                integrationOrder,
                now: app.datetime.fromJSDate(now),
                user,
                customer,
                contact,
                invoiceAddressId,
                invoiceAddress,
                deliveryAddressId,
                deliveryAddress,
                order,
                paymentMethod
            });
            order.paymentTermId = paymentTerm._id;
            order.paymentTerm = paymentTerm;
            order.paymentPlan = paymentPlan;
            order.paymentPlanningDate = paymentPlan.baseDate;
        } catch (error) {
            log({
                level: 'error',
                message: `An error occurred while creating order payment plan. Error: ${error.message}`
            });
        }
    } else {
        log({
            level: 'error',
            message: 'No default payment method found!'
        });
    }

    // Last-check.
    if ((await app.collection('sale.orders').count({code: order.code})) > 0) {
        return order;
    }

    order = await app.collection('sale.orders').create(order, {user, skipEvents: true});

    // Initialize profits.
    await app.rpc('sale.update-document-profits', {
        profitBase: app.setting('sale.profitBase'),
        documentId: order._id,
        documentCollection: 'sale.orders',
        currencyId: order.currencyId,
        currencyRate: order.currencyRate,
        date: order.orderDate,
        exchangeRates: order.exchangeRates ?? [],
        items: order.items.map(item => ({
            productId: item.productId,
            warehouseId: item.warehouseId,
            quantity: item.quantity,
            unitId: item.unitId,
            freight: item.freight || 0,
            totalSalesPrice: item.realTotal
        }))
    });

    // Initialize extras.
    const extra = {
        basePrice: 0,
        grossProfit: 0,
        profitRate: 0,
        profitMargin: 0,
        cashAmount: 0,
        cashInstallmentCount: 0,
        moneyTransferAmount: 0,
        moneyTransferInstallmentCount: 0,
        chequeAmount: 0,
        chequeInstallmentCount: 0,
        promissoryNoteAmount: 0,
        promissoryNoteInstallmentCount: 0,
        posAmount: 0,
        posInstallmentCount: 0,
        installmentCount: 0
    };
    const profit = await app.collection('sale.document-profits').findOne({
        documentId: order._id,
        documentCollection: 'sale.orders',
        $select: ['totalBasePrice', 'totalGrossProfit', 'profitRate', 'profitMargin']
    });
    if (_.isPlainObject(profit)) {
        extra.basePrice = profit.totalBasePrice;
        extra.grossProfit = profit.totalGrossProfit;
        extra.profitRate = profit.profitRate;
        extra.profitMargin = profit.profitMargin;
    }
    await app.collection('sale.orders').patch({_id: order._id}, {extra});

    // Create payments.
    if (
        !!paymentMethod &&
        paymentMethod.paymentType !== 'open-account' &&
        !!order.paymentTermId &&
        _.isDate(order.paymentPlanningDate)
    ) {
        try {
            await app.rpc('sale.create-order-receipts', [order._id], {user});
        } catch (error) {
            log({
                level: 'error',
                message: `An error occurred while creating order payment receipts. Error: ${error.message}`
            });
        }

        if (paymentMethod.paymentType === 'credit-card') {
            try {
                order = await app.collection('sale.orders').get(order._id);

                for (const entryId of order.financialEntryIds || []) {
                    await app.rpc('finance.approve-entry', entryId, {user});
                }

                if ((order.financialEntryIds ?? []).length > 0) {
                    const firstEntry = await app.collection('finance.entries').findOne({
                        _id: order.financialEntryIds[0],
                        $select: ['_id', 'receiptId']
                    });

                    if (!!firstEntry && !!firstEntry.receiptId) {
                        await app.collection('finance.receipts').patch(
                            {
                                _id: firstEntry.receiptId
                            },
                            {
                                status: 'closed'
                            }
                        );
                    }
                }
            } catch (error) {
                log({
                    level: 'error',
                    message: `An error occurred while approving order payments. Error: ${error.message}`
                });
            }
        }
    }

    try {
        await updateProductsSalesCount(app, store, order.items);
    } catch (error) {
        console.error(
            `An error occurred while updating product sales count! Store: ${store.name}, Error: `,
            error.message
        );
    }

    if (order.status === 'approved') {
        try {
            // Create external reservations.
            await (async () => {
                const items = order.items
                    .filter(item => item.productType !== 'service')
                    .map(item => {
                        const row = {};

                        row.productId = item.productId;
                        row.productType = item.productType;
                        row.date = item.scheduledDate;
                        row.warehouseId = item.warehouseId;
                        row.quantity = item.quantity;
                        row.unitId = item.unitId;
                        row.unitPrice = (item.realTotal / item.quantity) * (order.currencyRate || 1);

                        if (app.hasModule('pcm') && !!item.pcmHash) {
                            row.pcmModelId = item.pcmModelId;
                            row.pcmConfigurationId = item.pcmConfigurationId;
                            row.pcmHash = item.pcmHash;
                        }

                        return row;
                    });

                await app.rpc(
                    'inventory.create-external-reservations',
                    {
                        documentId: order._id,
                        documentCollection: 'sale.orders',
                        documentCode: order.code,
                        documentView: 'sale.sales.orders',
                        documentTitle: 'Sale Orders',
                        type: 'outgoing',
                        code: order.code,
                        partnerId: order.partnerId,
                        orderDate: order.orderDate,
                        exchangeRatesMap: order.exchangeRatesMap || {},
                        items,
                        checkProductStocks: false,
                        disableAdvancedDeliveryPlanning: true
                    },
                    {user}
                );
            })();

            let isAdvancedDeliveryPlanningEnabled = !!app.setting('inventory.advancedDeliveryPlanning');
            if (isAdvancedDeliveryPlanningEnabled) {
                const warehouse = await app.collection('inventory.warehouses').findOne({
                    _id: store.warehouseId,
                    $select: ['_id', 'advancedDeliveryPlanning'],
                    $disableActiveCheck: true,
                    $disableSoftDelete: true
                });

                if (!!warehouse && Array.isArray(warehouse.advancedDeliveryPlanning)) {
                    isAdvancedDeliveryPlanningEnabled = warehouse.advancedDeliveryPlanning.includes('sale');
                }
            }

            if (store.automaticDelivery && !isAdvancedDeliveryPlanningEnabled) {
                try {
                    await app.rpc('sale.create-deliveries', {orderIds: [order._id]});

                    if (store.automaticDeliveryConfirmation) {
                        const ecommerceOrder = await app.collection('sale.orders').findOne({
                            _id: order._id,
                            $select: ['transferIds'],
                            $disableActiveCheck: true,
                            $disableSoftDelete: true
                        });

                        if (
                            ecommerceOrder &&
                            Array.isArray(ecommerceOrder.transferIds) &&
                            ecommerceOrder.transferIds.length > 0
                        ) {
                            const transfers = await app.collection('inventory.transfers').find({
                                _id: {$in: ecommerceOrder.transferIds},
                                $disableActiveCheck: true,
                                $disableSoftDelete: true
                            });

                            for (const transfer of transfers) {
                                await app.rpc(
                                    'inventory.transfers-save-transfer',
                                    {
                                        data: {
                                            ..._.omit(transfer, '_id'),
                                            shippingDocumentType: 'none',
                                            status: 'approved'
                                        },
                                        id: transfer._id
                                    },
                                    {user}
                                );
                            }

                            if (store.automaticShippingOrder) {
                                const approvedTransfers = await app.collection('inventory.transfers').find({
                                    _id: {$in: ecommerceOrder.transferIds},
                                    status: 'approved',
                                    $select: ['_id'],
                                    $disableActiveCheck: true,
                                    $disableSoftDelete: true
                                });

                                const packageTypes = await app.collection('logistics.package-types').find({
                                    carrierId: order.carrierId,
                                    $select: ['_id', 'isDefault'],
                                    $disableActiveCheck: true,
                                    $disableSoftDelete: true
                                });

                                for (const transfer of approvedTransfers) {
                                    await app.rpc('inventory.transfers-create-shipping-order', {
                                        transferId: transfer._id,
                                        carrierId: order.carrierId,
                                        packageTypeId:
                                            packageTypes.find(pkg => pkg.isDefault)?._id ?? packageTypes[0]?._id,
                                        cashOnDeliveryAmount: 0,
                                        shippingPaymentType: 'freight-prepaid',
                                        packagingType: 'package',
                                        weight: 1,
                                        volumetricWeight: 1,
                                        deliveryAddress: deliveryAddress
                                    });
                                }
                            }
                        }
                    }
                } catch (error) {
                    console.log('Marketplace transfer creation and update error in [import-orders] ->', error);
                }
            }

            if (store.automaticInvoice) {
                await app.rpc('sale.create-invoices', {orderIds: [order._id]});

                if (integrationOrder.micro) {
                    const ecommerceOrder = await app.collection('sale.orders').findOne({
                        _id: order._id,
                        $select: ['relatedDocuments'],
                        $disableActiveCheck: true,
                        $disableSoftDelete: true
                    });

                    const rd = ecommerceOrder.relatedDocuments.find(
                        rd => rd.collection === 'accounting.customer-invoices'
                    );

                    if (rd && Array.isArray(rd.ids) && rd.ids.length > 0) {
                        await app.collection('accounting.customer-invoices').bulkWrite([
                            {
                                updateOne: {
                                    filter: {_id: rd.ids[0]},
                                    update: {
                                        $set: {
                                            ...(store.exportInvoiceScenario && {
                                                invoiceScenario: store.exportInvoiceScenario
                                            }),
                                            ...(store.exportEInvoiceTypeId && {
                                                eInvoiceTypeId: store.exportEInvoiceTypeId
                                            }),
                                            ...(store.exportEInvoiceTypeConditionCode && {
                                                eInvoiceTypeConditionCode: store.exportEInvoiceTypeConditionCode
                                            })
                                        }
                                    }
                                }
                            }
                        ]);
                    }
                }

                if (store.automaticInvoiceApproval) {
                    const invoice = await app.collection('accounting.customer-invoices').findOne({
                        reference: order.code
                    });

                    if (invoice) {
                        try {
                            if (!invoice.journalId) {
                                throw new Error(
                                    app.translate('{{label}} is required', {
                                        label: app.translate('Journal')
                                    })
                                );
                            }
                            if (!invoice.journalDescription) {
                                throw new Error(
                                    app.translate('{{label}} is required', {
                                        label: app.translate('Journal description')
                                    })
                                );
                            }
                            if (!invoice.accountingAccountId) {
                                throw new Error(
                                    app.translate('{{label}} is required', {
                                        label: app.translate('Account')
                                    })
                                );
                            }
                            if (invoice.documentNo) {
                                if (
                                    (await app.collection('accounting.customer-invoices').count({
                                        _id: {$ne: invoice._id},
                                        documentNo: invoice.documentNo,
                                        status: {$ne: 'canceled'}
                                    })) > 0
                                ) {
                                    throw new Error(app.translate('Document no must be unique!'));
                                }
                            }
                            if (invoice.invoiceScenario === 'normal' && app.setting('eops.isEInvoiceActivated')) {
                                throw new Error(
                                    app.translate(
                                        'You cannot approve the invoice with the normal invoice scenario when e-archive system is active!'
                                    )
                                );
                            }

                            const linkedDocumentIds = [];
                            for (const rd of invoice.relatedDocuments || []) {
                                if (rd.collection === 'sale.orders') {
                                    linkedDocumentIds.push(...rd.ids);
                                }
                            }
                            const limitResult = await app.rpc('finance.check-partner-limit', {
                                partnerId: invoice.partnerId,
                                currencyId: invoice.currencyId,
                                guaranteeId: invoice.guaranteeId,
                                amount: invoice.grandTotal,
                                linkedDocumentIds,
                                document: invoice.isReturn ? 'return-invoice' : 'invoice'
                            });
                            if (!!limitResult && limitResult.blacklist) {
                                throw new Error(
                                    app.translate(
                                        'The operation cannot be performed because the partner is in the blacklist!'
                                    )
                                );
                            }
                            if (!!limitResult && !limitResult.hasLimit && limitResult.reaction === 'block') {
                                throw new Error(limitResult.message);
                            }

                            await app.rpc(
                                'accounting.save-customer-invoice',
                                {
                                    data: {
                                        ..._.omit(invoice, ['_id', 'extra']),
                                        status: 'approved'
                                    },
                                    id: invoice._id
                                },
                                {user}
                            );
                        } catch (error) {
                            await app.collection('accounting.customer-invoices').bulkWrite([
                                {
                                    updateOne: {
                                        filter: {_id: invoice._id},
                                        update: {
                                            $set: {bulkApprovalError: error.message}
                                        }
                                    }
                                }
                            ]);
                        }
                    }
                }

                if (store.automaticInvoiceSending) {
                    const approvedInvoice = await app.collection('accounting.customer-invoices').findOne({
                        reference: order.code,
                        status: 'approved',
                        $select: ['_id', 'scopeRate', 'invoiceScenario']
                    });

                    if (approvedInvoice) {
                        try {
                            if (approvedInvoice.scopeRate === 0) {
                                throw new Error(
                                    app.translate('E-invoices cannot be sent for invoices with a zero scope ratio!')
                                );
                            }

                            if (approvedInvoice.invoiceScenario === 'e-archive-invoice') {
                                if (!store.eArchiveInvoiceNumberingId) {
                                    throw new Error(app.translate('E-Archive invoice numbering is not found!'));
                                }
                                if (!store.eArchiveInvoiceTemplateId) {
                                    throw new Error(app.translate('E-Archive invoice template is not found!'));
                                }

                                const eArchiveInvoice = await app.collection('eops.e-archive-invoices').findOne({
                                    invoiceId: approvedInvoice._id,
                                    $select: ['_id']
                                });

                                if (!eArchiveInvoice) {
                                    throw new Error(app.translate('E-archive Invoice is not found!'));
                                }

                                const invoiceDocumentNo = await app.rpc('kernel.common.request-number', {
                                    numberingId: store.eArchiveInvoiceNumberingId,
                                    date: now,
                                    save: false
                                });

                                await app.rpc('eops.e-archive-invoices-send', {
                                    eArchiveInvoiceId: eArchiveInvoice._id,
                                    invoiceDocumentNo,
                                    invoiceIssueDate: now,
                                    templateId: store.eArchiveInvoiceTemplateId,
                                    numberingId: store.eArchiveInvoiceNumberingId,
                                    saveNumbering: true
                                });
                            } else if (approvedInvoice.invoiceScenario !== 'normal') {
                                if (!store.eInvoiceNumberingId) {
                                    throw new Error(app.translate('E-Invoce numbering is not found!'));
                                }
                                if (!store.eInvoiceTemplateId) {
                                    throw new Error(app.translate('E-Invoice template is not found!'));
                                }

                                const eInvoice = await app.collection('eops.e-invoices').findOne({
                                    invoiceId: approvedInvoice._id,
                                    $select: ['_id', 'partnerTinIdentity']
                                });

                                if (!eInvoice) {
                                    throw new Error(app.translate('E-Invoice is not found!'));
                                }

                                let users = [];
                                if (
                                    approvedInvoice.invoiceScenario !== 'export-invoice' &&
                                    approvedInvoice.invoiceScenario !== 'passenger-invoice'
                                ) {
                                    users = await app.rpc('eops.e-invoices-get-users', eInvoice.partnerTinIdentity);
                                    users = _.orderBy(users, ['updatedAt'], ['desc']);
                                }

                                let allFailed = true;
                                let lastError = null;
                                for (let i = 0; i < users.length; i++) {
                                    try {
                                        const invoiceDocumentNo = await app.rpc('kernel.common.request-number', {
                                            numberingId: store.eInvoiceNumberingId,
                                            date: now,
                                            save: false
                                        });

                                        await app.rpc('eops.e-invoices-send', {
                                            eInvoiceId: eInvoice._id,
                                            pbLabel: users[i]?.label || null,
                                            invoiceDocumentNo,
                                            invoiceIssueDate: now,
                                            templateId: store.eInvoiceTemplateId,
                                            numberingId: store.eInvoiceNumberingId,
                                            saveNumbering: true
                                        });

                                        allFailed = false;
                                        break;
                                    } catch (error) {
                                        lastError = error.message;
                                    }
                                }

                                if (allFailed && lastError) {
                                    throw new Error(lastError);
                                }
                            }
                        } catch (error) {
                            await app.collection('accounting.customer-invoices').bulkWrite([
                                {
                                    updateOne: {
                                        filter: {_id: approvedInvoice._id},
                                        update: {
                                            $set: {bulkApprovalError: error.message}
                                        }
                                    }
                                }
                            ]);
                        }
                    }
                }
            }
        } catch (error) {
            console.log(error);
        }
    }

    return order;
}

async function getOrderItems({
    app,
    log,
    store,
    now,
    user,
    integrationOrder,
    customer,
    invoiceAddressId,
    invoiceAddress,
    deliveryAddressId,
    deliveryAddress,
    order,
    discount
}) {
    let items = [];

    for (const integrationOrderLine of integrationOrder.line_items ?? []) {
        const storeProduct = await app.collection('ecommerce.store-products').findOne({
            integrationId: (integrationOrderLine.variant_id ?? '').toString(),
            $select: ['_id', 'productId', 'productBarcode']
        });
        if (!storeProduct) {
            throw new Error(
                app.translate(
                    'The {{productName}} integration product could not be matched. No ERP product found for the relevant product!',
                    {
                        productName: integrationOrderLine.title
                    }
                )
            );
        }
        const product = await app.collection('inventory.products').findOne({
            _id: storeProduct.productId
        });

        if (!product) {
            throw new Error(
                app.translate(
                    'The {{productName}} integration product could not be matched. No ERP product found for the relevant product!',
                    {
                        productName: integrationOrderLine.title
                    }
                )
            );
        }

        const matchedBarcodeInfo = product.barcodes.find(pb => pb.barcode === storeProduct.productBarcode) ?? {};

        let item = {
            id: Random.id(16),
            productId: product._id,
            productCode: product.code,
            productDefinition: product.definition,
            description: integrationOrderLine.title || integrationOrderLine.name || product.displayName,
            productType: 'stockable',
            barcode: matchedBarcodeInfo.barcode || product.barcode,
            scheduledDate: now,
            branchId: order.branchId,
            warehouseId: order.warehouseId,
            quantity: integrationOrderLine.quantity ?? 1,
            unitId: matchedBarcodeInfo.unitId || product.baseUnitId,
            baseUnitId: product.baseUnitId,
            baseQuantity: integrationOrderLine.quantity,
            unitPrice: 0,
            grossUnitPrice: 0,
            discount: 0,
            unitPriceAfterDiscount: 0,
            grossUnitPriceAfterDiscount: 0,
            taxId: product.salesTaxId,
            taxTotal: 0,
            grossTotal: 0,
            stockQuantity: 0,
            orderedQuantity: 0,
            assignedQuantity: 0,
            availableQuantity: 0,
            warehouseStockQuantity: 0,
            warehouseOrderedQuantity: 0,
            warehouseAssignedQuantity: 0,
            warehouseAvailableQuantity: 0,
            total: 0,
            partnerId: customer._id,
            invoiceAddressId,
            invoiceAddress,
            deliveryAddressId,
            deliveryAddress
        };
        if (Array.isArray(product.nameTranslations) && product.nameTranslations.length > 0) {
            const nt = product.nameTranslations.find(nt => nt.languageId === customer.languageId);

            if (!!nt && !!nt.translation) {
                item.description = `${product.code} - ${nt.translation}`;
            }
        }

        if (item.unitId !== item.baseUnitId) {
            const unitRatio = (product.unitRatios ?? {})[item.unitId] ?? 1;

            item.baseQuantity = item.quantity * unitRatio;
        }

        // Vendor product.
        const pcItem = await app.collection('purchase.procurement-catalog').findOne({
            productId: product._id
        });
        if (!!pcItem) {
            item.vendorId = pcItem.vendorId;

            if (Array.isArray(pcItem.warehouses) && pcItem.warehouses.length > 0) {
                item.warehouseId = pcItem.warehouses[0].warehouseId;
            }
        }

        let taxRate = 0;
        if (Array.isArray(integrationOrderLine.tax_lines) && integrationOrderLine.tax_lines.length > 0) {
            let r = parseFloat(integrationOrderLine.tax_lines[0].rate);

            if (!isNaN(r)) {
                taxRate = r;
            }
        }

        const price = parseFloat(integrationOrderLine.price);
        if (!isNaN(price)) {
            item.unitPrice = app.round(price / (1 + taxRate), 'unit-price');

            if (item.quantity > 0) {
                const discountAmount = parseFloat(integrationOrderLine.total_discount);
                const rowTotal = price * item.quantity;

                if (discountAmount > 0 && rowTotal > 0) {
                    item.discount = (discountAmount / rowTotal) * 100;
                }
            }
        }

        // Stock quantity.
        const stockReport = await app.rpc('inventory.get-stock-report', {
            date: item.scheduledDate || app.datetime.local().toJSDate(),
            productId: item.productId
        });
        if (Array.isArray(stockReport) && stockReport.length > 0) {
            const r = stockReport[0];

            item.stockQuantity = r.stockQuantity;
            item.orderedQuantity = r.orderedQuantity;
            item.assignedQuantity = r.assignedQuantity;
            item.availableQuantity = r.availableQuantity;
        }
        const warehouseReport = await app.rpc('inventory.get-stock-report', {
            date: item.scheduledDate || app.datetime.local().toJSDate(),
            productId: item.productId,
            warehouseId: item.warehouseId
        });
        if (Array.isArray(warehouseReport) && warehouseReport.length > 0) {
            const r = warehouseReport[0];

            item.warehouseStockQuantity = r.stockQuantity;
            item.warehouseOrderedQuantity = r.orderedQuantity;
            item.warehouseAssignedQuantity = r.assignedQuantity;
            item.warehouseAvailableQuantity = r.availableQuantity;
        }

        item = await app.rpc(
            'sale.calculate-order-row-totals',
            {
                row: item,
                field: 'unitPrice',
                model: order
            },
            {user}
        );

        // ** Must be fixed in all integrations.
        if (!(taxRate > 0)) {
            const zeroTax = await app.collection('kernel.taxes').findOne({
                scope: 'sale',
                amount: 0,
                $select: ['_id']
            });

            if (!!zeroTax) {
                item = await app.rpc(
                    'sale.calculate-order-row-totals',
                    {
                        row: {
                            ...item,
                            taxId: zeroTax._id
                        },
                        field: 'taxId',
                        model: order
                    },
                    {user}
                );
            }
        }

        items.push(item);
    }

    if (
        _.isPlainObject(integrationOrder.total_shipping_price_set) &&
        _.isPlainObject(integrationOrder.total_shipping_price_set.shop_money) &&
        typeof integrationOrder.total_shipping_price_set.shop_money.amount === 'string'
    ) {
        const totalShippingPrice = parseFloat(integrationOrder.total_shipping_price_set.shop_money.amount);

        if (!isNaN(totalShippingPrice) && totalShippingPrice > 0) {
            try {
                const deliveryItem = await createDeliveryItem({
                    app,
                    log,
                    store,
                    now,
                    user,
                    integrationOrder,
                    customer,
                    invoiceAddressId,
                    invoiceAddress,
                    deliveryAddressId,
                    deliveryAddress,
                    order,
                    totalShippingPrice,
                    discount
                });

                items.push(deliveryItem);
            } catch (error) {
                log({
                    level: 'error',
                    message: error.message
                });
            }
        }
    }

    return items;
}

async function createDeliveryItem({
    app,
    store,
    now,
    user,
    integrationOrder,
    customer,
    invoiceAddressId,
    invoiceAddress,
    deliveryAddressId,
    deliveryAddress,
    order,
    totalShippingPrice,
    discount
}) {
    if (!store.defaultDeliveryOptionId) {
        throw new Error('No default delivery option found!');
    }
    const deliveryOption = store.deliveryOptions.find(o => o.id === store.defaultDeliveryOptionId);
    if (!deliveryOption) {
        throw new Error('No default delivery option found!');
    }

    const product = await app.collection('inventory.products').findOne({
        _id: deliveryOption.serviceProductId,
        $select: [
            '_id',
            'code',
            'definition',
            'type',
            'displayName',
            'barcode',
            'baseUnitId',
            'nameTranslations',
            'salesNote'
        ],
        $disableSoftDelete: true,
        $disableActiveCheck: true
    });
    if (!product) {
        throw new Error('Delivery option service product is not found!');
    }
    const tax = await app.collection('kernel.taxes').findOne({
        _id: deliveryOption.taxId,
        $select: ['_id', 'amount'],
        $disableSoftDelete: true,
        $disableActiveCheck: true
    });
    if (!tax) {
        throw new Error('Delivery option tax is not found!');
    }

    let unitPrice = totalShippingPrice / (1 + tax.amount / 100);
    if (discount > 0) {
        unitPrice += (unitPrice * discount) / 100;
    }
    unitPrice = app.round(unitPrice, 'unit-price');

    let item = {
        id: Random.id(16),
        productId: product._id,
        productCode: product.code,
        productDefinition: product.definition,
        productType: product.type,
        barcode: product.barcode || '',
        scheduledDate: order.scheduledDate,
        branchId: order.branchId,
        warehouseId: order.warehouseId,
        quantity: 1,
        unitId: product.baseUnitId,
        baseUnitId: product.baseUnitId,
        baseQuantity: 1,
        unitPrice,
        grossUnitPrice: 0,
        discount: 0,
        unitPriceAfterDiscount: 0,
        grossUnitPriceAfterDiscount: 0,
        taxId: deliveryOption.taxId,
        taxTotal: 0,
        grossTotal: 0,
        stockQuantity: 0,
        orderedQuantity: 0,
        assignedQuantity: 0,
        availableQuantity: 0,
        warehouseStockQuantity: 0,
        warehouseOrderedQuantity: 0,
        warehouseAssignedQuantity: 0,
        warehouseAvailableQuantity: 0,
        total: 0,
        partnerId: customer._id,
        invoiceAddressId,
        invoiceAddress,
        deliveryAddressId,
        deliveryAddress
    };

    item = await app.rpc(
        'sale.calculate-order-row-totals',
        {
            row: item,
            field: 'unitPrice',
            model: order
        },
        {user}
    );

    return item;
}

export async function getPaymentPlan({app, company, store, integrationOrder, now, user, order, paymentMethod}) {
    const currencyPrecision = app.setting('system.currencyPrecision');
    const percentagePrecision = app.setting('system.percentagePrecision');
    const round = app.roundNumber;

    let paymentTerm = null;
    if (!!paymentMethod.paymentTermId) {
        paymentTerm = await app.collection('finance.payment-terms').findOne({
            _id: paymentMethod.paymentTermId
        });
    } else {
        paymentTerm = await app.collection('finance.payment-terms').findOne({
            system: true
        });

        return {paymentTerm, paymentPlan: {}};
    }

    if (paymentMethod.paymentType === 'open-account') {
        return {paymentTerm, paymentPlan: {}};
    }

    const report = {
        baseTotal: 0,
        difference: 0,
        differencePercentage: 0,
        total: 0,
        changing: 0,
        openAmount: 0,
        dueDate: app.datetime.local().startOf('day').toJSDate(),
        dueDayDifference: 0,
        avgDueDate: app.datetime.local().startOf('day').toJSDate(),
        avgDueDayDifference: 0
    };
    const sectionReport = {};

    function findBaseDate(section, forcedDate = null) {
        const sectionTerm = paymentTerm[section];
        let baseDate = now.startOf('day').toJSDate();

        if (_.isNull(forcedDate)) {
            if (paymentTerm.dueDateBase === 'issueDate') {
                baseDate = order.orderDate;
            } else if (paymentTerm.dueDateBase === 'recordDate') {
                baseDate = order.recordDate;
            }
        } else {
            baseDate = forcedDate;
        }
        baseDate = app.datetime.fromJSDate(baseDate).startOf('day').toJSDate();

        if (forcedDate) {
            if (_.isNumber(sectionTerm.startDateMonths) && sectionTerm.startDateMonths > 0) {
                baseDate = app.datetime.fromJSDate(baseDate).plus({months: sectionTerm.startDateMonths}).toJSDate();
            }

            if (sectionTerm.startDateMonthPosition === 'start') {
                const date = app.datetime.fromJSDate(baseDate).startOf('month');

                if (app.datetime.fromJSDate(baseDate) > date) {
                    baseDate = date.plus({months: 1}).toJSDate();
                } else {
                    baseDate = date.toJSDate();
                }
            } else if (sectionTerm.startDateMonthPosition === 'middle') {
                const date = app.datetime.fromJSDate(baseDate).startOf('month').plus({days: 15});

                if (app.datetime.fromJSDate(baseDate) > date) {
                    baseDate = date.endOf('month').toJSDate();
                } else {
                    baseDate = date.toJSDate();
                }
            } else if (sectionTerm.startDateMonthPosition === 'end') {
                baseDate = app.datetime.fromJSDate(baseDate).endOf('month').toJSDate();
            }

            if (_.isNumber(sectionTerm.startDateDays) && sectionTerm.startDateDays > 0) {
                baseDate = app.datetime.fromJSDate(baseDate).plus({days: sectionTerm.startDateDays}).toJSDate();
            }

            if (_.isNumber(sectionTerm.toleranceDays) && sectionTerm.toleranceDays > 0) {
                baseDate = app.datetime.fromJSDate(baseDate).minus({days: sectionTerm.toleranceDays}).toJSDate();
            }
        }

        return baseDate;
    }

    async function initItem(item, itemExtra = {}) {
        if (!_.isString(item._id)) item._id = Random.id(8);
        item.currencyId = order.currencyId;
        item.currencyRate = order.currencyRate;
        item.isFixed = false;
        if (!_.isInteger(item.no)) item.no = 1;
        if (!_.isNumber(item.baseTotal)) item.baseTotal = 0;
        if (!_.isNumber(item.total)) item.total = 0;
        item.branchId = order.branchId;
        item.reference = order.code;
        item.description = app.translate('Receipts');
        item.issueDate = order.orderDate;
        item.recordDate = order.recordDate;
        item.countryId = company.address.countryId;
        if (!item.scope) item.scope = '1';

        if (item.type === 'pos') {
            const journal = await app.collection('accounting.journals').findOne({
                _id: paymentMethod.journalId,
                $disableBranchCheck: true
            });
            const pos = await app.collection('accounting.pos').findOne({
                journalId: journal._id,
                $disableBranchCheck: true
            });
            const bankAccount = await app.collection('accounting.bank-accounts').findOne({
                _id: pos.bankAccountId,
                $disableBranchCheck: true
            });
            const paymentJournal = await app.collection('accounting.journals').findOne({
                _id: bankAccount.journalId,
                $disableBranchCheck: true,
                $select: ['_id']
            });
            const baseDate = findBaseDate();
            const now = app.datetime.fromJSDate(baseDate).startOf('day');

            const extra = {};
            extra.installmentCount = item.installmentCount;
            extra.plusInstallmentCount = item.plusInstallmentCount || 0;
            extra.installmentAmount = 0;
            extra.dueDifference = 0;
            extra.total = 0;
            extra.documentNo = itemExtra.documentNo || null;
            extra.partnerCreditCardId = itemExtra.partnerCreditCardId || null;
            // extra.cardBrand = itemExtra.cardBrand || null;
            // extra.cardHolder = itemExtra.cardHolder || null;
            // extra.cardNumber = itemExtra.cardNumber || null;
            // extra.expireMonth = itemExtra.expireMonth || null;
            // extra.expireYear = itemExtra.expireYear || null;

            // if (
            //     !!pos &&
            //     pos.isVirtual &&
            //     Array.isArray(pos.commissions) &&
            //     pos.commissions.length > 0 &&
            //     !pos.commissions.some(c => !c.cardBrand)
            // ) {
            //     if (!model.selectedCardBrand) {
            //         this.model.selectedCardBrand = model.selectedCardBrand = this.pos.commissions[0].cardBrand;
            //     }

            //     this.pos.commissions = this.pos.commissions.filter(c => c.cardBrand === model.selectedCardBrand);
            // }

            const installment = pos.commissions.find(c => c.installment === extra.installmentCount);
            let installmentCount = extra.installmentCount;
            let installmentAmount = 0;
            let dueDifference = 0;
            let total = 0;

            if (pos.commissionType !== 'within' && !!installment) {
                dueDifference = round(
                    (order.grandTotal * (installment.commission || 0)) / 100 +
                        (order.grandTotal * (installment.serviceCommission || 0)) / 100,
                    currencyPrecision
                );
            }

            total = order.grandTotal + dueDifference;

            if (
                !!installment &&
                _.isNumber(installment.interestRate) &&
                installment.interestRate > 0 &&
                pos.commissionType !== 'within'
            ) {
                const dueDate = app.datetime
                    .fromJSDate(order.issueDate)
                    .startOf('day')
                    .plus({months: extra.installmentCount});
                let dueDateDaysDifference = dueDate.diff(now, 'days').toObject().days;

                if (pos.dueType === 'average-due-difference') {
                    let newDueDateDaysDifference = dueDate.plus({months: 1}).diff(now, 'days').toObject().days;

                    dueDateDaysDifference = round(newDueDateDaysDifference / 2);
                }

                const diff = round(
                    ((total * installment.interestRate) / 3000) * dueDateDaysDifference,
                    currencyPrecision
                );

                dueDifference += diff;
                total += diff;
            }

            installmentAmount = round(total / installmentCount, currencyPrecision);

            extra.installmentAmount = installmentAmount;
            extra.dueDifference = dueDifference;
            extra.total = total;

            item.posId = pos._id;
            item.branchId = journal.branchId;
            item.journalId = pos.journalId;
            item.paymentAccountId = paymentJournal._id;
            item.currencyId = pos.currencyId;
            item.baseTotal = item.baseTotal || extra.baseTotal || 0;
            item.total = extra.total;
            item.installmentCount = extra.installmentCount;
            item.plusInstallmentCount = extra.plusInstallmentCount;
            item.installmentAmount = extra.installmentAmount;
            item.dueDifference = extra.dueDifference;
            item.documentNo = extra.documentNo;
            item.partnerCreditCardId = extra.partnerCreditCardId;
            // item.cardBrand = extra.cardBrand;
            // item.cardHolder = extra.cardHolder;
            // item.cardNumber = extra.cardNumber;
            // item.expireMonth = extra.expireMonth;
            // item.expireYear = extra.expireYear;
            // item.cvv = extra.cvv;
            item.journalId = journal._id;
            item.journalName = journal.name;

            // Get due date.
            if (pos.paymentOnSpecificDate) {
                const cutoffDate = pos.cutoffDate;

                if (now.day > cutoffDate) {
                    item.dueDate = now.plus({months: 1}).set({day: cutoffDate}).toJSDate();
                } else {
                    item.dueDate = now.set({day: cutoffDate}).toJSDate();
                }

                item.dueDate = app.datetime
                    .fromJSDate(item.dueDate)
                    .plus({months: extra.installmentCount - 1})
                    .toJSDate();
            } else {
                if (pos.posRefund === 'lump-sum-payment') {
                    const lumpSumPayment = pos.lumpSumPayments.find(c => c.installment === extra.installmentCount);

                    if (!!lumpSumPayment) {
                        item.dueDate = now.plus({days: lumpSumPayment.refund || 0}).toJSDate();
                    }
                } else {
                    const installmentPayment = pos.installmentPayments.find(
                        c => c.installment === extra.installmentCount
                    );

                    if (!!installmentPayment) {
                        item.dueDate = now.plus({days: installmentPayment.refund || 0}).toJSDate();
                    }
                }
            }

            if (_.isDate(item.dueDate)) {
                const dueDate = app.datetime.fromJSDate(item.dueDate);
                let dueDayDifference = dueDate.diff(now, 'days').toObject().days;
                if (pos.dueType === 'average-due-difference') {
                    let newDueDaysDifference = dueDate.plus({months: 1}).diff(now, 'days').toObject().days;

                    dueDayDifference = round(newDueDaysDifference / 2);
                }
                item.dueDayDifference = dueDayDifference;

                item.dueDate = app.datetime.fromJSDate(item.dueDate).startOf('day').toJSDate();
            } else {
                item.dueDayDifference = 0;
                item.dueDate = now.toJSDate();
            }

            item.dueDifference = item.total - item.baseTotal;
            item.posTotal = item.total;
        } else {
            const journal = await app.collection('accounting.journals').findOne({
                _id: paymentMethod.journalId,
                $select: ['name']
            });

            item.journalId = journal._id;
            item.journalName = journal.name;
            item.documentNo = itemExtra.documentNo || null;
        }

        return item;
    }

    // let installmentCount = parseInt(orderPayload.installmentCount);
    let installmentCount = 1;
    if (!_.isNumber(installmentCount) || _.isNaN(installmentCount) || installmentCount === 0) {
        installmentCount = 1;
    }
    let installmentAmount = order.grandTotal / installmentCount;
    let items = [];

    let paymentType = 'cash';
    if (paymentMethod.paymentType === 'credit-card') {
        paymentType = 'pos';
    } else if (paymentMethod.paymentType === 'money-order' || paymentMethod.paymentType === 'cash-on-delivery') {
        paymentType = 'moneyTransfer';
    }

    if (paymentType === 'pos') {
        const installment = {};

        installment.type = 'pos';
        installment.index = items.length + 1;
        installment.isFixed = true;
        installment.baseTotal = order.grandTotal;
        installment.installmentCount = installmentCount;
        installment.installmentAmount = installmentAmount;
        items.push(await initItem(installment, {documentNo: order.code}));
    } else {
        for (let i = 1; i <= installmentCount; i++) {
            const sectionTerm = paymentTerm[paymentType];
            const baseDate = findBaseDate(paymentType, order.orderDate);
            const installmentPeriodMonths = sectionTerm.installmentPeriodMonths || 1;
            const installmentPeriodDays = sectionTerm.installmentPeriodDays || 0;
            const installment = {};

            let dueDate = app.datetime.fromJSDate(baseDate);
            dueDate = dueDate.plus({months: installmentPeriodMonths * (i - 1)});
            dueDate = dueDate.plus({days: installmentPeriodDays * (i - 1)});

            installment.type = paymentType;
            installment.index = i;
            installment.dueDate = dueDate.toJSDate();
            installment.baseTotal = round(installmentAmount, currencyPrecision);
            installment.total = round(installmentAmount, currencyPrecision);

            items.push(await initItem(installment, {documentNo: order.code}));
        }
    }

    const typeNoIncrementMap = {};
    items = items.map(item => {
        if (_.isNumber(typeNoIncrementMap[item.type])) {
            item.no = typeNoIncrementMap[item.type] + 1;
            typeNoIncrementMap[item.type]++;
        } else {
            item.no = 1;
            typeNoIncrementMap[item.type] = 1;
        }

        return item;
    });
    if (items.length > 0) {
        const total = round(_.sumBy(items, 'total'), currencyPrecision);
        const totalDiff = order.grandTotal - total;

        items[items.length - 1].total += totalDiff;
    }
    if (items.length > 0) {
        const total = round(_.sumBy(items, 'baseTotal'), currencyPrecision);
        const totalDiff = order.grandTotal - total;

        items[items.length - 1].baseTotal += totalDiff;
    }

    if (items.length > 0) {
        const baseDate = findBaseDate();
        const grouped = _.groupBy(items, 'type');
        const amount = round(_.sumBy(items, 'total'), currencyPrecision);

        for (const section of Object.keys(grouped)) {
            const items = grouped[section];

            if (section === 'pos') {
                const r = {
                    baseTotal: 0,
                    total: 0,
                    dueDate: now.toJSDate(),
                    dueDayDifference: 0,
                    avgDueDate: now.toJSDate(),
                    avgDueDayDifference: 0
                };

                const dueDateSortedItems = _.orderBy(items, ['dueDate'], ['desc']);
                if (dueDateSortedItems.length > 0) {
                    r.dueDate = dueDateSortedItems[0].dueDate;
                }
                r.dueDate = app.datetime.fromJSDate(r.dueDate).startOf('day').toJSDate();

                const dueDateStart = app.datetime.fromJSDate(baseDate);
                const dueDateEnd = app.datetime.fromJSDate(r.dueDate);
                r.dueDayDifference = Math.ceil(dueDateEnd.diff(dueDateStart, 'days').toObject().days);
                r.baseTotal = _.sumBy(items, item => round(item.baseTotal * item.currencyRate, currencyPrecision));
                r.difference = _.sumBy(items, item => round(item.dueDifference * item.currencyRate, currencyPrecision));
                r.total = _.sumBy(items, item => round(item.posTotal * item.currencyRate, currencyPrecision));
                r.changing = r.baseTotal > 0 ? (r.difference / r.baseTotal) * 100 : 0;

                // Avg. due date and Avg. due day difference.
                if (r.baseTotal > 0) {
                    let adat = 0;

                    items.forEach(item => {
                        adat += item.dueDayDifference * item.posTotal;
                    });

                    r.avgDueDayDifference = round(adat / r.total);
                    r.avgDueDate = app.datetime.fromJSDate(baseDate).plus({days: r.avgDueDayDifference}).toJSDate();
                }

                sectionReport['pos'] = r;
            } else {
                const vm = {
                    $app: app,
                    $datetime: app.datetime,
                    $setting: app.setting,
                    model: {
                        discount: 0
                    }
                };
                sectionReport[section] = calculateReport(vm, items, findBaseDate(section), paymentTerm[section]);
            }

            _.each(sectionReport, r => {
                report.baseTotal += round(r.baseTotal || 0, currencyPrecision);
                report.difference += round(r.difference || 0, currencyPrecision);
                report.total += round(r.total || 0, currencyPrecision);

                if (r.dueDate.getTime() > report.dueDate.getTime()) {
                    report.dueDate = r.dueDate;

                    const dueDateStart = app.datetime.fromJSDate(baseDate).startOf('day');
                    const dueDateEnd = app.datetime.fromJSDate(report.dueDate).startOf('day');
                    report.dueDayDifference = round(dueDateEnd.diff(dueDateStart, 'days').toObject().days);
                }
            });

            if (report.baseTotal > 0) {
                let adat = 0;

                _.each(sectionReport, prop => {
                    if (_.isNumber(prop.avgDueDayDifference)) {
                        adat += prop.baseTotal * prop.avgDueDayDifference;
                    }
                });

                report.avgDueDayDifference = round(adat / report.baseTotal);
                report.avgDueDate = app.datetime
                    .fromJSDate(baseDate)
                    .plus({days: report.avgDueDayDifference})
                    .toJSDate();
            }

            report.difference = round(report.difference, currencyPrecision);
            report.differencePercentage = _.isNumber(report.difference)
                ? round((report.difference / report.total) * 100, percentagePrecision)
                : 0;
            report.total = round(report.total, currencyPrecision);
            report.openAmount = round(amount - report.baseTotal, currencyPrecision);
            if (amount > 0) {
                report.changing = round((report.difference / amount) * 100, percentagePrecision);
            }
        }
    }

    const data = {};
    data.report = report;
    data.sectionReport = sectionReport;
    data.baseDate = findBaseDate();
    data.items = [];

    for (const row of items || []) {
        const item = {};

        item.documentType = row.type;
        item.id = row._id ? row._id : Random.id(16);
        item.no = row.no;
        item.documentNo = row.documentNo;
        item.currencyId = row.currencyId;
        item.currencyRate = row.currencyRate;
        item.dueDate = row.dueDate;
        item.total = row.total;
        item.amount = row.total;
        item.baseTotal = row.baseTotal;
        item.rounding = row.rounding || 0;
        item.journalId = row.journalId;
        item.isFixed = row.isFixed;
        item.branchId = row.branchId;
        item.scope = _.isString(row.scope) && row.scope ? row.scope : '1';
        item.guaranteeId = row.guaranteeId;
        item.description = row.description;
        item.reference = row.reference;
        item.recordDate = row.recordDate;
        item.issueDate = row.issueDate;
        item.issuedBy = row.issuedBy;
        item.status = row.status ? row.status : 'waiting';

        if (!item.transactionType) {
            item.transactionType = 'other';
        }

        if (row.type === 'pos') {
            item.posId = row.posId;
            item.paymentAccountId = row.paymentAccountId;
            item.amount = row.total - (row.dueDifference || 0);
            item.installmentCount = row.installmentCount;
            item.plusInstallmentCount = row.plusInstallmentCount;
            item.installmentAmount = row.installmentAmount;
            item.dueDifference = row.dueDifference;
            item.partnerCreditCardId = row.partnerCreditCardId;
            // item.cardBrand = row.cardBrand;
            // item.cardHolder = row.cardHolder;
            // item.cardNumber = row.cardNumber;
            // item.expireMonth = row.expireMonth;
            // item.expireYear = row.expireYear;
            // item.cvv = row.cvv;
            item.dueDayDifference = row.dueDayDifference;
            item.dueDifference = row.dueDifference;
            item.posTotal = row.posTotal;
        }

        data.items.push(item);
    }
    const journals = await app.collection('accounting.journals').find({
        _id: {$in: data.items.filter(i => !!i.journalId).map(i => i.journalId)},
        $select: ['name'],
        $disableBranchCheck: true,
        $disableActiveCheck: true,
        $disableSoftDelete: true
    });
    const branches = await app.collection('kernel.branches').find({
        _id: {$in: data.items.filter(i => !!i.branchId).map(i => i.branchId)},
        $select: ['name'],
        $disableActiveCheck: true,
        $disableSoftDelete: true
    });
    data.items = data.items.map(item => {
        if (item.journalId) {
            item.journalName = journals.find(j => j._id === item.journalId).name;
        }
        if (item.branchId) {
            item.branchName = branches.find(j => j._id === item.branchId).name;
        }

        return item;
    });

    // Find scope rate.
    let scope1Total = 0;
    let scope2Total = 0;
    let scopeTotal = 0;
    for (const item of data.items) {
        if (item.scope === '1') {
            scope1Total += item.total;
        } else {
            scope2Total += item.total;
        }
        scopeTotal += item.total;
    }
    data.scopeRate = scope1Total / scopeTotal;

    return {paymentTerm, paymentPlan: data};
}

async function getCustomer(app, company, user, store, integrationOrder) {
    if (!integrationOrder.customer) {
        throw new Error(`No customer found for the integration order ${integrationOrder.name}`);
    }
    if (!integrationOrder.shipping_address && !integrationOrder.customer.default_address) {
        throw new Error(`No shipping address found for the integration order ${integrationOrder.name}`);
    }
    if (!integrationOrder.billing_address && !integrationOrder.customer.default_address) {
        throw new Error(`No billing address found for the integration order ${integrationOrder.name}`);
    }

    const result = {
        customer: null,
        invoiceAddressId: null,
        invoiceAddress: null,
        deliveryAddressId: null,
        deliveryAddress: null,
        isNewCustomer: false
    };

    const integrationShippingAddress = _.isPlainObject(integrationOrder.shipping_address)
        ? integrationOrder.shipping_address
        : integrationOrder.customer.default_address;
    const integrationBillingAddress = _.isPlainObject(integrationOrder.billing_address)
        ? integrationOrder.billing_address
        : integrationOrder.customer.default_address;

    // Delivery address.
    const deliveryAddressResult = await normalizeAddress(app, integrationShippingAddress);
    const deliveryAddress = deliveryAddressResult.address;
    const deliveryAddressCountry = deliveryAddressResult.country;

    // Invoice address.
    const invoiceAddressResult = await normalizeAddress(app, integrationBillingAddress);
    const invoiceAddress = invoiceAddressResult.address;
    const invoiceAddressCountry = invoiceAddressResult.country;

    // Default address.
    let defaultAddress = null;
    let defaultAddressCountry = null;
    if (integrationOrder.customer.default_address) {
        const defaultAddressResult = await normalizeAddress(app, integrationShippingAddress);

        defaultAddress = defaultAddressResult.address;
        defaultAddressCountry = defaultAddressResult.country;
    } else {
        defaultAddress = invoiceAddress;
        defaultAddressCountry = invoiceAddressCountry;
    }

    const numbering = await app.collection('kernel.numbering').findOne(
        {
            code: 'partnerCustomerNumbering',
            $select: ['_id']
        },
        {
            disableInUseCheck: true,
            disableActiveCheck: true,
            disableSoftDelete: true
        }
    );
    let customer = {};

    // Get delivery contact.
    let deliveryAddressContact = {};
    deliveryAddressContact.type = 'delivery-address';
    deliveryAddressContact.name = trim(
        `${integrationShippingAddress.first_name || ''} ${integrationShippingAddress.last_name || ''}`
    );
    if (isArabic(deliveryAddressContact.name)) {
        deliveryAddressContact.name = arabicToEnName(deliveryAddressContact.name);
    }
    deliveryAddressContact.partnerId = '';
    deliveryAddressContact.partnerType = 'customer';
    if (!!integrationShippingAddress.phone) {
        const phoneCode = deliveryAddressCountry.phoneCode ?? '+90';
        let phone = integrationShippingAddress.phone.replace(phoneCode, '').trim();

        deliveryAddressContact.phone = phone;
        deliveryAddressContact.phoneCountryCode = phoneCode;
        deliveryAddressContact.phoneNumbers = [
            {
                type: 'work',
                phoneCode,
                countryCode: deliveryAddressCountry.code,
                number: phone
            }
        ];
    } else if (!!integrationOrder.customer.default_address && !!integrationOrder.customer.default_address.phone) {
        const phoneCode = defaultAddressCountry.phoneCode ?? '+90';
        let phone = integrationOrder.customer.default_address.phone.replace(phoneCode, '').trim();

        deliveryAddressContact.phone = phone;
        deliveryAddressContact.phoneCountryCode = phoneCode;
        deliveryAddressContact.phoneNumbers = [
            {
                type: 'work',
                phoneCode: phoneCode,
                countryCode: defaultAddressCountry.code,
                number: phone
            }
        ];
    } else if (Array.isArray(company.phoneNumbers) && company.phoneNumbers.length > 0) {
        deliveryAddressContact.phoneCountryCode = '+90';
        deliveryAddressContact.phone = company.phone.replace('+90', '').trim();
        deliveryAddressContact.phoneNumbers = company.phoneNumbers;
    }
    deliveryAddressContact.address = deliveryAddress;

    // Get invoice contact.
    let invoiceAddressContact = {};
    invoiceAddressContact.type = 'invoice-address';
    invoiceAddressContact.name = trim(
        `${integrationBillingAddress.first_name || ''} ${integrationBillingAddress.last_name || ''}`
    );
    if (isArabic(invoiceAddressContact.name)) {
        invoiceAddressContact.name = arabicToEnName(invoiceAddressContact.name);
    }
    invoiceAddressContact.partnerId = '';
    invoiceAddressContact.partnerType = 'customer';
    if (!!integrationBillingAddress.phone) {
        const phoneCode = invoiceAddressCountry.phoneCode ?? '+90';
        let phone = integrationBillingAddress.phone.replace(phoneCode, '').trim();

        invoiceAddressContact.phone = phone;
        invoiceAddressContact.phoneCountryCode = phoneCode;
        invoiceAddressContact.phoneNumbers = [
            {
                type: 'work',
                phoneCode,
                countryCode: invoiceAddressCountry.code,
                number: phone
            }
        ];
    } else if (!!integrationOrder.customer.default_address && !!integrationOrder.customer.default_address.phone) {
        const phoneCode = defaultAddressCountry.phoneCode ?? '+90';
        let phone = integrationOrder.customer.default_address.phone.replace(phoneCode, '').trim();

        invoiceAddressContact.phone = phone;
        invoiceAddressContact.phoneCountryCode = phoneCode;
        invoiceAddressContact.phoneNumbers = [
            {
                type: 'work',
                phoneCode,
                countryCode: defaultAddressCountry.code,
                number: phone
            }
        ];
    } else if (Array.isArray(company.phoneNumbers) && company.phoneNumbers.length > 0) {
        invoiceAddressContact.phoneCountryCode = '+90';
        invoiceAddressContact.phone = company.phone.replace('+90', '').trim();
        invoiceAddressContact.phoneNumbers = company.phoneNumbers;
    }
    invoiceAddressContact.address = invoiceAddress;
    invoiceAddressContact.identity = '**********1';
    invoiceAddressContact.tin = '**********';
    // invoiceAddressContact.companyName = trim(integrationOrder.invoiceAddress.company || '');
    // invoiceAddressContact.tin = trim(integrationOrder.invoiceAddress.taxNumber || '**********');
    // invoiceAddressContact.taxDepartment = trim(integrationOrder.invoiceAddress.taxOffice || '');
    // invoiceAddressContact.identity = trim(integrationOrder.tcIdentityNumber || '**********1');
    // if (typeof integrationOrder.identityNumber === 'string' && integrationOrder.identityNumber.trim().length > 0) {
    //     invoiceAddressContact.identity = integrationOrder.identityNumber.trim().slice(0, 11);
    // }
    // if (!!integrationOrder.commercial === true) {
    //     invoiceAddressContact.invoiceType = 'corporate';
    //     invoiceAddressContact.identity = '';
    //
    //     if (!invoiceAddressContact.companyName) {
    //         invoiceAddressContact.companyName = invoiceAddressContact.name;
    //         if (isArabic(invoiceAddressContact.companyName)) {
    //             invoiceAddressContact.companyName = arabicToEnName(invoiceAddressContact.companyName);
    //         }
    //     }
    // } else {
    //     invoiceAddressContact.invoiceType = 'individual';
    //     invoiceAddressContact.tin = '';
    // }

    // Phone numbers.
    let phoneNumbers = [];
    let phone = null;
    let phoneCountryCode = null;
    if (!!integrationOrder.customer.phone) {
        const phoneCode = defaultAddressCountry.phoneCode ?? '+90';
        phone = integrationOrder.customer.phone.replace(phoneCode, '').trim();

        phoneCountryCode = phoneCode;
        phoneNumbers = [
            {
                type: 'work',
                phoneCode,
                countryCode: defaultAddressCountry.code,
                number: phone
            }
        ];
    } else if (!!invoiceAddressContact.phone) {
        phone = invoiceAddressContact.phone;
        phoneCountryCode = invoiceAddressContact.phoneCountryCode;
        phoneNumbers = invoiceAddressContact.phoneNumbers;
    }

    // General.
    customer.integrationId = integrationOrder.customer.id.toString();
    customer.type = 'customer';
    customer.name = trim(`${integrationOrder.customer.first_name || ''} ${integrationOrder.customer.last_name || ''}`);
    if (isArabic(customer.name)) {
        customer.name = arabicToEnName(customer.name);
    }
    customer.firstName = trim(integrationOrder.customer.first_name || '');
    if (isArabic(customer.firstName)) {
        customer.firstName = arabicToEnName(customer.firstName);
    }
    customer.lastName = trim(integrationOrder.customer.last_name || '');
    if (isArabic(customer.lastName)) {
        customer.lastName = arabicToEnName(customer.lastName);
    }
    if (integrationOrder.customer.email) {
        customer.email = integrationOrder.customer.email;
    }
    if (integrationOrder.customer.note) {
        customer.note = integrationOrder.customer.note;
    }
    customer.groupId = store.customerGroupId;
    customer.countryId = invoiceAddress.countryId;
    customer.timezone = app.config('app.timezone');
    customer.currencyId = store.currencyId;
    customer.languageId = store.languageId;
    customer.branchIds = [store.branchId];
    customer.invoiceScenario = 'e-archive-invoice';
    customer.address = defaultAddress;
    customer.accountingAccountId = app.defaultAccountingAccount('domesticAccountsReceivableAccount', 'sale');
    customer.taxDepartment = invoiceAddressContact.taxOffice;
    if (invoiceAddressContact.invoiceType === 'corporate') {
        customer.isCompany = true;
        customer.legalName = invoiceAddressContact.companyName;
        if (isArabic(customer.legalName)) {
            customer.legalName = arabicToEnName(customer.legalName);
        }
        customer.invoiceScenario = 'commercial-invoice';
        customer.tin = '**********';
    } else {
        customer.isCompany = false;
        customer.legalName = customer.name;
        customer.invoiceScenario = 'e-archive-invoice';
        customer.identity = '**********1';
    }

    // E-Invoice type.
    const eInvoiceTypes = await app.collection('eops.e-invoice-types').find({
        scenarios: customer.invoiceScenario,
        $select: ['_id']
    });
    if (eInvoiceTypes.length > 0) {
        customer.eInvoiceTypeId = eInvoiceTypes[0]._id;
    }

    // Phone number.
    if (!!phone) {
        customer.phone = phone;
        customer.phoneCountryCode = phoneCountryCode;
        customer.phoneNumbers = phoneNumbers;
    }

    const existingCustomer = await app.collection('kernel.partners').findOne({
        integrationId: customer.integrationId,
        $select: ['_id']
    });
    if (!!existingCustomer) {
        customer = await app.collection('kernel.partners').patch({_id: existingCustomer._id}, customer);

        const existingInvoiceAddressContact = await app.collection('kernel.contacts').findOne({
            partnerId: customer._id,
            type: 'invoice-address',
            'address.address': invoiceAddressContact.address.address
        });
        if (!!existingInvoiceAddressContact) {
            invoiceAddressContact = await app
                .collection('kernel.contacts')
                .patch({_id: existingInvoiceAddressContact._id}, invoiceAddressContact, {user});
        } else {
            invoiceAddressContact.partnerId = customer._id;
            invoiceAddressContact = await app.collection('kernel.contacts').create(invoiceAddressContact, {user});
        }

        const existingDeliveryAddressContact = await app.collection('kernel.contacts').findOne({
            partnerId: customer._id,
            type: 'delivery-address',
            'address.address': deliveryAddressContact.address.address
        });
        if (!!existingDeliveryAddressContact) {
            deliveryAddressContact = await app
                .collection('kernel.contacts')
                .patch({_id: existingDeliveryAddressContact._id}, deliveryAddressContact, {user});
        } else {
            deliveryAddressContact.partnerId = customer._id;
            deliveryAddressContact = await app.collection('kernel.contacts').create(deliveryAddressContact, {user});
        }
    } else {
        customer.code = await app.rpc('kernel.common.request-number', {
            numberingId: numbering._id,
            save: true
        });

        // Default limit definition.
        const pld = await app.collection('finance.partner-limit-definitions').findOne({
            partnerType: 'customer',
            $or: [{partnerGroupId: {$exists: false}}, {partnerGroupId: {$eq: null}}, {partnerGroupId: customer.groupId}]
        });
        if (_.isPlainObject(pld)) {
            customer.enableLimitChecks = pld.enableLimitChecks;
            customer.limitControlDocument = pld.limitControlDocument;
            customer.limitOrderControl = pld.limitOrderControl;
            customer.limitInvoiceControl = pld.limitInvoiceControl;
            customer.openAccountLimit = pld.openAccountLimit;
            customer.totalLimit = pld.openAccountLimit;
        }

        if ((await app.collection('kernel.partners').count({code: customer.code})) > 0) {
            customer.code = microtime.now();
        }

        // Create customer.
        customer = await app.collection('kernel.partners').create(customer, {user});

        invoiceAddressContact.partnerId = customer._id;
        invoiceAddressContact = await app.collection('kernel.contacts').create(invoiceAddressContact, {user});

        deliveryAddressContact.partnerId = customer._id;
        deliveryAddressContact = await app.collection('kernel.contacts').create(deliveryAddressContact, {user});

        result.isNewCustomer = true;
    }

    // Assign values.
    result.customer = customer;
    result.invoiceAddressId = invoiceAddressContact._id;
    result.invoiceAddress = invoiceAddressContact.address;
    result.deliveryAddressId = deliveryAddressContact._id;
    result.deliveryAddress = deliveryAddressContact.address;

    return result;
}

async function normalizeAddress(app, integrationAddress) {
    if (!_.isPlainObject(integrationAddress)) {
        return null;
    }

    const country = await app.collection('kernel.countries').findOne({
        code: toUpper(integrationAddress.country_code),
        $select: ['_id', 'name', 'addressFormat'],
        $disableSoftDelete: true,
        $disableActiveCheck: true
    });
    const normalized = {};

    let addressFormat = country.addressFormat;
    if (!addressFormat) {
        addressFormat = `{{street}}\n{{subDistrict}} {{district}} {{city}} {{postalCode}}\n{{country}}`;
    }

    normalized.countryId = trim(country._id);
    normalized.city = trim(integrationAddress.city || '');
    // normalized.district = trim(integrationAddress.province || '');
    normalized.district = '';
    normalized.subDistrict = '';
    normalized.street = `${trim(integrationAddress.address1 || '')} ${trim(integrationAddress.address2 || '')}`.trim();
    normalized.postalCode = trim((integrationAddress.zip || '').toString());
    normalized.apartmentNumber = '';
    normalized.doorNumber = '';
    normalized.address = '';

    if (!!normalized.postalCode && !normalized.district) {
        const location = await app.collection('kernel.locations').findOne({
            postalCode: normalized.postalCode
        });

        if (!!location) {
            if (location.district) {
                normalized.district = location.district;
            }

            if (location.subDistrict) {
                normalized.subDistrict = location.subDistrict;

                normalized.street = normalized.street.replaceAll(`${normalized.subDistrict} Mh.`, '');
                normalized.street = normalized.street.replaceAll(`${normalized.subDistrict} Mah.`, '');
                normalized.street = normalized.street.replaceAll(`${normalized.subDistrict} Mh`, '');
                normalized.street = normalized.street.replaceAll(`${normalized.subDistrict} Mah`, '');
                normalized.street = normalized.street.replaceAll(`${normalized.subDistrict} Mahallesi`, '');
                normalized.street = normalized.street.replaceAll(normalized.district, '');
            }
        }
    }

    let formatted = '';
    formatted = template(addressFormat, {
        subDistrict: normalized.subDistrict,
        street: normalized.street,
        apartmentNumber: normalized.apartmentNumber,
        doorNumber: normalized.doorNumber,
        postalCode: normalized.postalCode,
        district: normalized.district,
        city: normalized.city,
        country: country.name
    });
    formatted = formatted.trim();
    formatted = formatted.replace(' /', ' ');
    formatted = formatted.replace('/ ', ' ');
    formatted = formatted.replace('/,', ',');
    formatted = formatted.replace(',/', ',');
    formatted = formatted.replace('No: ,', ',');
    formatted = formatted
        .split(' ')
        .filter(part => !_.isEmpty(part.trim()) && part.trim() !== '/')
        .join(' ');
    formatted = formatted
        .split(',')
        .filter(part => !_.isEmpty(part.trim()) && part.trim() !== '/')
        .join(',');
    formatted = formatted.trim();
    if (formatted[0] === '/') {
        formatted = formatted.slice(1);
    }
    normalized.address = formatted;

    return {address: normalized, country};
}
