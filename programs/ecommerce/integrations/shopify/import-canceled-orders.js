import _ from 'lodash';
import {initShopifyClient} from './utils';
import {sleep} from 'framework/helpers';

export default async function (app, store, onProgress, startDate, endDate) {
    const log = payload => {
        payload.collection = 'ecommerce.stores';
        payload.documentId = store._id;
        payload.documentIdentifier = store.code;
        payload.method = 'update';
        payload.collectionName = `${app.translate('E-Commerce')} / ${app.translate('Stores')}`;

        return app.log(payload);
    };

    try {
        const integrationParams = JSON.parse(store.integrationParams);
        const {shopName, accessToken} = integrationParams;
        const now = app.datetime.local().toJSDate();
        const client = initShopifyClient(shopName, accessToken);
        const user = await app.collection('kernel.users').findOne({isRoot: true});

        let start = app.datetime.local().minus({days: 5}).startOf('day').toISO();
        let end = app.datetime.local().endOf('day').toISO();
        if (_.isDate(startDate) && _.isDate(endDate)) {
            start = app.datetime.fromJSDate(startDate).startOf('day').toISO();
            end = app.datetime.fromJSDate(endDate).endOf('day').toISO();
        }

        let params = {
            fields: 'id, order_number',
            limit: 10,
            status: 'cancelled',
            updated_at_min: start,
            updated_at_max: end
        };
        do {
            await sleep(1000);

            const orders = await client.order.list(params);

            for (const integrationOrder of orders) {
                const code = `${store.code}/${integrationOrder.order_number}`;

                try {
                    const order = await app.collection('sale.orders').findOne({
                        integrationId: integrationOrder.id.toString(),
                        status: {$ne: 'canceled'},
                        $select: [
                            '_id',
                            'code',
                            'transferIds',
                            'integrationId',
                            'financialEntryIds',
                            'relatedDocuments'
                        ],
                        $disableActiveCheck: true,
                        $disableSoftDelete: true
                    });
                    if (!order) continue;

                    if (Array.isArray(order.relatedDocuments) && order.relatedDocuments.length > 0) {
                        const rd = order.relatedDocuments.find(rd => rd.collection === 'logistics.shipping-orders');

                        if (rd && Array.isArray(rd.ids) && rd.ids.length > 0) {
                            try {
                                await app.rpc('logistics.shipping-orders-cancel', rd.ids[0]);
                            } catch (error) {
                                console.log(
                                    'Marketplace shipping order cancellation error in [import-canceled-orders] ->',
                                    error.message
                                );
                            }
                        }
                    }

                    const invoice = await app.collection('accounting.customer-invoices').findOne({
                        reference: order.code,
                        $select: ['_id']
                    });
                    if (invoice) {
                        const eArchiveInvoice = await app.collection('eops.e-archive-invoices').findOne({
                            invoiceId: invoice._id,
                            status: 'sent',
                            $select: ['_id']
                        });

                        if (eArchiveInvoice) {
                            await app.rpc('eops.e-archive-invoices-cancel', [eArchiveInvoice._id]);
                        }

                        await app.rpc('accounting.cancel-customer-invoice', invoice._id, {user});
                    }

                    if (order.transferIds.length > 0) {
                        const transfers = await app.collection('inventory.transfers').find({
                            _id: {$in: order.transferIds},
                            $select: ['_id'],
                            $disableActiveCheck: true,
                            $disableSoftDelete: true
                        });

                        for (const transfer of transfers) {
                            await app.rpc('inventory.cancel-transfer', transfer._id, {user});
                        }
                    }

                    if ((order.financialEntryIds ?? []).length > 0) {
                        await app.rpc('finance.cancel-entries', order.financialEntryIds);
                    }

                    await app.rpc('sale.cancel-orders', [order._id], {user});

                    await app.collection('sale.orders').bulkWrite([
                        {
                            updateOne: {
                                filter: {_id: order._id},
                                update: {
                                    $set: {
                                        code: `${order.code}-I`
                                    }
                                }
                            }
                        }
                    ]);
                } catch (error) {
                    log({
                        level: 'error',
                        message: `An error occurred while importing canceled integration order with the code ${code}! Error: ${error.message}`
                    });
                }
            }

            params = orders.nextPageParameters;
        } while (params !== undefined && params !== null);
    } catch (error) {
        log({
            level: 'error',
            message: `An error occurred while importing canceled integration orders! Error: ${error.message}`
        });
    }
}
