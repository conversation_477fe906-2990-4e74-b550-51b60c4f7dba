import _ from 'lodash';
import {n11ClientV2, validateIntegrationParams} from './utils';

export default async function (app, store, items) {
    const log = payload => {
        payload.collection = 'ecommerce.stores';
        payload.documentId = store._id;
        payload.documentIdentifier = store.code;
        payload.method = 'update';
        payload.level = 'error';
        payload.collectionName = `${app.translate('E-Commerce')} / ${app.translate('Stores')}`;
        return app.log(payload);
    };

    const {apiKey} = validateIntegrationParams(store.integrationParams);
    const client = await n11ClientV2(store.integrationParams);

    const payload = [];
    try {
        for (const item of items.filter(item => !item.isConfigurable)) {
            const storeProduct = item.storeProduct;

            // Round the prices to decimals for N11
            const p = {
                stockCode: storeProduct.productCode
            };

            if (typeof item.discountedSalesPrice === 'number') {
                p.salePrice = item.discountedSalesPrice;
                p.listPrice = storeProduct.salesPrice;
            } else {
                p.salePrice = item.salesPrice;
                p.listPrice = item.salesPrice;
            }

            if (_.isFinite(p.salePrice)) {
                p.salePrice = app.roundNumber(p.salePrice, 2);
            } else {
                continue;
            }
            if (_.isFinite(p.listPrice)) {
                p.listPrice = app.roundNumber(p.listPrice, 2);
            }

            if (storeProduct.onSale === false) {
                p.listPrice = 0;
                p.salePrice = 0;
            }

            payload.push(p);
        }
    } catch (error) {
        console.error(error.message);
    }

    for (const p of _.chunk(payload, 100)) {
        try {
            const result = (
                await client.post('/ms/product/tasks/price-stock-update', {
                    payload: {integrator: apiKey, skus: p}
                })
            ).data;

            if (result.status === 'IN_QUEUE') {
                const transaction = {};
                transaction.storeId = store._id;
                transaction.trackingId = result.id;
                transaction.status = 'processing';
                transaction.transactionType = 'price-update';
                transaction.date = app.datetime.local().toJSDate();

                await app.collection('ecommerce.store-transactions').create(transaction);
            } else if (result.status === 'REJECT' && Array.isArray(result.reasons)) {
                throw new Error(result.reasons.join(' '));
            } else {
                throw new Error('Unknown error occurred!');
            }
        } catch (error) {
            const messages = [];
            if (error.response && !!error.response.data && !!error.response.data.message) {
                messages.push(error.response.data.message);
            } else {
                messages.push(error.message);
            }

            log({level: 'error', message: messages.join(' ')});
        }
    }
}
