import _ from 'lodash';
import axios from 'axios';
import * as yup from 'yup';

const integrationParamsSchema = yup.object().shape({
    apiKey: yup.string().required(),
    apiSecret: yup.string().required(),
    shippingTemplate: yup.string().required()
});

export function validateIntegrationParams(integrationParams) {
    let parsedIntegrationParams;
    try {
        parsedIntegrationParams = JSON.parse(integrationParams);
    } catch (error) {
        throw new Error('Failed to parse integration params!');
    }
    return integrationParamsSchema.validateSync(parsedIntegrationParams);
}

export async function n11Client(integrationParams) {
    const {apiKey, apiSecret} = validateIntegrationParams(integrationParams);

    const n11Instance = axios.create({
        baseURL: 'https://api.n11.com/ws',
        headers: {'Content-Type': 'text/xml'}
    });

    n11Instance.interceptors.request.use(
        async function (config) {
            config.data = `
            <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:sch="http://www.n11.com/ws/schemas">
                <soapenv:Body>
                    <sch:${config.action}>
                        <auth>
                            <appKey>${apiKey}</appKey>
                            <appSecret>${apiSecret}</appSecret>
                        </auth>
                        ${config.data}
                    </sch:${config.action}>
                </soapenv:Body>
            </soapenv:Envelope>
            `;

            return config;
        },
        function (error) {
            return Promise.reject(error);
        }
    );

    return n11Instance;
}

export async function n11ClientV2(integrationParams) {
    const {apiKey, apiSecret} = validateIntegrationParams(integrationParams);

    return axios.create({
        baseURL: 'https://api.n11.com',
        headers: {
            'Content-Type': 'application/json',
            appKey: apiKey,
            appSecret: apiSecret
        }
    });
}
