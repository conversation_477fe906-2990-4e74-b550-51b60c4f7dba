import _ from 'lodash';
import Random from 'framework/random';
import {template, trim} from 'framework/helpers';
import {apiRequest} from './utils';
import microtime from 'microtime';

export default async function (app, store, onProgress, startDate, endDate) {
    const log = payload => {
        payload.collection = 'ecommerce.stores';
        payload.documentId = store._id;
        payload.documentIdentifier = store.code;
        payload.method = 'update';
        payload.level = 'error';
        payload.collectionName = `${app.translate('E-Commerce')} / ${app.translate('Stores')}`;
        return app.log(payload);
    };

    const user = await app.collection('kernel.users').findOne({isRoot: true});
    const numbering = await app.collection('kernel.numberings').findOne({
        code: 'CUSTOMER',
        $select: ['_id']
    });

    try {
        const fromDate = app.datetime
            .fromJSDate(startDate || app.datetime.local().minus({days: 1}).toJSDate())
            .startOf('day')
            .format('YYYY-MM-DD');

        const toDate = app.datetime
            .fromJSDate(endDate || app.datetime.local().toJSDate())
            .endOf('day')
            .format('YYYY-MM-DD');

        let page = 1;
        let hasMorePages = true;
        const pageSize = 50;

        while (hasMorePages) {
            try {
                const response = await apiRequest({
                    method: 'GET',
                    path: `api/Orders/GetSoldOrders?orderState=0&page=${page}&count=${pageSize}&startDate=${fromDate}&endDate=${toDate}`,
                    integrationParams: store.integrationParams
                });

                const orders = response?.result || [];

                if (orders.length === 0) {
                    hasMorePages = false;
                    continue;
                }

                for (const [index, integrationOrder] of orders.entries()) {
                    try {
                        if (onProgress) {
                            onProgress({
                                current: (page - 1) * pageSize + index + 1,
                                total: orders.length,
                                message: app.translate('Importing orders...')
                            });
                        }

                        const code = `${store.code}/${integrationOrder.orderReferenceNo}`;

                        if ((await app.collection('sale.orders').count({code})) > 0) {
                            continue;
                        }

                        const customerResult = await prepareCustomer(app, store, integrationOrder, numbering, user);

                        let status;
                        switch (integrationOrder.orderStateId) {
                            case 1:
                            case 512:
                            case 1024:
                                status = 'draft';
                                break;
                            case 2:
                                status = 'approved';
                                break;
                            case 4:
                                status = 'in-transfer';
                                break;
                            case 32:
                                status = 'completed';
                                break;
                            default:
                                status = 'draft';
                        }

                        const order = {
                            code,
                            status,
                            partnerId: customerResult.customer._id,
                            partnerCode: customerResult.customer.code,
                            partnerName: customerResult.customer.name,
                            orderDate: new Date(integrationOrder.orderDate),
                            currencyId: store.currencyId,
                            branchId: store.branchId,
                            warehouseId: store.warehouseId,
                            invoiceAddressId: customerResult.invoiceAddressId,
                            invoiceAddress: customerResult.invoiceAddress,
                            deliveryAddressId: customerResult.deliveryAddressId,
                            deliveryAddress: customerResult.deliveryAddress
                        };

                        order.items = await Promise.all(
                            (integrationOrder.orderDetails || []).map(async detail => {
                                const product = await app.collection('catalog.products').findOne({
                                    'integrationIds.farmazon': detail.orderDetailProductId.toString(),
                                    $select: ['_id', 'code', 'name', 'unitId']
                                });

                                if (!product) {
                                    throw new Error(
                                        `Product not found! Farmazon Product ID: ${detail.orderDetailProductId}`
                                    );
                                }

                                return {
                                    productId: product._id,
                                    productCode: product.code,
                                    productName: product.name,
                                    unitId: product.unitId,
                                    quantity: detail.orderDetailListingCount,
                                    price: detail.orderDetailPrice
                                };
                            })
                        );

                        await app.collection('sale.orders').create(order, {user});
                    } catch (error) {
                        log({
                            message:
                                app.translate('An error occurred while importing order {{orderNumber}}!', {
                                    orderNumber: integrationOrder.orderReferenceNo
                                }) + error.message
                        });
                    }
                }

                await new Promise(resolve => setTimeout(resolve, 6000));
                page++;
            } catch (error) {
                if (error.message?.includes('1015')) {
                    await new Promise(resolve => setTimeout(resolve, 15000));
                    continue;
                }
                throw error;
            }
        }
    } catch (error) {
        log({
            message: app.translate('Failed to import orders!') + error.message
        });
    }
}

async function prepareCustomer(app, store, integrationOrder, numbering, user) {
    const result = {
        customer: null,
        invoiceAddressId: null,
        invoiceAddress: null,
        deliveryAddressId: null,
        deliveryAddress: null,
        isNewCustomer: false
    };

    const deliveryAddress = await normalizeAddress(app, {
        city: integrationOrder['a:FaturaIli'],
        district: integrationOrder['a:FaturaIlce'],
        address1: integrationOrder['a:FaturaAdresi']
    });

    const invoiceAddress = await normalizeAddress(app, {
        city: integrationOrder['a:SiparisIli'],
        district: integrationOrder['a:SiparisIlce'],
        address1: integrationOrder['a:SiparisAdresi']
    });

    // Get delivery contact.
    let deliveryAddressContact = {};
    deliveryAddressContact.type = 'delivery-address';
    deliveryAddressContact.name = trim(
        `${integrationOrder['a:MusteriAdi'] || ''} ${integrationOrder['a:MusteriSoyadi'] || ''}`
    );
    deliveryAddressContact.partnerId = '';
    deliveryAddressContact.partnerType = 'customer';
    if (!!integrationOrder['a:TelefonNo']) {
        let phone = integrationOrder['a:TelefonNo'].replace(/\D/g, '');

        deliveryAddressContact.phone = phone;
        deliveryAddressContact.phoneCountryCode = '+90';
        deliveryAddressContact.phoneNumbers = [
            {
                type: 'work',
                phoneCode: '+90',
                countryCode: 'TR',
                number: phone
            }
        ];
    }
    deliveryAddressContact.address = deliveryAddress;

    // Get invoice contact.
    let invoiceAddressContact = {};
    invoiceAddressContact.type = 'invoice-address';
    invoiceAddressContact.name = trim(
        `${integrationOrder['a:FaturaMusteriAdi'] || ''} ${integrationOrder['a:FaturaMusteriSoyadi'] || ''}`
    );
    invoiceAddressContact.partnerId = '';
    invoiceAddressContact.partnerType = 'customer';
    if (!!integrationOrder['a:TelefonNo']) {
        let phone = integrationOrder['a:TelefonNo'].replace(/\D/g, '');

        invoiceAddressContact.phone = phone;
        invoiceAddressContact.phoneCountryCode = '+90';
        invoiceAddressContact.phoneNumbers = [
            {
                type: 'work',
                phoneCode: '+90',
                countryCode: 'TR',
                number: phone
            }
        ];
    }
    invoiceAddressContact.address = invoiceAddress;
    invoiceAddressContact.companyName = trim(integrationOrder['a:FirmaUnvani'] || '');
    invoiceAddressContact.tin = trim(integrationOrder['a:VergiNo'] || '**********1');
    invoiceAddressContact.taxDepartment = trim(integrationOrder['a:VergiDaire'] || '');
    invoiceAddressContact.identity = trim(integrationOrder['a:TCKN'] || '**********1');
    if (integrationOrder['a:FaturaTip'] === 'Kurumsal') {
        invoiceAddressContact.invoiceType = 'corporate';
        invoiceAddressContact.identity = '';
        if (!invoiceAddressContact.companyName) {
            invoiceAddressContact.companyName = invoiceAddressContact.name;
        }
    } else {
        invoiceAddressContact.invoiceType = 'individual';
        invoiceAddressContact.tin = '';
    }

    // Phone numbers.
    let phoneNumbers = [];
    let phone = null;
    let phoneCountryCode = null;
    if (!!invoiceAddressContact.phone) {
        phone = invoiceAddressContact.phone;
        phoneCountryCode = invoiceAddressContact.phoneCountryCode;
        phoneNumbers = invoiceAddressContact.phoneNumbers;
    }

    // General.
    customer.integrationId = integrationOrder['a:MusteriId'];
    customer.type = 'customer';
    customer.code = await app.rpc('kernel.common.request-number', {
        numberingId: numbering._id,
        save: true
    });
    customer.name = trim(`${integrationOrder['a:MusteriAdi'] || ''} ${integrationOrder['a:MusteriSoyadi'] || ''}`);
    customer.firstName = trim(integrationOrder['a:MusteriAdi'] || '');
    customer.lastName = trim(integrationOrder['a:MusteriSoyadi'] || '');
    customer.groupId = store.customerGroupId;
    customer.countryId = invoiceAddress.countryId;
    customer.timezone = app.config('app.timezone');
    customer.currencyId = store.currencyId;
    customer.languageId = store.languageId;
    customer.branchIds = [store.branchId];
    customer.invoiceScenario = 'e-archive-invoice';
    customer.address = invoiceAddress;
    customer.accountingAccountId = app.defaultAccountingAccount('domesticAccountsReceivableAccount', 'sale');
    customer.taxDepartment = invoiceAddressContact.taxOffice;
    if (invoiceAddressContact.invoiceType === 'corporate') {
        customer.isCompany = true;
        customer.legalName = invoiceAddressContact.companyName;
        customer.invoiceScenario = 'commercial-invoice';
        customer.tin = '**********';
    } else {
        customer.isCompany = false;
        customer.legalName = customer.name;
        customer.invoiceScenario = 'e-archive-invoice';
        customer.identity = '**********1';
    }

    // E-Invoice type.
    const eInvoiceTypes = await app.collection('eops.e-invoice-types').find({
        scenarios: customer.invoiceScenario,
        $select: ['_id']
    });
    if (eInvoiceTypes.length > 0) {
        customer.eInvoiceTypeId = eInvoiceTypes[0]._id;
    }

    // Phone number.
    if (!!phone) {
        customer.phone = phone;
        customer.phoneCountryCode = phoneCountryCode;
        customer.phoneNumbers = phoneNumbers;
    }

    // Default limit definition.
    const pld = await app.collection('finance.partner-limit-definitions').findOne({
        partnerType: 'customer',
        $or: [{partnerGroupId: {$exists: false}}, {partnerGroupId: {$eq: null}}, {partnerGroupId: customer.groupId}]
    });
    if (_.isPlainObject(pld)) {
        customer.enableLimitChecks = pld.enableLimitChecks;
        customer.limitControlDocument = pld.limitControlDocument;
        customer.limitOrderControl = pld.limitOrderControl;
        customer.limitInvoiceControl = pld.limitInvoiceControl;
        customer.openAccountLimit = pld.openAccountLimit;
        customer.totalLimit = pld.openAccountLimit;
    }

    if ((await app.collection('kernel.partners').count({code: customer.code})) > 0) {
        customer.code = microtime.now();
    }

    // Create customer.
    customer = await app.collection('kernel.partners').create(customer, {user});

    invoiceAddressContact.partnerId = customer._id;
    invoiceAddressContact = await app.collection('kernel.contacts').create(invoiceAddressContact, {user});

    deliveryAddressContact.partnerId = customer._id;
    deliveryAddressContact = await app.collection('kernel.contacts').create(deliveryAddressContact, {user});

    // Assign values.
    result.isNewCustomer = true;
    result.customer = customer;
    result.invoiceAddressId = invoiceAddressContact._id;
    result.invoiceAddress = invoiceAddressContact.address;
    result.deliveryAddressId = deliveryAddressContact._id;
    result.deliveryAddress = deliveryAddressContact.address;

    return result;
}

async function normalizeAddress(app, integrationAddress) {
    const country = await app.collection('kernel.countries').findOne({
        code: 'TR',
        $select: ['_id', 'name', 'addressFormat'],
        $disableSoftDelete: true,
        $disableActiveCheck: true
    });
    const normalized = {};

    let addressFormat = country.addressFormat;
    if (!addressFormat) {
        addressFormat = `{{street}}\n{{subDistrict}} {{district}} {{city}} {{postalCode}}\n{{country}}`;
    }

    normalized.countryId = trim(country._id);
    normalized.city = trim(integrationAddress.city || '');
    normalized.district = trim(integrationAddress.district || '');
    normalized.street = trim(integrationAddress.address1 || '');
    normalized.subDistrict = '';
    normalized.postalCode = '';
    normalized.apartmentNumber = '';
    normalized.doorNumber = '';
    normalized.address = '';

    let formatted = '';
    formatted = template(addressFormat, {
        subDistrict: normalized.subDistrict,
        street: normalized.street,
        apartmentNumber: normalized.apartmentNumber,
        doorNumber: normalized.doorNumber,
        postalCode: normalized.postalCode,
        district: normalized.district,
        city: normalized.city,
        country: country.name
    });
    formatted = formatted.trim();
    formatted = formatted.replace(' /', ' ');
    formatted = formatted.replace('/ ', ' ');
    formatted = formatted.replace('/,', ',');
    formatted = formatted.replace(',/', ',');
    formatted = formatted.replace('No: ,', ',');
    formatted = formatted
        .split(' ')
        .filter(part => !_.isEmpty(part.trim()) && part.trim() !== '/')
        .join(' ');
    formatted = formatted
        .split(',')
        .filter(part => !_.isEmpty(part.trim()) && part.trim() !== '/')
        .join(',');
    formatted = formatted.trim();
    if (formatted[0] === '/') {
        formatted = formatted.slice(1);
    }
    normalized.address = formatted;

    return normalized;
}
