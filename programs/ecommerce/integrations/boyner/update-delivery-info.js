import _ from 'lodash';
import {apiRequest} from './utils';

export default async function (app, store, payload) {
    let integrationParams = null;
    try {
        integrationParams = JSON.parse(store.integrationParams);
    } catch (error) {
        console.error(error);

        return;
    }
    const {url, supplierId, apiKey, apiSecret} = integrationParams;
    const {orderId, boxQuantity, volumetricWeight} = payload;
    const order = await app.collection('sale.orders').findOne({
        _id: orderId,
        $select: ['_id', 'integrationPayload']
    });

    await apiRequest({
        url,
        method: 'PUT',
        path: `sapigw/suppliers/${supplierId}/shipment-packages/${(order.integrationPayload ?? {}).packageId}/box-info`,
        supplierId,
        apiKey,
        apiSecret,
        data: {
            boxQuantity: parseInt(boxQuantity),
            deci: volumetricWeight
        }
    });
}
