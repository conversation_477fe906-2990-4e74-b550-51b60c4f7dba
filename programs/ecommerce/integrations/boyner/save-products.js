import {apiRequest} from './utils';
import _ from 'lodash';

export default async function (app, store, items) {
    let integrationParams = null;
    try {
        integrationParams = JSON.parse(store.integrationParams);
    } catch (error) {
        console.error(error);

        return;
    }
    const {url, supplierId, apiKey, apiSecret} = integrationParams;
    const log = payload => {
        payload.collection = 'ecommerce.stores';
        payload.documentId = store._id;
        payload.documentIdentifier = store.code;
        payload.method = 'update';
        payload.collectionName = `${app.translate('E-Commerce')} / ${app.translate('Stores')}`;

        return app.log(payload);
    };
    const createPayload = [];
    const updatePayload = [];
    const storeProductOperations = [];

    for (const item of items) {
        const product = item.product;
        const storeProduct = item.storeProduct;
        const configurableProduct = item.configurableProduct;
        const units = item.units;
        const tax = item.tax;
        const row = {};

        let brandId = product.brandId;
        if (!brandId) {
            log({
                level: 'error',
                message: app.translate(
                    'The {{productName}} ERP product could not be published. Product {{field}} is invalid!',
                    {
                        productName: product.definition,
                        field: app.translate('Brand')
                    }
                )
            });
            continue;
        }
        if (!brandId) {
            log({
                level: 'error',
                message: app.translate(
                    'The {{productName}} ERP product could not be published. Product {{field}} is invalid!',
                    {
                        productName: product.definition,
                        field: app.translate('Brand')
                    }
                )
            });
            continue;
        }

        let categoryId = product.categoryId;
        if (!categoryId) {
            log({
                level: 'error',
                message: app.translate(
                    'The {{productName}} ERP product could not be published. Product {{field}} is invalid!',
                    {
                        productName: product.definition,
                        field: app.translate('Category')
                    }
                )
            });
            continue;
        }
        categoryId = ((store.categoryMappings ?? []).find(m => m.erpCategoryId === categoryId) ?? {}).integrationCategoryId;
        if (!categoryId) {
            log({
                level: 'error',
                message: app.translate(
                    'The {{productName}} ERP product could not be published. Product {{field}} is invalid!',
                    {
                        productName: product.definition,
                        field: app.translate('Category')
                    }
                )
            });
            continue;
        }

        row.barcode = product.barcode;
        row.title = product.definition;
        row.productMainId = product.code;
        row.brandId = parseInt(brandId);
        row.categoryId = parseInt(categoryId);
        row.quantity = storeProduct.quantity || 0;
        row.stockCode = product.code;
        row.dimensionalWeight = 1;
        row.description = product.description || '';
        row.currencyType = 'TRY';
        row.listPrice = parseFloat(storeProduct.listPrice || 0);
        row.salePrice = parseFloat(storeProduct.salePrice || 0);
        row.vatRate = parseInt(tax?.rate || 18);
        row.cargoCompanyId = 10;
        row.deliveryDuration = 3;

        if (storeProduct.integrationId) {
            updatePayload.push(row);
        } else {
            createPayload.push(row);
        }
    }

    if (createPayload.length > 0) {
        try {
            const result = await apiRequest({
                method: 'post',
                url,
                path: `sapigw/products`,
                data: {items: createPayload},
                supplierId,
                apiKey,
                apiSecret
            });

            log({
                level: 'info',
                message: app.translate('{{count}} products have been created.', {count: createPayload.length})
            });
        } catch (error) {
            log({
                level: 'error',
                message: error.message
            });
        }
    }

    if (updatePayload.length > 0) {
        try {
            const result = await apiRequest({
                method: 'put',
                url,
                path: `sapigw/products`,
                data: {items: updatePayload},
                supplierId,
                apiKey,
                apiSecret
            });

            log({
                level: 'info',
                message: app.translate('{{count}} products have been updated.', {count: updatePayload.length})
            });
        } catch (error) {
            log({
                level: 'error',
                message: error.message
            });
        }
    }
}
