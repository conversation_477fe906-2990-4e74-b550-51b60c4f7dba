import _ from 'lodash';
import {apiRequest} from './utils';

export default async function (app, store, onProgress, startDate, endDate) {
    let integrationParams = null;
    try {
        integrationParams = JSON.parse(store.integrationParams);
    } catch (error) {
        console.error(error);
        return;
    }
    const {url, supplierId, apiKey, apiSecret} = integrationParams;

    const log = payload => {
        payload.collection = 'ecommerce.stores';
        payload.documentId = store._id;
        payload.documentIdentifier = store.code;
        payload.method = 'update';
        payload.collectionName = `${app.translate('E-Commerce')} / ${app.translate('Stores')}`;

        return app.log(payload);
    };

    let page = 0;
    let currentCount = 0;
    let totalCount = 0;

    await (async function iterator() {
        let start = app.datetime.local().minus({days: 30}).startOf('day').toJSDate().getTime();
        let end = app.datetime.local().endOf('day').toJSDate().getTime();

        if (_.isDate(startDate) && _.isDate(endDate)) {
            start = app.datetime.fromJSDate(startDate).startOf('day').toJSDate().getTime();
            end = app.datetime.fromJSDate(endDate).endOf('day').toJSDate().getTime();
        }

        const result = await apiRequest({
            url,
            path: `sapigw/suppliers/${supplierId}/v2/claims?startDate=${start}&endDate=${end}&page=${page}&size=50`,
            supplierId,
            apiKey,
            apiSecret
        });

        if (result && result.content && Array.isArray(result.content) && result.content.length > 0) {
            totalCount = result.totalElements || result.content.length;

            for (const claim of result.content) {
                try {
                    await processReturn(app, store, claim, log);
                    currentCount++;
                } catch (error) {
                    await log({
                        level: 'error',
                        message: `Failed to process return ${claim.id}: ${error.message}`,
                        data: {claim, error: error.message}
                    });
                }
            }

            if (onProgress && totalCount > 0) {
                onProgress(Math.min((currentCount / totalCount) * 100, 100));
            }

            page++;

            if (result.content.length === 50 && page < result.totalPages) {
                await iterator();
            } else if (onProgress) {
                onProgress(100);
            }
        } else if (onProgress) {
            onProgress(100);
        }
    })();
}

async function processReturn(app, store, boynerClaim, log) {
    // Check if return already exists
    const existingReturn = await app.collection('ecommerce.returns').findOne({
        storeId: store._id,
        integrationClaimId: boynerClaim.id
    });

    if (existingReturn) {
        // Update existing return
        await updateExistingReturn(app, existingReturn, boynerClaim, log);
        return;
    }

    // Create new return
    await createNewReturn(app, store, boynerClaim, log);
}

async function createNewReturn(app, store, boynerClaim, log) {
    const returnData = {
        storeId: store._id,
        integrationClaimId: boynerClaim.id,
        orderNumber: boynerClaim.orderNumber,
        orderDate: app.datetime.fromJSDate(new Date(boynerClaim.orderDate * 1000)),
        claimDate: app.datetime.fromJSDate(new Date(boynerClaim.claimDate)),
        customerName: boynerClaim.customerFullName || '',
        claimSource: boynerClaim.claimSource || '',
        status: 'pending',
        items: []
    };

    // Process claim items
    if (boynerClaim.items && Array.isArray(boynerClaim.items)) {
        for (const item of boynerClaim.items) {
            if (item.claimItems && Array.isArray(item.claimItems)) {
                for (const claimItem of item.claimItems) {
                    const orderLine = item.orderLine?.[0];
                    if (orderLine) {
                        returnData.items.push({
                            claimItemId: claimItem.id,
                            orderLineItemId: claimItem.orderLineItemId,
                            productName: orderLine.productName || '',
                            barcode: orderLine.barcode || '',
                            sku: orderLine.merchantSku || '',
                            quantity: claimItem.quantity || 1,
                            price: orderLine.price || 0,
                            amount: orderLine.amount || 0,
                            productColor: orderLine.productColor || '',
                            productSize: orderLine.productSize || '',
                            reason: claimItem.reason?.description || '',
                            customerNote: claimItem.customerNote || '',
                            status: mapClaimItemStatus(claimItem.claimItemStatus?.name),
                            resolved: claimItem.resolved || false
                        });
                    }
                }
            }
        }
    }

    await app.collection('ecommerce.returns').insertOne(returnData);

    await log({
        level: 'info',
        message: `New return created: ${boynerClaim.id}`,
        data: {claimId: boynerClaim.id, orderNumber: boynerClaim.orderNumber}
    });
}

async function updateExistingReturn(app, existingReturn, boynerClaim, log) {
    const updates = {
        status: mapClaimStatus(boynerClaim.status),
        updatedAt: app.datetime.local()
    };

    // Update item statuses
    if (boynerClaim.items && Array.isArray(boynerClaim.items)) {
        for (const item of boynerClaim.items) {
            if (item.claimItems && Array.isArray(item.claimItems)) {
                for (const claimItem of item.claimItems) {
                    const itemIndex = existingReturn.items.findIndex(
                        i => i.claimItemId === claimItem.id
                    );
                    if (itemIndex >= 0) {
                        updates[`items.${itemIndex}.status`] = mapClaimItemStatus(claimItem.claimItemStatus?.name);
                        updates[`items.${itemIndex}.resolved`] = claimItem.resolved || false;
                    }
                }
            }
        }
    }

    await app.collection('ecommerce.returns').updateOne(
        {_id: existingReturn._id},
        {$set: updates}
    );

    await log({
        level: 'info',
        message: `Return updated: ${boynerClaim.id}`,
        data: {claimId: boynerClaim.id, orderNumber: boynerClaim.orderNumber}
    });
}

function mapClaimStatus(status) {
    // Map Boyner claim status to internal status
    return 'pending'; // Default status
}

function mapClaimItemStatus(status) {
    const statusMap = {
        '1': 'created',
        'Created': 'created',
        'WaitingInAction': 'waiting',
        'InCargo': 'in_cargo',
        'Accepted': 'accepted',
        'Rejected': 'rejected',
        'Cancelled': 'cancelled',
        'Unresolved': 'unresolved',
        'InAnalysis': 'in_analysis'
    };
    
    return statusMap[status] || 'created';
}
