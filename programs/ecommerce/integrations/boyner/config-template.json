{"production": {"url": "https://merchantapi.boyner.com.tr", "supplierId": "YOUR_SUPPLIER_ID", "apiKey": "YOUR_API_KEY", "apiSecret": "YOUR_API_SECRET"}, "test": {"url": "https://merchantapi-test.boyner.com.tr", "supplierId": "TEST_SUPPLIER_ID", "apiKey": "TEST_API_KEY", "apiSecret": "TEST_API_SECRET"}, "carriers": [{"id": 42, "code": "DHLMP", "name": "DHL Marketplace"}, {"id": 38, "code": "SENDEOMP", "name": "Sendeo Marketplace"}, {"id": 36, "code": "NETMP", "name": "NetKargo Lojistik Marketplace"}, {"id": 34, "code": "MARSMP", "name": "Mars Lojistik Marketplace"}, {"id": 39, "code": "BIRGUNDEMP", "name": "Bir Günde Kargo Marketplace"}, {"id": 35, "code": "OCTOMP", "name": "Octovan Lojistik Marketplace"}, {"id": 30, "code": "BORMP", "name": "Borusan Lojistik Marketplace"}, {"id": 12, "code": "UPSMP", "name": "UPS Kargo Marketplace"}, {"id": 13, "code": "AGTMP", "name": "AGT Marketplace"}, {"id": 14, "code": "CAIMP", "name": "Cainiao Marketplace"}, {"id": 10, "code": "MNGMP", "name": "MNG Kargo Marketplace"}, {"id": 19, "code": "PTTMP", "name": "PTT Kargo Marketplace"}, {"id": 9, "code": "SURATMP", "name": "<PERSON><PERSON>rat <PERSON> Marketplace"}, {"id": 6, "code": "HOROZMP", "name": "Horoz Kargo Marketplace"}, {"id": 20, "code": "CEVAMP", "name": "CEVA Marketplace"}, {"id": 4, "code": "YKMP", "name": "Yurtiçi Kargo Marketplace"}, {"id": 7, "code": "ARASMP", "name": "Aras Kargo Marketplace"}], "orderStatuses": {"Created": "pending", "Picking": "processing", "Shipped": "shipped", "Delivered": "delivered", "Cancelled": "cancelled", "UnDelivered": "failed", "Repack": "processing", "Unpackaged": "pending"}, "returnStatuses": {"Created": "created", "WaitingInAction": "waiting", "InCargo": "in_cargo", "Accepted": "accepted", "Rejected": "rejected", "Cancelled": "cancelled", "Unresolved": "unresolved", "InAnalysis": "in_analysis"}, "vatRates": [0, 1, 8, 18], "deliveryDurations": [1, 2, 3], "deliveryOptions": {"1": "SAME_DAY_SHIPPING", "2": "FAST_DELIVERY", "3": "STANDARD_DELIVERY"}, "imageRequirements": {"format": "HTTPS URLs only", "dimensions": "1200x1800px", "dpi": 96, "maxCount": 8}, "rateLimits": {"productOperations": "100 requests per hour per merchant", "batchRecommended": true, "retryOnRateLimit": true, "maxRetries": 3}}