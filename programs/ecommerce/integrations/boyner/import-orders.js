import _ from 'lodash';
import {template, trim, isArabic, arabicToEnName, toUpper} from 'framework/helpers';
import Random from 'framework/random';
import {apiRequest} from './utils';
import ecommerceIntegrations from '../index';
import microtime from 'microtime';
import {updateProductsSalesCount} from '../utils';

export default async function (app, store, onProgress, startDate, endDate) {
    let integrationParams = null;
    try {
        integrationParams = JSON.parse(store.integrationParams);
    } catch (error) {
        console.error(error);

        return;
    }
    const {url, supplierId, apiKey, apiSecret} = integrationParams;
    const log = payload => {
        payload.collection = 'ecommerce.stores';
        payload.documentId = store._id;
        payload.documentIdentifier = store.code;
        payload.method = 'update';
        payload.collectionName = `${app.translate('E-Commerce')} / ${app.translate('Stores')}`;

        return app.log(payload);
    };
    const now = app.datetime.local();
    const user = await app.collection('kernel.users').findOne({isRoot: true});
    const company = await app.collection('kernel.company').findOne({});
    const integrationCarriers = await app.rpc('ecommerce.get-carriers', {storeId: store._id});
    let page = 0;
    let currentCount = 0;
    let totalCount = 0;

    await (async function iterator() {
        let start = app.datetime.local().minus({days: 5}).startOf('day').toJSDate().getTime();
        let end = app.datetime.local().endOf('day').toJSDate().getTime();

        if (_.isDate(startDate) && _.isDate(endDate)) {
            start = app.datetime.fromJSDate(startDate).startOf('day').toJSDate().getTime();
            end = app.datetime.fromJSDate(endDate).endOf('day').toJSDate().getTime();
        }

        const result = await apiRequest({
            url,
            path: `sapigw/orders?startDate=${start}&endDate=${end}&page=${page}&size=50&orderByField=PackageLastModifiedDate&orderByDirection=DESC`,
            supplierId,
            apiKey,
            apiSecret
        });

        if (result && result.content && Array.isArray(result.content) && result.content.length > 0) {
            totalCount = result.totalElements || result.content.length;
            
            for (const order of result.content) {
                try {
                    await processOrder(app, store, order, user, company, integrationCarriers, log);
                    currentCount++;
                } catch (error) {
                    await log({
                        level: 'error',
                        message: `Failed to process order ${order.orderNumber}: ${error.message}`,
                        data: {order, error: error.message}
                    });
                }
            }

            if (onProgress && totalCount > 0) {
                onProgress(Math.min((currentCount / totalCount) * 100, 100));
            }

            page++;
            
            if (result.content.length === 50 && page < result.totalPages) {
                await iterator();
            } else if (onProgress) {
                onProgress(100);
            }
        } else if (onProgress) {
            onProgress(100);
        }
    })();
}

async function processOrder(app, store, boynerOrder, user, company, integrationCarriers, log) {
    // Check if order already exists
    const existingOrder = await app.collection('ecommerce.orders').findOne({
        storeId: store._id,
        integrationOrderNumber: boynerOrder.orderNumber
    });

    if (existingOrder) {
        // Update existing order status if needed
        await updateExistingOrder(app, existingOrder, boynerOrder, log);
        return;
    }

    // Create new order
    await createNewOrder(app, store, boynerOrder, user, company, integrationCarriers, log);
}

async function createNewOrder(app, store, boynerOrder, user, company, integrationCarriers, log) {
    const orderData = {
        storeId: store._id,
        integrationOrderNumber: boynerOrder.orderNumber,
        integrationPackageNumber: boynerOrder.packageNumber,
        orderDate: app.datetime.fromJSDate(new Date(boynerOrder.orderDate * 1000)),
        status: mapOrderStatus(boynerOrder.shipmentPackageStatus),
        totalAmount: boynerOrder.totalPrice || 0,
        currency: boynerOrder.currencyCode || 'TRY',
        customer: {
            name: boynerOrder.customerFullName || '',
            email: boynerOrder.customerEmail || '',
            phone: boynerOrder.shipmentAddress?.phone || '',
            tcNumber: boynerOrder.tcIdentityNumber || ''
        },
        shippingAddress: mapAddress(boynerOrder.shipmentAddress),
        billingAddress: mapAddress(boynerOrder.invoiceAddress),
        items: boynerOrder.lines?.map(line => mapOrderItem(line)) || [],
        cargoInfo: {
            trackingNumber: boynerOrder.cargoTrackingNumber || '',
            trackingLink: boynerOrder.cargoTrackingLink || '',
            providerName: boynerOrder.cargoProviderName || '',
            senderNumber: boynerOrder.cargoSenderNumber || ''
        }
    };

    await app.collection('ecommerce.orders').insertOne(orderData);
    
    await log({
        level: 'info',
        message: `New order created: ${boynerOrder.orderNumber}`,
        data: {orderNumber: boynerOrder.orderNumber, packageNumber: boynerOrder.packageNumber}
    });
}

async function updateExistingOrder(app, existingOrder, boynerOrder, log) {
    const updates = {
        status: mapOrderStatus(boynerOrder.shipmentPackageStatus),
        'cargoInfo.trackingNumber': boynerOrder.cargoTrackingNumber || '',
        'cargoInfo.trackingLink': boynerOrder.cargoTrackingLink || '',
        'cargoInfo.providerName': boynerOrder.cargoProviderName || '',
        'cargoInfo.senderNumber': boynerOrder.cargoSenderNumber || ''
    };

    await app.collection('ecommerce.orders').updateOne(
        {_id: existingOrder._id},
        {$set: updates}
    );
}

function mapOrderStatus(boynerStatus) {
    const statusMap = {
        'Created': 'pending',
        'Picking': 'processing',
        'Shipped': 'shipped',
        'Delivered': 'delivered',
        'Cancelled': 'cancelled',
        'UnDelivered': 'failed',
        'Repack': 'processing',
        'Unpackaged': 'pending'
    };
    
    return statusMap[boynerStatus] || 'pending';
}

function mapAddress(address) {
    if (!address) return null;
    
    return {
        fullName: address.fullName || '',
        company: address.company || '',
        address1: address.address1 || '',
        address2: address.address2 || '',
        city: address.cityName || '',
        district: address.districtName || '',
        neighborhood: address.neighborhoodName || '',
        postalCode: address.postalCode || '',
        phone: address.phone || '',
        email: address.email || '',
        country: address.countryName || 'Türkiye'
    };
}

function mapOrderItem(line) {
    return {
        productName: line.productName || '',
        barcode: line.barcode || '',
        sku: line.sku || line.merchantSku || '',
        quantity: line.quantity || 1,
        price: line.price || 0,
        amount: line.amount || 0,
        discount: line.discount || 0,
        vatAmount: line.vatBaseAmount || 0,
        productColor: line.productColor || '',
        productSize: line.productSize || '',
        status: line.orderLineItemStatusName || 'Created'
    };
}
