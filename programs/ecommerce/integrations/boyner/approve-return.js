import {apiRequest} from './utils';

export default async function (app, store, claimId, claimItemIds) {
    let integrationParams = null;
    try {
        integrationParams = JSON.parse(store.integrationParams);
    } catch (error) {
        console.error(error);
        return;
    }
    const {url, supplierId, apiKey, apiSecret} = integrationParams;

    const log = payload => {
        payload.collection = 'ecommerce.stores';
        payload.documentId = store._id;
        payload.documentIdentifier = store.code;
        payload.method = 'update';
        payload.collectionName = `${app.translate('E-Commerce')} / ${app.translate('Stores')}`;

        return app.log(payload);
    };

    try {
        const result = await apiRequest({
            method: 'get',
            url,
            path: `sapigw/claims/${claimId}/items/approve`,
            data: {
                claimLineItemIdList: Array.isArray(claimItemIds) ? claimItemIds : [claimItemIds]
            },
            supplierId,
            apiKey,
            apiSecret
        });

        await log({
            level: 'info',
            message: `Return items approved successfully`,
            data: {claimId, claimItemIds, result}
        });

        // Update local return status
        await app.collection('ecommerce.returns').updateOne(
            {storeId: store._id, integrationClaimId: claimId},
            {
                $set: {
                    status: 'approved',
                    approvedAt: app.datetime.local()
                }
            }
        );

        return result;

    } catch (error) {
        await log({
            level: 'error',
            message: `Failed to approve return items: ${error.message}`,
            data: {claimId, claimItemIds, error: error.message}
        });
        throw error;
    }
}
