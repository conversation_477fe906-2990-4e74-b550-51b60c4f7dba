import {apiRequest} from './utils';

export default async function (app, store) {
    let integrationParams = null;
    try {
        integrationParams = JSON.parse(store.integrationParams);
    } catch (error) {
        console.error(error);
        return [];
    }
    const {url, supplierId, apiKey, apiSecret} = integrationParams;

    try {
        const result = await apiRequest({
            url,
            path: 'sapigw/suppliers/addresses',
            supplierId,
            apiKey,
            apiSecret
        });

        if (Array.isArray(result)) {
            return result.map(address => ({
                id: address.id,
                type: address.addressType,
                country: address.country,
                countryId: address.countryId,
                city: address.city,
                cityCode: address.cityCode,
                district: address.district,
                districtId: address.districtId,
                neighbourhood: address.neighbourhood,
                neighbourhoodId: address.neighbourhoodId,
                postCode: address.postCode,
                address: address.address,
                fullAddress: address.fullAddress,
                isDefault: address.default,
                isReturningAddress: address.returningAddress,
                isShipmentAddress: address.shipmentAddress,
                isInvoiceAddress: address.invoiceAddress
            }));
        }

        return [];
    } catch (error) {
        console.error('Error fetching addresses:', error);
        return [];
    }
}
