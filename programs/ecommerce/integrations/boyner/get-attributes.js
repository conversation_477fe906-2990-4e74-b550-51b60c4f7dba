import {apiRequest} from './utils';

export default async function (app, store, categoryId) {
    let integrationParams = null;
    try {
        integrationParams = JSON.parse(store.integrationParams);
    } catch (error) {
        console.error(error);

        return [];
    }
    const {url, supplierId, apiKey, apiSecret} = integrationParams;

    try {
        const result = await apiRequest({
            url,
            path: `sapigw/product-categories/${categoryId}/attributes`,
            supplierId,
            apiKey,
            apiSecret
        });

        if (result && result.categoryAttributes) {
            return result.categoryAttributes.map(attr => ({
                id: attr.attribute.id,
                name: attr.attribute.name,
                required: attr.required,
                allowCustom: attr.allowCustom,
                varianter: attr.varianter,
                slicer: attr.slicer,
                values: attr.attribute.attributeValues || []
            }));
        }

        return [];
    } catch (error) {
        console.error('Error fetching attributes for category:', categoryId, error);
        return [];
    }
}
