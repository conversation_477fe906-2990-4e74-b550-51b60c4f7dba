// Boyner carriers are predefined in the API documentation
// This function returns the static list of carriers

export default async function (app, store) {
    // Static carrier list from Boyner API documentation
    const carriers = [
        { id: 42, code: 'DHLMP', name: 'DHL Marketplace', taxNumber: '951-241-77-13' },
        { id: 38, code: 'SENDEOMP', name: 'Sendeo Marketplace', taxNumber: '2910804196' },
        { id: 36, code: 'NETMP', name: 'NetKargo Lojistik Marketplace', taxNumber: '6930094440' },
        { id: 34, code: 'MARSMP', name: 'Mars Lojistik Marketplace', taxNumber: '6120538808' },
        { id: 39, code: 'BIRGUNDEMP', name: 'Bir Günde Kargo Marketplace', taxNumber: '1770545653' },
        { id: 35, code: 'OCTOMP', name: 'Octovan Lojistik Marketplace', taxNumber: '6330506845' },
        { id: 30, code: '<PERSON><PERSON><PERSON>', name: '<PERSON><PERSON><PERSON> Marketplace', taxNumber: '1800038254' },
        { id: 12, code: 'UPSMP', name: 'UPS Kargo Marketplace', taxNumber: '9170014856' },
        { id: 13, code: 'AGTMP', name: 'AGT Marketplace', taxNumber: '6090414309' },
        { id: 14, code: 'CAIMP', name: 'Cainiao Marketplace', taxNumber: '0' },
        { id: 10, code: 'MNGMP', name: 'MNG Kargo Marketplace', taxNumber: '6080712084' },
        { id: 19, code: 'PTTMP', name: 'PTT Kargo Marketplace', taxNumber: '7320068060' },
        { id: 9, code: 'SURATMP', name: 'Sürat Kargo Marketplace', taxNumber: '7870233582' },
        { id: 6, code: 'HOROZMP', name: 'Horoz Kargo Marketplace', taxNumber: '4630097122' },
        { id: 20, code: 'CEVAMP', name: 'CEVA Marketplace', taxNumber: '8450298557' },
        { id: 4, code: 'YKMP', name: 'Yurtiçi Kargo Marketplace', taxNumber: '3130557669' },
        { id: 7, code: 'ARASMP', name: 'Aras Kargo Marketplace', taxNumber: '*********' }
    ];

    return carriers;
}
