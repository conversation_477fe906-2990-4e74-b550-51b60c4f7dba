import {apiRequest} from './utils';

export default async function (app, store, products, onProgress) {
    let integrationParams = null;
    try {
        integrationParams = JSON.parse(store.integrationParams);
    } catch (error) {
        console.error(error);
        return;
    }
    const {url, supplierId, apiKey, apiSecret} = integrationParams;

    const log = payload => {
        payload.collection = 'ecommerce.stores';
        payload.documentId = store._id;
        payload.documentIdentifier = store.code;
        payload.method = 'update';
        payload.collectionName = `${app.translate('E-Commerce')} / ${app.translate('Stores')}`;

        return app.log(payload);
    };

    let processedCount = 0;
    const totalCount = products.length;

    // Process products in batches
    const batchSize = 50;
    for (let i = 0; i < products.length; i += batchSize) {
        const batch = products.slice(i, i + batchSize);
        const items = [];

        for (const product of batch) {
            try {
                // Map product data to Boyner stock update format
                const stockUpdate = {
                    barcode: product.barcode,
                    quantity: parseInt(product.quantity || 0),
                    listPrice: parseFloat(product.listPrice || 0),
                    salePrice: parseFloat(product.salePrice || 0)
                };

                items.push(stockUpdate);
            } catch (error) {
                await log({
                    level: 'error',
                    message: `Stock update mapping error for ${product.barcode}: ${error.message}`,
                    data: {product, error: error.message}
                });
            }
        }

        if (items.length > 0) {
            try {
                const result = await apiRequest({
                    method: 'post',
                    url,
                    path: `sapigw/suppliers/${supplierId}/products/price-and-inventory`,
                    data: {items},
                    supplierId,
                    apiKey,
                    apiSecret
                });

                await log({
                    level: 'info',
                    message: `Stock update batch of ${items.length} products sent successfully`,
                    data: {batchRequestId: result.batchRequestId, success: result.success}
                });

                processedCount += items.length;
                if (onProgress) {
                    onProgress(Math.min((processedCount / totalCount) * 100, 100));
                }

            } catch (error) {
                await log({
                    level: 'error',
                    message: `Failed to send stock update batch: ${error.message}`,
                    data: {items, error: error.message}
                });
            }
        }

        // Add delay between batches to respect rate limits
        await new Promise(resolve => setTimeout(resolve, 1000));
    }

    if (onProgress) {
        onProgress(100);
    }
}
