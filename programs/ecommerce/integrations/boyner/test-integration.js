// Test file to verify Boyner integration structure
import boynerIntegration from './index';

console.log('Boyner Integration Methods:');
console.log(Object.keys(boynerIntegration));

// Expected methods:
const expectedMethods = [
    'downloadCategories',
    'downloadBrands', 
    'getAttributes',
    'getCarriers',
    'getAddresses',
    'saveProducts',
    'deleteProducts',
    'saveProductsStock',
    'saveProductsPrice',
    'importProducts',
    'syncProductStatuses',
    'importOrders',
    'importCanceledOrders',
    'updateOrderStatus',
    'sendInvoiceLink',
    'importReturns',
    'approveReturn',
    'rejectReturn',
    'checkBatchStatus'
];

console.log('\nMethod Verification:');
expectedMethods.forEach(method => {
    const exists = typeof boynerIntegration[method] === 'function';
    console.log(`${method}: ${exists ? '✓' : '✗'}`);
});

export default boynerIntegration;
