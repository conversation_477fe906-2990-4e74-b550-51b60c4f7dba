import axios from 'axios';
import {trim} from 'framework/helpers';

export function apiRequest(payload) {
    const {method = 'get', url, path, data, supplierId, apiKey, apiSecret, headers = {}, tryCount = 0} = payload;

    return new Promise(async (resolve, reject) => {
        try {
            const response = await axios({
                method,
                url: `${trim(url, '/')}/${path}`,
                headers: {
                    'Content-Type': 'application/json',
                    'User-Agent': `${supplierId} - SelfIntegration`,
                    ...headers
                },
                auth: {
                    username: api<PERSON>ey,
                    password: apiSecret
                },
                ...(!!data ? {data: JSON.stringify(data)} : {})
            });

            resolve(response.data);
        } catch (error) {
            if ((error.message ?? '').includes('429') && tryCount < 3) {
                await new Promise(r => setTimeout(r, 10 * 1000));

                await apiRequest({
                    ...payload,
                    tryCount: tryCount + 1
                });
            } else {
                reject(error);
            }
        }
    });
}
