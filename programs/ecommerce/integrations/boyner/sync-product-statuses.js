import {apiRequest} from './utils';

export default async function (app, store, onProgress) {
    let integrationParams = null;
    try {
        integrationParams = JSON.parse(store.integrationParams);
    } catch (error) {
        console.error(error);
        return;
    }
    const {url, supplierId, apiKey, apiSecret} = integrationParams;

    const log = payload => {
        payload.collection = 'ecommerce.stores';
        payload.documentId = store._id;
        payload.documentIdentifier = store.code;
        payload.method = 'update';
        payload.collectionName = `${app.translate('E-Commerce')} / ${app.translate('Stores')}`;

        return app.log(payload);
    };

    let page = 0;
    let currentCount = 0;
    let totalCount = 0;

    await (async function iterator() {
        const result = await apiRequest({
            url,
            path: `sapigw/suppliers/${supplierId}/products?page=${page}&size=50`,
            supplierId,
            apiKey,
            apiSecret
        });

        if (result && result.content && Array.isArray(result.content) && result.content.length > 0) {
            totalCount = result.totalElements || result.content.length;

            for (const product of result.content) {
                try {
                    await syncProductStatus(app, store, product, log);
                    currentCount++;
                } catch (error) {
                    await log({
                        level: 'error',
                        message: `Failed to sync product status ${product.barcode}: ${error.message}`,
                        data: {product, error: error.message}
                    });
                }
            }

            if (onProgress && totalCount > 0) {
                onProgress(Math.min((currentCount / totalCount) * 100, 100));
            }

            page++;

            if (result.content.length === 50 && page < result.totalPages) {
                await iterator();
            } else if (onProgress) {
                onProgress(100);
            }
        } else if (onProgress) {
            onProgress(100);
        }
    })();
}

async function syncProductStatus(app, store, boynerProduct, log) {
    // Find local product
    const localProduct = await app.collection('ecommerce.products').findOne({
        storeId: store._id,
        barcode: boynerProduct.barcode
    });

    if (localProduct) {
        // Update product status and other fields that might have changed
        const updates = {
            approved: boynerProduct.approved || false,
            archived: boynerProduct.archived || false,
            onSale: boynerProduct.onsale || false,
            locked: boynerProduct.locked || false,
            quantity: boynerProduct.quantity || 0,
            listPrice: boynerProduct.listPrice || 0,
            salePrice: boynerProduct.salePrice || 0,
            commission: boynerProduct.commission || 0,
            updatedAt: boynerProduct.lastUpdateDate ? 
                app.datetime.fromJSDate(new Date(boynerProduct.lastUpdateDate * 1000)) : 
                app.datetime.local()
        };

        await app.collection('ecommerce.products').updateOne(
            {_id: localProduct._id},
            {$set: updates}
        );

        await log({
            level: 'info',
            message: `Product status synced: ${boynerProduct.barcode}`,
            data: {
                barcode: boynerProduct.barcode,
                approved: boynerProduct.approved,
                onSale: boynerProduct.onsale,
                quantity: boynerProduct.quantity
            }
        });
    } else {
        await log({
            level: 'warning',
            message: `Product not found locally for status sync: ${boynerProduct.barcode}`,
            data: {barcode: boynerProduct.barcode}
        });
    }
}
