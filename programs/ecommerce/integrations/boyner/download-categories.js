import {apiRequest} from './utils';

export default async function (app, store, onProgress) {
    let integrationParams = null;
    try {
        integrationParams = JSON.parse(store.integrationParams);
    } catch (error) {
        console.error(error);

        return;
    }
    const {url, supplierId, apiKey, apiSecret} = integrationParams;

    await app.collection('ecommerce.categories').bulkWrite([{deleteMany: {filter: {storeId: store._id}}}], {
        ordered: false
    });

    try {
        const result = await apiRequest({
            url,
            path: `sapigw/product-categories`,
            supplierId,
            apiKey,
            apiSecret
        });

        const categories = Array.isArray(result) ? result : (result && result.content ? result.content : []);

        const operations = (function recursiveCategories(nodes, path = '') {
            const operations = [];
            if (!Array.isArray(nodes)) return operations;

            for (const node of nodes) {
                if (!node || !node.id || !node.name) continue;

                const newPath = path ? `${path}/${node.name}` : node.name;

                if (!node.subCategories || node.subCategories.length === 0) {
                    operations.push({
                        insertOne: {
                            document: {
                                storeId: store._id,
                                integrationId: node.id.toString(),
                                name: newPath,
                                path: newPath,
                                isActive: true,
                                parentPath: '',
                                tree: {
                                    depth: 0,
                                    hasChild: false,
                                    current: '',
                                    path: '',
                                    parent: ''
                                }
                            }
                        }
                    });
                } else if (Array.isArray(node.subCategories) && node.subCategories.length > 0) {
                    operations.push(...recursiveCategories(node.subCategories, newPath));
                }
            }
            return operations;
        })(categories);

        if (Array.isArray(operations) && operations.length > 0) {
            await app.collection('ecommerce.categories').bulkWrite(operations, {skipEvents: true});
        }
    } catch (error) {
        console.error('Error downloading categories:', error);
    }

    onProgress(100);
}
