import {apiRequest} from './utils';

export default async function (app, store, packageId, invoiceLink, invoiceNumber, invoiceDateTime) {
    let integrationParams = null;
    try {
        integrationParams = JSON.parse(store.integrationParams);
    } catch (error) {
        console.error(error);
        return;
    }
    const {url, supplierId, apiKey, apiSecret} = integrationParams;

    const log = payload => {
        payload.collection = 'ecommerce.stores';
        payload.documentId = store._id;
        payload.documentIdentifier = store.code;
        payload.method = 'update';
        payload.collectionName = `${app.translate('E-Commerce')} / ${app.translate('Stores')}`;

        return app.log(payload);
    };

    try {
        // Validate that the invoice link is a PDF
        if (!invoiceLink.toLowerCase().endsWith('.pdf')) {
            throw new Error('Invoice link must be a PDF file. HTML formats are not accepted by Boyner.');
        }

        const invoiceData = {
            invoiceLink: invoiceLink,
            companyId: 0, // Will be set by Boyner
            shipmentPackageId: parseInt(packageId),
            invoiceDateTime: invoiceDateTime || new Date().toISOString(),
            invoiceNumber: invoiceNumber || ''
        };

        const result = await apiRequest({
            method: 'post',
            url,
            path: `sapigw/suppliers/${supplierId}/supplier-invoice-links`,
            data: invoiceData,
            supplierId,
            apiKey,
            apiSecret
        });

        await log({
            level: 'info',
            message: `Invoice link sent successfully`,
            data: {packageId, invoiceLink, invoiceNumber, result}
        });

        return result;

    } catch (error) {
        await log({
            level: 'error',
            message: `Failed to send invoice link: ${error.message}`,
            data: {packageId, invoiceLink, invoiceNumber, error: error.message}
        });
        throw error;
    }
}
