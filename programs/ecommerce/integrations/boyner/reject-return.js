import {apiRequest} from './utils';

export default async function (app, store, claimId, claimItemIds, description, attachments = []) {
    let integrationParams = null;
    try {
        integrationParams = JSON.parse(store.integrationParams);
    } catch (error) {
        console.error(error);
        return;
    }
    const {url, supplierId, apiKey, apiSecret} = integrationParams;

    const log = payload => {
        payload.collection = 'ecommerce.stores';
        payload.documentId = store._id;
        payload.documentIdentifier = store.code;
        payload.method = 'update';
        payload.collectionName = `${app.translate('E-Commerce')} / ${app.translate('Stores')}`;

        return app.log(payload);
    };

    try {
        const requestData = {
            claimLineItemIdList: Array.isArray(claimItemIds) ? claimItemIds : [claimItemIds],
            description: description || 'Return rejected'
        };

        // Add attachments if provided
        if (attachments && attachments.length > 0) {
            requestData.item = attachments;
        }

        const result = await apiRequest({
            method: 'put',
            url,
            path: `sapigw/claims/${claimId}/issue`,
            data: requestData,
            supplierId,
            apiKey,
            apiSecret
        });

        await log({
            level: 'info',
            message: `Return items rejected successfully`,
            data: {claimId, claimItemIds, description, result}
        });

        // Update local return status
        await app.collection('ecommerce.returns').updateOne(
            {storeId: store._id, integrationClaimId: claimId},
            {
                $set: {
                    status: 'rejected',
                    rejectedAt: app.datetime.local(),
                    rejectionReason: description
                }
            }
        );

        return result;

    } catch (error) {
        await log({
            level: 'error',
            message: `Failed to reject return items: ${error.message}`,
            data: {claimId, claimItemIds, description, error: error.message}
        });
        throw error;
    }
}
