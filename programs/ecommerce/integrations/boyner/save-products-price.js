import {apiRequest} from './utils';

export default async function (app, store, items) {
    let integrationParams = null;
    try {
        integrationParams = JSON.parse(store.integrationParams);
    } catch (error) {
        console.error(error);

        return;
    }
    const {url, supplierId, apiKey, apiSecret} = integrationParams;
    const log = payload => {
        payload.collection = 'ecommerce.stores';
        payload.documentId = store._id;
        payload.documentIdentifier = store.code;
        payload.method = 'update';
        payload.collectionName = `${app.translate('E-Commerce')} / ${app.translate('Stores')}`;

        return app.log(payload);
    };
    const payload = [];

    for (const item of items.filter(item => !item.isConfigurable)) {
        const storeProduct = item.storeProduct;
        if (!storeProduct.productBarcode) {
            continue;
        }

        const p = {
            barcode: storeProduct.productBarcode
        };

        if (typeof item.discountedSalesPrice === 'number') {
            p.salePrice = item.discountedSalesPrice;
            p.listPrice = storeProduct.salesPrice;
        } else {
            p.salePrice = item.salesPrice;
            p.listPrice = item.salesPrice;
        }

        if (storeProduct.onSale === false) {
            p.quantity = 0;
            p.listPrice = 0;
            p.salePrice = 0;
        }

        payload.push(p);
    }

    if (payload.length > 0) {
        try {
            const result = await apiRequest({
                url,
                method: 'POST',
                path: `sapigw/products/price-and-inventory`,
                supplierId,
                apiKey,
                apiSecret,
                data: {
                    items: payload
                }
            });

            if (!!result.batchRequestId) {
                const transaction = {};
                transaction.storeId = store._id;
                transaction.trackingId = result.batchRequestId;
                transaction.status = 'processing';
                transaction.transactionType = 'price-update';
                transaction.date = app.datetime.local().toJSDate();
                await app.collection('ecommerce.store-transactions').create(transaction);
            }
        } catch (error) {
            const messages = [];
            if (error.response && !!error.response.data && Array.isArray(error.response.data.errors)) {
                for (const e of error.response.data.errors) {
                    messages.push(e.message);
                }
            } else {
                messages.push(error.message);
            }

            log({
                level: 'error',
                message: messages.join(' ')
            });
        }
    }
}
