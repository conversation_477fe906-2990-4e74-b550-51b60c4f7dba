# Boyner E-Commerce Integration

This integration provides comprehensive support for Boyner Marketplace API, enabling seamless product management, order processing, and return handling.

## Features

### Product Management
- **Download Categories**: Fetch and sync product categories from Boyner
- **Download Brands**: Import brand information
- **Get Attributes**: Retrieve category-specific product attributes
- **Save Products**: Create and update products on Boyner
- **Delete Products**: Remove products from Boyner
- **Stock & Price Updates**: Update product inventory and pricing
- **Import Products**: Sync products from Boyner to local system
- **Sync Product Statuses**: Keep product approval/status in sync

### Order Management
- **Import Orders**: Fetch new orders from Boyner
- **Import Canceled Orders**: Handle order cancellations
- **Update Order Status**: Update order status (Picking, Shipped, Delivered, etc.)
- **Send Invoice Link**: Submit invoice PDFs to Boyner

### Return Management
- **Import Returns**: Fetch return requests from customers
- **Approve Returns**: Accept return requests
- **Reject Returns**: Reject returns with reasons and attachments

### Utilities
- **Get Carriers**: Retrieve available shipping carriers
- **Get Addresses**: Fetch supplier addresses (shipping, billing, return)
- **Check Batch Status**: Monitor bulk operation progress

## Configuration

The integration requires the following parameters in the store's `integrationParams`:

```json
{
  "url": "https://merchantapi.boyner.com.tr",
  "supplierId": "your-supplier-id",
  "apiKey": "your-api-key",
  "apiSecret": "your-api-secret"
}
```

## API Endpoints

### Product Endpoints
- `GET /sapigw/product-categories` - Categories
- `GET /sapigw/brands` - Brands
- `GET /sapigw/product-categories/{categoryId}/attributes` - Attributes
- `POST /sapigw/suppliers/{supplierId}/v2/products` - Create/Update Products
- `POST /sapigw/suppliers/{supplierId}/products/price-and-inventory` - Stock/Price Updates
- `DELETE /sapigw/suppliers/{supplierId}/products/delete-products` - Delete Products

### Order Endpoints
- `GET /sapigw/suppliers/{supplierId}/orders` - Orders
- `PUT /sapigw/suppliers/{supplierId}/shipment-packages/{packageId}/status/{status}` - Update Status
- `POST /sapigw/suppliers/{supplierId}/supplier-invoice-links` - Invoice Links

### Return Endpoints
- `GET /sapigw/suppliers/{supplierId}/v2/claims` - Returns
- `GET /sapigw/claims/{claimId}/items/approve` - Approve Returns
- `PUT /sapigw/claims/{claimId}/issue` - Reject Returns

## Authentication

Uses Basic Authentication with API Key and Secret. All requests include:
- `Authorization: Basic base64(apiKey:apiSecret)`
- `User-Agent: {supplierId} - SelfIntegration`

## Rate Limiting

- Product operations: 100 requests per hour per merchant
- Batch operations recommended for bulk updates
- Automatic retry with exponential backoff for rate limit errors

## Important Notes

### Product Requirements
- Currency must be TRY (Turkish Lira)
- Images must be HTTPS URLs, 1200x1800px, 96dpi
- Maximum 8 images per product
- Products must be mapped to leaf categories only
- Barcode format: alphanumeric with dots, hyphens, underscores only

### Invoice Requirements
- **PDF format only** - HTML invoices are rejected for security reasons
- Must be accessible via HTTPS URL
- Required for order completion

### Order Status Flow
1. `Created` - New order received
2. `Picking` - Order being prepared
3. `Shipped` - Order dispatched
4. `Delivered` - Successfully delivered
5. `UnDelivered` - Delivery failed
6. `Cancelled` - Order cancelled

### Return Process
1. Customer initiates return via Boyner
2. System imports return request
3. Merchant approves/rejects with reasons
4. Status updates sync back to Boyner

## Error Handling

All operations include comprehensive error logging with:
- Request/response details
- Error messages and codes
- Retry mechanisms for transient failures
- Batch operation status tracking

## Testing

Use the test environment for development:
- Test URL: `https://merchantapi-test.boyner.com.tr`
- Test order creation available (disabled in production)
- Separate test credentials required
