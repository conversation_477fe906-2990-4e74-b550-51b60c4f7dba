import _ from 'lodash';
import {apiRequest} from './utils';

export default async function (app, store, onProgress, startDate, endDate) {
    let integrationParams = null;
    try {
        integrationParams = JSON.parse(store.integrationParams);
    } catch (error) {
        console.error(error);
        return;
    }
    const {url, supplierId, apiKey, apiSecret} = integrationParams;

    const log = payload => {
        payload.collection = 'ecommerce.stores';
        payload.documentId = store._id;
        payload.documentIdentifier = store.code;
        payload.method = 'update';
        payload.collectionName = `${app.translate('E-Commerce')} / ${app.translate('Stores')}`;

        return app.log(payload);
    };

    let page = 0;
    let currentCount = 0;
    let totalCount = 0;

    await (async function iterator() {
        let start = app.datetime.local().minus({days: 5}).startOf('day').toJSDate().getTime();
        let end = app.datetime.local().endOf('day').toJSDate().getTime();

        if (_.isDate(startDate) && _.isDate(endDate)) {
            start = app.datetime.fromJSDate(startDate).startOf('day').toJSDate().getTime();
            end = app.datetime.fromJSDate(endDate).endOf('day').toJSDate().getTime();
        }

        const result = await apiRequest({
            url,
            path: `sapigw/suppliers/${supplierId}/orders?shipmentPackageStatus=Cancelled&startDate=${start}&endDate=${end}&page=${page}&size=50`,
            supplierId,
            apiKey,
            apiSecret
        });

        if (result && result.content && Array.isArray(result.content) && result.content.length > 0) {
            totalCount = result.totalElements || result.content.length;

            for (const order of result.content) {
                try {
                    await processCanceledOrder(app, store, order, log);
                    currentCount++;
                } catch (error) {
                    await log({
                        level: 'error',
                        message: `Failed to process canceled order ${order.orderNumber}: ${error.message}`,
                        data: {order, error: error.message}
                    });
                }
            }

            if (onProgress && totalCount > 0) {
                onProgress(Math.min((currentCount / totalCount) * 100, 100));
            }

            page++;

            if (result.content.length === 50 && page < result.totalPages) {
                await iterator();
            } else if (onProgress) {
                onProgress(100);
            }
        } else if (onProgress) {
            onProgress(100);
        }
    })();
}

async function processCanceledOrder(app, store, boynerOrder, log) {
    // Find existing order
    const existingOrder = await app.collection('ecommerce.orders').findOne({
        storeId: store._id,
        integrationOrderNumber: boynerOrder.orderNumber
    });

    if (existingOrder) {
        // Update order status to cancelled
        await app.collection('ecommerce.orders').updateOne(
            {_id: existingOrder._id},
            {
                $set: {
                    status: 'cancelled',
                    cancelledAt: app.datetime.local(),
                    cancelReason: 'Cancelled by marketplace'
                }
            }
        );

        await log({
            level: 'info',
            message: `Order marked as cancelled: ${boynerOrder.orderNumber}`,
            data: {orderNumber: boynerOrder.orderNumber, packageNumber: boynerOrder.packageNumber}
        });
    } else {
        await log({
            level: 'warning',
            message: `Cancelled order not found in local database: ${boynerOrder.orderNumber}`,
            data: {orderNumber: boynerOrder.orderNumber}
        });
    }
}
