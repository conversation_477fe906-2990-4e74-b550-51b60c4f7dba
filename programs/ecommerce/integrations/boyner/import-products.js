import {apiRequest} from './utils';

export default async function (app, store, onProgress, filters = {}) {
    let integrationParams = null;
    try {
        integrationParams = JSON.parse(store.integrationParams);
    } catch (error) {
        console.error(error);
        return;
    }
    const {url, supplierId, apiKey, apiSecret} = integrationParams;

    const log = payload => {
        payload.collection = 'ecommerce.stores';
        payload.documentId = store._id;
        payload.documentIdentifier = store.code;
        payload.method = 'update';
        payload.collectionName = `${app.translate('E-Commerce')} / ${app.translate('Stores')}`;

        return app.log(payload);
    };

    let page = 0;
    let currentCount = 0;
    let totalCount = 0;

    await (async function iterator() {
        // Build query parameters
        const queryParams = new URLSearchParams({
            page: page.toString(),
            size: '50'
        });

        // Add filters
        if (filters.approved !== undefined) {
            queryParams.append('approved', filters.approved.toString());
        }
        if (filters.onSale !== undefined) {
            queryParams.append('onSale', filters.onSale.toString());
        }
        if (filters.barcode) {
            queryParams.append('barcode', filters.barcode);
        }
        if (filters.stockCode) {
            queryParams.append('stockCode', filters.stockCode);
        }
        if (filters.productMainId) {
            queryParams.append('productMainId', filters.productMainId);
        }

        const result = await apiRequest({
            url,
            path: `sapigw/suppliers/${supplierId}/products?${queryParams.toString()}`,
            supplierId,
            apiKey,
            apiSecret
        });

        if (result && result.content && Array.isArray(result.content) && result.content.length > 0) {
            totalCount = result.totalElements || result.content.length;

            for (const product of result.content) {
                try {
                    await processProduct(app, store, product, log);
                    currentCount++;
                } catch (error) {
                    await log({
                        level: 'error',
                        message: `Failed to process product ${product.barcode}: ${error.message}`,
                        data: {product, error: error.message}
                    });
                }
            }

            if (onProgress && totalCount > 0) {
                onProgress(Math.min((currentCount / totalCount) * 100, 100));
            }

            page++;

            if (result.content.length === 50 && page < result.totalPages) {
                await iterator();
            } else if (onProgress) {
                onProgress(100);
            }
        } else if (onProgress) {
            onProgress(100);
        }
    })();
}

async function processProduct(app, store, boynerProduct, log) {
    // Check if product already exists
    const existingProduct = await app.collection('ecommerce.products').findOne({
        storeId: store._id,
        barcode: boynerProduct.barcode
    });

    const productData = {
        storeId: store._id,
        integrationId: boynerProduct.id?.toString(),
        barcode: boynerProduct.barcode,
        title: boynerProduct.title,
        productMainId: boynerProduct.productMainId,
        brand: boynerProduct.brand,
        brandId: boynerProduct.brandId?.toString(),
        categoryName: boynerProduct.categoryName,
        categoryId: boynerProduct.pimCategoryId?.toString(),
        description: boynerProduct.description,
        stockCode: boynerProduct.stockCode,
        quantity: boynerProduct.quantity || 0,
        listPrice: boynerProduct.listPrice || 0,
        salePrice: boynerProduct.salePrice || 0,
        vatRate: boynerProduct.vatRate || 0,
        dimensionalWeight: boynerProduct.dimensionalWeight || 0,
        deliveryDuration: boynerProduct.deliveryDuration || 3,
        commission: boynerProduct.commission || 0,
        approved: boynerProduct.approved || false,
        archived: boynerProduct.archived || false,
        onSale: boynerProduct.onsale || false,
        locked: boynerProduct.locked || false,
        color: boynerProduct.color || '',
        size: boynerProduct.size || '',
        images: boynerProduct.images || [],
        attributes: boynerProduct.attributes || [],
        shipmentAddress: boynerProduct.shipmentAddress,
        returningAddress: boynerProduct.returningAddress,
        createdAt: boynerProduct.createDateTime ? 
            app.datetime.fromJSDate(new Date(boynerProduct.createDateTime * 1000)) : 
            app.datetime.local(),
        updatedAt: boynerProduct.lastUpdateDate ? 
            app.datetime.fromJSDate(new Date(boynerProduct.lastUpdateDate * 1000)) : 
            app.datetime.local()
    };

    if (existingProduct) {
        // Update existing product
        await app.collection('ecommerce.products').updateOne(
            {_id: existingProduct._id},
            {$set: productData}
        );

        await log({
            level: 'info',
            message: `Product updated: ${boynerProduct.barcode}`,
            data: {barcode: boynerProduct.barcode, title: boynerProduct.title}
        });
    } else {
        // Create new product
        await app.collection('ecommerce.products').insertOne(productData);

        await log({
            level: 'info',
            message: `New product created: ${boynerProduct.barcode}`,
            data: {barcode: boynerProduct.barcode, title: boynerProduct.title}
        });
    }
}
