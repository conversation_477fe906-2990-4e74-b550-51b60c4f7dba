import _ from 'lodash';
import {apiRequest} from './utils';
import {removeUnmatchedIntegrationProducts} from '../utils';

export default async function (app, store, onProgress) {
    let integrationParams = null;
    try {
        integrationParams = JSON.parse(store.integrationParams);
    } catch (error) {
        console.error(error);

        return;
    }
    const {url, supplierId, apiKey, apiSecret} = integrationParams;
    const log = payload => {
        payload.collection = 'ecommerce.stores';
        payload.documentId = store._id;
        payload.documentIdentifier = store.code;
        payload.method = 'update';
        payload.collectionName = `${app.translate('E-Commerce')} / ${app.translate('Stores')}`;

        return app.log(payload);
    };
    const now = app.datetime.local().toJSDate();
    let page = 0;
    let currentCount = 0;
    let totalCount = 0;

    await app.collection('ecommerce.store-products').remove({
        storeId: store._id,
        $or: [{productBarcode: {$eq: null}}, {productBarcode: {$eq: ''}}, {productBarcode: {$exists: false}}],
        $disableSoftDelete: true
    });

    const matchedProducts = [];
    await (async function iterator() {
        const result = await apiRequest({
            url,
            path: `sapigw/suppliers/${supplierId}/products?page=${page}&size=50`,
            supplierId,
            apiKey,
            apiSecret
        });
        totalCount = result.totalElements;

        if (Array.isArray(result.content) && result.content.length > 0) {
            const items = [];

            for (const item of result.content) {
                const storeProduct = {
                    storeId: store._id,
                    productBarcode: item.barcode,
                    productName: item.title,
                    productCode: item.productCode,
                    brandName: item.brand,
                    categoryName: item.categoryName,
                    description: item.description,
                    quantity: item.quantity,
                    listPrice: item.listPrice,
                    salesPrice: item.salePrice,
                    vatRate: item.vatRate,
                    dimensionalWeight: item.dimensionalWeight,
                    stockCode: item.stockCode,
                    approved: item.approved,
                    archived: item.archived,
                    onSale: item.onsale,
                    locked: item.locked,
                    images: item.images || [],
                    attributes: item.attributes || [],
                    lastUpdateDate: now
                };

                items.push(storeProduct);
                matchedProducts.push(item.barcode);
            }

            if (items.length > 0) {
                await app.collection('ecommerce.store-products').bulkWrite(
                    items.map(item => ({
                        updateOne: {
                            filter: {storeId: store._id, productBarcode: item.productBarcode},
                            update: {$set: item},
                            upsert: true
                        }
                    }))
                );

                currentCount += items.length;
                if (onProgress) {
                    onProgress(Math.min((currentCount / totalCount) * 100, 100));
                }
            }

            page++;
            if (result.content.length === 50) {
                await iterator();
            }
        }
    })();

    await removeUnmatchedIntegrationProducts(app, store, matchedProducts);

    if (onProgress) {
        onProgress(100);
    }
}