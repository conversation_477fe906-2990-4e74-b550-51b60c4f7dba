import {apiRequest} from './utils';

export default async function (app, store, batchRequestId) {
    let integrationParams = null;
    try {
        integrationParams = JSON.parse(store.integrationParams);
    } catch (error) {
        console.error(error);
        return null;
    }
    const {url, supplierId, apiKey, apiSecret} = integrationParams;

    const log = payload => {
        payload.collection = 'ecommerce.stores';
        payload.documentId = store._id;
        payload.documentIdentifier = store.code;
        payload.method = 'update';
        payload.collectionName = `${app.translate('E-Commerce')} / ${app.translate('Stores')}`;

        return app.log(payload);
    };

    try {
        const result = await apiRequest({
            url,
            path: `sapigw/suppliers/${supplierId}/products/batch-requests/${batchRequestId}`,
            supplierId,
            apiKey,
            apiSecret
        });

        await log({
            level: 'info',
            message: `Batch status checked: ${batchRequestId}`,
            data: {
                batchRequestId,
                status: result.status,
                itemCount: result.itemCount,
                batchRequestType: result.batchRequestType
            }
        });

        return {
            batchRequestId: result.batchRequestId,
            status: result.status, // COMPLETED, PROCESSING, FAILED, etc.
            itemCount: result.itemCount,
            batchRequestType: result.batchRequestType, // CreateProduct, UpdateProduct, etc.
            sourceType: result.sourceType,
            items: result.items || [],
            completedAt: result.status === 'COMPLETED' ? app.datetime.local() : null,
            failedItems: result.items ? result.items.filter(item => !item.status) : []
        };

    } catch (error) {
        await log({
            level: 'error',
            message: `Failed to check batch status: ${error.message}`,
            data: {batchRequestId, error: error.message}
        });
        throw error;
    }
}
