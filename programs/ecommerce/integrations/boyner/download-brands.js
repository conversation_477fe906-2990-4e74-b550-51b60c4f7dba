import {apiRequest} from './utils';

export default async function (app, store, onProgress) {
    let integrationParams = null;
    try {
        integrationParams = JSON.parse(store.integrationParams);
    } catch (error) {
        console.error(error);

        return;
    }
    const {url, supplierId, apiKey, apiSecret} = integrationParams;
    let page = 0;
    let currentCount = 0;
    let totalCount = 450000;

    await app.collection('ecommerce.brands').remove(
        {storeId: store._id},
        {
            disableSoftDelete: true,
            skipEvents: true
        }
    );

    await (async function iterator() {
        try {
            const result = await apiRequest({
                url,
                path: `sapigw/brands?page=${page}&size=500`,
                supplierId,
                apiKey,
                apiSecret
            });

            const brands = Array.isArray(result) ? result : (result && result.content ? result.content : []);

        if (Array.isArray(brands) && brands.length > 0) {
            const operations = [];

            for (const brand of brands) {
                if (brand && brand.id && brand.name) {
                    operations.push({
                        insertOne: {
                            document: {
                                storeId: store._id,
                                integrationId: brand.id.toString(),
                                name: brand.name,
                                isActive: true
                            }
                        }
                    });
                }
            }

            if (operations.length > 0) {
                await app.collection('ecommerce.brands').bulkWrite(operations, {
                    skipEvents: true
                });
            }

            page++;
            currentCount += brands.length;

            if (!!onProgress && totalCount > 0) {
                onProgress(Math.min((currentCount / totalCount) * 100, 100));
            }

            if (brands.length === 500) {
                await iterator();
            } else if (!!onProgress) {
                onProgress(100);
            }
        } else if (!!onProgress) {
            onProgress(100);
        }
        } catch (error) {
            console.error('Error downloading brands:', error);
            if (!!onProgress) {
                onProgress(100);
            }
        }
    })();
}
