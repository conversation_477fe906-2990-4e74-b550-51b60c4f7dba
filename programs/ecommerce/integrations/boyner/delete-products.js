import {apiRequest} from './utils';

export default async function (app, store, barcodes, onProgress) {
    let integrationParams = null;
    try {
        integrationParams = JSON.parse(store.integrationParams);
    } catch (error) {
        console.error(error);
        return;
    }
    const {url, supplierId, apiKey, apiSecret} = integrationParams;

    const log = payload => {
        payload.collection = 'ecommerce.stores';
        payload.documentId = store._id;
        payload.documentIdentifier = store.code;
        payload.method = 'update';
        payload.collectionName = `${app.translate('E-Commerce')} / ${app.translate('Stores')}`;

        return app.log(payload);
    };

    let processedCount = 0;
    const totalCount = barcodes.length;

    // Process barcodes in batches
    const batchSize = 50;
    for (let i = 0; i < barcodes.length; i += batchSize) {
        const batch = barcodes.slice(i, i + batchSize);

        try {
            const result = await apiRequest({
                method: 'delete',
                url,
                path: `sapigw/suppliers/${supplierId}/products/delete-products`,
                data: {barcodes: batch},
                supplierId,
                apiKey,
                apiSecret
            });

            await log({
                level: 'info',
                message: `Delete batch of ${batch.length} products sent successfully`,
                data: {result}
            });

            processedCount += batch.length;
            if (onProgress) {
                onProgress(Math.min((processedCount / totalCount) * 100, 100));
            }

        } catch (error) {
            await log({
                level: 'error',
                message: `Failed to delete product batch: ${error.message}`,
                data: {barcodes: batch, error: error.message}
            });
        }

        // Add delay between batches to respect rate limits
        await new Promise(resolve => setTimeout(resolve, 1000));
    }

    if (onProgress) {
        onProgress(100);
    }
}
