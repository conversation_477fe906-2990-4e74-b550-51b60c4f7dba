export async function removeUnmatchedIntegrationProducts({app, store, products}) {
    if (!(Array.isArray(products) && products.length > 0)) return;

    const log = payload => {
        payload.collection = 'ecommerce.stores';
        payload.documentId = store._id;
        payload.documentIdentifier = store.code;
        payload.method = 'update';
        payload.collectionName = `${app.translate('E-Commerce')} / ${app.translate('Stores')}`;
        return app.log(payload);
    };

    const result = await app.collection('ecommerce.store-products').bulkWrite([
        {
            deleteMany: {
                filter: {
                    storeId: store._id,
                    productId: {$nin: products.map(product => product.productId)}
                }
            }
        }
    ]);

    if (result && typeof result.deletedCount === 'number') {
        log({
            message: app.translate(
                '{{productCount}} products were removed from the store because the integration identifier was not found.',
                {productCount: result.deletedCount}
            )
        });
    }
}

export async function updateProductsSalesCount(app, store, items) {
    const productIds = items.map(item => item.productId);
    const storeProducts = await app.collection('ecommerce.store-products').find({
        storeId: store._id,
        productId: {$in: productIds},
        $select: ['_id', 'productId', 'salesCount'],
        $disableActiveCheck: true,
        $disableSoftDelete: true
    });
    const now = app.datetime.local().toJSDate();
    const storeProductsOperations = [];

    for (const item of items) {
        const storeProduct = storeProducts.find(storeProduct => storeProduct.productId === item.productId);

        if (!!storeProduct) {
            storeProductsOperations.push({
                updateOne: {
                    filter: {_id: storeProduct._id},
                    update: {$set: {salesCount: (storeProduct.salesCount || 0) + item.quantity, updatedAt: now}}
                }
            });
        }
    }

    if (storeProductsOperations.length > 0) {
        await app.collection('ecommerce.store-products').bulkWrite(storeProductsOperations);
    }
}
