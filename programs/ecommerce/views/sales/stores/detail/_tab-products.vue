<template>
    <div class="ecommerce-sales-stores-detail-tab-products">
        <div class="tab-products-top">
            <div class="tab-products-top-actions">
                <el-dropdown
                    @command="handleAdd"
                    trigger="click"
                    placement="bottom-start"
                    v-show="!store.barcodeMatchingOnly"
                >
                    <el-button size="mini" plain icon="far fa-plus">
                        {{ 'Add' | t }}<i class="el-icon-arrow-down el-icon--right"></i>
                    </el-button>
                    <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item command="custom">
                            {{ 'Custom' | t }}
                        </el-dropdown-item>
                        <el-dropdown-item command="from-groups">
                            {{ 'Add from product groups' | t }}
                        </el-dropdown-item>
                        <el-dropdown-item command="from-categories">
                            {{ 'Add from product categories' | t }}
                        </el-dropdown-item>
                        <el-dropdown-item command="from-kits">
                            {{ 'Add from kit products' | t }}
                        </el-dropdown-item>
                        <el-dropdown-item command="add-all">
                            {{ 'Add all products' | t }}
                        </el-dropdown-item>
                    </el-dropdown-menu>
                </el-dropdown>

                <el-dropdown
                    @command="handlePublish"
                    trigger="click"
                    placement="bottom-start"
                    v-show="!store.barcodeMatchingOnly"
                >
                    <el-button size="mini" plain icon="far fa-globe">
                        {{ 'Publish' | t }}<i class="el-icon-arrow-down el-icon--right"></i>
                    </el-button>
                    <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item command="publish-selected" :disabled="selected.length < 1">
                            {{ 'Publish selected' | t }}
                        </el-dropdown-item>
                        <el-dropdown-item command="publish-filtered">
                            {{ 'Publish filtered' | t }}
                        </el-dropdown-item>
                        <el-dropdown-item command="publish-all">
                            {{ 'Publish all' | t }}
                        </el-dropdown-item>
                    </el-dropdown-menu>
                </el-dropdown>

                <el-dropdown
                    @command="handleUnPublish"
                    trigger="click"
                    placement="bottom-start"
                    v-if="integrationType.name === 'enterstore'"
                >
                    <el-button size="mini" plain icon="far fa-globe-snow">
                        {{ 'Un-Publish' | t }}<i class="el-icon-arrow-down el-icon--right"></i>
                    </el-button>
                    <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item command="un-publish-selected" :disabled="selected.length < 1">
                            {{ 'Un-Publish selected' | t }}
                        </el-dropdown-item>
                        <el-dropdown-item command="un-publish-filtered">
                            {{ 'Un-Publish filtered' | t }}
                        </el-dropdown-item>
                        <el-dropdown-item command="un-publish-all">
                            {{ 'Un-Publish all' | t }}
                        </el-dropdown-item>
                    </el-dropdown-menu>
                </el-dropdown>

                <el-dropdown
                    @command="handleSync"
                    trigger="click"
                    placement="bottom-start"
                    v-show="!store.barcodeMatchingOnly"
                >
                    <el-button size="mini" plain icon="far fa-sync">
                        {{ 'Synchronize' | t }}<i class="el-icon-arrow-down el-icon--right"></i>
                    </el-button>
                    <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item command="synchronize-selected" :disabled="selected.length < 1">
                            {{ 'Synchronize selected' | t }}
                        </el-dropdown-item>
                        <el-dropdown-item command="synchronize-filtered">
                            {{ 'Synchronize filtered' | t }}
                        </el-dropdown-item>
                        <el-dropdown-item command="synchronize-all">
                            {{ 'Synchronize all' | t }}
                        </el-dropdown-item>
                    </el-dropdown-menu>
                </el-dropdown>

                <el-dropdown @command="handleSyncStocks" trigger="click" placement="bottom-start">
                    <el-button size="mini" plain icon="far fa-sync">
                        {{ 'Synchronize Stocks' | t }}<i class="el-icon-arrow-down el-icon--right"></i>
                    </el-button>
                    <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item command="synchronize-selected" :disabled="selected.length < 1">
                            {{ 'Synchronize selected' | t }}
                        </el-dropdown-item>
                        <el-dropdown-item command="synchronize-filtered">
                            {{ 'Synchronize filtered' | t }}
                        </el-dropdown-item>
                        <el-dropdown-item command="synchronize-all">
                            {{ 'Synchronize all' | t }}
                        </el-dropdown-item>
                    </el-dropdown-menu>
                </el-dropdown>

                <el-dropdown
                    @command="handleRemove"
                    trigger="click"
                    placement="bottom-start"
                    v-if="integrationType.name === 'enterstore'"
                >
                    <el-button size="mini" plain icon="far fa-times">
                        {{ 'Remove' | t }}<i class="el-icon-arrow-down el-icon--right"></i>
                    </el-button>
                    <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item command="remove-selected" :disabled="selected.length < 1">
                            {{ 'Remove selected' | t }}
                        </el-dropdown-item>
                        <el-dropdown-item command="remove-filtered">
                            {{ 'Remove filtered' | t }}
                        </el-dropdown-item>
                        <el-dropdown-item command="remove-all">
                            {{ 'Remove all' | t }}
                        </el-dropdown-item>
                    </el-dropdown-menu>
                </el-dropdown>
            </div>

            <ui-scope
                id="ecommerce.sales.stores.detail.tab-products"
                :filters="scopeApplicableFilters"
                @changed="handleScopeChange"
                @initialized="scopeInitialized = true"
            />

            <div class="product-search">
                <el-input-search class="relation-search" v-model="search" />
            </div>
        </div>

        <div class="tab-products-content">
            <ui-table
                ref="table"
                collection="ecommerce.store-products"
                id="ecommerce.sales.stores.detail.tab-products"
                :columns="columns"
                :filters="filters"
                :search="search"
                :extra-fields="extraFields"
                :enable-sorting="false"
                :row-height="60"
                no-zebra
                @selected="handleSelect"
                @double-clicked="handleDetail"
                v-if="scopeInitialized"
            />
        </div>
    </div>
</template>

<script>
import _ from 'lodash';
import fastCopy from 'fast-copy';
import integrationTypes from '../../../../integrations/integration-types';
import StatusCellRenderer from './_status-cell-renderer';

export default {
    props: {
        store: Object,
        integrationType: Object,
        formatOptions: Object
    },

    data: () => ({
        selected: [],
        scopeQuery: {},
        search: '',
        extraFields: [
            'isSynchronized',
            'isPublished',
            'currencyId',
            'isAdultProduct',
            'isBestSellingProduct',
            'isDiscountedProduct',
            'isNewProduct',
            'isSuggestedProduct'
        ],
        scopeInitialized: false,
        integrationTypes
    }),

    computed: {
        filters() {
            const filters = fastCopy(this.scopeQuery);

            filters.storeId = this.$params('id');
            filters.$sort = {updatedAt: -1};

            return filters;
        },
        scopeApplicableFilters() {
            const barcodeMatchingOnly = !!this.store.barcodeMatchingOnly;

            return [
                ...(!barcodeMatchingOnly
                    ? [
                          {label: 'Is published', query: {isPublished: {$ne: false}}, type: 'boolean'},
                          {label: 'Is not published', query: {isPublished: false}, type: 'boolean'}
                      ]
                    : []),
                {label: 'On sale', query: {onSale: {$ne: false}}, type: 'boolean'},
                {label: 'Not on sale', query: {onSale: false}, type: 'boolean'},
                ...(!barcodeMatchingOnly
                    ? [
                          {label: 'Synchronized', query: {isSynchronized: true}, type: 'boolean'},
                          {label: 'Not synchronized', query: {isSynchronized: {$ne: true}}, type: 'boolean'}
                      ]
                    : []),

                {
                    field: 'integrationStatus',
                    label: 'Integration status',
                    pinned: 'right',
                    translateLabels: true,
                    items: [
                        {value: 'waiting', label: 'Waiting'},
                        {value: 'approved', label: 'Approved'},
                        {value: 'rejected', label: 'Rejected'},
                        {value: 'blacklisted', label: 'Blacklisted'}
                    ],
                    condition: () => this.integrationType.name !== 'enterstore'
                },
                {field: 'productCode', label: 'Code'},
                {field: 'productDefinition', label: 'Definition'},
                {field: 'productBarcode', label: 'Barcode'},
                {
                    field: 'productCategoryPath',
                    label: 'Category',
                    collection: 'inventory.product-categories',
                    valueFrom: 'path',
                    labelFrom: 'path',
                    operator: 'starts-with'
                },
                {
                    field: 'productGroupIds',
                    label: 'Group',
                    collection: 'inventory.product-groups'
                },
                {
                    field: 'productBrandId',
                    label: 'Brand',
                    collection: 'inventory.product-brands'
                },
                {
                    field: 'salesPrice',
                    label: 'Sales price',
                    type: 'decimal'
                },
                {
                    field: 'discountedSalesPrice',
                    label: 'Discounted sales price',
                    type: 'decimal'
                },
                {
                    field: 'discount',
                    label: 'Discount',
                    type: 'decimal'
                },
                {
                    field: 'quantity',
                    label: 'Quantity',
                    type: 'decimal'
                },
                {
                    field: 'criticalStockQuantity',
                    label: 'Critical stock quantity',
                    type: 'decimal'
                },
                {
                    field: 'publishedAt',
                    label: 'Published at',
                    type: 'datetime',
                    condition: () => !barcodeMatchingOnly
                },
                {
                    field: 'isSuggestedProduct',
                    type: 'boolean',
                    label: 'Suggested product',
                    condition: () => this.integrationType.name === 'enterstore'
                },
                {
                    field: 'isBestSellingProduct',
                    type: 'boolean',
                    label: 'Best selling product',
                    condition: () => this.integrationType.name === 'enterstore'
                },
                {
                    field: 'isNewProduct',
                    type: 'boolean',
                    label: 'New product',
                    condition: () => this.integrationType.name === 'enterstore'
                },
                {
                    field: 'isDiscountedProduct',
                    type: 'boolean',
                    label: 'Discounted product',
                    condition: () => this.integrationType.name === 'enterstore'
                },
                {
                    field: 'isAdultProduct',
                    type: 'boolean',
                    label: 'Adult product',
                    condition: () => this.integrationType.name === 'enterstore'
                }
            ];
        },
        columns() {
            return [
                {
                    field: 'productImage',
                    label: 'Image',
                    imageCell: true,
                    width: 60,
                    maxWidth: 60,
                    minWidth: 60
                },
                {
                    field: 'productCode',
                    label: 'Code',
                    width: 120,
                    visibleForExport: true,
                    selectedForExport: true
                },
                {
                    field: 'productDefinition',
                    label: 'Definition',
                    exportField: 'productDefinition',
                    hidden: true
                },
                {
                    field: 'productDefinitionDisplay',
                    label: 'Definition',
                    cellClass: 'product-cell',
                    minWidth: 180,
                    visibleForExport: false,
                    render: params => {
                        const {data} = params;

                        if (_.isPlainObject(data)) {
                            return `<div class="product-title">${data.productDefinition}</div>`;
                        }

                        return '';
                    }
                },
                {field: 'productBarcode', label: 'Barcode', visible: false},
                {
                    field: 'productCategoryPath',
                    label: 'Category',
                    valueGetter(params) {
                        if (_.isObject(params.data) && !params.node.group) {
                            return params.data.productCategoryPath;
                        }

                        return '';
                    }
                },
                {field: 'productBrandName', label: 'Brand', visible: false, width: 120},
                {
                    field: 'salesPrice',
                    label: 'Sales price',
                    format: 'currency',
                    formatOptions: row => {
                        if (_.isPlainObject(row) && !!row.currencyId) {
                            const options = {currency: {}};

                            options.currency.symbol = this.formatOptions.currency.symbol;
                            options.currency.format =
                                this.formatOptions.currency.symbolPosition === 'before' ? '%s %v' : '%v %s';

                            return options;
                        }

                        return {};
                    },
                    width: 150
                },
                {
                    field: 'discountedSalesPrice',
                    label: 'Discounted sales price',
                    format: 'currency',
                    formatOptions: row => {
                        if (_.isPlainObject(row) && !!row.currencyId) {
                            const options = {currency: {}};

                            options.currency.symbol = this.formatOptions.currency.symbol;
                            options.currency.format =
                                this.formatOptions.currency.symbolPosition === 'before' ? '%s %v' : '%v %s';

                            return options;
                        }

                        return {};
                    },
                    width: 150
                },
                {
                    field: 'quantity',
                    label: 'Quantity',
                    format: 'unit',
                    width: 120
                },
                {
                    field: 'criticalStockQuantity',
                    label: 'Critical stock quantity',
                    format: 'unit',
                    width: 120,
                    visible: false
                },
                {
                    field: 'synchronizedAt',
                    label: 'Synchronized at',
                    format: 'datetime',
                    visible: false,
                    hidden: !!this.store.barcodeMatchingOnly,
                    width: 150
                },
                {
                    field: 'publishedAt',
                    label: 'Published at',
                    format: 'datetime',
                    visible: false,
                    hidden: !!this.store.barcodeMatchingOnly,
                    width: 150
                },
                {
                    field: 'integrationStatus',
                    label: 'Integration status',
                    width: 150,
                    pinned: 'right',
                    translateLabels: true,
                    tagsCell: true,
                    tagLabels: [
                        {value: 'waiting', label: 'Waiting', color: 'warning'},
                        {value: 'approved', label: 'Approved', color: 'success'},
                        {value: 'rejected', label: 'Rejected', color: 'danger'},
                        {value: 'blacklisted', label: 'Blacklisted', color: 'danger'}
                    ],
                    hidden: this.integrationType.name === 'enterstore'
                },
                {
                    field: 'status',
                    label: 'Status',
                    width: 140,
                    pinned: 'right',
                    cellRendererFramework: StatusCellRenderer
                },
                {
                    field: 'settings',
                    label: 'Settings',
                    render: params => {
                        const data = params.data;

                        if (!!data) {
                            let content = '';

                            if (data.isAdultProduct) {
                                content += `
                                <div class="el-label" style="background-color: rgb(29, 78, 216)">
                                    ${this.$t('Adult product')}
                                </div>`;
                            }
                            if (data.isBestSellingProduct) {
                                content += `
                                <div class="el-label" style="background-color: rgb(29, 78, 216)">
                                    ${this.$t('Best selling product')}
                                </div>`;
                            }
                            if (data.isDiscountedProduct) {
                                content += `
                                <div class="el-label" style="background-color: rgb(29, 78, 216)">
                                    ${this.$t('Discounted product')}
                                </div>`;
                            }
                            if (data.isNewProduct) {
                                content += `
                                <div class="el-label" style="background-color: rgb(29, 78, 216)">
                                    ${this.$t('New product')}
                                </div>`;
                            }
                            if (data.isSuggestedProduct) {
                                content += `
                                <div class="el-label" style="background-color: rgb(29, 78, 216)">
                                    ${this.$t('Suggested product')}
                                </div>`;
                            }

                            return `
                            <div class="ui-table-tags-renderer">
                                ${content}
                            </div>`;
                        }

                        return '';
                    },
                    width: 150,
                    visible: false,
                    hidden: this.integrationType.name !== 'enterstore'
                }
                // {
                //     field: 'isSynchronized',
                //     label: 'Is synchronized',
                //     render: params => {
                //         const data = params.data;
                //
                //         if (!!data) {
                //             if (!!data.isSynchronized) {
                //                 return `<span class="text-success">${this.$t('Synchronized')}</span>`;
                //             } else {
                //                 return `<span class="text-danger">${this.$t('Not synchronized')}</span>`;
                //             }
                //         }
                //
                //         return '';
                //     },
                //     width: 150
                // },
                // {
                //     field: 'isPublished',
                //     label: 'Publication status',
                //     tagsCell: true,
                //     translateLabels: true,
                //     tagLabels: [
                //         {value: 'published', label: 'Published', color: 'success'},
                //         {value: 'un-published', label: 'Un-Published', color: 'warning'}
                //     ],
                //     valueGetter: params => {
                //         const {data} = params;
                //
                //         return !!data && !!data.isPublished ? 'published' : 'un-published';
                //     },
                //     width: 150
                // }
            ];
        },
        integrationType() {
            return integrationTypes.find(it => it.name === this.store.integrationType);
        }
    },

    methods: {
        async handleAdd(type) {
            const productsQuery = {
                canBeSold: true,
                isECommerceProduct: true,
                isSimple: true
            };

            if (type === 'custom') {
                const filters = {
                    canBeSold: true,
                    isSimple: true,
                    isECommerceProduct: true
                };

                this.$program.dialog({
                    component: 'inventory.catalog.products.master',
                    params: {filters},
                    onSelect: async selected => {
                        this.$params('loading', true);

                        try {
                            await this.$rpc('ecommerce.stores-add-products', {
                                type,
                                storeId: this.$params('id'),
                                productsQuery: {
                                    ...productsQuery,
                                    _id: {$in: selected.map(s => s._id)}
                                }
                            });
                        } catch (error) {
                            this.$program.message('error', error.message);
                        }

                        this.$params('loading', false);
                    }
                });
            } else if (type === 'from-groups') {
                this.$program.dialog({
                    component: 'inventory.configuration.product-groups.master',
                    params: {},
                    onSelect: async selected => {
                        this.$params('loading', true);

                        try {
                            await this.$rpc('ecommerce.stores-add-products', {
                                type,
                                storeId: this.$params('id'),
                                productsQuery: {
                                    ...productsQuery,
                                    groupIds: {$in: selected.map(s => s._id)}
                                }
                            });
                        } catch (error) {
                            this.$program.message('error', error.message);
                        }

                        this.$params('loading', false);
                    }
                });
            } else if (type === 'from-categories') {
                this.$program.dialog({
                    component: 'inventory.configuration.product-categories.master',
                    params: {},
                    onSelect: async selected => {
                        this.$params('loading', true);

                        if (!Array.isArray(productsQuery.$or)) {
                            productsQuery.$or = [];
                        }

                        for (const category of selected) {
                            productsQuery.$or.push({
                                categoryPath: {
                                    $regex: `^${category.path}`,
                                    $options: 'i'
                                }
                            });
                            // productsQuery.$or.push(
                            //     ...[
                            //         {
                            //             categoryPath: {
                            //                 $regex: `^${toUpper(escapeRegExp(category.path))}`,
                            //                 $options: 'i'
                            //             }
                            //         },
                            //         {
                            //             categoryPath: {
                            //                 $regex: `^${toLower(escapeRegExp(category.path))}`,
                            //                 $options: 'i'
                            //             }
                            //         }
                            //     ]
                            // );
                        }

                        try {
                            await this.$rpc('ecommerce.stores-add-products', {
                                type,
                                storeId: this.$params('id'),
                                productsQuery
                            });
                        } catch (error) {
                            this.$program.message('error', error.message);
                        }

                        this.$params('loading', false);
                    }
                });
            } else if (type === 'from-kits') {
                const filters = {
                    canBeSold: true,
                    isKit: true,
                    isECommerceProduct: true
                };

                this.$program.dialog({
                    component: 'inventory.catalog.products.master',
                    params: {filters},
                    onSelect: async selected => {
                        this.$params('loading', true);

                        try {
                            await this.$rpc('ecommerce.stores-add-products', {
                                type,
                                storeId: this.$params('id'),
                                productsQuery: {
                                    ...{
                                        ...productsQuery,
                                        isSimple: false,
                                        isKit: true
                                    },
                                    _id: {$in: selected.map(s => s._id)}
                                }
                            });
                        } catch (error) {
                            this.$program.message('error', error.message);
                        }

                        this.$params('loading', false);
                    }
                });
            } else if (type === 'add-all') {
                this.$params('loading', true);

                try {
                    await this.$rpc('ecommerce.stores-add-products', {
                        type,
                        storeId: this.$params('id'),
                        productsQuery
                    });
                } catch (error) {
                    this.$program.message('error', error.message);
                }

                this.$params('loading', false);
            }
        },
        async handlePublish(type) {
            const isConfirmed = await new Promise(resolve => {
                this.$program.alert(
                    'confirm',
                    this.$t(
                        'You are about to publish store products. This action will publish the relevant products in the store. Do you want to continue?'
                    ),
                    confirmed => {
                        resolve(confirmed);
                    }
                );
            });

            if (!isConfirmed) return;

            this.$params('loading', true);

            let query = {};
            if (type === 'publish-selected' && this.selected.length > 0) {
                query._id = {$in: this.selected.map(s => s._id)};
            } else if (type === 'publish-filtered') {
                query = {...this.filters};
            }

            try {
                await this.$rpc('ecommerce.stores-publish-products', {
                    storeId: this.$params('id'),
                    query
                });
            } catch (error) {
                this.$program.message('error', error.message);
            }

            if (!!this.$refs.table) {
                const api = this.$refs.table.api;
                api.forEachNode(node => {
                    if (this.selected.map(s => s._id).indexOf(node.data._id) !== -1) {
                        node.setSelected(false);
                    }
                });
            }
            this.selected = [];

            this.$params('loading', false);
        },
        async handleUnPublish(type) {
            const isConfirmed = await new Promise(resolve => {
                this.$program.alert(
                    'confirm',
                    this.$t(
                        'You are about to un-publish store products. This action will un-publish the relevant products in the store. Do you want to continue?'
                    ),
                    confirmed => {
                        resolve(confirmed);
                    }
                );
            });

            if (!isConfirmed) return;

            this.$params('loading', true);

            let query = {};

            if (type === 'un-publish-selected' && this.selected.length > 0) {
                query._id = {$in: this.selected.map(s => s._id)};
            } else if (type === 'un-publish-filtered') {
                query = {...this.filters};
            }

            try {
                await this.$rpc('ecommerce.stores-un-publish-products', {
                    storeId: this.$params('id'),
                    query
                });
            } catch (error) {
                this.$program.message('error', error.message);
            }

            if (!!this.$refs.table) {
                const api = this.$refs.table.api;
                api.forEachNode(node => {
                    if (this.selected.map(s => s._id).indexOf(node.data._id) !== -1) {
                        node.setSelected(false);
                    }
                });
            }
            this.selected = [];

            this.$params('loading', false);
        },
        async handleSync(type) {
            const isConfirmed = await new Promise(resolve => {
                this.$program.alert(
                    'confirm',
                    this.$t(
                        'You are about to synchronize store products. This action will synchronize the relevant products in the store. Do you want to continue?'
                    ),
                    confirmed => {
                        resolve(confirmed);
                    }
                );
            });

            if (!isConfirmed) return;

            this.$params('loading', true);

            let query = {};
            if (type === 'synchronize-selected' && this.selected.length > 0) {
                query._id = {$in: this.selected.map(s => s._id)};
            } else if (type === 'synchronize-filtered') {
                query = {...this.filters};
            }

            try {
                await this.$rpc('ecommerce.stores-sync-products', {
                    storeId: this.$params('id'),
                    query
                });
            } catch (error) {
                this.$program.message('error', error.message);
            }

            if (!!this.$refs.table) {
                const api = this.$refs.table.api;
                api.forEachNode(node => {
                    if (this.selected.map(s => s._id).indexOf(node.data._id) !== -1) {
                        node.setSelected(false);
                    }
                });
            }
            this.selected = [];

            this.$params('loading', false);
        },
        async handleSyncStocks(type) {
            const isConfirmed = await new Promise(resolve => {
                this.$program.alert(
                    'confirm',
                    this.$t(
                        'You are about to synchronize stocks. This action will synchronize and publish the relevant product stocks. Do you want to continue?'
                    ),
                    confirmed => {
                        resolve(confirmed);
                    }
                );
            });

            if (!isConfirmed) return;

            this.$params('loading', true);

            let query = {};
            if (type === 'synchronize-selected' && this.selected.length > 0) {
                query._id = {$in: this.selected.map(s => s._id)};
            } else if (type === 'synchronize-filtered') {
                query = {...this.filters};
            }

            try {
                await this.$rpc('ecommerce.stores-sync-stocks', {
                    storeId: this.$params('id'),
                    query
                });
            } catch (error) {
                this.$program.message('error', error.message);
            }

            if (!!this.$refs.table) {
                const api = this.$refs.table.api;
                api.forEachNode(node => {
                    if (this.selected.map(s => s._id).indexOf(node.data._id) !== -1) {
                        node.setSelected(false);
                    }
                });
            }
            this.selected = [];

            this.$params('loading', false);
        },
        async handleRemove(type) {
            const isConfirmed = await new Promise(resolve => {
                this.$program.alert(
                    'confirm',
                    this.$t(
                        'You are about to remove store products. This action will delete or unpublish related products from the store. Do you want to continue?'
                    ),
                    confirmed => {
                        resolve(confirmed);
                    }
                );
            });

            if (!isConfirmed) return;

            this.$params('loading', true);

            let query = {};
            if (type === 'remove-selected' && this.selected.length > 0) {
                query._id = {$in: this.selected.map(s => s._id)};
            } else if (type === 'remove-filtered') {
                query = {...this.filters};
            }

            try {
                await this.$rpc('ecommerce.stores-remove-products', {
                    storeId: this.$params('id'),
                    query
                });
            } catch (error) {
                this.$program.message('error', error.message);
            }

            if (!!this.$refs.table) {
                const api = this.$refs.table.api;
                api.forEachNode(node => {
                    if (this.selected.map(s => s._id).indexOf(node.data._id) !== -1) {
                        node.setSelected(false);
                    }
                });
            }
            this.selected = [];

            this.$params('loading', false);
        },
        handleScopeChange(model) {
            this.scopeQuery = model.query;
        },
        handleSelect(selected) {
            this.selected = selected;
        },
        handleDetail(row) {
            this.$program.dialog({
                component: 'ecommerce.sales.stores.detail.product',
                params: {
                    id: row._id,
                    store: this.store,
                    integrationType: this.integrationType,
                    isPreview: false
                }
            });
            if (!!this.$refs.table) {
                const api = this.$refs.table.api;
                api.forEachNode(node => {
                    if (this.selected.indexOf(node.data._id) !== -1) {
                        node.setSelected(false);
                    }
                });
            }
        }
    }
};
</script>

<style lang="scss">
//noinspection CssUnknownTarget
@import 'core';

.ecommerce-sales-stores-detail-tab-products {
    position: relative;
    width: 100%;
    height: 100%;
    padding-top: 45px;
    overflow: hidden;

    .tab-products-top {
        position: absolute;
        display: flex;
        flex-flow: row nowrap;
        top: 0;
        left: 0;
        width: 100%;
        height: 45px;
        padding: 0 10px 0 10px;

        .tab-products-top-actions {
            position: relative;
            padding: 9.5px 20px 9.5px 8.5px;

            &:after {
                position: absolute;
                top: 50%;
                right: 0;
                width: 1px;
                height: 27px;
                transform: translateY(-50%);
                background-color: $border-color;
                content: ' ';
            }
        }

        .ui-scope {
            flex: 1 1 0;
            margin-left: 10px;
        }

        .product-search {
            position: relative;
            padding: 9.5px 0 9.5px 20px;
            flex: 0 0 240px;

            &:before {
                position: absolute;
                top: 50%;
                left: 0;
                width: 1px;
                height: 27px;
                transform: translateY(-50%);
                background-color: $border-color;
                content: ' ';
            }
        }
    }

    .tab-products-content {
        width: 100%;
        height: 100%;
        overflow: hidden;

        .ag-cell {
            &,
            > span {
                display: flex;
                flex-flow: column;
                justify-content: center;
                width: 100%;
                height: 100%;
            }
        }

        .product-cell {
            width: 100%;

            .product-title {
                width: 100%;
                font-size: 13px;
                line-height: 1.4;
                white-space: break-spaces;
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                line-clamp: 2;
                -webkit-line-clamp: 2;
                box-orient: vertical;
                -webkit-box-orient: vertical;
                font-weight: bold;
                color: $text-color-light;
            }
        }
    }
}
</style>
