<template>
    <ui-view
        type="form"
        :model="model"
        :schema="schema"
        :title="title"
        :extra-fields="['id']"
        :before-init="beforeInit"
        :before-validate="beforeValidate"
        :before-submit="beforeSubmit"
        actions="edit,cancel"
        no-inner-padding
        full-height
        @changed="handleChange"
        v-if="initialized"
    >
        <el-tabs v-model="activeTab" class="full-tabs">
            <el-tab-pane name="general" :label="'GENERAL' | t">
                <el-scrollbar>
                    <div class="columns">
                        <div class="column is-half">
                            <ui-legend title="General" />
                            <ui-field name="code" />
                            <ui-field name="name" />
                            <ui-field
                                name="paymentType"
                                :options="paymentTypeOptions"
                                translate-labels
                                v-show="integrationType.name === 'enterstore'"
                            />
                            <ui-field
                                name="journalId"
                                collection="accounting.journals"
                                view="accounting.configuration.journals"
                                :filters="journalIdFilters"
                                v-show="model.paymentType !== 'open-account'"
                                disable-detail
                                disable-create
                            />

                            <ui-legend title="Description" class="mt30" />
                            <ui-field name="description" label="hide" :rows="2" />
                        </div>

                        <div class="column is-half">
                            <ui-legend title="Details" />
                            <ui-field
                                name="paymentTermId"
                                collection="finance.payment-terms"
                                view="finance.configuration.payment-terms"
                                :filters="{scope: 'receipt'}"
                                disable-detail
                                disable-create
                            />
                            <ui-field
                                name="serviceProductId"
                                collection="inventory.products"
                                view="inventory.catalog.products"
                                :filters="{type: 'service', isSimple: true}"
                                :extra-fields="['code', 'definition']"
                                :template="'{{code}} - {{definition}}'"
                                disable-create
                                disable-detail
                                v-show="model.paymentType === 'cash-on-delivery'"
                            />
                            <ui-field
                                name="taxId"
                                collection="kernel.taxes"
                                :filters="{scope: 'sale'}"
                                disable-create
                                disable-detail
                                v-show="model.paymentType === 'cash-on-delivery'"
                            />
                            <ui-field
                                name="cashOnDeliveryServiceFee"
                                v-show="model.paymentType === 'cash-on-delivery'"
                            />
                            <ui-field name="isActive" />
                        </div>
                    </div>
                </el-scrollbar>
            </el-tab-pane>

            <el-tab-pane name="locations" :label="'LOCATIONS' | t" :disabled="integrationType.name !== 'enterstore'">
                <ui-field name="locations" class="mb0" full-height :enable-enlarge="true" />
            </el-tab-pane>
        </el-tabs>
    </ui-view>
</template>

<script>
import Random from 'framework/random';

export default {
    data: () => ({
        model: {},
        paymentTypeOptions: [
            {value: 'credit-card', label: 'Credit card'},
            {value: 'money-order', label: 'Money order'},
            {value: 'cash-on-delivery', label: 'Cash on delivery'},
            {value: 'open-account', label: 'Open account'}
        ],
        activeTab: 'general',
        countries: [],
        integrationType: null,
        initialized: false
    }),

    computed: {
        title() {
            const model = this.model;

            return model.name ? model.name : this.$t('New Payment Method');
        },
        schema() {
            return {
                id: {
                    type: 'string',
                    label: 'ID'
                },
                code: {
                    type: 'string',
                    label: 'Code'
                },
                name: {
                    type: 'string',
                    label: 'Name'
                },
                paymentType: {
                    type: 'string',
                    label: 'Payment type'
                },
                journalId: {
                    type: 'string',
                    label: 'Account',
                    required: false
                },
                description: {
                    type: 'string',
                    label: 'Description',
                    required: false
                },

                paymentTermId: {
                    type: 'string',
                    label: 'Payment term'
                },
                serviceProductId: {
                    type: 'string',
                    label: 'Service product',
                    required: false
                },
                taxId: {
                    type: 'string',
                    label: 'Tax',
                    required: false
                },
                cashOnDeliveryServiceFee: {
                    type: 'decimal',
                    label: 'Cash on delivery service fee',
                    default: 0
                },
                isActive: {
                    type: 'boolean',
                    label: 'Is active',
                    default: true
                },

                locations: {
                    type: [
                        {
                            countryId: {
                                type: 'string',
                                label: 'Country',
                                editor: {
                                    options: () =>
                                        this.countries.map(country => ({
                                            value: country._id,
                                            label: country.name
                                        }))
                                }
                            },
                            city: {
                                type: 'string',
                                label: 'City',
                                required: false
                            },
                            isActive: {
                                type: 'boolean',
                                label: 'Is active',
                                default: true,
                                column: {
                                    width: 75
                                }
                            }
                        }
                    ],
                    default: []
                }
            };
        },
        journalIdFilters() {
            const model = this.model;
            let type = '';

            if (model.paymentType === 'money-order' || model.paymentType === 'cash-on-delivery') {
                type = 'bank';
            } else if (model.paymentType === 'credit-card') {
                type = 'pos';
            }

            return {type};
        }
    },

    methods: {
        async beforeInit(model) {
            if (this.integrationType.name === 'shopify') {
                model.paymentType = 'credit-card';
            }

            return model;
        },
        async beforeValidate(model) {
            if (!model.id) {
                model.id = Random.id(16);
            }

            if (model.paymentType === 'cash-on-delivery' && !model.serviceProductId) {
                throw new this.$app.errors.Unprocessable({
                    message: this.$t('{{label}} is required', {label: this.$t('Service product')}),
                    field: 'serviceProductId'
                });
            }
            if (model.paymentType === 'cash-on-delivery' && !model.taxId) {
                throw new this.$app.errors.Unprocessable({
                    message: this.$t('{{label}} is required', {label: this.$t('Tax')}),
                    field: 'taxId'
                });
            }
            if (model.paymentType !== 'open-account' && !model.journalId) {
                throw new this.$app.errors.Unprocessable({
                    message: this.$t('{{label}} is required', {label: this.$t('Account')}),
                    field: 'journalId'
                });
            }

            return model;
        },
        async beforeSubmit(model) {
            return model;
        },
        async handleChange(model, field) {
            if (field === 'paymentType') {
                this.model.journalId = '';
            }
        }
    },

    async created() {
        this.$params('loading', true);

        this.countries = await this.$collection('kernel.countries').find({});
        this.integrationType = this.$params('integrationType');

        this.initialized = true;

        this.$params('loading', false);
    }
};
</script>
