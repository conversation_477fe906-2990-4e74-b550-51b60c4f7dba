<template>
    <ui-view
        type="form"
        :model="model"
        :schema="schema"
        :title="title"
        :extra-fields="['id']"
        :before-validate="beforeValidate"
        :before-submit="beforeSubmit"
        actions="edit,cancel"
        no-inner-padding
        full-height
        @changed="handleChange"
        v-if="initialized"
    >
        <el-tabs v-model="activeTab" class="full-tabs">
            <el-tab-pane name="general" :label="'GENERAL' | t">
                <el-scrollbar>
                    <div class="columns">
                        <div class="column is-half">
                            <ui-legend title="General" />
                            <ui-field name="code" />
                            <ui-field name="name" />
                            <ui-field name="type" :options="typeOptions" translate-labels />
                            <ui-field
                                name="carrierId"
                                collection="logistics.carriers"
                                view="logistics.configuration.carriers"
                                disable-create
                                disable-detail
                                v-show="model.type === 'cargo'"
                            />
                            <ui-field
                                name="serviceProductId"
                                collection="inventory.products"
                                view="inventory.catalog.products"
                                :filters="{type: 'service', isSimple: true}"
                                :extra-fields="['code', 'definition']"
                                :template="'{{code}} - {{definition}}'"
                                disable-create
                                disable-detail
                                v-show="
                                    !!integrationType &&
                                    (integrationType.name === 'enterstore' || integrationType.name === 'shopify')
                                "
                            />
                            <ui-field
                                name="taxId"
                                collection="kernel.taxes"
                                :filters="{scope: 'sale'}"
                                disable-create
                                disable-detail
                                v-show="
                                    !!integrationType &&
                                    (integrationType.name === 'enterstore' || integrationType.name === 'shopify')
                                "
                            />

                            <ui-legend title="Description" class="mt30" />
                            <ui-field name="description" label="hide" :rows="2" />
                        </div>

                        <div class="column is-half">
                            <ui-legend title="Details" />
                            <ui-field
                                name="volumetricWeightFactor"
                                v-show="!!integrationType && integrationType.name === 'enterstore'"
                            />
                            <ui-field
                                name="integrationId"
                                :options="integrationIdOptions"
                                v-show="model.type === 'cargo' && integrationType.name !== 'shopify'"
                                dont-update-label-cases
                            />
                            <ui-field name="isActive" />

                            <ui-legend
                                title="Pricing"
                                class="full-width mt30 mb0"
                                v-show="!!integrationType && integrationType.name === 'enterstore'"
                            />
                            <ui-field
                                name="pricing"
                                :min-empty-rows="1"
                                v-show="!!integrationType && integrationType.name === 'enterstore'"
                            />
                        </div>
                    </div>
                </el-scrollbar>
            </el-tab-pane>

            <el-tab-pane
                name="locations"
                :label="'LOCATIONS' | t"
                :disabled="!!integrationType && integrationType.name !== 'enterstore'"
            >
                <ui-field name="locations" class="mb0" full-height :enable-enlarge="true" />
            </el-tab-pane>

            <el-tab-pane
                name="warehouses"
                :label="'WAREHOUSES' | t"
                :disabled="
                    (!!integrationType && integrationType.name !== 'enterstore') || model.type !== 'store-delivery'
                "
            >
                <ui-field
                    name="warehouses"
                    class="mb0"
                    full-height
                    :enable-enlarge="true"
                    :before-init="beforeWarehousesInit"
                />
            </el-tab-pane>
        </el-tabs>
    </ui-view>
</template>

<script>
import _ from 'lodash';
import Random from 'framework/random';

export default {
    data: () => ({
        model: {},
        activeTab: 'general',
        countries: [],
        integrationType: null,
        integrationCarriers: [],
        initialized: false
    }),

    computed: {
        title() {
            const model = this.model;

            return model.name ? model.name : this.$t('New Delivery Option');
        },
        schema() {
            return {
                id: {
                    type: 'string',
                    label: 'ID'
                },
                code: {
                    type: 'string',
                    label: 'Code'
                },
                type: {
                    type: 'string',
                    label: 'Type',
                    allowed: ['cargo', 'store-delivery'],
                    default: 'cargo'
                },
                name: {
                    type: 'string',
                    label: 'Name'
                },
                carrierId: {
                    type: 'string',
                    label: 'Carrier',
                    required: false
                },
                serviceProductId: {
                    type: 'string',
                    label: 'Service product',
                    required: false
                },
                taxId: {
                    type: 'string',
                    label: 'Tax',
                    required: false
                },
                description: {
                    type: 'string',
                    label: 'Description',
                    required: false
                },
                integrationId: {
                    type: 'string',
                    label: 'Integration ID',
                    required: false
                },
                volumetricWeightFactor: {
                    type: 'decimal',
                    label: 'Volumetric weight factor',
                    default: 5000
                },
                isActive: {
                    type: 'boolean',
                    label: 'Is active',
                    default: true
                },
                warehouses: {
                    type: [
                        {
                            warehouseId: {
                                type: 'string',
                                label: 'Warehouse',
                                column: {
                                    populate: 'warehouse'
                                },
                                editor: {
                                    collection: 'inventory.warehouses',
                                    extraFields: ['shortName'],
                                    template: '{{shortName}} - {{name}}'
                                }
                            },
                            storeName: {
                                type: 'string',
                                label: 'Store name'
                            }
                        }
                    ],
                    default: []
                },
                pricing: {
                    type: [
                        {
                            minVW: {
                                type: 'decimal',
                                label: 'Min Vol. Weight',
                                default: 0
                            },
                            maxVW: {
                                type: 'decimal',
                                label: 'Max Vol. Weight',
                                default: 0
                            },
                            price: {
                                type: 'decimal',
                                label: 'Price',
                                default: 0
                            }
                        }
                    ],
                    default: []
                },
                locations: {
                    type: [
                        {
                            countryId: {
                                type: 'string',
                                label: 'Country',
                                editor: {
                                    options: () =>
                                        this.countries.map(country => ({
                                            value: country._id,
                                            label: country.name
                                        }))
                                }
                            },
                            city: {
                                type: 'string',
                                label: 'City',
                                required: false
                            },
                            district: {
                                type: 'string',
                                label: 'District',
                                required: false
                            },
                            minVW: {
                                type: 'decimal',
                                label: 'Min Vol. Weight',
                                default: 0
                            },
                            maxVW: {
                                type: 'decimal',
                                label: 'Max Vol. Weight',
                                default: 0
                            },
                            price: {
                                type: 'decimal',
                                label: 'Price',
                                default: 0
                            },
                            isActive: {
                                type: 'boolean',
                                label: 'Is active',
                                default: true,
                                column: {
                                    width: 75
                                }
                            }
                        }
                    ],
                    default: []
                }
            };
        },
        typeOptions() {
            if (!!this.integrationType && this.integrationType.name !== 'enterstore') {
                return [{value: 'cargo', label: 'Cargo'}];
            }

            return [
                {value: 'cargo', label: 'Cargo'},
                {value: 'store-delivery', label: 'Store delivery'}
            ];
        },
        integrationIdOptions() {
            const integrationCarriers = this.integrationCarriers ?? [];

            return integrationCarriers.map(c => ({
                value: c.id,
                label: c.name
            }));
        }
    },

    methods: {
        async beforeWarehousesInit(items) {
            let warehouseIds = [];
            let warehouses = [];
            items.forEach(item => {
                if (item.warehouseId) warehouseIds.push(item.warehouseId);
            });
            if (warehouseIds.length > 0) {
                warehouses = await this.$collection('inventory.warehouses').find({
                    _id: {$in: _.uniq(warehouseIds)},
                    $select: ['_id', 'shortName', 'name'],
                    $disableActiveCheck: true,
                    $disableSoftDelete: true
                });
            }

            return items.map(item => {
                if (item.warehouseId && warehouses.length > 0) {
                    item.warehouse = warehouses.find(warehouse => warehouse._id === item.warehouseId);
                }
                return item;
            });
        },
        async beforeValidate(model) {
            if (!model.id) {
                model.id = Random.id(16);
            }
            if (model.type === 'cargo' && _.isEmpty(model.carrierId)) {
                throw new this.$app.errors.Unprocessable({
                    message: this.$t('{{label}} is required', {label: this.$t('Carrier')}),
                    field: 'carrierId'
                });
            }

            if (this.integrationType.name === 'enterstore' || this.integrationType.name === 'shopify') {
                if (!model.serviceProductId) {
                    throw new this.$app.errors.Unprocessable({
                        message: this.$t('{{label}} is required', {label: this.$t('Service product')}),
                        field: 'serviceProductId'
                    });
                }

                if (!model.taxId) {
                    throw new this.$app.errors.Unprocessable({
                        message: this.$t('{{label}} is required', {label: this.$t('Tax')}),
                        field: 'taxId'
                    });
                }
            }

            return model;
        },
        async beforeSubmit(model) {
            if (model.type === 'store-delivery') {
                model.carrierId = '';
                model.integrationId = '';
            }

            const store = await this.$collection('ecommerce.stores').findOne({_id: this.$params('store')._id});
            const storeDeliveryOption = ((store || {}).deliveryOptions || []).find(
                option => option.type === 'store-delivery'
            );
            if (storeDeliveryOption && storeDeliveryOption.id !== model.id) {
                throw new this.$app.errors.Unprocessable({
                    message: this.$t('There can only be one delivery option with a delivery type of ‘Store Delivery’')
                });
            }

            return model;
        },
        async handleChange(model, field) {},
        fetchIntegrationIdSuggestions() {
            return [];
        }
    },

    async created() {
        this.$params('loading', true);

        this.countries = await this.$collection('kernel.countries').find({});
        this.integrationType = this.$params('integrationType');
        try {
            this.integrationCarriers = await this.$rpc('ecommerce.get-carriers', {
                storeId: this.$params('store')._id
            });
        } catch (error) {}

        this.initialized = true;

        this.$params('loading', false);
    }
};
</script>
