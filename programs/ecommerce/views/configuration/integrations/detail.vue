<template>
    <ui-view
        type="form"
        class="ui-tabbed-form"
        collection="ecommerce.stores"
        method="ecommerce.save-integration"
        :model="model"
        :title="title"
        :extra-fields="extraFields"
        :before-init="beforeInit"
        :before-validate="beforeValidate"
        :before-submit="beforeSubmit"
        actions="edit,cancel"
        :extra-actions="extraActions"
        no-inner-padding
        full-height
        progress-id="ecommerce.configuration.integrations.detail"
        @changed="handleChange"
        v-if="initialized"
    >
        <div class="form-wrapper">
            <div class="form-content">
                <el-tabs v-model="activeTab" class="full-tabs">
                    <el-tab-pane name="general" :label="'GENERAL' | t">
                        <integration-tab-general
                            :model="model"
                            :integration-types="integrationTypes"
                            :integration-type="integrationType"
                            :e-invoice-types="eInvoiceTypes"
                            :export-e-invoice-type-condition-options="exportEInvoiceTypeConditionOptions"
                            :e-archive-invoice-numbering-ids="eArchiveInvoiceNumberingIds"
                            :e-invoice-numbering-ids="eInvoiceNumberingIds"
                        />
                    </el-tab-pane>

                    <template v-if="!!$params('id') && !!integrationType && integrationType.name === 'enterstore'">
                        <el-tab-pane name="store-navigation" :label="'NAVIGATION' | t">
                            <integration-tab-store-navigation
                                :model="model"
                                :key="integrationTypeKey"
                                v-if="activeTab === 'store-navigation'"
                            />
                        </el-tab-pane>

                        <el-tab-pane name="store-pages" :label="'PAGES' | t">
                            <integration-tab-store-pages
                                :model="model"
                                :key="integrationTypeKey"
                                v-if="activeTab === 'store-pages'"
                            />
                        </el-tab-pane>

                        <el-tab-pane name="delivery-options" :label="'DELIVERY OPTIONS' | t" :disabled="!$params('id')">
                            <ui-field
                                class="mb0"
                                name="deliveryOptions"
                                :columns="deliveryOptionsColumns"
                                view="ecommerce.configuration.integrations.detail.delivery-option"
                                actions="create,delete"
                                field-type="relation"
                                enable-ordering
                                :update-params="updateDeliveryOptionsParams"
                                v-if="activeTab === 'delivery-options'"
                            />
                        </el-tab-pane>

                        <el-tab-pane name="payment-methods" :label="'PAYMENT METHODS' | t" :disabled="!$params('id')">
                            <ui-field
                                class="mb0"
                                name="paymentMethods"
                                :columns="paymentMethodsColumns"
                                view="ecommerce.configuration.integrations.detail.payment-method"
                                actions="create,delete"
                                field-type="relation"
                                enable-ordering
                                :update-params="updatePaymentMethodsParams"
                                v-if="activeTab === 'payment-methods'"
                            />
                        </el-tab-pane>

                        <el-tab-pane name="store-texts" :label="$t('TEXTS')">
                            <integration-tab-store-texts :model="model" v-if="activeTab === 'store-texts'" />
                        </el-tab-pane>

                        <el-tab-pane
                            name="policies"
                            :label="'POLICIES' | t"
                            v-if="model.useB2BPolicies && integrationType.name === 'enterstore'"
                        >
                            <integration-tab-policies
                                :model="model"
                                :key="integrationTypeKey"
                                v-if="activeTab === 'policies'"
                            />
                        </el-tab-pane>
                    </template>

                    <template v-if="!!$params('id') && !!integrationType && integrationType.name !== 'enterstore'">
                        <el-tab-pane
                            name="delivery-options"
                            :label="'DELIVERY OPTIONS' | t"
                            :disabled="!$params('id') || !integrationType.needsCargoCompanyMappings"
                        >
                            <ui-field
                                class="mb0"
                                name="deliveryOptions"
                                :columns="deliveryOptionsColumns"
                                view="ecommerce.configuration.integrations.detail.delivery-option"
                                actions="create,delete"
                                field-type="relation"
                                enable-ordering
                                :update-params="updateDeliveryOptionsParams"
                                v-if="activeTab === 'delivery-options'"
                            />
                        </el-tab-pane>

                        <el-tab-pane
                            name="payment-methods"
                            :label="'PAYMENT METHODS' | t"
                            :disabled="!$params('id') || !integrationType.needsProductPaymentMethodMappings"
                        >
                            <ui-field
                                class="mb0"
                                name="paymentMethods"
                                :columns="paymentMethodsColumns"
                                view="ecommerce.configuration.integrations.detail.payment-method"
                                actions="create,delete"
                                field-type="relation"
                                enable-ordering
                                :update-params="updatePaymentMethodsParams"
                                v-if="activeTab === 'payment-methods'"
                            />
                        </el-tab-pane>

                        <el-tab-pane
                            name="category-mappings"
                            :label="'CATEGORY MAPPINGS' | t"
                            :disabled="!integrationType.needsProductCategoryMappings"
                        >
                            <integration-tab-category-mappings
                                :model="model"
                                :key="integrationTypeKey"
                                :integration-types="integrationTypes"
                                v-if="
                                    activeTab === 'category-mappings' && !!integrationType.needsProductCategoryMappings
                                "
                            />
                        </el-tab-pane>

                        <el-tab-pane
                            name="brand-mappings"
                            :label="'BRAND MAPPINGS' | t"
                            :disabled="!integrationType.needsProductBrandMappings"
                        >
                            <integration-tab-brand-mappings
                                :model="model"
                                :key="integrationTypeKey"
                                :integration-types="integrationTypes"
                                v-if="activeTab === 'brand-mappings' && !!integrationType.needsProductBrandMappings"
                            />
                        </el-tab-pane>

                        <el-tab-pane
                            name="attribute-mappings"
                            :label="'ATTRIBUTE MAPPINGS' | t"
                            :disabled="
                                !integrationType.needsProductAttributeMappings ||
                                integrationType.attributesAreCategoryBased
                            "
                        >
                            <integration-tab-attribute-mappings
                                :model="model"
                                :key="integrationTypeKey"
                                :integration-types="integrationTypes"
                                v-if="
                                    activeTab === 'attribute-mappings' &&
                                    !!integrationType.needsProductAttributeMappings &&
                                    !integrationType.attributesAreCategoryBased
                                "
                            />
                        </el-tab-pane>
                    </template>
                </el-tabs>
            </div>
        </div>
    </ui-view>
</template>

<script>
import _ from 'lodash';
import integrationTypes from '../../../integrations/integration-types';
import IntegrationTabGeneral from './detail/_tab-general';
import IntegrationTabStoreNavigation from './detail/_tab-store-navigation';
import IntegrationTabStorePages from './detail/_tab-store-pages';
import IntegrationTabStoreTexts from './detail/_tab-store-texts';
import IntegrationTabCategoryMappings from './detail/_tab-category-mappings';
import IntegrationTabBrandMappings from './detail/_tab-brand-mappings';
import IntegrationTabAttributeMappings from './detail/_tab-atrribute-mappings';
import IntegrationTabPolicies from './detail/_tab-policies';
import {tr} from 'faker/lib/locales';

export default {
    data: () => ({
        model: {},
        integrationTypes,
        extraFields: [
            'paymentMethods',
            'deliveryOptions',
            'categoryMappings',
            'brandMappings',
            'attributeMappingsPayload',
            'texts'
        ],
        eInvoiceTypes: [],
        exportEInvoiceTypeConditionOptions: [],
        eArchiveInvoiceNumberingIds: [],
        eInvoiceNumberingIds: [],
        activeTab: 'general',
        initialized: false
    }),

    computed: {
        title() {
            const model = this.model;

            if (this.$params('id')) {
                return model.name ? model.name : '';
            }

            return this.$t('New Integration');
        },
        extraActions() {
            const self = this;

            return [
                // {
                //     name: 'download-payment-methods',
                //     title: 'Download Payment Methods',
                //     icon: 'fal fa-cloud-download',
                //     handler: this.handleDownloadPaymentMethods,
                //     disabled() {
                //         return (
                //             !self.$params('id') ||
                //             !self.integrationType ||
                //             !self.integrationType.needsPaymentMethodMappings
                //         );
                //     }
                // },
                // {
                //     name: 'download-cargo-companies',
                //     title: 'Download Cargo Companies',
                //     icon: 'fal fa-cloud-download',
                //     handler: this.handleDownloadCargoCompanies,
                //     disabled() {
                //         return (
                //             !self.$params('id') ||
                //             !self.integrationType ||
                //             !self.integrationType.needsCargoCompanyMappings
                //         );
                //     }
                // },
                {
                    name: 'download-categories',
                    title: 'Download Categories',
                    icon: 'fal fa-cloud-download',
                    handler: this.handleDownloadCategories,
                    disabled() {
                        return (
                            !self.$params('id') ||
                            !self.integrationType ||
                            !self.integrationType.needsProductCategoryMappings
                        );
                    }
                },
                {
                    name: 'download-brands',
                    title: 'Download Brands',
                    icon: 'fal fa-cloud-download',
                    handler: this.handleDownloadBrands,
                    disabled() {
                        return (
                            !self.$params('id') ||
                            !self.integrationType ||
                            !self.integrationType.needsProductBrandMappings
                        );
                    }
                }
            ];
        },
        integrationType() {
            return this.integrationTypes.find(i => i.name === this.model.integrationType);
        },
        integrationTypeKey() {
            return JSON.stringify(this.integrationType);
        },
        deliveryOptionsColumns() {
            return [
                {
                    field: 'code',
                    label: 'Code',
                    maxWidth: 120
                },
                {field: 'name', label: 'Name'},
                {field: 'description', label: 'Description'},
                {
                    field: 'isActive',
                    type: 'boolean',
                    label: 'Is Active',
                    isEditable: false,
                    maxWidth: 100
                }
            ];
        },
        paymentMethodsColumns() {
            return [
                {
                    field: 'code',
                    label: 'Code',
                    maxWidth: 120
                },
                {
                    field: 'paymentType',
                    label: 'Payment type',
                    valueLabels: [
                        {value: 'credit-card', label: 'Credit card'},
                        {value: 'money-order', label: 'Money order'},
                        {value: 'cash-on-delivery', label: 'Cash on delivery'},
                        {value: 'open-account', label: 'Open account'}
                    ],
                    translateLabels: true,
                    maxWidth: 150
                },
                {field: 'name', label: 'Name'},
                {field: 'description', label: 'Description'},
                {
                    field: 'isActive',
                    type: 'boolean',
                    label: 'Is Active',
                    isEditable: false,
                    maxWidth: 100
                }
            ];
        }
    },

    methods: {
        async handleDownloadPaymentMethods() {
            this.$params('loading', true);

            try {
                await this.$rpc('ecommerce.download-payment-methods', this.$params('id'));
            } catch (error) {
                this.$program.message('error', error.message);
            }

            this.$params('loading', false);
        },
        async handleDownloadCargoCompanies() {
            this.$params('loading', true);

            try {
                await this.$rpc('ecommerce.download-cargo-companies', this.$params('id'));
            } catch (error) {
                this.$program.message('error', error.message);
            }

            this.$params('loading', false);
        },
        async handleDownloadCategories() {
            this.$params('loading', true);

            try {
                await this.$rpc('ecommerce.download-categories', this.$params('id'));
            } catch (error) {
                this.$program.message('error', error.message);
            }

            this.$params('loading', false);
        },
        async handleDownloadBrands() {
            this.$params('loading', true);

            try {
                await this.$rpc('ecommerce.download-brands', this.$params('id'));
            } catch (error) {
                this.$program.message('error', error.message);
            }

            this.$params('loading', false);
        },
        async beforeInit(model) {
            const company = this.$store.getters['session/company'];

            if (!model.languageId) model.languageId = company.languageId;
            if (!model.currencyId) model.currencyId = company.currencyId;
            if (!model.customerGroupId) {
                const group = await this.$collection('kernel.partner-groups').findOne({
                    code: 'general-customer',
                    $select: ['_id'],
                    $disableActiveCheck: true,
                    $disableSoftDelete: true
                });

                model.customerGroupId = group._id;
            }
            if (!model.branchId) model.branchId = this.$user.branchIds[0];
            if (!model.warehouseId) {
                const warehouse = await this.$collection('inventory.warehouses').findOne({
                    branchId: model.branchId,
                    $select: ['_id']
                });

                if (_.isPlainObject(warehouse)) {
                    model.warehouseId = warehouse._id;
                }
            }

            if (!model.exportInvoiceScenario) {
                model.exportInvoiceScenario = 'e-archive-invoice';
            }
            if (!model.exportEInvoiceTypeId) {
                model.exportEInvoiceTypeId = (this.eInvoiceTypes.find(type => type.code === 'ISTISNA') || {})._id;
            }
            if (!model.exportEInvoiceTypeConditionCode) {
                model.exportEInvoiceTypeConditionCode = '301';
            }

            if (!!model.exportEInvoiceTypeId) {
                const eInvoiceType = this.eInvoiceTypes.find(et => et._id === model.exportEInvoiceTypeId);

                if (_.isObject(eInvoiceType)) {
                    this.exportEInvoiceTypeConditionOptions = (eInvoiceType.conditions || []).map(c => ({
                        value: c.code,
                        label: `${c.code} - ${c.name}` + (_.isNumber(c.rate) ? ` - %${c.rate}` : '')
                    }));
                } else {
                    this.exportEInvoiceTypeConditionOptions = [];
                }
            } else {
                this.exportEInvoiceTypeConditionOptions = [];
            }

            if (!!this.$setting('system.multiBranch')) {
                const branches = await this.$collection('kernel.branches').find({
                    $select: ['eArchiveInvoiceNumberingId', 'eInvoiceNumberingId'],
                    $disableActiveCheck: true,
                    $disableSoftDelete: true
                });

                this.eArchiveInvoiceNumberingIds = branches.map(branch => branch.eArchiveInvoiceNumberingId);
                this.eInvoiceNumberingIds = branches.map(branch => branch.eInvoiceNumberingId);
            }

            return model;
        },
        async beforeValidate(model) {
            if (!model.automaticOrderApproval && model.automaticDelivery) {
                throw new this.$app.errors.Unprocessable({
                    message: this.$t(
                        'You cannot create an automatic delivery without the automatic order approval setting being activated!'
                    )
                });
            }
            if (model.automaticDelivery && !model.defaultDeliveryOptionId) {
                throw new this.$app.errors.Unprocessable({
                    message: this.$t('Default delivery option is mandatory when automatic delivery is enabled!')
                });
            }

            let isAdvancedDeliveryPlanningEnabled = !!this.$setting('inventory.advancedDeliveryPlanning');
            if (isAdvancedDeliveryPlanningEnabled) {
                const warehouse = await this.$collection('inventory.warehouses').findOne({
                    _id: model.warehouseId,
                    $select: ['_id', 'advancedDeliveryPlanning'],
                    $disableActiveCheck: true,
                    $disableSoftDelete: true
                });

                if (!!warehouse && Array.isArray(warehouse.advancedDeliveryPlanning)) {
                    isAdvancedDeliveryPlanningEnabled = warehouse.advancedDeliveryPlanning.includes('sale');
                }
            }

            if (
                model.integrationType !== 'enterstore' &&
                isAdvancedDeliveryPlanningEnabled &&
                model.automaticDelivery
            ) {
                throw new this.$app.errors.Unprocessable({
                    message: this.$t(
                        'You cannot create an automatic delivery when the advanced delivery planning feature is on!'
                    )
                });
            }

            if (!model.exportEInvoiceTypeConditionCode && this.exportEInvoiceTypeConditionOptions.length > 0) {
                throw new this.$app.errors.Unprocessable({
                    message: this.$t('{{label}} is required', {
                        label: this.$t('E-Invoice type condition')
                    }),
                    field: 'exportEInvoiceTypeConditionCode'
                });
            }
            if (model.exportInvoiceScenario && !model.exportEInvoiceTypeId) {
                throw new this.$app.errors.Unprocessable({
                    message: this.$t('{{label}} is required', {
                        label: this.$t('E-Invoice type')
                    }),
                    field: 'exportEInvoiceTypeId'
                });
            }

            if (model.integrationType === 'enterstore' && !model.returnWarehouseId) {
                throw new this.$app.errors.Unprocessable({
                    message: this.$t('{{label}} is required', {
                        label: this.$t('Return warehouse')
                    }),
                    field: 'returnWarehouseId'
                });
            }

            return model;
        },
        async beforeSubmit(model) {
            return model;
        },
        async handleChange(model, field) {
            if (field === 'branchId') {
                this.model.warehouseId = '';

                if (model.integrationType === 'enterstore') {
                    this.model.returnWarehouseId = '';
                }
            } else if (field === 'integrationType') {
                const integrationType = this.integrationTypes.find(i => i.name === model.integrationType);

                if (_.isPlainObject(integrationType.params)) {
                    this.model.integrationParams = JSON.stringify(integrationType.params, null, 4);
                }

                if (integrationType.name !== 'enterstore') {
                    this.model.taxApplication = 'tax-included';
                }

                if (integrationType.name === 'shopify') {
                    this.model.automaticOrderApproval = true;
                }

                if (!!integrationType.barcodeMatchingOnly) {
                    this.model.barcodeMatchingOnly = true;
                }
            } else if (field === 'automaticOrderApproval') {
                if (!this.model.automaticOrderApproval) {
                    this.model.updateMarketplaceOrderStatus = false;
                    this.model.automaticDelivery = false;
                    this.model.automaticDeliveryConfirmation = false;
                    this.model.automaticShippingOrder = false;
                    this.model.automaticInvoice = false;
                    this.model.automaticInvoiceApproval = false;
                    this.model.automaticInvoiceSending = false;
                }
            } else if (field === 'exportEInvoiceTypeId') {
                if (!!model.exportEInvoiceTypeId) {
                    const eInvoiceType = this.eInvoiceTypes.find(et => et._id === model.exportEInvoiceTypeId);

                    if (_.isObject(eInvoiceType)) {
                        this.exportEInvoiceTypeConditionOptions = (eInvoiceType.conditions || []).map(c => ({
                            value: c.code,
                            label: `${c.code} - ${c.name}` + (_.isNumber(c.rate) ? ` - %${c.rate}` : '')
                        }));
                    } else {
                        this.exportEInvoiceTypeConditionOptions = [];
                    }
                } else {
                    this.exportEInvoiceTypeConditionOptions = [];
                }

                this.model.exportEInvoiceTypeConditionCode = '';
            } else if (field === 'exportEInvoiceTypeId') {
                this.model.exportEInvoiceTypeConditionCode = '';
            } else if (field === 'exportInvoiceScenario') {
                this.model.exportEInvoiceTypeId = '';
            } else if (field === 'automaticDelivery') {
                if (!this.model.automaticDelivery) {
                    this.model.automaticDeliveryConfirmation = false;
                    this.model.automaticShippingOrder = false;
                }
            } else if (field === 'automaticDeliveryConfirmation') {
                if (!this.model.automaticDeliveryConfirmation) {
                    this.model.automaticShippingOrder = false;
                }
            } else if (field === 'automaticInvoice') {
                if (!this.model.automaticInvoice) {
                    this.model.automaticInvoiceApproval = false;
                    this.model.automaticInvoiceSending = false;
                }
            } else if (field === 'automaticInvoiceApproval') {
                if (!this.model.automaticInvoiceApproval) {
                    this.model.automaticInvoiceSending = false;
                }
            }
        },
        updateDeliveryOptionsParams(params, action, row) {
            params.store = {
                ...this.model,
                _id: this.$params('id')
            };
            params.integrationType = this.integrationType;

            return params;
        },
        updatePaymentMethodsParams(params, action, row) {
            params.store = {
                ...this.model,
                _id: this.$params('id')
            };
            params.integrationType = this.integrationType;

            return params;
        }
    },

    async created() {
        this.$params('loading', true);

        this.eInvoiceTypes = await this.$collection('eops.e-invoice-types').find();

        this.initialized = true;

        this.$params('loading', false);
    },

    components: {
        IntegrationTabGeneral,
        IntegrationTabStoreNavigation,
        IntegrationTabStorePages,
        IntegrationTabStoreTexts,
        IntegrationTabCategoryMappings,
        IntegrationTabBrandMappings,
        IntegrationTabAttributeMappings,
        IntegrationTabPolicies
    }
};
</script>
