export default {
    name: 'stores',
    title: 'Stores',
    order: true,
    cache: true,
    schema: {
        // General
        code: {
            type: 'string',
            label: 'Code'
        },
        name: {
            type: 'string',
            label: 'Name'
        },

        // Integration
        integrationType: {
            type: 'string',
            label: 'Integration type'
        },
        integrationParams: {
            type: 'string',
            label: 'Integration params',
            required: false
        },

        // Pricing
        pricingPolicy: {
            type: 'string',
            label: 'Pricing policy',
            default: 'manuel'
        },
        taxApplication: {
            type: 'string',
            label: 'Tax application',
            default: 'tax-included'
        },
        priceListId: {
            type: 'string',
            label: 'Price list',
            required: false
        },
        discountedPriceListId: {
            type: 'string',
            label: 'Discounted price list',
            required: false
        },
        useProductImportPrices: {
            type: 'boolean',
            label: 'Use product import prices',
            default: false
        },
        usePosBankMatching: {
            type: 'boolean',
            label: 'Use POS bank matching',
            default: false
        },
        applyZeroTax: {
            type: 'boolean',
            label: 'Apply zero tax',
            default: false
        },
        useFixedExchangeRates: {
            type: 'boolean',
            label: 'Use fixed exchange rates',
            default: false
        },
        useSellout: {
            type: 'boolean',
            label: 'Use sellout system',
            default: false
        },

        // Stock
        stockPolicy: {
            type: 'string',
            label: 'Stock policy',
            default: 'use-available-stock'
        },
        useProductImportStocks: {
            type: 'boolean',
            label: 'Use product import stocks',
            default: false
        },
        publishOutOfStockItems: {
            type: 'boolean',
            label: 'Publish out-of-stock items',
            default: false
        },
        useAlternateProductsWhenStockRunsOut: {
            type: 'boolean',
            label: 'Use alternate products when kit stocks run out',
            default: false
        },

        // Details
        currencyId: {
            type: 'string',
            label: 'Currency'
        },
        languageId: {
            type: 'string',
            label: 'Language'
        },
        customerGroupId: {
            type: 'string',
            label: 'Customer group'
        },
        branchId: {
            type: 'string',
            label: 'Branch office'
        },
        warehouseId: {
            type: 'string',
            label: 'Warehouse'
        },
        returnWarehouseId: {
            type: 'string',
            label: 'Return warehouse',
            required: false
        },
        salesDocumentTypeId: {
            type: 'string',
            label: 'Sales document type'
        },
        sourceId: {
            type: 'string',
            label: 'Source',
            required: false
        },
        communicationChannelId: {
            type: 'string',
            label: 'Communication channel',
            required: false
        },
        website: {
            type: 'string',
            label: 'Website',
            required: false
        },
        email: {
            type: 'string',
            label: 'Email address',
            regexp: 'EmailWithTLD',
            required: false
        },
        isActive: {
            type: 'boolean',
            label: 'Is active',
            default: true
        },
        tagManagerId: {
            type: 'string',
            label: 'Google tag manager id',
            required: false
        },
        evaluateVariations: {
            type: 'boolean',
            label: 'Evaluate variations',
            default: false
        },
        automaticOrderApproval: {
            type: 'boolean',
            label: 'Automatic order approval',
            default: false
        },
        updateMarketplaceOrderStatus: {
            type: 'boolean',
            label: 'Update marketplace order status',
            default: false
        },
        automaticDelivery: {
            type: 'boolean',
            label: 'Create automatic delivery',
            default: false
        },
        barcodeMatchingOnly: {
            type: 'boolean',
            label: 'Only work with the barcode matching model',
            default: false
        },
        automaticInvoice: {
            type: 'boolean',
            label: 'Create automatic invoice',
            default: false
        },
        automaticOrderCancellation: {
            type: 'boolean',
            label: 'Automatic order cancellation',
            default: false
        },
        automaticDeliveryConfirmation: {
            type: 'boolean',
            label: 'Automatic delivery confirmation',
            default: false
        },
        automaticShippingOrder: {
            type: 'boolean',
            label: 'Create automatic shipping order',
            default: false
        },
        automaticInvoiceApproval: {
            type: 'boolean',
            label: 'Automatic invoice approval',
            default: false
        },
        automaticInvoiceSending: {
            type: 'boolean',
            label: 'Automatic invoice sending',
            default: false
        },
        useB2BPolicies: {
            type: 'boolean',
            label: 'Use B2B policies',
            default: false
        },
        sendB2bMail: {
            type: 'boolean',
            label: 'Send B2B order mail',
            default: false
        },
        createDraftOrder: {
            type: 'boolean',
            label: 'Create draft order',
            default: false
        },
        allowedPartnerGroupIds: {
            type: ['string'],
            label: 'Allowed partner groups',
            default: []
        },
        visibleStockWarehouseIds: {
            type: ['string'],
            label: 'Visible stock warehouses',
            default: []
        },

        // Payment Settings.
        defaultPaymentMethodId: {
            type: 'string',
            label: 'Default payment method',
            required: false
        },

        // Delivery Settings.
        defaultDeliveryOptionId: {
            type: 'string',
            label: 'Default delivery option',
            required: false
        },
        defaultVolumetricWeight: {
            type: 'decimal',
            label: 'Default volumetric weight',
            default: 1
        },
        calculateDeliveryPricePerItem: {
            type: 'boolean',
            label: 'Calculate delivery price per item',
            default: true
        },
        showEstimatedDeliveryDuration: {
            type: 'boolean',
            label: 'Show estimated delivery duration',
            default: true
        },
        estimatedDeliveryDuration: {
            type: 'decimal',
            label: 'Estimated delivery duration',
            default: 3
        },
        defaultReturnOrderDuration: {
            type: 'decimal',
            label: 'Default return order duration',
            default: 14
        },
        storageServiceProductId: {
            type: 'string',
            label: 'Storage service product',
            required: false
        },
        storageTaxId: {
            type: 'string',
            label: 'Storage tax',
            required: false
        },
        disableReturnCreation: {
            type: 'boolean',
            label: 'Disable return creation',
            default: false
        },

        // Invoice Settings.
        exportInvoiceScenario: {
            type: 'string',
            label: 'Invoice scenario',
            required: false
        },
        exportEInvoiceTypeId: {
            type: 'string',
            label: 'E-Invoice type',
            required: false
        },
        exportEInvoiceTypeConditionCode: {
            type: 'string',
            label: 'E-Invoice type condition',
            required: false
        },
        eInvoiceNumberingId: {
            type: 'string',
            label: 'E-Invoice numbering',
            required: false
        },
        eInvoiceTemplateId: {
            type: 'string',
            label: 'E-Invoice template',
            required: false
        },
        eArchiveInvoiceNumberingId: {
            type: 'string',
            label: 'E-Archive invoice numbering',
            required: false
        },
        eArchiveInvoiceTemplateId: {
            type: 'string',
            label: 'E-Archive invoice template',
            required: false
        },

        // Payment methods
        paymentMethods: {
            type: [
                {
                    id: {
                        type: 'string',
                        label: 'ID'
                    },
                    code: {
                        type: 'string',
                        label: 'Code'
                    },
                    name: {
                        type: 'string',
                        label: 'Name'
                    },
                    paymentType: {
                        type: 'string',
                        label: 'Payment type'
                    },
                    journalId: {
                        type: 'string',
                        label: 'Account',
                        required: false
                    },
                    description: {
                        type: 'string',
                        label: 'Description',
                        required: false
                    },

                    paymentTermId: {
                        type: 'string',
                        label: 'Payment term'
                    },
                    serviceProductId: {
                        type: 'string',
                        label: 'Service product',
                        required: false
                    },
                    taxId: {
                        type: 'string',
                        label: 'Tax',
                        required: false
                    },
                    cashOnDeliveryServiceFee: {
                        type: 'decimal',
                        label: 'Cash on delivery service fee',
                        default: 0
                    },
                    isActive: {
                        type: 'boolean',
                        label: 'Is active',
                        default: true
                    },

                    locations: {
                        type: [
                            {
                                countryId: {
                                    type: 'string',
                                    label: 'Country'
                                },
                                city: {
                                    type: 'string',
                                    label: 'City',
                                    required: false
                                },
                                isActive: {
                                    type: 'boolean',
                                    label: 'Is active',
                                    default: true
                                }
                            }
                        ],
                        default: []
                    }
                }
            ],
            default: []
        },

        // Delivery options
        deliveryOptions: {
            type: [
                {
                    id: {
                        type: 'string',
                        label: 'ID'
                    },
                    code: {
                        type: 'string',
                        label: 'Code'
                    },
                    type: {
                        type: 'string',
                        label: 'Type',
                        allowed: ['cargo', 'store-delivery'],
                        default: 'cargo'
                    },
                    name: {
                        type: 'string',
                        label: 'Name'
                    },
                    carrierId: {
                        type: 'string',
                        label: 'Carrier',
                        required: false
                    },
                    serviceProductId: {
                        type: 'string',
                        label: 'Service product',
                        required: false
                    },
                    taxId: {
                        type: 'string',
                        label: 'Tax',
                        required: false
                    },
                    description: {
                        type: 'string',
                        label: 'Description',
                        required: false
                    },
                    integrationId: {
                        type: 'string',
                        label: 'Integration ID',
                        required: false
                    },

                    volumetricWeightFactor: {
                        type: 'decimal',
                        label: 'Volumetric weight factor',
                        default: 5000
                    },
                    isActive: {
                        type: 'boolean',
                        label: 'Is active',
                        default: true
                    },
                    warehouses: {
                        type: [
                            {
                                warehouseId: {
                                    type: 'string',
                                    label: 'Warehouse'
                                },
                                storeName: {
                                    type: 'string',
                                    label: 'Store name'
                                }
                            }
                        ],
                        default: []
                    },
                    pricing: {
                        type: [
                            {
                                minVW: {
                                    type: 'decimal',
                                    label: 'Min Vol. Weight',
                                    default: 0
                                },
                                maxVW: {
                                    type: 'decimal',
                                    label: 'Max Vol. Weight',
                                    default: 0
                                },
                                price: {
                                    type: 'decimal',
                                    label: 'Price',
                                    default: 0
                                }
                            }
                        ],
                        default: []
                    },
                    locations: {
                        type: [
                            {
                                countryId: {
                                    type: 'string',
                                    label: 'Country'
                                },
                                city: {
                                    type: 'string',
                                    label: 'City',
                                    required: false
                                },
                                district: {
                                    type: 'string',
                                    label: 'City',
                                    required: false
                                },
                                minVW: {
                                    type: 'decimal',
                                    label: 'Min Vol. Weight',
                                    default: 0
                                },
                                maxVW: {
                                    type: 'decimal',
                                    label: 'Max Vol. Weight',
                                    default: 0
                                },
                                price: {
                                    type: 'decimal',
                                    label: 'Price',
                                    default: 0
                                },
                                isActive: {
                                    type: 'boolean',
                                    label: 'Is active',
                                    default: true
                                }
                            }
                        ],
                        default: []
                    }
                }
            ],
            default: []
        },

        // Brand mappings.
        brandMappings: {
            type: [
                {
                    integrationBrandId: {
                        type: 'string',
                        label: 'Integration brand'
                    },
                    erpBrandId: {
                        type: 'string',
                        label: 'ERP brand'
                    }
                }
            ],
            default: []
        },

        // Category mappings.
        categoryMappings: {
            type: [
                {
                    integrationCategoryId: {
                        type: 'string',
                        label: 'Integration category'
                    },
                    erpCategoryId: {
                        type: 'string',
                        label: 'ERP category'
                    },
                    attributeMappingsPayload: {
                        type: 'object',
                        blackbox: true,
                        required: false
                    }
                }
            ],
            default: []
        },

        // Attribute mappings payload.
        attributeMappingsPayload: {
            type: 'object',
            blackbox: true,
            required: false
        },

        // Text.
        texts: {
            type: [
                {
                    type: 'object',
                    blackbox: true,
                    required: false
                }
            ],
            default: []
        },
        // salesContractText: {
        //     type: 'string',
        //     required: false
        // },
        // termsOfMembershipText: {
        //     type: 'string',
        //     required: false
        // },
        // clarificationText: {
        //     type: 'string',
        //     required: false
        // },
        // commentPostingCriteriaText: {
        //     type: 'string',
        //     required: false
        // },

        // Reports.
        totalProductCount: {
            type: 'decimal',
            default: 0
        },
        unPublishedProductCount: {
            type: 'decimal',
            default: 0
        },
        publishedProductCount: {
            type: 'decimal',
            default: 0
        },

        // PCM.
        pcmModelId: {
            type: 'string',
            label: 'PCM Model',
            required: false
        }
    },
    attributes: {
        currency: {
            collection: 'kernel.currencies',
            parentField: 'currencyId',
            childField: '_id'
        },
        language: {
            collection: 'kernel.languages',
            parentField: 'languageId',
            childField: '_id'
        },
        customerGroup: {
            collection: 'kernel.partner-groups',
            parentField: 'customerGroupId',
            childField: '_id'
        },
        branch: {
            collection: 'kernel.branches',
            parentField: 'branchId',
            childField: '_id'
        },
        warehouse: {
            collection: 'inventory.warehouses',
            parentField: 'warehouseId',
            childField: '_id'
        },
        salesDocumentType: {
            collection: 'sale.sales-document-types',
            parentField: 'salesDocumentTypeId',
            childField: '_id'
        },
        source: {
            collection: 'sale.sources',
            parentField: 'sourceId',
            childField: '_id'
        },
        communicationChannel: {
            collection: 'kernel.communication-channels',
            parentField: 'communicationChannelId',
            childField: '_id'
        }
    }
};
