export default {
    name: 'store-products',
    title: 'Store Products',
    schema: {
        // General
        storeId: {
            type: 'string',
            label: 'Store',
            index: true
        },
        // Used by e-commerce integrations. Very important.
        integrationId: {
            type: 'string',
            label: 'Integration ID',
            index: true,
            required: false
        },
        integrationStatus: {
            type: 'string',
            label: 'Integration status',
            allowed: ['waiting', 'approved', 'rejected', 'blacklisted'],
            index: true,
            required: false
        },
        integrationRejectionReason: {
            type: 'string',
            label: 'Integration rejection reason',
            required: false
        },
        integrationParams: {
            type: 'object',
            blackbox: true,
            required: false
        },
        productId: {
            type: 'string',
            label: 'Product',
            index: true
        },
        productImage: {
            type: 'string',
            label: 'Product image',
            index: true
        },
        productCode: {
            type: 'string',
            label: 'Product code',
            index: true
        },
        productDefinition: {
            type: 'string',
            label: 'Product definition',
            index: true
        },
        productBarcode: {
            type: 'string',
            label: 'Product barcode',
            required: false,
            index: true
        },
        productAdditionalCategoryPaths: {
            type: ['string'],
            label: 'Additional categories',
            default: [],
            index: true
        },
        productCategoryId: {
            type: 'string',
            label: 'Product category',
            index: true
        },
        productCategoryPath: {
            type: 'string',
            label: 'Product category path',
            index: true
        },
        productGroupIds: {
            type: ['string'],
            label: 'Product groups',
            default: [],
            index: true
        },
        productBrandId: {
            type: 'string',
            label: 'Product brand',
            required: false,
            index: true
        },
        productBrandName: {
            type: 'string',
            label: 'Product brand name',
            required: false,
            index: true
        },
        productBrandSlug: {
            type: 'string',
            label: 'Product brand slug',
            required: false,
            index: true
        },

        // Settings.
        defaultDeliveryOptionId: {
            type: 'string',
            label: 'Default delivery option',
            required: false
        },
        isSuggestedProduct: {
            type: 'boolean',
            label: 'Suggested product',
            default: false,
            index: true
        },
        isBestSellingProduct: {
            type: 'boolean',
            label: 'Best selling product',
            default: false,
            index: true
        },
        isNewProduct: {
            type: 'boolean',
            label: 'New product',
            default: false,
            index: true
        },
        isDiscountedProduct: {
            type: 'boolean',
            label: 'Discounted product',
            default: false,
            index: true
        },
        isAdultProduct: {
            type: 'boolean',
            label: 'Adult product',
            default: false,
            index: true
        },
        isKitProduct: {
            type: 'boolean',
            label: 'Kit product',
            default: false,
            index: true
        },

        // Customisation
        darkMode: {
            type: 'boolean',
            label: 'Dark mode',
            default: false
        },
        bannerSlug: {
            type: 'string',
            label: 'Banner slug',
            required: false
        },
        bannerImageIds: {
            type: ['string'],
            default: []
        },

        // Sync
        synchronizedAt: {
            type: 'datetime',
            label: 'Synchronized at',
            required: false,
            index: true
        },
        isSynchronized: {
            type: 'boolean',
            label: 'Is synchronized',
            default: true,
            index: true
        },

        // Publication
        publishedAt: {
            type: 'datetime',
            label: 'Published at',
            required: false,
            index: true
        },
        isPublished: {
            type: 'boolean',
            label: 'Is published',
            default: false,
            index: true
        },

        // Pricing
        vendorId: {
            type: 'string',
            label: 'Vendor',
            required: false
        },
        taxApplication: {
            type: 'string',
            label: 'Tax application',
            default: 'tax-excluded',
            index: true
        },
        taxId: {
            type: 'string',
            label: 'Tax',
            index: true
        },
        currencyId: {
            type: 'string',
            label: 'Currency',
            index: true
        },
        salesPrice: {
            type: 'decimal',
            label: 'Sales price',
            default: 0,
            index: true
        },
        discountedSalesPrice: {
            type: 'decimal',
            label: 'Discounted sales price',
            required: false,
            index: true
        },
        discount: {
            type: 'decimal',
            label: 'Discount',
            required: false,
            index: true
        },
        onSale: {
            type: 'boolean',
            label: 'On sale',
            default: true,
            index: true
        },

        // Stock
        quantity: {
            type: 'decimal',
            label: 'Quantity',
            default: 0,
            index: true
        },
        criticalStockQuantity: {
            type: 'decimal',
            label: 'Critical stock quantity',
            default: 0
        },

        // Reports.
        rating: {
            type: 'decimal',
            label: 'Rating',
            default: 0,
            index: true
        },
        reviewCount: {
            type: 'decimal',
            label: 'Rating',
            default: 0,
            index: true
        },
        salesCount: {
            type: 'decimal',
            label: 'Favorites count',
            default: 0,
            index: true
        },
        favoritesCount: {
            type: 'decimal',
            label: 'Favorites count',
            default: 0,
            index: true
        },
        priceListId: {
            type: 'string',
            required: false
        },
        discountedPriceListId: {
            type: 'string',
            required: false
        },

        // Unit
        unitId: {
            type: 'string',
            label: 'Unit',
            required: false
        }
    },
    attributes: {
        product: {
            collection: 'inventory.products',
            parentField: 'productId',
            childField: '_id'
        },
        store: {
            collection: 'ecommerce.stores',
            parentField: 'storeId',
            childField: '_id'
        }
    }
};
