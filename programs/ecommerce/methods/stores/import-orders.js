import integrations from '../../integrations';

export default {
    name: 'stores-import-orders',
    async action(payload, params) {
        const app = this.app;
        const t = app.translate;
        const {storeId, startDate, endDate} = payload;
        const progressId = `ecommerce.sales.stores.detail-${(params.user || {})._id}`;
        const progress = progressParams => {
            if (!!params.user) {
                app.progress({
                    id: progressId,
                    ...progressParams
                });
            }
        };
        const store = await app.collection('ecommerce.stores').findOne({
            _id: storeId,
            $disableActiveCheck: true,
            $disableSoftDelete: true
        });
        const log = payload => {
            payload.collection = 'ecommerce.stores';
            payload.documentId = storeId;
            payload.documentIdentifier = store.code;
            payload.method = 'update';
            payload.collectionName = `${t('E-Commerce')} / ${t('Stores')}`;

            return app.log(payload);
        };
        let lastPercentage = 0;

        progress({
            status: 'started',
            message: t('Importing the orders..')
        });

        try {
            await integrations.importOrders(
                app,
                storeId,
                percentage => {
                    progress({
                        status: 'info',
                        message: t('Importing the orders..'),
                        percentage
                    });

                    lastPercentage = percentage;
                },
                startDate,
                endDate
            );
            await app.rpc('ecommerce.stores-update-reports', storeId);

            log({
                level: 'success',
                message: t('Orders are imported successfully.')
            });
            progress({
                status: 'success',
                percentage: 100
            });
        } catch (error) {
            progress({
                status: 'error',
                message: error.message,
                percentage: lastPercentage
            });
            log({
                level: 'error',
                message: error.message
            });
        }

        try {
            await integrations.importCanceledOrders(app, storeId, startDate, endDate);
        } catch (error) {
            log({
                level: 'error',
                message: error.message
            });
        }

        try {
            await integrations.importReturns(app, storeId, startDate, endDate);
        } catch (error) {
            log({
                level: 'error',
                message: error.message
            });
        }
    }
};
