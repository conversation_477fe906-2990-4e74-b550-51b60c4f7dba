<template>
    <ui-view type="content" v-if="isInitialized" :title="title">
        <ui-table :items="items" :columns="columns" />
    </ui-view>
</template>

<script>
export default {
    data: () => ({
        items: [],
        currencies: [],
        isInitialized: false
    }),

    computed: {
        title() {
            return this.$t('Reconciliation Detail');
        },
        columns() {
            const self = this;

            return [
                {field: 'issueDate', label: 'Issue date', format: 'datetime'},
                {field: 'dueDate', label: 'Due date', format: 'datetime'},
                {field: 'documentNo', label: 'Document no'},
                {
                    field: 'voucherNo',
                    label: 'Voucher no',
                    relationParams(params) {
                        const data = params.data;
                        const relation = {};

                        relation.isVisible = _.isString(data.entryId) && _.isString(data.voucherNo);

                        if (relation.isVisible) {
                            relation.view = 'accounting.adviser.journal-entries-detail';
                            relation.id = data.entryId;
                        }

                        return relation;
                    }
                },
                {field: 'description', label: 'Description'},
                {field: 'reference', label: 'Reference'},
                {field: 'currency.name', label: 'Currency'},
                {field: 'debit', label: 'Debit', format: 'currency'},
                {field: 'credit', label: 'Credit', format: 'currency'},
                {field: 'balance', label: 'Balance', format: 'currency'},
                {field: 'debitFC', label: 'Debit (FC)', format: 'currency',formatOptions: data => {
                        let options = {currency: {}};

                        if (_.isPlainObject(data) && data.currencyId) {
                            const currency = this.currencies.find(c => String(c._id) === String(data.currencyId));
                            console.log('debitFC currencyId:', data.currencyId, 'found:', currency); // DEBUG
                            if (!!currency) {
                                options.currency.symbol = currency.symbol;
                                options.currency.format = currency.symbolPosition === 'before' ? '%s %v' : '%v %s';
                            }
                        }

                        return options;
                    },},
                {field: 'creditFC', label: 'Credit (FC)', format: 'currency',formatOptions: data => {
                        let options = {currency: {}};

                        if (_.isPlainObject(data) && data.currencyId) {
                            const currency = this.currencies.find(c => String(c._id) === String(data.currencyId));
                            console.log('creditFC currencyId:', data.currencyId, 'found:', currency); // DEBUG
                            if (!!currency) {
                                options.currency.symbol = currency.symbol;
                                options.currency.format = currency.symbolPosition === 'before' ? '%s %v' : '%v %s';
                            }
                        }

                        return options;
                    },},
                {field: 'balanceFC', label: 'Balance (FC)', format: 'currency',formatOptions: data => {
                        let options = {currency: {}};

                        if (_.isPlainObject(data) && data.currencyId) {
                            const currency = this.currencies.find(c => String(c._id) === String(data.currencyId));
                            console.log('balanceFC currencyId:', data.currencyId, 'found:', currency); // DEBUG
                            if (!!currency) {
                                options.currency.symbol = currency.symbol;
                                options.currency.format = currency.symbolPosition === 'before' ? '%s %v' : '%v %s';
                            }
                        }

                        return options;
                    },},
                {
                    field: 'status',
                    label: 'Status',
                    tagsCell: true,
                    translateLabels: true,
                    tagLabels: [
                        {label: 'Waiting For Approval', value: 'waiting-for-approval', color: 'warning'},
                        {label: 'Approved', value: 'approved', color: 'green'},
                        {label: 'Denied', value: 'denied', color: 'danger'}
                    ],
                    width: 120
                },
                {field: 'reconciledOnline', label: 'Reconciled online', type: 'boolean'},
                {field: 'onlineReconciliationCode', label: 'Online reconciliation code'},
                {
                    field: 'overdueDays',
                    label: 'Overdue Days',
                    cellClassRules: {
                        'is-danger': params => params.value > 0
                    },
                    valueGetter(params) {
                        if (_.isObject(params.data) && _.isDate(params.data.dueDate)) {
                            const dueDateStart = self.$datetime.local().startOf('day');
                            const dueDateEnd = self.$datetime.fromJSDate(params.data.dueDate).startOf('day');

                            return -self.$app.roundNumber(dueDateEnd.diff(dueDateStart, 'days').toObject().days);
                        }

                        return 0;
                    },
                    width: 100
                },
                {field: 'rejectionReason', label: 'Rejection reason'},
                {field: 'note', label: 'Note'},
                {
                    field: 'fileId',
                    label: 'File',
                    sortable: false,
                    cellRenderer(params) {
                        if (params.data && params.data.fileId) {
                            return `<div class="reconciliation-rejection-file-btn"><span class="el-button el-button--mini">${self.$app.translate(
                                'Download'
                            )}</span></div>`;
                        }

                        return '';
                    },
                    onCellClicked(params) {
                        if (params.data && params.data.fileId) {
                            const fileUrl = self.$app.absoluteUrl(`files/${params.data.fileId}`);
                            window.open(fileUrl, '_blank');
                        }
                    },
                    width: 100
                }
            ];
        }
    },

    async created() {
        this.currencies = await this.$collection('kernel.currencies').find();
        
        this.items = await this.$collection('eops.reconciliation-record-items').find({recordId: this.$params('id')});


        this.isInitialized = true;
    }
};
</script>

<style lang="scss">
//noinspection CssUnknownTarget
@import 'core';

.reconciliation-rejection-file-btn {
    display: flex;
    flex-flow: row nowrap;
    width: 100%;
    height: 100%;
    align-items: center;

    .el-button--mini {
        height: auto;
        padding: 2px 12px 3px;
        line-height: 10px;
        font-size: 10px;
        font-weight: 600;
    }
}
</style>
