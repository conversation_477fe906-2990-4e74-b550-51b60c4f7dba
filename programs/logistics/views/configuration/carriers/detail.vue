<template>
    <ui-view
        type="form"
        :title="title"
        class="ui-tabbed-form has-header"
        collection="logistics.carriers"
        :model="model"
        :before-init="beforeInit"
        :before-submit="beforeSubmit"
        actions="edit,cancel"
        no-inner-padding
        full-height
        @changed="handleChange"
    >
        <div class="form-wrapper">
            <div class="form-header">
                <div class="header-image">
                    <ui-field name="logo" label="hide" field-type="image" />
                </div>

                <div class="header-info">
                    <div class="info-name">
                        <span>{{ title }}</span>
                    </div>

                    <div class="info-description">{{ model.code }}</div>
                </div>
            </div>

            <div class="form-content">
                <el-tabs v-model="activeTab" class="full-tabs">
                    <el-tab-pane name="general" :label="'GENERAL' | t">
                        <el-scrollbar>
                            <div class="columns">
                                <div class="column is-half">
                                    <ui-legend title="General Information" />
                                    <ui-field name="code" />
                                    <ui-field name="name" />
                                    <ui-field name="storeCode" />
                                    <ui-field
                                        name="partnerId"
                                        collection="kernel.partners"
                                        view="partners.partners"
                                        :extra-fields="['code']"
                                        :template="'{{code}} - {{name}}'"
                                        :filters="{type: 'vendor'}"
                                        :update-params="updatePartnerIdParams"
                                    />
                                    <ui-field name="email" />
                                    <ui-field name="website" />
                                    <ui-field
                                        name="defaultPrintingTemplateId"
                                        collection="kernel.templates"
                                        :filters="{source: 'logistics.shipping-order-label'}"
                                        label-from="title"
                                    />
                                    <ui-field name="isActive" />

                                    <ui-legend title="Legal Information" class="mt30" />
                                    <ui-field name="countryId" collection="kernel.countries" />
                                    <ui-field name="tin" />
                                    <ui-field name="taxDepartment" />
                                    <ui-field name="legalName" />

                                    <ui-legend title="Note" class="mt30" />
                                    <ui-field name="note" :rows="3" label="hide" />
                                </div>

                                <div class="column is-half">
                                    <ui-legend title="Phone" />
                                    <ui-field name="phoneNumbers" label="hide" field-type="phone-numbers" />

                                    <ui-legend title="Address Information" class="mt30" />
                                    <ui-field name="address" field-type="address" :is-preview="$params('isPreview')" />

                                    <ui-legend title="Integration" class="mt30" />
                                    <ui-field
                                        name="integrationType"
                                        :options="integrationTypeOptions"
                                        translate-labels
                                    />
                                    <ui-field name="integrationParams" :rows="4" />
                                    <ui-field name="volumetricWeightFactor" />
                                </div>
                            </div>
                        </el-scrollbar>
                    </el-tab-pane>
                </el-tabs>
            </div>
        </div>
    </ui-view>
</template>

<script>
import _ from 'lodash';

const INTEGRATIONS = [
    {
        name: 'mng',
        label: 'MNG Kargo',
        paramsTemplate: {
            customerNumber: '',
            password: '',
            differentSenderAddresses: false,
            cargoTrackingUrlTemplate: 'https://kargotakip.mngkargo.com.tr/?takipNo={{cargoTrackingCode}}'
        }
    },
    {
        name: 'surat',
        label: 'Sürat Kargo',
        paramsTemplate: {
            customerNumber: '',
            password: '',
            differentSenderAddresses: false,
            cargoTrackingUrlTemplate: 'https://www.suratkargo.com.tr/KargoTakip/?kargotakipno={{cargoTrackingCode}}'
        }
    },
    {
        name: 'yurtici',
        label: 'Yurtiçi Kargo',
        paramsTemplate: {
            customerNumber: '',
            password: '',
            differentSenderAddresses: false,
            cargoTrackingUrlTemplate:
                'https://www.yurticikargo.com/tr/online-servisler/gonderi-sorgula?code={{cargoTrackingCode}}'
        }
    },
    {
        name: 'aras',
        label: 'Aras Kargo',
        paramsTemplate: {
            customerNumber: '',
            username: '',
            password: '',
            differentSenderAddresses: false,
            cargoTrackingUrlTemplate: 'http://kargotakip.araskargo.com.tr/mainpage.aspx?code={{cargoTrackingCode}}'
        }
    },
    {
        name: 'sendeo',
        label: 'Sendeo',
        paramsTemplate: {
            customerNumber: '',
            password: '',
            differentSenderAddresses: false
        }
    },
    {
        name: 'bolt',
        label: 'Bolt Kargo',
        paramsTemplate: {
            apiKey: '',
            apiSecret: '',
            differentSenderAddresses: false
        }
    },
    {
        name: 'shipentegra',
        label: 'ShipEntegra',
        paramsTemplate: {
            clientId: '',
            clientSecret: '',
            differentSenderAddresses: false
        }
    },
    {
        name: 'hepsijet',
        label: 'HepsiJET',
        paramsTemplate: {
            username: '',
            password: '',
            companyName: '',
            abbreviationCode: '',
            companyAddressId: '',
            countryName: '',
            cityName: '',
            townName: '',
            districtName: '',
            address: '',
            differentSenderAddresses: false,
            cargoTrackingUrlTemplate: 'https://www.hepsijet.com/gonderi-takibi/{{cargoTrackingCode}}'
        }
    }
];

export default {
    data: () => ({
        model: {address: {}},
        activeTab: 'general'
    }),

    computed: {
        title() {
            const model = this.model;

            if (!!this.$params('id')) {
                return model.name ? model.name : '';
            }

            return this.$t('New Carrier');
        },
        integrationTypeOptions() {
            return INTEGRATIONS.map(integration => ({
                value: integration.name,
                label: integration.label
            }));
        }
    },

    methods: {
        async beforeInit(model) {
            const company = this.$store.getters['session/company'];

            if (!model.countryId) model.countryId = company.address.countryId;

            return model;
        },
        async beforeSubmit(model) {
            return model;
        },
        async handleChange(model, field) {
            if (field === 'partnerId' && model.partnerId) {
                const partner = await this.$collection('kernel.partners').findOne({
                    _id: model.partnerId,
                    $select: [
                        'code',
                        'name',
                        'email',
                        'website',
                        'countryId',
                        'tin',
                        'taxDepartment',
                        'legalName',
                        'phoneNumbers',
                        'address'
                    ]
                });

                this.model.code = partner.code;
                this.model.name = partner.name;
                this.model.email = partner.email;
                this.model.website = partner.website;
                this.model.countryId = partner.countryId;
                this.model.tin = partner.tin;
                this.model.taxDepartment = partner.taxDepartment;
                this.model.legalName = partner.legalName;
                this.model.phoneNumbers = partner.phoneNumbers;
                this.model.address = partner.address;
            }

            if (field === 'integrationType' && !!model.integrationType) {
                const integration = INTEGRATIONS.find(integration => integration.name === model.integrationType);

                this.model.integrationParams = JSON.stringify(integration.paramsTemplate, null, 2);
            }
        },

        updatePartnerIdParams(params) {
            params.model = {type: 'vendor'};

            return params;
        }
    }
};
</script>
