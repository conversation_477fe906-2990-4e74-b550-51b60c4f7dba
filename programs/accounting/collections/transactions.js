import _ from 'lodash';

export default {
    name: 'transactions',
    extraIndexes: [
        {type: 'normal', key: 'account.*', value: 1},
        {type: 'normal', key: 'account.tree.path', value: 1},
        {type: 'normal', key: 'mainAccount.*', value: 1},
        {type: 'normal', key: 'partner.*', value: 1},
        {type: 'normal', key: 'period.*', value: 1},
        {type: 'normal', key: 'journal.*', value: 1},
        {type: 'normal', key: 'tax.*', value: 1},
        {type: 'normal', key: 'invoice.*', value: 1},
        {type: 'normal', key: 'financialProject.*', value: 1},
        {type: 'normal', key: 'fixture.*', value: 1},
        {type: 'normal', key: 'cashFlowItem.*', value: 1}
        // {type: 'normal', key: 'account.code', value: 1},
        // {type: 'normal', key: 'account.name', value: 1},
        // {type: 'normal', key: 'account.type', value: 1},
        // {type: 'normal', key: 'account.tree.path', value: 1},
        // {type: 'normal', key: 'mainAccount.code', value: 1},
        // {type: 'normal', key: 'partner.type', value: 1},
        // {type: 'normal', key: 'partner.code', value: 1},
        // {type: 'normal', key: 'partner.name', value: 1},
        // {type: 'normal', key: 'partner.tinIdentity', value: 1},
        // {type: 'normal', key: 'period.fiscalYearId', value: 1},
        // {type: 'normal', key: 'period.fiscalYear', value: 1},
        // {type: 'normal', key: 'period.code', value: 1},
        // {type: 'normal', key: 'period.name', value: 1},
        // {type: 'normal', key: 'journal.name', value: 1},
        // {type: 'normal', key: 'journal.type', value: 1},
        // {type: 'normal', key: 'tax.scope', value: 1},
        // {type: 'normal', key: 'tax.groupId', value: 1},
        // {type: 'normal', key: 'tax._id', value: 1},
        // {type: 'normal', key: 'financialProject.code', value: 1},
        // {type: 'normal', key: 'financialProject.name', value: 1},
        // {type: 'normal', key: 'cashFlowItem.code', value: 1},
        // {type: 'normal', key: 'cashFlowItem.name', value: 1}
    ],
    branch: true,
    schema: {
        entryNo: {
            type: 'integer',
            label: 'Entry No',
            index: true
        },
        declaredEntryNo: {
            type: 'integer',
            label: 'Declared entry no',
            required: false,
            index: true
        },
        voucherNo: {
            type: 'string',
            label: 'Voucher No',
            index: true
        },
        documentNo: {
            type: 'string',
            label: 'Document No',
            index: true,
            required: false
        },
        reference: {
            type: 'string',
            label: 'Reference',
            index: true,
            required: false
        },
        referenceId: {
            type: 'string',
            label: 'Reference',
            index: true,
            required: false
        },
        referenceView: {
            type: 'string',
            required: false
        },
        description: {
            type: 'string',
            label: 'Description',
            required: false
        },
        entryId: {
            type: 'string',
            label: 'Entry',
            index: true
        },
        accountId: {
            type: 'string',
            label: 'Account',
            index: true
        },
        partnerId: {
            type: 'string',
            label: 'Partner',
            required: false,
            index: true
        },
        currencyId: {
            type: 'string',
            label: 'Currency',
            required: false,
            index: true
        },
        recordDate: {
            type: 'date',
            label: 'Record date',
            index: true
        },
        issueDate: {
            type: 'date',
            label: 'Issue date',
            index: true
        },
        dueDate: {
            type: 'date',
            label: 'Due date',
            index: true
        },
        journalId: {
            type: 'string',
            label: 'Journal',
            index: true
        },
        debitFC: {
            type: 'decimal',
            label: 'Debit (FC)',
            default: 0,
            index: true
        },
        creditFC: {
            type: 'decimal',
            label: 'Credit (FC)',
            default: 0,
            index: true
        },
        debit: {
            type: 'decimal',
            label: 'Debit',
            default: 0,
            index: true
        },
        credit: {
            type: 'decimal',
            label: 'Credit',
            default: 0,
            index: true
        },
        total: {
            type: 'decimal',
            label: 'Total',
            default: 0,
            index: true
        },
        balance: {
            type: 'decimal',
            label: 'Balance',
            default: 0,
            index: true
        },
        balanceFC: {
            type: 'decimal',
            label: 'Balance (FC)',
            default: 0,
            index: true
        },
        isInverseRecord: {
            type: 'boolean',
            default: false,
            index: true
        },
        hasInverseRecord: {
            type: 'boolean',
            default: false,
            index: true
        },
        scope: {
            type: 'string',
            label: 'Scope',
            default: '1',
            index: true
        },
        onlineReconciliationCode: {
            type: 'string',
            label: 'Online reconciliation code',
            required: false
        },
        reconciledOnline: {
            type: 'boolean',
            label: 'Reconciled online',
            default: false,
            index: true
        },
        externalReconciliationCode: {
            type: 'string',
            label: 'External reconciliation code',
            required: false,
            index: true
        },
        reconciledExternally: {
            type: 'boolean',
            label: 'Reconciled eternally',
            default: false,
            index: true
        },
        reconciliationCode: {
            type: 'string',
            label: 'Reconciliation code',
            required: false,
            index: true
        },
        reconciled: {
            type: 'boolean',
            label: 'Reconciled',
            default: false,
            index: true
        },
        reconciliationBalance: {
            type: 'decimal',
            label: 'Reconciliation balance',
            default: 0,
            index: true
        },
        reconciliationBalanceFC: {
            type: 'decimal',
            label: 'Reconciliation balance (FC)',
            required: false
        },
        transactionType: {
            type: 'string',
            label: 'Transaction type',
            required: false,
            index: true
        },
        financialProjectId: {
            type: 'string',
            label: 'Project',
            required: false,
            index: true
        },
        cashFlowItemId: {
            type: 'string',
            label: 'Cash flow item',
            required: false,
            index: true
        },
        fixtureId: {
            type: 'string',
            label: 'Fixture',
            required: false,
            index: true
        },
        salespersonId: {
            type: 'string',
            label: 'Salesperson',
            required: false,
            index: true
        },
        isLocked: {
            type: 'boolean',
            label: 'Is locked',
            default: false,
            index: true
        },
        belongsToOpeningRecord: {
            type: 'boolean',
            label: 'Belongs to opening record',
            default: false
        },
        belongsToClosingRecord: {
            type: 'boolean',
            label: 'Belongs to closing record',
            default: false
        },
        params: {
            type: 'object',
            blackbox: true,
            required: false
        },
        exchangeRatesMap: {
            type: 'object',
            blackbox: true,
            required: false
        },
        journal: {
            type: 'object',
            required: false,
            blackbox: true
        },
        account: {
            type: 'object',
            required: false,
            blackbox: true
        },
        mainAccount: {
            type: 'object',
            required: false,
            blackbox: true
        },
        partner: {
            type: 'object',
            required: false,
            blackbox: true
        },
        branch: {
            type: 'object',
            required: false,
            blackbox: true
        },
        period: {
            type: 'object',
            required: false,
            blackbox: true
        },
        currency: {
            type: 'object',
            required: false,
            blackbox: true
        },
        tax: {
            type: 'object',
            blackbox: true,
            required: false
        },
        taxAssessment: {
            type: 'decimal',
            label: 'Tax assessment',
            default: 0
        },
        taxAssessmentSC: {
            type: 'decimal',
            label: 'Tax assessment sc',
            default: 0
        },
        expenseCategoryId: {
            type: 'string',
            label: 'Expense category',
            required: false
        },
        expenseTagId: {
            type: 'string',
            label: 'Expense tag',
            required: false
        },
        financialProject: {
            type: 'object',
            blackbox: true,
            required: false
        },
        cashFlowItem: {
            type: 'object',
            blackbox: true,
            required: false
        },
        fixture: {
            type: 'object',
            blackbox: true,
            required: false
        },
        reconciliationData: {
            type: 'object',
            blackbox: true,
            required: false
        },
        invoice: {
            type: 'object',
            blackbox: true,
            required: false
        },
        salesperson: {
            type: 'object',
            blackbox: true,
            required: false
        },
        expenseCategory: {
            type: 'object',
            blackbox: true,
            required: false
        },
        expenseTag: {
            type: 'object',
            blackbox: true,
            required: false
        },
        chequeTrackingCode: {
            type: 'string',
            label: 'Cheque tracking code',
            required: false
        }
    },
    async searchTerms(document) {
        const values = Object.values(
            _.pick(document, [
                'voucherNo',
                'documentNo',
                'reference',
                'description',
                'externalReconciliationCode',
                'reconciliationCode'
            ])
        );

        if (_.isPlainObject(document.account)) {
            values.push(document.account.code);
            values.push(document.account.name);
        }
        if (_.isPlainObject(document.partner)) {
            values.push(document.partner.code);
            values.push(document.partner.name);
            values.push(document.partner.tinIdentity);
        }
        if (_.isPlainObject(document.financialProject)) {
            values.push(document.financialProject.code);
            values.push(document.financialProject.name);
        }
        if (_.isPlainObject(document.salesperson)) {
            values.push(document.salesperson.code);
            values.push(document.salesperson.name);
        }

        return values;
    },
    hooks: {
        after: {
            remove: [updateJournalParams]
        }
    }
};

function hideScope2(context) {
    const app = context.app;

    if (!app.setting('system.scopes')) {
        context.params.query = _.assign(context.params.query || {}, {
            scope: '1'
        });
    }

    return context;
}

function updateJournalParams(context) {
    (async () => {
        const app = context.app;
        const accountIds = [];

        for (const transaction of Array.isArray(context.result) ? context.result : [context.result]) {
            if (!accountIds.includes(transaction.accountId)) {
                accountIds.push(transaction.accountId);
            }
        }

        await app.addQueuedJob('accounting.update-journal-params', {accountIds});

        // const jobInProgress = await app.cache.get('accounting-update-journal-params-in-progress');
        //
        // if (!jobInProgress) {
        //     await app.cache.set('accounting-update-journal-params-in-progress', true, 10);
        //     await app.addQueuedJob(
        //         'accounting.update-journal-params',
        //         {},
        //         {
        //             delayUntil: app.datetime.local().plus({seconds: 15}).toJSDate()
        //         }
        //     );
        // }
    })();

    return context;
}
