import _ from 'lodash';

export default {
    name: 'journal-entries',
    title: 'Journal Entries',
    extraIndexes: [
        {type: 'normal', key: 'period.*', value: 1}
        // {type: 'normal', key: 'period.fiscalYearId', value: 1},
        // {type: 'normal', key: 'period.fiscalYear', value: 1},
        // {type: 'normal', key: 'period.code', value: 1},
        // {type: 'normal', key: 'period.name', value: 1}
    ],
    branch: true,
    assignable: true,
    view: 'accounting.adviser.journal-entries',
    labelParams: {
        from: 'voucherNo'
    },
    schema: {
        entryNo: {
            type: 'integer',
            label: 'Entry No',
            required: false,
            index: true
        },
        declaredEntryNo: {
            type: 'integer',
            label: 'Declared entry no',
            required: false,
            index: true
        },
        voucherNo: {
            type: 'string',
            label: 'Voucher No',
            index: true,
            unique: true
        },
        documentNo: {
            type: 'string',
            label: 'Document No',
            index: true,
            required: false
        },
        reference: {
            type: 'string',
            label: 'Reference',
            index: true,
            required: false
        },
        referenceId: {
            type: 'string',
            index: true,
            required: false
        },
        referenceView: {
            type: 'string',
            required: false
        },
        description: {
            type: 'string',
            label: 'Description',
            required: false
        },
        recordDate: {
            type: 'date',
            label: 'Record date',
            default: 'date:now',
            index: true
        },
        issueDate: {
            type: 'date',
            label: 'Issue date',
            default: 'date:now',
            index: true
        },
        dueDate: {
            type: 'date',
            label: 'Due date',
            default: 'date:now',
            index: true
        },
        postedOn: {
            type: 'date',
            label: 'Due date',
            index: true,
            required: false
        },
        journalId: {
            type: 'string',
            label: 'Journal',
            index: true
        },
        automaticCurrencyConversion: {
            type: 'boolean',
            label: 'Automatic currency conversion',
            default: true
        },
        status: {
            type: 'string',
            label: 'Status',
            allowed: ['draft', 'posted', 'canceled'],
            index: true
        },
        partner: {
            type: 'object',
            required: false,
            blackbox: true
        },
        period: {
            type: 'object',
            required: false,
            blackbox: true
        },
        total: {
            type: 'decimal',
            label: 'Total',
            default: 0,
            index: true
        },
        isInverseEntry: {
            type: 'boolean',
            default: false,
            index: true
        },
        hasInverseEntry: {
            type: 'boolean',
            default: false,
            index: true
        },
        items: [
            {
                accountId: {
                    type: 'string',
                    label: 'Account'
                },
                partnerId: {
                    type: 'string',
                    label: 'Partner',
                    required: false
                },
                description: {
                    type: 'string',
                    label: 'Description',
                    required: false
                },
                recordDate: {
                    type: 'date',
                    label: 'Record date',
                    required: false
                },
                issueDate: {
                    type: 'date',
                    label: 'Issue date',
                    required: false
                },
                dueDate: {
                    type: 'date',
                    label: 'Due date',
                    required: false
                },
                branchId: {
                    type: 'string',
                    label: 'Branch Office',
                    required: false
                },
                taxId: {
                    type: 'string',
                    label: 'Tax',
                    required: false
                },
                taxAssessment: {
                    type: 'decimal',
                    label: 'Tax assessment',
                    default: 0
                },
                currencyId: {
                    type: 'string',
                    label: 'Currency',
                    required: false
                },
                financialProjectId: {
                    type: 'string',
                    label: 'Project',
                    required: false
                },
                cashFlowItemId: {
                    type: 'string',
                    label: 'Cash flow item',
                    required: false
                },
                fixtureId: {
                    type: 'string',
                    label: 'Fixture',
                    required: false
                },
                salespersonId: {
                    type: 'string',
                    label: 'Salesperson',
                    required: false
                },
                expenseCategoryId: {
                    type: 'string',
                    label: 'Expense category',
                    required: false
                },
                expenseTagId: {
                    type: 'string',
                    label: 'Expense tag',
                    required: false
                },
                scope: {
                    type: 'string',
                    label: 'Scope',
                    default: '1',
                    index: true
                },
                debitFC: {
                    type: 'decimal',
                    label: 'Debit (FC)',
                    required: false
                },
                creditFC: {
                    type: 'decimal',
                    label: 'Credit (FC)',
                    required: false
                },
                debit: {
                    type: 'decimal',
                    label: 'Debit',
                    default: 0
                },
                credit: {
                    type: 'decimal',
                    label: 'Credit',
                    default: 0
                },
                tax: {
                    type: 'object',
                    blackbox: true,
                    required: false
                },
                invoice: {
                    type: 'object',
                    blackbox: true,
                    required: false
                },
                declaredEntryNo: {
                    type: 'integer',
                    label: 'Declared entry no',
                    required: false
                }
            }
        ],
        relatedDocuments: {
            type: [
                {
                    collection: 'string',
                    view: 'string',
                    title: 'string',
                    ids: {
                        type: ['string'],
                        default: []
                    }
                }
            ],
            default: []
        },
        chequeTrackingCode: {
            type: 'string',
            label: 'Cheque tracking code',
            required: false
        },
        moveIds: {
            type: ['string'],
            default: []
        },
        periodEndEntry: {
            type: 'boolean',
            label: 'Period end entry',
            required: false,
            index: true
        }
    },
    attributes: {
        journal: {
            collection: 'accounting.journals',
            parentField: 'journalId',
            childField: '_id'
        },
        branch: {
            collection: 'kernel.branches',
            parentField: 'branchId',
            childField: '_id'
        }
    },
    async searchTerms(document) {
        const values = Object.values(_.pick(document, ['voucherNo', 'documentNo', 'reference', 'description']));
        if (_.isPlainObject(document.partner)) {
            values.push(document.partner.code);
            values.push(document.partner.name);
            values.push(document.partner.tinIdentity);
        }

        return values;
    }
};
