<template>
    <ui-view
        class="accounting-document-journal-view"
        type="table"
        collection="accounting.transactions"
        :extra-actions="extraActions"
        :columns="columns"
        :filters="filters"
        :options="tableOptions"
        :extra-fields="[
            'entryId',
            'accountId',
            'partnerId',
            'journalId',
            'referenceId',
            'referenceView',
            'belongsToOpeningRecord',
            'belongsToClosingRecord'
        ]"
        :enable-detail="false"
        force-full-text-search
        progress-id="accounting.reports.document-journal"
        :process-result="processResult"
        v-if="initialized"
    >
        <template slot="top-panel">
            <ui-scope
                id="accounting.reports.document-journal"
                :filters="scopeApplicableFilters"
                :applied-items="scopeAppliedItems"
                @changed="handleScopeChange"
            />
        </template>
    </ui-view>
</template>

<script>
import _ from 'lodash';
import fastCopy from 'fast-copy';
import {rawMongoQuery} from 'framework/helpers';

export default {
    data: () => ({
        scopeQuery: {},
        fiscalYear: null,
        initialized: false
    }),

    computed: {
        extraActions() {
            const actions = [];

            if (this.$app.hasModule('local-tr')) {
                actions.push({
                    name: 'create-e-journal',
                    title: 'Create E-Journal',
                    icon: 'fal fa-file-alt',
                    handler: this.handleCreateEJournal
                });
            }

            actions.push({
                name: 'check-account-correctness',
                title: 'Check Account Correctness',
                icon: 'fal fa-file-alt',
                handler: this.handleCheckAccountCorrectness
            });

            return actions;
        },
        filters() {
            const filters = fastCopy(this.scopeQuery);

            return filters;
        },
        columns() {
            const company = this.$store.getters['session/company'];
            const canUpdate = this.$checkPermission({
                type: 'permission',
                name: 'accounting.canUpdatePostedJournalEntries'
            });
            const scopeOptions = [
                {value: '1', label: this.$setting('system.scope1Label')},
                {value: '2', label: this.$setting('system.scope2Label')}
            ];

            return [
                {
                    type: 'integer',
                    field: 'entryNo',
                    label: 'Entry no',
                    width: 100
                },
                {
                    type: 'integer',
                    field: 'declaredEntryNo',
                    label: 'Declared entry no',
                    visible: false,
                    width: 100
                },
                {
                    field: 'recordDate',
                    label: 'Record Date',
                    format: 'date',
                    width: 120
                },
                {
                    field: 'dueDate',
                    label: 'Due Date',
                    format: 'date',
                    visible: false,
                    width: 120
                },
                {
                    field: 'issueDate',
                    label: 'Issue Date',
                    format: 'date',
                    sort: 'desc',
                    width: 120
                },
                {
                    field: 'documentNo',
                    label: 'Document No',
                    visible: false,
                    width: 150
                },
                {
                    field: 'voucherNo',
                    label: 'Voucher No',
                    relationParams(params) {
                        const data = params.data;
                        let view = 'accounting.adviser.journal-entries-detail';

                        if (data.belongsToOpeningRecord) {
                            view = 'accounting.adviser.opening-records-detail';
                        } else if (data.belongsToClosingRecord) {
                            view = 'accounting.adviser.closing-records-detail';
                        }

                        return {
                            id: data.entryId,
                            view
                        };
                    },
                    width: 150
                },
                {
                    field: 'chequeTrackingCode',
                    label: 'Cheque tracking code',
                    visible: false,
                    width: 150
                },
                {
                    field: 'journal.name',
                    label: 'Journal',
                    width: 150
                },
                {
                    field: 'journal.type',
                    label: 'Journal type',
                    translateLabels: true,
                    valueLabels: [
                        {label: 'Sale', value: 'sale'},
                        {label: 'Purchase', value: 'purchase'},
                        {label: 'Sale return', value: 'sale-return'},
                        {label: 'Purchase return', value: 'purchase-return'},
                        {label: 'Cash safe', value: 'cash-safe'},
                        {label: 'Bank', value: 'bank'},
                        {label: 'Cheque', value: 'cheque'},
                        {label: 'Promissory note', value: 'promissory-note'},
                        {label: 'Credit card', value: 'credit-card'},
                        {label: 'POS', value: 'pos'},
                        {label: 'Guarantee', value: 'guarantee'},
                        {label: 'Loan', value: 'loan'},
                        {label: 'Stock', value: 'stock'},
                        {label: 'Salary', value: 'salary'},
                        {label: 'Expense', value: 'expense'},
                        {label: 'Income', value: 'income'},
                        {label: 'Exchange Rate Difference', value: 'exchange-rate-difference'},
                        {label: 'Depreciation', value: 'depreciation'},
                        {label: 'Miscellaneous', value: 'miscellaneous'}
                    ],
                    visible: false,
                    width: 150
                },
                {
                    field: 'description',
                    label: 'Description',
                    width: 150
                },
                {
                    field: 'account.code',
                    label: 'Account code',
                    width: 150
                },
                {
                    field: 'account.name',
                    label: 'Account name',
                    minWidth: 180
                },
                {
                    field: 'partner.code',
                    label: 'Partner code',
                    view: 'partners.partners-detail',
                    relationParams(params) {
                        const data = params.data;

                        return {
                            id: data.partnerId
                        };
                    },
                    width: 150
                },
                {
                    field: 'partner.name',
                    label: 'Partner name',
                    view: 'partners.partners-detail',
                    relationParams(params) {
                        const data = params.data;

                        return {
                            id: data.partnerId
                        };
                    },
                    width: 210
                },
                {
                    field: 'financialProject.code',
                    label: 'Project code',
                    visible: false,
                    width: 90
                },
                {
                    field: 'financialProject.name',
                    label: 'Project name',
                    visible: false,
                    width: 150
                },
                {
                    field: 'cashFlowItem.name',
                    label: 'Cash flow item',
                    visible: false,
                    width: 150
                },
                {
                    field: 'reconciliationCode',
                    label: 'Reconciliation',
                    visible: false,
                    width: 150
                },
                {
                    field: 'salesperson.code',
                    label: 'Salesperson code',
                    view: 'partners.partners-detail',
                    relationParams(params) {
                        const data = params.data;

                        return {
                            id: data.salespersonId
                        };
                    },
                    visible: false,
                    width: 150
                },
                {
                    field: 'salesperson.name',
                    label: 'Salesperson name',
                    view: 'partners.partners-detail',
                    relationParams(params) {
                        const data = params.data;

                        return {
                            id: data.salespersonId
                        };
                    },
                    visible: false,
                    width: 210
                },
                {
                    field: 'expenseCategory.code',
                    label: 'Expense category code',
                    visible: false,
                    width: 90
                },
                {
                    field: 'expenseCategory.name',
                    label: 'Expense category name',
                    visible: false,
                    width: 150
                },
                {
                    field: 'expenseTag.name',
                    label: 'Expense tag',
                    visible: false,
                    width: 90
                },
                {
                    field: 'tax.name',
                    label: 'Tax',
                    visible: false,
                    width: 150
                },
                {
                    field: 'tax.groupName',
                    label: 'Tax group',
                    visible: false,
                    width: 120
                },
                {
                    field: 'tax.scope',
                    label: 'Tax scope',
                    valueLabels: [
                        {value: 'sale', label: 'Sale'},
                        {value: 'purchase', label: 'Purchase'}
                    ],
                    translateLabels: true,
                    visible: false,
                    width: 120
                },
                {
                    type: 'boolean',
                    field: 'tax.isDeduction',
                    label: 'Is deduction tax',
                    visible: false,
                    width: 90
                },
                {
                    field: 'tax.amount',
                    label: 'Tax rate',
                    format: 'amount',
                    visible: false,
                    width: 120
                },
                {
                    field: 'tax.unTaxedAmount',
                    label: 'Tax assessment',
                    format: 'currency',
                    visible: false,
                    width: 120
                },
                {
                    field: 'debit',
                    label: 'Debit',
                    format: 'currency',
                    width: 120
                },
                {
                    field: 'credit',
                    label: 'Credit',
                    format: 'currency',
                    width: 120
                },
                {
                    field: 'currency.name',
                    label: 'Currency',
                    visible: false,
                    width: 120
                },
                {
                    field: 'debitFC',
                    label: 'Currency Debit',
                    format: 'currency',
                    formatOptions(data) {
                        let options = {currency: {}};

                        if (_.isObject(data) && _.isObject(data.currency)) {
                            if (data.currencyId !== company.currencyId) {
                                options.currency.symbol = data.currency.symbol;
                                options.currency.format = data.currency.symbolPosition === 'before' ? '%s %v' : '%v %s';
                            }
                        }

                        return options;
                    },
                    visible: false,
                    width: 120
                },
                {
                    field: 'creditFC',
                    label: 'Currency Credit',
                    format: 'currency',
                    formatOptions(data) {
                        let options = {currency: {}};

                        if (_.isObject(data) && _.isObject(data.currency)) {
                            if (data.currencyId !== company.currencyId) {
                                options.currency.symbol = data.currency.symbol;
                                options.currency.format = data.currency.symbolPosition === 'before' ? '%s %v' : '%v %s';
                            }
                        }

                        return options;
                    },
                    visible: false,
                    width: 120
                },
                {
                    field: 'reconciliationBalance',
                    label: 'Reconciliation balance',
                    format: 'currency',
                    visible: false,
                    width: 150
                },
                {
                    field: 'reference',
                    label: 'Reference',
                    relationParams(params) {
                        const data = params.data;

                        if (!!data.referenceId && !!data.referenceView) {
                            return {
                                id: data.referenceId,
                                view: data.referenceView
                            };
                        }

                        return {};
                    },
                    width: 150
                },
                {
                    field: 'branch.name',
                    label: 'Branch Office',
                    hidden: !this.$setting('system.multiBranch'),
                    width: 150,
                    visible: false
                },
                {
                    field: 'scope',
                    label: 'Scope',
                    hidden: !this.$setting('system.scopes'),
                    width: 90,
                    valueLabels: scopeOptions
                },
                ...(canUpdate
                    ? [
                          {
                              headerName: '#',
                              field: 'popup-edit-cell',
                              width: 35,
                              maxWidth: 35,
                              pinned: 'right',
                              lockPosition: true,
                              lockVisible: true,
                              lockPinned: true,
                              editable: false,
                              cellClass: () => {
                                  return 'popup-edit-cell';
                              },
                              headerClass: 'popup-edit-header-cell',
                              resizable: false,
                              suppressNavigable: true,
                              suppressMovable: true,
                              suppressSizeToFit: true,
                              sortable: false,
                              suppressMenu: true,
                              suppressColumnsToolPanel: true,
                              headerComponentParams: {
                                  template:
                                      '<div class="ag-cell-label-container" role="presentation">' +
                                      '  <span ref="eMenu" class="ag-header-icon ag-header-cell-menu-button"></span>' +
                                      '  <div ref="eLabel" class="ag-header-cell-label" role="presentation">' +
                                      `     <span class="ag-header-cell-text" role="columnheader"></span>` +
                                      '  </div>' +
                                      '</div>'
                              },
                              cellRenderer(params) {
                                  if (!!params.data) {
                                      return '<i class="fal fa-pencil-alt"></i>';
                                  }

                                  return '';
                              },
                              cellStyle(params) {
                                  if (!!params.data && params.data) {
                                      return {
                                          'background-color': 'transparent !important',
                                          'font-size': '14px',
                                          cursor: 'pointer'
                                      };
                                  }

                                  return {
                                      'background-color': 'transparent !important',
                                      cursor: 'default'
                                  };
                              },
                              onCellClicked: params => {
                                  if (!!params.data) {
                                      this.handleUpdateTransaction(params.data);
                                  }

                                  return false;
                              }
                          }
                      ]
                    : [])
            ];
        },
        scopeAppliedItems() {
            if (!!this.$params('disableFiscalYearScope')) {
                return [];
            }

            return [
                {
                    type: 'filter',
                    payload: {
                        label: 'Fiscal year',
                        field: 'period.fiscalYearId',
                        value: {
                            value: this.fiscalYear._id,
                            label: this.fiscalYear.name
                        },
                        collection: 'kernel.periods',
                        isRemovable: false,
                        singleSelect: true
                    }
                }
            ];
        },
        scopeApplicableFilters() {
            const self = this;

            const scopeOptions = [
                {value: '1', label: this.$setting('system.scope1Label')},
                {value: '2', label: this.$setting('system.scope2Label')}
            ];

            return [
                // Pre-defined
                {code: 'today', label: 'Today', query: 'issueDate|today'},
                {code: 'yesterday', label: 'Yesterday', query: 'issueDate|yesterday'},
                {code: 'thisWeek', label: 'This week', query: 'issueDate|thisWeek'},
                {code: 'lastWeek', label: 'Last week', query: 'issueDate|lastWeek'},
                {code: 'thisMonth', label: 'This month', query: 'issueDate|thisMonth'},
                {code: 'lastMonth', label: 'Last month', query: 'issueDate|lastMonth'},
                {code: 'thisQuarter', label: 'This quarter', query: 'issueDate|thisQuarter'},
                {code: 'lastQuarter', label: 'Last quarter', query: 'issueDate|lastQuarter'},
                {field: 'declared', label: 'Declared journals', query: {declaredEntryNo: {$exists: true}}},

                // Filters.
                {field: 'entryNo', label: 'Entry no', type: 'integer'},
                {field: 'declaredEntryNo', label: 'Declared entry no', type: 'integer'},
                {field: 'account.code', label: 'Account code', type: 'text'},
                {
                    field: 'recordDate',
                    code: 'recordDate',
                    label: 'Record date',
                    type: 'date'
                },
                {
                    field: 'issueDate',
                    code: 'issueDate',
                    label: 'Issue date',
                    type: 'date'
                },
                {
                    field: 'dueDate',
                    code: 'dueDate',
                    label: 'Due date',
                    type: 'date'
                },
                {field: 'documentNo', label: 'Document no'},
                {field: 'voucherNo', label: 'Voucher no'},
                {field: 'chequeTrackingCode', label: 'Cheque tracking code'},
                {
                    field: 'journalId',
                    label: 'Journal',
                    collection: 'accounting.journals',
                    filters: {
                        $sort: {name: 1}
                    }
                },
                {
                    field: 'journal.type',
                    label: 'Journal type',
                    translateLabels: true,
                    valueLabels: [
                        {label: 'Sale', value: 'sale'},
                        {label: 'Purchase', value: 'purchase'},
                        {label: 'Sale return', value: 'sale-return'},
                        {label: 'Purchase return', value: 'purchase-return'},
                        {label: 'Cash safe', value: 'cash-safe'},
                        {label: 'Bank', value: 'bank'},
                        {label: 'Cheque', value: 'cheque'},
                        {label: 'Promissory note', value: 'promissory-note'},
                        {label: 'Credit card', value: 'credit-card'},
                        {label: 'POS', value: 'pos'},
                        {label: 'Guarantee', value: 'guarantee'},
                        {label: 'Loan', value: 'loan'},
                        {label: 'Stock', value: 'stock'},
                        {label: 'Salary', value: 'salary'},
                        {label: 'Expense', value: 'expense'},
                        {label: 'Income', value: 'income'},
                        {label: 'Opening record', value: 'opening-record'},
                        {label: 'Closing record', value: 'closing-record'},
                        {label: 'Period end', value: 'period-end'},
                        {label: 'Exchange Rate Difference', value: 'exchange-rate-difference'},
                        {label: 'Depreciation', value: 'depreciation'},
                        {label: 'Miscellaneous', value: 'miscellaneous'}
                    ]
                },
                {
                    field: 'accountId',
                    label: 'Account',
                    collection: 'kernel.accounts',
                    filters: {
                        'tree.hasChild': {$ne: true},
                        $sort: {
                            code: 1
                        }
                    },
                    extraFields: ['code', 'name'],
                    template: '{{code}} - {{name}}'
                },
                {
                    field: 'branchId',
                    label: 'Branch office',
                    collection: 'kernel.branches',
                    filters: {
                        _id: {$in: this.$user.branchIds},
                        $sort: {name: 1}
                    },
                    condition() {
                        return self.$setting('system.multiBranch');
                    }
                },
                {
                    field: 'partnerId',
                    label: 'Partner',
                    collection: 'kernel.partners',
                    extraFields: ['code'],
                    filters: {$sort: {name: 1}},
                    template: '{{code}} - {{name}}'
                },
                {
                    field: 'partner.tinIdentity',
                    label: 'TIN / Identity'
                },
                {
                    field: 'financialProjectId',
                    label: 'Project',
                    collection: 'kernel.financial-projects',
                    extraFields: ['code'],
                    filters: {$sort: {name: 1}},
                    template: '{{code}} - {{name}}'
                },
                {
                    field: 'cashFlowItemId',
                    label: 'Cash flow item',
                    collection: 'finance.cash-flow-items',
                    filters: {
                        $sort: {name: 1}
                    }
                },
                {
                    field: 'salespersonId',
                    label: 'Salesperson',
                    collection: 'kernel.partners',
                    extraFields: ['code'],
                    filters: {type: 'employee', $sort: {name: 1}},
                    template: '{{code}} - {{name}}'
                },
                {
                    field: 'expenseCategoryId',
                    label: 'Expense category',
                    collection: 'expense.expense-categories',
                    extraFields: ['code'],
                    filters: {$sort: {name: 1}},
                    template: '{{code}} - {{name}}'
                },
                {
                    field: 'expenseTagId',
                    label: 'Expense tag',
                    collection: 'expense.expense-tags',
                    filters: {$sort: {name: 1}}
                },
                {field: 'reconciliationCode', label: 'Reconciliation'},
                {
                    field: 'tax._id',
                    label: 'Tax',
                    collection: 'kernel.taxes',
                    extraFields: ['scope'],
                    htmlTemplate(item) {
                        return `<span style="display: inline-block; margin-left: 10px; margin-right: 15px; float: left">${
                            item.name
                        }</span><span style="float: right; display:inline-block; margin-right: 10px; color: #8492a6;">${
                            item.scope === 'sale' ? self.$t('Sale') : self.$t('Purchase')
                        }</span>`;
                    }
                },
                {
                    field: 'tax.groupId',
                    label: 'Tax group',
                    collection: 'kernel.tax-groups'
                },
                {
                    field: 'tax.scope',
                    label: 'Tax scope',
                    items: [
                        {value: 'sale', label: 'Sale'},
                        {value: 'purchase', label: 'Purchase'}
                    ],
                    translateLabels: true
                },
                {
                    type: 'boolean',
                    field: 'tax.isDeduction',
                    label: 'Is deduction tax'
                },
                {
                    field: 'tax.amount',
                    label: 'Tax rate',
                    type: 'decimal'
                },
                {
                    field: 'tax.unTaxedAmount',
                    label: 'Tax assessment',
                    type: 'decimal'
                },
                {field: 'reference', label: 'Reference'},
                {field: 'description', label: 'Description'},
                {
                    field: 'scope',
                    label: 'Scope',
                    condition() {
                        return self.$setting('system.scopes');
                    },
                    valueLabels: scopeOptions
                },
                {
                    field: 'currencyId',
                    label: 'Currency',
                    collection: 'kernel.currencies',
                    filters: {$sort: {name: 1}},
                    condition() {
                        return self.$setting('system.multiCurrency');
                    }
                }
            ];
        },
        tableOptions() {
            return {
                rowClassRules: {
                    'even-entry-rows': params => {
                        const data = params.data;

                        return _.isObject(data) && data.blockType === 'even';
                    },
                    'odd-entry-rows': params => {
                        const data = params.data;

                        return _.isObject(data) && data.blockType === 'odd';
                    }
                }
            };
        },
        exportParams() {
            return {
                name: this.$t('Document Journal'),
                filters: this.filters,
                collection: 'accounting.transactions',
                scope: 'filtered',
                sort: {issueDate: -1},
                columns: [
                    {field: 'entryNo', label: 'Journal No', width: 10, selected: true},
                    {field: 'recordDate', label: 'Record Date', width: 15, selected: true},
                    {field: 'dueDate', label: 'Due Date', width: 15, selected: true},
                    {field: 'issueDate', label: 'Issue Date', width: 15, selected: true},
                    {field: 'documentNo', label: 'Document No', width: 20, selected: true},
                    {field: 'voucherNo', label: 'Voucher No', width: 20, selected: true},
                    {field: 'journal.name', label: 'Journal', width: 45, selected: true},
                    {field: 'description', label: 'Description', width: 45, selected: true},
                    {field: 'reference', label: 'Reference', width: 30, selected: true},
                    {field: 'account.code', label: 'Account code', width: 30, selected: true},
                    {field: 'account.name', label: 'Account name', width: 45, selected: true},
                    {field: 'partner.code', label: 'Partner code', width: 30, selected: true},
                    {field: 'partner.name', label: 'Partner name', width: 45, selected: true},
                    {field: 'branch.code', label: 'Branch code', width: 30, selected: true},
                    {field: 'branch.name', label: 'Branch name', width: 45, selected: true},
                    {field: 'financialProject.code', label: 'Project code', width: 30, selected: true},
                    {field: 'financialProject.name', label: 'Project name', width: 45, selected: true},
                    {field: 'tax.name', label: 'Tax', width: 30, selected: false},
                    {field: 'tax.groupName', label: 'Tax group', width: 30, selected: false},
                    {
                        field: 'tax.scope',
                        label: 'Tax scope',
                        width: 15,
                        selected: false,
                        valueLabels: [
                            {value: 'sale', label: 'Sale'},
                            {value: 'purchase', label: 'Purchase'}
                        ],
                        translateLabels: true
                    },
                    {field: 'tax.amount', label: 'Tax rate', width: 15, selected: false},
                    {field: 'tax.unTaxedAmount', label: 'Tax assessment', width: 15, selected: false},
                    {field: 'currency.name', label: 'Currency', width: 20},
                    {field: 'debit', label: 'Debit', width: 20, selected: true},
                    {field: 'credit', label: 'Credit', width: 20, selected: true}
                ].concat(
                    !!this.$setting('system.scopes')
                        ? [{field: 'scope', label: 'Scope', width: 15, selected: true}]
                        : []
                )
            };
        }
    },
    methods: {
        handleUpdateTransaction(transaction) {
            this.$program.dialog({
                component: 'accounting.reports.document-journal.update',
                params: {
                    isPreview: false,
                    transaction
                }
            });
        },
        handleCreateEJournal() {
            const onSubmit = async data => {
                this.$params('loading', true);

                try {
                    const result = await this.$rpc('accounting.create-e-journal', data);

                    if (!!result) {
                        const link = document.createElement('a');
                        link.href = this.$app.absoluteUrl(`temp-files/${result.file}?filename=${result.name}`);
                        link.download = result.name;
                        document.body.appendChild(link);
                        link.click();
                        document.body.removeChild(link);
                    }
                } catch (error) {
                    this.$program.message('error', error.message);
                }

                this.$params('loading', false);
            };

            this.$program.dialog({
                component: 'accounting.reports.document-journal.create-e-journal',
                params: {
                    isPreview: false
                },
                onSubmit
            });
        },
        async handleCheckAccountCorrectness() {
            const payload = await new Promise(resolve => {
                this.$program.dialog({
                    component: 'accounting.reports.document-journal.check-accounts-correctness',
                    params: {
                        isPreview: false
                    },
                    onSubmit: model => resolve(model),
                    onCancel: () => resolve(false)
                });
            });
            if (!payload) {
                return;
            }

            try {
                const result = await this.$rpc('accounting.check-account-correctness', payload);

                if (!!result) {
                    const link = document.createElement('a');
                    link.href = this.$app.absoluteUrl(`temp-files/${result.file}?filename=${result.name}`);
                    link.download = result.name;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                }
            } catch (error) {
                this.$program.message('error', error.message);
            }
        },
        handleScopeChange(model) {
            this.scopeQuery = model.query;
        },
        processResult(result) {
            if (result.total > 0) {
                if (!this.lastEntryNo) {
                    this.lastEntryNo = result.data[0].entryNo;
                    this.blockType = 'odd';
                }

                result.data = result.data.map(row => {
                    if (row.entryNo !== this.lastEntryNo) {
                        this.lastEntryNo = row.entryNo;
                        this.blockType = this.blockType === 'odd' ? 'even' : 'odd';
                    }

                    row.blockType = this.blockType;

                    return row;
                });
            }

            return result;
        }
        // async summaryRow() {
        //     return await this.$rpc('accounting.reports-document-journal-summary-row', this.filters);
        // }
    },

    async created() {
        this.$params('loading', true);

        this.fiscalYear = await this.$collection('kernel.periods').findOne({
            fiscalYear: this.$datetime.local().year,
            $select: ['_id', 'name']
        });

        this.initialized = true;

        this.$nextTick(() => {
            this.$params('loading', false);
        });
    }
};
</script>

<style lang="scss">
//noinspection CssUnknownTarget
@import 'core';

.accounting-document-journal-view {
    .odd-entry-rows:not(.ag-row-selected):not(.ag-row-hover) {
        background-color: #fff !important;
    }

    .even-entry-rows:not(.ag-row-selected):not(.ag-row-hover) {
        background-color: darken($light-25, 1%) !important;
    }
}
</style>
