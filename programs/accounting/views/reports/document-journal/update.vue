<template>
    <ui-view
        type="form"
        :model="model"
        :schema="schema"
        :title="title"
        :dialog-width="960"
        :dialog-height="620"
        :before-init="beforeInit"
        :before-validate="beforeValidate"
        :before-submit="beforeSubmit"
        actions="edit,cancel"
        @changed="handleChange"
    >
        <div class="columns">
            <div class="column is-half">
                <ui-legend title="General" />
                <ui-field name="accountId" v-bind="accountIdOptions" />
                <ui-field
                    name="partnerId"
                    collection="kernel.partners"
                    view="partners.partners"
                    :extra-fields="['code']"
                    :template="'{{code}} - {{name}}'"
                />
                <kernel-common-branch-select />
                <ui-field
                    name="journalId"
                    collection="accounting.journals"
                    view="accounting.configuration.journals"
                    :filters="journalIdFilters"
                    disable-create
                    disable-detail
                />
                <ui-field name="description" />
                <ui-field name="documentNo" />
            </div>

            <div class="column is-half">
                <ui-legend title="Details" />
                <ui-field name="reference" />
                <ui-field name="declaredEntryNo" />
                <ui-field name="recordDate" />
                <ui-field name="issueDate" />
                <ui-field name="dueDate" />
            </div>
        </div>

        <ui-legend title="Update History" class="full-width mt30" />
        <div style="height: 245px">
            <ui-table
                collection="kernel.logs"
                :filters="updateHistoryFilters"
                :columns="updateHistoryColumns"
                :enable-auto-height="false"
                disable-no-rows-overlay
            />
        </div>
    </ui-view>
</template>

<script>
import _ from 'lodash';

export default {
    data: () => ({
        model: {},
        dialogHeight: 300
    }),

    computed: {
        title() {
            const transaction = this.$params('transaction');

            return transaction.voucherNo;
        },
        schema() {
            return {
                accountId: {
                    type: 'string',
                    label: 'Account'
                },
                partnerId: {
                    type: 'string',
                    label: 'Partner',
                    required: false
                },
                branchId: {
                    type: 'string',
                    label: 'Branch office'
                },
                journalId: {
                    type: 'string',
                    label: 'Journal'
                },
                recordDate: {
                    type: 'date',
                    label: 'Record date'
                },
                issueDate: {
                    type: 'date',
                    label: 'Issue date'
                },
                dueDate: {
                    type: 'date',
                    label: 'Issue date'
                },
                documentNo: {
                    type: 'string',
                    label: 'Document no',
                    required: false
                },
                reference: {
                    type: 'string',
                    label: 'Reference',
                    required: false
                },
                description: {
                    type: 'string',
                    label: 'Description',
                    required: false
                },
                declaredEntryNo: {
                    type: 'integer',
                    label: 'Declared entry no',
                    required: false
                }
            };
        },
        accountIdOptions() {
            const filters = {
                'tree.hasChild': {$ne: true},
                $sort: {
                    code: 1
                }
            };

            return {
                collection: 'kernel.accounts',
                view: 'system.management.configuration.chart-of-accounts',
                filters,
                updateParams(params, type) {
                    params.isRowSelectable = row => {
                        let result = !row.tree.hasChild;

                        return result;
                    };

                    if (!_.isPlainObject(params.model)) {
                        params.model = {};
                    }

                    delete params.filters;

                    return params;
                },
                extraFields: ['code', 'name'],
                template: '{{code}} - {{name}}'
            };
        },
        journalIdFilters() {
            const branchId = this.model.branchId;
            const filters = {$disableHideDepositAccount: true, $disableBranchCheck: true};

            filters.$or = [];
            filters.$or.push({branchId});
            filters.$or.push({branchId: {$eq: null}});
            filters.$or.push({branchId: {$exists: false}});

            return filters;
        },
        updateHistoryFilters() {
            const transaction = this.$params('transaction');

            return {
                collection: 'accounting.transactions',
                documentId: transaction._id
            };
        },
        updateHistoryColumns() {
            return [
                {
                    field: 'date',
                    label: 'Date',
                    format: 'datetime',
                    sort: 'desc',
                    width: 150
                },
                {
                    field: 'user.name',
                    label: 'User name',
                    minWidth: 180
                },
                {
                    field: 'user.email',
                    label: 'User email',
                    width: 210
                }
            ];
        }
    },

    methods: {
        async beforeInit(model) {
            const transaction = this.$params('transaction');

            for (const key of Object.keys(
                _.pick(transaction, [
                    'accountId',
                    'partnerId',
                    'branchId',
                    'journalId',
                    'recordDate',
                    'issueDate',
                    'dueDate',
                    'documentNo',
                    'reference',
                    'description',
                    'declaredEntryNo'
                ])
            )) {
                model[key] = transaction[key];
            }

            return model;
        },
        async beforeValidate(model) {
            return model;
        },
        async beforeSubmit(model) {
            const transaction = this.$params('transaction');

            await this.$rpc('accounting.update-transaction', {
                id: transaction._id,
                payload: model,
                voucherNo: transaction.voucherNo
            });

            return model;
        },
        async handleChange(model, field) {}
    }
};
</script>
