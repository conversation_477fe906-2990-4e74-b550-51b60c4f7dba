<template>
    <ui-view
        type="form"
        :model="model"
        :schema="schema"
        :title="$t('Create E-Journal')"
        :dialog-width="450"
        :dialog-height="dialogHeight"
        :before-init="beforeInit"
        :before-validate="beforeValidate"
        :before-submit="beforeSubmit"
        actions="edit,cancel"
        @changed="handleChange"
    >
        <div ref="container" v-resize="handleResize">
            <ui-field name="startDate" label-position="top" />
            <ui-field name="endDate" label-position="top" />
            <ui-field name="nextEntryNo" label-position="top" />
            <ui-field name="isBranchBased" label="hide" :active-text="$t('Is branch based')" />
            <ui-field name="subCompanyId" :options="subCompanyIdOptions" v-show="$setting('system.multiCompany')" />
        </div>
    </ui-view>
</template>

<script>
export default {
    data: () => ({
        model: {},
        dialogHeight: 300
    }),

    computed: {
        schema() {
            return {
                startDate: {
                    type: 'date',
                    label: 'Start date'
                },
                endDate: {
                    type: 'date',
                    label: 'End date'
                },
                nextEntryNo: {
                    type: 'integer',
                    label: 'Next entry no'
                },
                isBranchBased: {
                    type: 'boolean',
                    label: 'Is branch based',
                    default: false
                },
                subCompanyId: {
                    type: 'string',
                    label: 'Sub company',
                    required: false
                }
            };
        },
        subCompanyIdOptions() {
            const company = this.$store.getters['session/company'];

            return (company.subCompanies || []).map(subCompany => ({
                value: subCompany.id,
                label: subCompany.name
            }));
        }
    },

    methods: {
        async beforeInit(model) {
            const now = this.$app.datetime.local();
            model.startDate = this.$app.datetime.local().minus({months: 1}).startOf('month').toJSDate();
            if (model.startDate.getFullYear() < now.year) {
                model.startDate = now.startOf('year').toJSDate();
            }
            model.endDate = this.$app.datetime.fromJSDate(model.startDate).endOf('month').toJSDate();

            const lastRecord = await this.$collection('accounting.journal-entries').findOne({
                status: 'posted',
                issueDate: {$gte: now.startOf('year').toJSDate(), $lt: model.startDate},
                $sort: {issueDate: -1},
                $select: ['entryNo']
            });
            model.nextEntryNo = !!lastRecord ? lastRecord.entryNo + 1 : 1;

            return model;
        },
        async beforeValidate(model) {
            if (model.endDate.getTime() < model.startDate.getTime()) {
                throw new this.$app.errors.Unprocessable(this.$t('End date cannot be earlier than start date!'));
            }

            if (!!this.$setting('system.multiCompany') && !model.subCompanyId) {
                throw new Error(
                    this.$t('{{label}} is required', {
                        label: this.$t('Sub company')
                    })
                );
            }

            return model;
        },
        async beforeSubmit(model) {
            model.startDate = this.$app.datetime.fromJSDate(model.startDate).startOf('day').toJSDate();
            model.endDate = this.$app.datetime.fromJSDate(model.endDate).endOf('day').toJSDate();

            return model;
        },
        async handleChange(model, field) {
            const now = this.$app.datetime.local();

            if (field === 'startDate') {
                this.model.endDate = model.endDate = this.$app.datetime
                    .fromJSDate(model.startDate)
                    .endOf('month')
                    .toJSDate();
            }

            if (field === 'startDate' || field === 'endDate') {
                this.$params('loading', true);
                const lastRecord = await this.$collection('accounting.journal-entries').findOne({
                    status: 'posted',
                    issueDate: {$gte: now.startOf('year').toJSDate(), $lt: model.startDate},
                    $sort: {issueDate: -1},
                    $select: ['entryNo']
                });
                this.model.nextEntryNo = !!lastRecord ? lastRecord.entryNo + 1 : 1;
                this.$params('loading', false);
            }
        },
        handleResize() {
            this.dialogHeight = this.$refs.container.clientHeight + 125;
        }
    }
};
</script>
