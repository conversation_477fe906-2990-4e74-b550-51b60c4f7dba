<template>
    <ui-view
        :title="'Reconciliations' | t"
        :extra-actions="extraActions"
        :left-panel-width="270"
        progress-id="accounting.adviser.reconciliations"
        v-if="isInitialized"
    >
        <template slot="left-panel">
            <div class="reconciliation-left">
                <ui-list
                    :key="partnerListKey"
                    ref="partnerList"
                    :get-rows="getPartners"
                    :enable-search="true"
                    :item-height="60"
                    label-from="partner.name"
                    :extra-fields="['partner.code', 'partner.name']"
                    :html-template="getPartnersCellTemplate"
                    single-select
                    @selected-items="handlePartnerSelect"
                />

                <div class="reconcile-all">
                    <el-button
                        type="primary"
                        icon="far fa-handshake-alt"
                        size="small"
                        @click="handleReconcileAllAutomatically"
                    >
                        {{ 'AI: Reconcile Automatically' | t }}
                    </el-button>
                </div>
            </div>
        </template>

        <template v-if="!!selectedPartner">
            <div class="reconciliation-records-container">
                <div class="reconciliation-records-header">
                    <div class="selected-partner-info">
                        <div class="selected-partner-avatar-container">
                            <div :class="selectedPartnerAvatarClasses">
                                {{ selectedPartnerShortName }}
                            </div>
                        </div>

                        <div class="selected-partner-content">
                            <div class="selected-partner-name">
                                {{ selectedPartner.name }}
                            </div>
                            <div class="selected-partner-details">
                                {{ selectedPartner.code }} - {{ selectedPartnerType }}
                            </div>
                        </div>
                    </div>

                    <div class="reconciliations-progress">
                        <el-progress
                            :text-inside="true"
                            :stroke-width="18"
                            :percentage="reconciliationProgress"
                            status="success"
                        />

                        <div class="reconciliations-progress-text">
                            {{ reconciledCount + ' / ' + (toReconcileCount || 0) }}
                        </div>
                    </div>

                    <div class="reconciliation-search">
                        <el-input v-model="recordsQuery" clearable size="small" :placeholder="'Search records..' | t">
                            <i slot="prefix" class="el-input__icon el-icon-search"></i>
                        </el-input>
                    </div>

                    <div class="reload-reconciliation-btn">
                        <el-button plain icon="far fa-handshake-alt" @click="handleReconcileAutomatically">
                            {{ 'AI: Reconcile Automatically' | t }}
                        </el-button>
                    </div>
                </div>

                <el-scrollbar class="reconciliation-records-content">
                    <ui-card
                        v-for="item in reconciliations.slice(0, 10)"
                        :key="item._id"
                        v-if="editingItemId === null || editingItemId === item._id"
                    >
                        <el-button
                            class="btn-reconcile"
                            plain
                            icon="far fa-handshake-alt"
                            :disabled="!item.canReconcile"
                            @click="() => handleReconcile(item)"
                        >
                            {{ 'Reconcile' | t }}
                        </el-button>
                        <el-button
                            class="btn-save-reconciliation mr10"
                            plain
                            icon="far fa-check"
                            :disabled="item.matchedRecords.length < 1"
                            @click="() => handleSaveReconciliation(item)"
                            v-if="editingItemId === item._id"
                        >
                            {{ 'Save' | t }}
                        </el-button>
                        <el-button
                            class="btn-save-reconciliation mr10"
                            plain
                            icon="far fa-pencil"
                            :disabled="item.matchedRecords.length < 1"
                            @click="() => (editingItemId = item._id)"
                            v-if="editingItemId === null"
                        >
                            {{ 'Update' | t }}
                        </el-button>

                        <!-- Matched items -->
                        <el-table
                            :data="item.matchedRecords"
                            :show-header="false"
                            size="mini"
                            class="matched-table"
                            style="width: 100%"
                            @row-click="$event => handleRemoveMatch(item, $event)"
                        >
                            <el-table-column prop="action" label="Action" width="30">
                                <template slot-scope="scope">
                                    <div class="reconcile-item-action">
                                        <i class="fal fa-minus-circle" />
                                    </div>
                                </template>
                            </el-table-column>
                            <el-table-column prop="date" label="Date" width="100" />
                            <el-table-column prop="description" label="Description" />
                            <el-table-column prop="reference" label="Reference" width="150" />
                            <el-table-column prop="currency.name" label="Currency" width="90" />
                            <el-table-column
                                prop="debitFormatted"
                                label="Debit"
                                class-name="t-cell text-right"
                                width="150"
                            />
                            <el-table-column
                                prop="creditFormatted"
                                label="Credit"
                                class-name="t-cell text-right"
                                width="150"
                            />
                            <el-table-column prop="info" label="Info" width="30">
                                <template slot-scope="scope">
                                    <el-popover
                                        placement="left-start"
                                        popper-class="reconciliation-info-popover"
                                        width="400"
                                        trigger="hover"
                                        v-model="scope.row.isInfoShown"
                                    >
                                        <div slot="reference" class="item-info">
                                            <i class="fas fa-info-circle" />
                                        </div>

                                        <table class="definition-table">
                                            <tr>
                                                <td style="min-width: 150px">{{ 'Due Date' | t }}</td>
                                                <td>{{ scope.row.date }}</td>
                                            </tr>
                                            <tr>
                                                <td style="min-width: 150px">{{ 'Issue Date' | t }}</td>
                                                <td>{{ $format(scope.row.issueDate, 'date') }}</td>
                                            </tr>
                                            <tr>
                                                <td>{{ 'Description' | t }}</td>
                                                <td>{{ scope.row.description }}</td>
                                            </tr>
                                            <tr>
                                                <td>{{ 'Reference' | t }}</td>
                                                <td>{{ scope.row.reference }}</td>
                                            </tr>
                                            <tr>
                                                <td>{{ 'Voucher No' | t }}</td>
                                                <td>{{ scope.row.voucherNo }}</td>
                                            </tr>
                                            <tr>
                                                <td>{{ 'Account code' | t }}</td>
                                                <td>{{ scope.row.account.code }}</td>
                                            </tr>
                                            <tr>
                                                <td>{{ 'Account name' | t }}</td>
                                                <td>{{ scope.row.account.name }}</td>
                                            </tr>
                                            <tr>
                                                <td>{{ 'Journal' | t }}</td>
                                                <td>{{ scope.row.journal.name }}</td>
                                            </tr>
                                            <tr>
                                                <td>{{ 'Currency' | t }}</td>
                                                <td>{{ scope.row.currency.name }}</td>
                                            </tr>
                                            <tr>
                                                <td>{{ 'Debit' | t }}</td>
                                                <td>{{ scope.row.originalDebitFormatted }}</td>
                                            </tr>
                                            <tr>
                                                <td>{{ 'Credit' | t }}</td>
                                                <td>{{ scope.row.originalCreditFormatted }}</td>
                                            </tr>
                                            <tr>
                                                <td>{{ 'Debit (FC)' | t }}</td>
                                                <td>{{ scope.row.originalDebitFCFormatted }}</td>
                                            </tr>
                                            <tr>
                                                <td>{{ 'Credit (FC)' | t }}</td>
                                                <td>{{ scope.row.originalCreditFCFormatted }}</td>
                                            </tr>

                                            <tr>
                                                <td>{{ 'R. Balance' | t }}</td>
                                                <td>{{ scope.row.reconciliationBalanceFormatted }}</td>
                                            </tr>
                                            <tr>
                                                <td>{{ 'R. Balance (FC)' | t }}</td>
                                                <td>{{ scope.row.reconciliationBalanceFCFormatted }}</td>
                                            </tr>
                                        </table>

                                        <div class="reconciliation-info-popover-actions">
                                            <el-button
                                                plain
                                                icon="far fa-arrow-right"
                                                @click="handleOpenDocument('journal-entry', scope.row)"
                                            >
                                                {{ 'Journal Entry' | t }}
                                            </el-button>

                                            <el-button
                                                type="primary"
                                                plain
                                                icon="far fa-arrow-right"
                                                @click="() => handleOpenDocument('document', scope.row)"
                                            >
                                                {{ 'Source Document' | t }}
                                            </el-button>
                                        </div>
                                    </el-popover>
                                </template>
                            </el-table-column>
                        </el-table>
                        <el-table
                            :data="item.remainingRecords"
                            :show-header="false"
                            :span-method="$event => getRemainingRowSpan(item, $event)"
                            ref="remainingTable"
                            size="mini"
                            class="remaining-table"
                            style="width: 100%"
                            @row-click="$event => false"
                            v-if="!!item.remainingRecords && item.remainingRecords.length > 0"
                        >
                            <el-table-column prop="action" label="Action" width="30" />
                            <el-table-column prop="date" label="Date" width="100" />
                            <el-table-column prop="description" label="Description" />
                            <el-table-column prop="reference" label="Reference" width="150" />
                            <el-table-column
                                prop="debitFormatted"
                                label="Debit"
                                class-name="t-cell text-right"
                                width="150"
                            />
                            <el-table-column
                                prop="creditFormatted"
                                label="Credit"
                                class-name="t-cell text-right"
                                width="150"
                            />
                            <el-table-column prop="info" label="Info" width="30" />
                        </el-table>

                        <!-- Un-Matched items -->
                        <template v-if="editingItemId === item._id">
                            <el-input
                                v-model="reconciliationsUnMatchedRecordsQueryMap[item._id]"
                                clearable
                                :placeholder="'Filter records..' | t"
                                style="max-width: 210px; margin: 20px 0 10px 0"
                                v-if="
                                    !!reconciliationsUnMatchedRecordsQueryMap[item._id] ||
                                    getFilteredReconciliationsUnMatchedRecords(item).length > 0
                                "
                            >
                                <i slot="prefix" class="el-input__icon el-icon-search"></i>
                            </el-input>
                            <el-table
                                :data="getFilteredReconciliationsUnMatchedRecords(item)"
                                :key="reconciliationsUnMatchedRecordsQueryMap[item._id]"
                                :show-header="false"
                                :empty-text="'Unable to find records that can be match!' | t"
                                size="mini"
                                class="matches-table"
                                style="width: 100%"
                                @row-click="$event => handleAddMatch(item, $event)"
                                v-if="getFilteredReconciliationsUnMatchedRecords(item).length > 0"
                            >
                                <el-table-column prop="action" label="Action" width="30">
                                    <template slot-scope="scope">
                                        <div class="reconcile-item-action">
                                            <i class="fal fa-plus-circle" />
                                        </div>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="date" label="Date" width="100" />
                                <el-table-column prop="description" label="Description" />
                                <el-table-column prop="reference" label="Reference" width="150" />
                                <el-table-column prop="currency.name" label="Currency" width="90" />
                                <el-table-column
                                    prop="debitFormatted"
                                    label="Debit"
                                    class-name="t-cell text-right"
                                    width="150"
                                />
                                <el-table-column
                                    prop="creditFormatted"
                                    label="Credit"
                                    class-name="t-cell text-right"
                                    width="150"
                                />
                                <el-table-column prop="info" label="Info" width="30">
                                    <template slot-scope="scope">
                                        <el-popover
                                            placement="left-start"
                                            popper-class="reconciliation-info-popover"
                                            width="400"
                                            trigger="hover"
                                            v-model="scope.row.isInfoShown"
                                        >
                                            <div slot="reference" class="item-info">
                                                <i class="fas fa-info-circle" />
                                            </div>

                                            <table class="definition-table">
                                                <tr>
                                                    <td style="min-width: 150px">{{ 'Due Date' | t }}</td>
                                                    <td>{{ scope.row.date }}</td>
                                                </tr>
                                                <tr>
                                                    <td style="min-width: 150px">{{ 'Issue Date' | t }}</td>
                                                    <td>{{ $format(scope.row.issueDate, 'date') }}</td>
                                                </tr>
                                                <tr>
                                                    <td>{{ 'Description' | t }}</td>
                                                    <td>{{ scope.row.description }}</td>
                                                </tr>
                                                <tr>
                                                    <td>{{ 'Reference' | t }}</td>
                                                    <td>{{ scope.row.reference }}</td>
                                                </tr>
                                                <tr>
                                                    <td>{{ 'Voucher No' | t }}</td>
                                                    <td>{{ scope.row.voucherNo }}</td>
                                                </tr>
                                                <tr>
                                                    <td>{{ 'Account code' | t }}</td>
                                                    <td>{{ scope.row.account.code }}</td>
                                                </tr>
                                                <tr>
                                                    <td>{{ 'Account name' | t }}</td>
                                                    <td>{{ scope.row.account.name }}</td>
                                                </tr>
                                                <tr>
                                                    <td>{{ 'Journal' | t }}</td>
                                                    <td>{{ scope.row.journal.name }}</td>
                                                </tr>
                                                <tr>
                                                    <td>{{ 'Currency' | t }}</td>
                                                    <td>{{ scope.row.currency.name }}</td>
                                                </tr>
                                                <tr>
                                                    <td>{{ 'Debit' | t }}</td>
                                                    <td>{{ scope.row.originalDebitFormatted }}</td>
                                                </tr>
                                                <tr>
                                                    <td>{{ 'Credit' | t }}</td>
                                                    <td>{{ scope.row.originalCreditFormatted }}</td>
                                                </tr>
                                                <tr>
                                                    <td>{{ 'Debit (FC)' | t }}</td>
                                                    <td>{{ scope.row.originalDebitFCFormatted }}</td>
                                                </tr>
                                                <tr>
                                                    <td>{{ 'Credit (FC)' | t }}</td>
                                                    <td>{{ scope.row.originalCreditFCFormatted }}</td>
                                                </tr>

                                                <tr>
                                                    <td>{{ 'R. Balance' | t }}</td>
                                                    <td>{{ scope.row.reconciliationBalanceFormatted }}</td>
                                                </tr>
                                                <tr>
                                                    <td>{{ 'R. Balance (FC)' | t }}</td>
                                                    <td>{{ scope.row.reconciliationBalanceFCFormatted }}</td>
                                                </tr>
                                            </table>

                                            <div class="reconciliation-info-popover-actions">
                                                <el-button
                                                    plain
                                                    icon="far fa-arrow-right"
                                                    @click="() => handleOpenDocument('journal-entry', scope.row)"
                                                >
                                                    {{ 'Journal Entry' | t }}
                                                </el-button>

                                                <el-button
                                                    type="primary"
                                                    plain
                                                    icon="far fa-arrow-right"
                                                    @click="() => handleOpenDocument('document', scope.row)"
                                                >
                                                    {{ 'Source Document' | t }}
                                                </el-button>
                                            </div>
                                        </el-popover>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </template>
                    </ui-card>

                    <ui-card v-if="unMatchedRecords.length > 0 && editingItemId === null">
                        <div class="reconciliation-card-title">{{ 'Records' | t }}</div>

                        <el-input
                            v-model="unMatchedRecordsQuery"
                            clearable
                            :placeholder="'Filter records..' | t"
                            style="max-width: 210px; margin: 20px 0 10px 0"
                        >
                            <i slot="prefix" class="el-input__icon el-icon-search"></i>
                        </el-input>
                        <el-table
                            :data="filteredUnMatchedRecords"
                            :show-header="false"
                            :empty-text="'Unable to find records that can be match!' | t"
                            size="mini"
                            class="matches-table"
                            style="width: 100%"
                            @row-click="$event => handleAddReconciliation($event)"
                            v-if="filteredUnMatchedRecords.length > 0"
                        >
                            <el-table-column prop="action" label="Action" width="30">
                                <template slot-scope="scope">
                                    <div class="reconcile-item-action">
                                        <i class="fal fa-plus-circle" />
                                    </div>
                                </template>
                            </el-table-column>
                            <el-table-column prop="date" label="Date" width="100" />
                            <el-table-column prop="description" label="Description" />
                            <el-table-column prop="reference" label="Reference" width="150" />
                            <el-table-column prop="currency.name" label="Currency" width="90" />
                            <el-table-column
                                prop="debitFormatted"
                                label="Debit"
                                class-name="t-cell text-right"
                                width="150"
                            />
                            <el-table-column
                                prop="creditFormatted"
                                label="Credit"
                                class-name="t-cell text-right"
                                width="150"
                            />
                            <el-table-column prop="info" label="Info" width="30">
                                <template slot-scope="scope">
                                    <el-popover
                                        placement="left-start"
                                        popper-class="reconciliation-info-popover"
                                        width="400"
                                        trigger="hover"
                                        v-model="scope.row.isInfoShown"
                                    >
                                        <div slot="reference" class="item-info">
                                            <i class="fas fa-info-circle" />
                                        </div>

                                        <table class="definition-table">
                                            <tr>
                                                <td style="min-width: 150px">{{ 'Due Date' | t }}</td>
                                                <td>{{ scope.row.date }}</td>
                                            </tr>
                                            <tr>
                                                <td style="min-width: 150px">{{ 'Issue Date' | t }}</td>
                                                <td>{{ $format(scope.row.issueDate, 'date') }}</td>
                                            </tr>
                                            <tr>
                                                <td>{{ 'Description' | t }}</td>
                                                <td>{{ scope.row.description }}</td>
                                            </tr>
                                            <tr>
                                                <td>{{ 'Reference' | t }}</td>
                                                <td>{{ scope.row.reference }}</td>
                                            </tr>
                                            <tr>
                                                <td>{{ 'Voucher No' | t }}</td>
                                                <td>{{ scope.row.voucherNo }}</td>
                                            </tr>
                                            <tr>
                                                <td>{{ 'Account code' | t }}</td>
                                                <td>{{ scope.row.account.code }}</td>
                                            </tr>
                                            <tr>
                                                <td>{{ 'Account name' | t }}</td>
                                                <td>{{ scope.row.account.name }}</td>
                                            </tr>
                                            <tr>
                                                <td>{{ 'Journal' | t }}</td>
                                                <td>{{ scope.row.journal.name }}</td>
                                            </tr>
                                            <tr>
                                                <td>{{ 'Currency' | t }}</td>
                                                <td>{{ scope.row.currency.name }}</td>
                                            </tr>
                                            <tr>
                                                <td>{{ 'Debit' | t }}</td>
                                                <td>{{ scope.row.originalDebitFormatted }}</td>
                                            </tr>
                                            <tr>
                                                <td>{{ 'Credit' | t }}</td>
                                                <td>{{ scope.row.originalCreditFormatted }}</td>
                                            </tr>
                                            <tr>
                                                <td>{{ 'Debit (FC)' | t }}</td>
                                                <td>{{ scope.row.originalDebitFCFormatted }}</td>
                                            </tr>
                                            <tr>
                                                <td>{{ 'Credit (FC)' | t }}</td>
                                                <td>{{ scope.row.originalCreditFCFormatted }}</td>
                                            </tr>

                                            <tr>
                                                <td>{{ 'R. Balance' | t }}</td>
                                                <td>{{ scope.row.reconciliationBalanceFormatted }}</td>
                                            </tr>
                                            <tr>
                                                <td>{{ 'R. Balance (FC)' | t }}</td>
                                                <td>{{ scope.row.reconciliationBalanceFCFormatted }}</td>
                                            </tr>
                                        </table>

                                        <div class="reconciliation-info-popover-actions">
                                            <el-button
                                                plain
                                                icon="far fa-arrow-right"
                                                @click="() => handleOpenDocument('journal-entry', scope.row)"
                                            >
                                                {{ 'Journal Entry' | t }}
                                            </el-button>

                                            <el-button
                                                type="primary"
                                                plain
                                                icon="far fa-arrow-right"
                                                @click="() => handleOpenDocument('document', scope.row)"
                                            >
                                                {{ 'Source Document' | t }}
                                            </el-button>
                                        </div>
                                    </el-popover>
                                </template>
                            </el-table-column>
                        </el-table>
                    </ui-card>
                </el-scrollbar>
            </div>
        </template>
        <el-empty-state class="reconciliations-empty-state" v-else />
    </ui-view>
</template>

<script>
import _ from 'lodash';
import fastCopy from 'fast-copy';
import {rawMongoQuery, toLower, toUpper, trim} from 'framework/helpers';
import Random from 'framework/random';

export default {
    data: () => ({
        selectedPartner: null,
        transactions: [],
        recordsQuery: '',
        unMatchedRecordsQuery: '',
        reconciliationsUnMatchedRecordsQueryMap: {},
        editingItemId: null,
        partnerListKey: _.uniqueId('partnerListKey_'),
        isInitialized: false
    }),

    computed: {
        extraActions() {
            return [
                {
                    name: 'reload-reconciliations',
                    title: 'Reload Reconciliations',
                    icon: 'fal fa-sync-alt',
                    handler: this.handleReloadTransactions,
                    disabled: () => {
                        return !this.selectedPartner;
                    }
                },
                {
                    name: 'reset-saved-reconciliations',
                    title: 'Reset Saved Reconciliations',
                    icon: 'fal fa-trash-alt',
                    handler: () => this.handleResetTransactions(true),
                    disabled: () => {
                        return !this.selectedPartner;
                    }
                },
                {
                    name: 'reset-all-reconciliations',
                    title: 'Reset All Reconciliations',
                    icon: 'fal fa-trash-alt',
                    handler: () => this.handleResetTransactions(false),
                    disabled: () => {
                        return !this.selectedPartner;
                    }
                }
            ];
        },
        selectedPartnerShortName() {
            return ((this.selectedPartner || {}).name || '')
                .split(' ')
                .map(s => toUpper(s[0]))
                .slice(0, 3)
                .join('');
        },
        selectedPartnerType() {
            const type = (this.selectedPartner || {}).type || 'customer';

            return type === 'customer'
                ? this.$t('Customer')
                : type === 'vendor'
                ? this.$t('Vendor')
                : this.$t('Employee');
        },
        selectedPartnerAvatarClasses() {
            const type = (this.selectedPartner || {}).type || 'customer';
            const classes = ['selected-partner-avatar'];

            if (type === 'customer') {
                classes.push('is-customer');
            } else if (type === 'vendor') {
                classes.push('is-vendor');
            } else if (type === 'employee') {
                classes.push('is-employee');
            }

            return classes;
        },
        records() {
            return fastCopy(this.transactions).map(transaction => {
                if (_.isPlainObject(transaction.reconciliationData) && !_.isEmpty(transaction.reconciliationData)) {
                    transaction = {
                        ...transaction,
                        ...transaction.reconciliationData
                    };
                }

                const formatOptions = {
                    currency: {
                        symbol: transaction.currency.symbol,
                        format: transaction.currency.symbolPosition === 'before' ? '%s %v' : '%v %s'
                    }
                };

                transaction.date = this.$format(transaction.dueDate, 'date');

                if (_.isNumber(transaction.debit) && transaction.debit !== 0)
                    transaction.debitFormatted = this.$format(transaction.debit, 'currency');
                if (_.isNumber(transaction.credit) && transaction.credit !== 0)
                    transaction.creditFormatted = this.$format(transaction.credit, 'currency');
                if (_.isNumber(transaction.debitFC) && transaction.debitFC !== 0)
                    transaction.debitFCFormatted = this.$format(transaction.debitFC, 'currency', formatOptions);
                if (_.isNumber(transaction.creditFC) && transaction.creditFC !== 0)
                    transaction.creditFCFormatted = this.$format(transaction.creditFC, 'currency', formatOptions);

                if (_.isNumber(transaction.originalDebit) && transaction.originalDebit !== 0)
                    transaction.originalDebitFormatted = this.$format(transaction.originalDebit, 'currency');
                if (_.isNumber(transaction.originalCredit) && transaction.originalCredit !== 0)
                    transaction.originalCreditFormatted = this.$format(transaction.originalCredit, 'currency');
                if (_.isNumber(transaction.originalDebitFC) && transaction.originalDebitFC !== 0) {
                    transaction.originalDebitFCFormatted = this.$format(
                        transaction.originalDebitFC,
                        'currency',
                        formatOptions
                    );
                }
                if (_.isNumber(transaction.originalCreditFC) && transaction.originalCreditFC !== 0) {
                    transaction.originalCreditFCFormatted = this.$format(
                        transaction.originalCreditFC,
                        'currency',
                        formatOptions
                    );
                }

                if (_.isNumber(transaction.reconciliationBalance) && transaction.reconciliationBalance !== 0) {
                    transaction.reconciliationBalanceFormatted = this.$format(
                        transaction.reconciliationBalance,
                        'currency'
                    );
                }

                if (_.isNumber(transaction.reconciliationBalanceFC) && transaction.reconciliationBalanceFC !== 0) {
                    transaction.reconciliationBalanceFCFormatted = this.$format(
                        transaction.reconciliationBalanceFC,
                        'currency',
                        formatOptions
                    );
                }

                return transaction;
            });
        },
        reconciledCount() {
            return this.records.filter(record => !!record.reconciliationCode).length;
        },
        toReconcileCount() {
            return this.records.length;
        },
        reconciliationProgress() {
            if (_.isNumber(this.toReconcileCount) && this.toReconcileCount > 0) {
                return this.$app.roundNumber((this.reconciledCount / this.toReconcileCount) * 100);
            }

            return 0;
        },
        reconciliations() {
            const company = this.$store.getters['session/company'];
            const query = toLower((this.recordsQuery || '').trim());
            const records = !!query
                ? this.records.filter(
                      record =>
                          !!record.reconciliationCode &&
                          (toLower(record.account.code).indexOf(query) > -1 ||
                              toLower(record.date).indexOf(query) > -1 ||
                              toLower(record.reference).indexOf(query) > -1 ||
                              toLower(record.description).indexOf(query) > -1 ||
                              toLower(record.debitFormatted).indexOf(query) > -1 ||
                              toLower(record.creditFormatted).indexOf(query) > -1 ||
                              toLower(record.debitFCFormatted).indexOf(query) > -1 ||
                              toLower(record.creditFCFormatted).indexOf(query) > -1)
                  )
                : this.records.filter(record => !!record.reconciliationCode);
            const grouped = _.groupBy(records, 'reconciliationCode');
            const items = [];

            for (const reconciliationCode of Object.keys(grouped)) {
                const item = {};

                item._id = reconciliationCode;
                item.canReconcile = false;
                item.showForeignCurrencies = false;
                item.matchedRecords = _.sortBy(grouped[reconciliationCode], 'reconciliationData.order');
                // item.unMatchedRecords = this.unMatchedRecords;

                let debitTotal = 0;
                let creditTotal = 0;
                let debitFCTotal = 0;
                let creditFCTotal = 0;
                for (const record of item.matchedRecords) {
                    debitTotal += record.debit || 0;
                    creditTotal += record.credit || 0;
                    debitFCTotal += record.debitFC || 0;
                    creditFCTotal += record.creditFC || 0;
                }
                debitTotal = this.$app.round(debitTotal, 'currency');
                creditTotal = this.$app.round(creditTotal, 'currency');
                debitFCTotal = this.$app.round(debitFCTotal, 'currency');
                creditFCTotal = this.$app.round(creditFCTotal, 'currency');
                if (debitTotal !== creditTotal || debitFCTotal !== creditFCTotal) {
                    const formatOptions = {
                        currency: {
                            symbol: item.matchedRecords[0].currency.symbol,
                            format: item.matchedRecords[0].currency.symbolPosition === 'before' ? '%s %v' : '%v %s'
                        }
                    };
                    const amount = -(debitTotal - creditTotal);
                    const amountFC = -(debitFCTotal - creditFCTotal);

                    const remainingRecord = {
                        date: this.$t('Choose a counterpart.'),
                        debit: amount > 0 ? amount : 0,
                        credit: amount < 0 ? Math.abs(amount) : 0,
                        debitFC: amountFC > 0 ? amountFC : 0,
                        creditFC: amountFC < 0 ? Math.abs(amountFC) : 0,
                        currency: {
                            name: ' '
                        }
                    };

                    if (_.isNumber(remainingRecord.debit) && remainingRecord.debit !== 0)
                        remainingRecord.debitFormatted = this.$format(remainingRecord.debit, 'currency');
                    if (_.isNumber(remainingRecord.credit) && remainingRecord.credit !== 0)
                        remainingRecord.creditFormatted = this.$format(remainingRecord.credit, 'currency');
                    if (_.isNumber(remainingRecord.debitFC) && remainingRecord.debitFC !== 0)
                        remainingRecord.debitFCFormatted = this.$format(
                            remainingRecord.debitFC,
                            'currency',
                            formatOptions
                        );
                    if (_.isNumber(remainingRecord.creditFC) && remainingRecord.creditFC !== 0)
                        remainingRecord.creditFCFormatted = this.$format(
                            remainingRecord.creditFC,
                            'currency',
                            formatOptions
                        );

                    item.remainingRecords = [remainingRecord];
                }

                item.canReconcile = debitTotal === creditTotal;

                items.push(item);
            }

            return items;
        },
        unMatchedRecords() {
            const query = toLower((this.recordsQuery || '').trim());

            return !!query
                ? this.records.filter(
                      record =>
                          !record.reconciliationCode &&
                          (toLower(record.account.code).indexOf(query) > -1 ||
                              toLower(record.date).indexOf(query) > -1 ||
                              toLower(record.reference).indexOf(query) > -1 ||
                              toLower(record.description).indexOf(query) > -1 ||
                              toLower(record.debitFormatted).indexOf(query) > -1 ||
                              toLower(record.creditFormatted).indexOf(query) > -1 ||
                              toLower(record.debitFCFormatted).indexOf(query) > -1 ||
                              toLower(record.creditFCFormatted).indexOf(query) > -1)
                  )
                : this.records.filter(record => !record.reconciliationCode);
        },
        filteredUnMatchedRecords() {
            const query = toLower((this.unMatchedRecordsQuery || '').trim());

            if (!!query) {
                return this.unMatchedRecords.filter(
                    record =>
                        toLower(record.account.code).indexOf(query) > -1 ||
                        toLower(record.date).indexOf(query) > -1 ||
                        toLower(record.reference).indexOf(query) > -1 ||
                        toLower(record.description).indexOf(query) > -1 ||
                        toLower(record.debitFormatted).indexOf(query) > -1 ||
                        toLower(record.creditFormatted).indexOf(query) > -1 ||
                        toLower(record.debitFCFormatted).indexOf(query) > -1 ||
                        toLower(record.creditFCFormatted).indexOf(query) > -1
                );
            }

            return this.unMatchedRecords;
        }
    },

    methods: {
        async getPartners(query, params) {
            const result = await this.$rpc('accounting.reconciliations-get-partners', {
                query: rawMongoQuery(query),
                skip: query.$skip,
                limit: query.$limit
            });

            setTimeout(() => {
                if (result.data.length > 0 && !this.selectedPartner) {
                    this.$refs.partnerList.select(result.data[0]._id);
                }
            }, 50);

            return result;
        },
        getPartnersCellTemplate(item) {
            const type = (item || {}).type || 'customer';
            let avatarTypeClass = '';

            if (type === 'customer') {
                avatarTypeClass = 'is-customer';
            } else if (type === 'vendor') {
                avatarTypeClass = 'is-vendor';
            } else if (type === 'employee') {
                avatarTypeClass = 'is-employee';
            }

            return `
<div class="reconciliations-partner-cell">
    <div class="partner-cell-avatar-container">
        <div class="partner-cell-avatar ${avatarTypeClass}">
            ${(item.name || '')
                .split(' ')
                .map(s => toUpper(s[0]))
                .slice(0, 3)
                .join('')}
        </div>
    </div>
    <div class="partner-cell-content">
        <div class="partner-cell-name">${item.name}</div>
        <div class="partner-cell-details">
            ${item.code} - ${
                item.type === 'customer'
                    ? this.$t('Customer')
                    : item.type === 'vendor'
                    ? this.$t('Vendor')
                    : this.$t('Employee')
            }
        </div>
    </div>
</div>
            `.trim();
        },
        async handlePartnerSelect(selected) {
            this.$params('loading', true);

            this.selectedPartner = selected[0];
            const items = await this.$rpc('accounting.reconciliations-get-transactions', {
                partnerId: this.selectedPartner._id
            });
            this.recordsQuery = '';
            this.editingItemId = null;
            this.transactions = this.getTransactions(items);

            this.$params('loading', false);
        },
        async handleReloadTransactions() {
            this.$params('loading', true);

            const items = await this.$rpc('accounting.reconciliations-get-transactions', {
                partnerId: this.selectedPartner._id
            });
            this.transactions = this.getTransactions(items);
            this.editingItemId = null;

            this.$params('loading', false);
        },
        async handleResetTransactions(onlySaved) {
            const isConfirmed = await new Promise(resolve => {
                this.$program.alert(
                    'confirm',
                    this.$t(
                        onlySaved
                            ? 'Saved reconciliations will be reset. Do you want to continue?'
                            : 'All the reconciliations will be reset. Do you want to continue?'
                    ),
                    confirmed => {
                        resolve(confirmed);
                    }
                );
            });
            if (!isConfirmed) return;

            this.$params('loading', true);

            try {
                await this.$rpc('accounting.reconciliations-reset-reconciliations', {
                    partnerId: this.selectedPartner._id,
                    onlySaved
                });
            } catch (error) {
                this.$program.message('error', error.message);
            }

            const items = await this.$rpc('accounting.reconciliations-get-transactions', {
                partnerId: this.selectedPartner._id
            });
            this.transactions = this.getTransactions(items);
            this.editingItemId = null;

            this.$params('loading', false);
        },
        handleAddReconciliation(transaction) {
            this.transactions = fastCopy(this.transactions).map(t => {
                if (t._id === transaction._id) {
                    t.reconciliationCode = Random.id(8);
                    t.reconciliationData = {
                        order: 1
                    };
                }

                return t;
            });
        },
        async handleAddMatch(reconciliation, transaction) {
            const company = this.$store.getters['session/company'];
            const transactions = fastCopy(this.transactions);
            const matchedRecords = _.orderBy(
                transactions.filter(t => t.reconciliationCode === reconciliation._id),
                ['reconciliationData.order'],
                ['asc']
            );
            const index = transactions.findIndex(t => t._id === transaction._id);

            transactions[index].reconciliationCode = reconciliation._id;
            transactions[index].reconciliationData = {
                ...(transactions[index].reconciliationData || {}),
                // currencyId: company.currencyId,
                // currency: company.currency,
                order: matchedRecords.length + 1
            };

            this.transactions = transactions;
        },
        handleRemoveMatch(reconciliation, transaction) {
            this.transactions = fastCopy(this.transactions).map(t => {
                if (t._id === transaction._id) {
                    delete t.reconciliationData;
                    delete t.reconciliationCode;
                }

                return t;
            });

            if (!this.reconciliations.find(r => r._id === reconciliation._id) && !!this.editingItemId) {
                this.editingItemId = null;
            }
        },
        getRemainingRowSpan(item, {row, column, rowIndex, columnIndex}) {
            if (column.property === 'date') {
                return {
                    rowspan: 1,
                    colspan: 3
                };
            } else if (
                column.property === 'action' ||
                column.property === 'debitFCFormatted' ||
                column.property === 'creditFCFormatted' ||
                column.property === 'debitFormatted' ||
                column.property === 'creditFormatted' ||
                column.property === 'info'
            ) {
                return {
                    rowspan: 1,
                    colspan: 1
                };
            } else {
                return {
                    rowspan: 1,
                    colspan: 0
                };
            }
        },
        async handleSaveReconciliation(item) {
            this.$params('loading', true);

            item = fastCopy(item);

            const company = this.$store.getters['session/company'];
            let invoiceReconciliationCode = null;
            if (
                item.matchedRecords[0].currencyId !== company.currencyId &&
                !!item.remainingRecords &&
                item.remainingRecords.length > 0 &&
                (item.remainingRecords[0].debit > 0 || item.remainingRecords[0].credit > 0)
            ) {
                const isConfirmed = await new Promise(resolve => {
                    this.$program.alert(
                        'confirm',
                        this.$t('Do you want to create an exchange rate difference invoice?'),
                        confirmed => {
                            resolve(confirmed);
                        }
                    );
                });
                if (isConfirmed) {
                    const firstRecord = item.matchedRecords[0];
                    const partnerType = firstRecord.partner.type;

                    for (const matchedRecord of item.matchedRecords.slice(1)) {
                        if (
                            matchedRecord.currencyId !== firstRecord.currencyId &&
                            matchedRecord.currencyId !== company.currencyId
                        ) {
                            this.$program.message(
                                'error',
                                this.$t(
                                    'The matching group cannot have multiple different currencies except the system currency!'
                                )
                            );
                            this.$params('loading', false);
                            return;
                        }
                    }

                    const debitRecords = [];
                    const creditRecords = [];
                    for (const matchedRecord of item.matchedRecords) {
                        const item = {};

                        item.transactionId = matchedRecord._id;

                        item.balance = matchedRecord.balance;
                        if (matchedRecord.currencyId === company.currencyId) {
                            const currencyAmount = await this.$convertCurrency({
                                from: company.currency.name,
                                to: firstRecord.currency.name,
                                value: Math.abs(item.balance),
                                options: {
                                    date: matchedRecord.issueDate
                                }
                            });
                            item.currencyRate = currencyAmount > 0 ? Math.abs(item.balance) / currencyAmount : 1;
                            item.balanceFC = item.balance > 0 ? currencyAmount : -currencyAmount;
                        } else {
                            item.currencyRate = matchedRecord.currencyRate;
                            item.balanceFC = matchedRecord.balanceFC;
                        }

                        item.isEvaluted = false;
                        item.remaining = Math.abs(item.balance);
                        item.remainingFC = Math.abs(item.balanceFC);

                        if (matchedRecord.credit > 0) {
                            creditRecords.push({...item, isDebit: false});
                        } else if (matchedRecord.debit > 0) {
                            debitRecords.push({...item, isDebit: true});
                        }
                    }

                    if (partnerType === 'customer') {
                        for (const debitRecord of debitRecords) {
                            for (const creditRecord of creditRecords) {
                                if (!(creditRecord.remaining > 0)) {
                                    continue;
                                }

                                if (creditRecord.remaining > debitRecord.remaining) {
                                    let currentDebitRemaining = debitRecord.remaining;

                                    debitRecord.remaining -= creditRecord.remaining;
                                    creditRecord.remaining -= currentDebitRemaining;
                                } else {
                                    debitRecord.remaining -= creditRecord.remaining;

                                    creditRecord.remaining = 0;
                                }

                                debitRecord.remaining = this.$app.round(Math.max(0, debitRecord.remaining), 'currency');
                                creditRecord.remaining = this.$app.round(
                                    Math.max(0, creditRecord.remaining),
                                    'currency'
                                );
                                debitRecord.isEvaluted = true;
                                creditRecord.isEvaluted = true;

                                if (!(debitRecord.remaining > 0)) {
                                    break;
                                }
                            }
                        }
                        for (const debitRecord of debitRecords) {
                            for (const creditRecord of creditRecords) {
                                if (!(creditRecord.remainingFC > 0)) {
                                    continue;
                                }

                                if (creditRecord.remainingFC > debitRecord.remainingFC) {
                                    let currentDebitRemainingFC = debitRecord.remainingFC;

                                    debitRecord.remainingFC -= creditRecord.remainingFC;
                                    creditRecord.remainingFC -= currentDebitRemainingFC;
                                } else {
                                    debitRecord.remainingFC -= creditRecord.remainingFC;

                                    creditRecord.remainingFC = 0;
                                }

                                debitRecord.remainingFC = this.$app.round(
                                    Math.max(0, debitRecord.remainingFC),
                                    'currency'
                                );
                                creditRecord.remainingFC = this.$app.round(
                                    Math.max(0, creditRecord.remainingFC),
                                    'currency'
                                );
                                debitRecord.isEvaluted = true;
                                creditRecord.isEvaluted = true;

                                if (!(debitRecord.remainingFC > 0)) {
                                    break;
                                }
                            }
                        }
                    } else {
                        for (const creditRecord of creditRecords) {
                            for (const debitRecord of debitRecords) {
                                if (!(debitRecord.remaining > 0)) {
                                    continue;
                                }

                                if (debitRecord.remaining > creditRecord.remaining) {
                                    let currentCreditRemaining = creditRecord.remaining;

                                    creditRecord.remaining -= debitRecord.remaining;
                                    debitRecord.remaining -= currentCreditRemaining;
                                } else {
                                    creditRecord.remaining -= debitRecord.remaining;

                                    debitRecord.remaining = 0;
                                }

                                creditRecord.remaining = this.$app.round(
                                    Math.max(0, creditRecord.remaining),
                                    'currency'
                                );
                                debitRecord.remaining = this.$app.round(Math.max(0, debitRecord.remaining), 'currency');
                                debitRecord.isEvaluted = true;
                                creditRecord.isEvaluted = true;

                                if (!(creditRecord.remaining > 0)) {
                                    break;
                                }
                            }
                        }
                        for (const creditRecord of creditRecords) {
                            for (const debitRecord of debitRecords) {
                                if (!(debitRecord.remainingFC > 0)) {
                                    continue;
                                }

                                if (debitRecord.remainingFC > creditRecord.remainingFC) {
                                    let currentCreditRemainingFC = creditRecord.remainingFC;

                                    creditRecord.remainingFC -= debitRecord.remainingFC;
                                    debitRecord.remainingFC -= currentCreditRemainingFC;
                                } else {
                                    creditRecord.remainingFC -= debitRecord.remainingFC;

                                    debitRecord.remainingFC = 0;
                                }

                                creditRecord.remainingFC = this.$app.round(
                                    Math.max(0, creditRecord.remainingFC),
                                    'currency'
                                );
                                debitRecord.remainingFC = this.$app.round(
                                    Math.max(0, debitRecord.remainingFC),
                                    'currency'
                                );
                                debitRecord.isEvaluted = true;
                                creditRecord.isEvaluted = true;

                                if (!(creditRecord.remainingFC > 0)) {
                                    break;
                                }
                            }
                        }
                    }

                    let result = debitRecords
                        .concat(creditRecords)
                        .filter(item => item.isEvaluted && (item.remaining > 0 || item.remainingFC > 0))
                        .map(item => {
                            item.exchangeRateDifference = item.remainingFC * item.currencyRate - item.remaining;

                            return item;
                        });
                    if (result.length < 1) {
                        this.$params('loading', false);
                        return;
                    }
                    const amount = result[0].exchangeRateDifference;
                    const isDebit = result[0].isDebit;

                    const partnerId = firstRecord.partnerId;
                    const component =
                        partnerType === 'customer'
                            ? 'accounting.sales.customer-invoices-detail'
                            : 'accounting.purchase.vendor-invoices-detail';
                    let isReturn = false;

                    if (partnerType === 'customer' && isDebit) {
                        isReturn = true;
                    } else if (partnerType === 'vendor' && !isDebit) {
                        isReturn = true;
                    }

                    const numbering = await this.$collection('kernel.numbering').findOne({
                        code: 'reconciliationNumbering',
                        $select: ['_id'],
                        $disableInUseCheck: true,
                        $disableActiveCheck: true,
                        $disableSoftDelete: true
                    });
                    invoiceReconciliationCode = await this.$rpc('kernel.common.request-number', {
                        numberingId: numbering._id,
                        save: true
                    });

                    const invoice = await new Promise(resolve => {
                        this.$program.dialog({
                            component,
                            params: {
                                forcedPreview: false,
                                isReturn,
                                exchangeRateInvoicePayload: {
                                    isReturn,
                                    partnerId,
                                    exchangeDifferenceCurrencyId: firstRecord.currencyId,
                                    reference: invoiceReconciliationCode,
                                    productId: this.$setting('accounting.reconciliationsExchangeRateServiceProductId'),
                                    taxId: this.$setting('accounting.reconciliationsExchangeRateTaxId'),
                                    amount: Math.abs(amount)
                                }
                            },
                            onSubmit: async payload => {
                                resolve(payload);
                            },
                            onClose() {
                                resolve(null);
                            }
                        });
                    });
                    if (!invoice) {
                        this.$params('loading', false);
                        return;
                    }

                    this.$params('loading', true);

                    await this.$collection('accounting.transactions').bulkWrite([
                        {
                            updateOne: {
                                filter: {
                                    _id: result[0].transactionId
                                },
                                update: {
                                    $set: {
                                        reconciliationBalanceFC:
                                            result[0].balance < 0 ? -result[0].remainingFC : result[0].remainingFC
                                    }
                                }
                            }
                        }
                    ]);
                }
            }

            try {
                await this.$rpc('accounting.reconciliations-save-reconciliation', {...item, invoiceReconciliationCode});
                this.editingItemId = null;
                await this.handleReloadTransactions();
            } catch (error) {
                this.$program.message('error', error.message);
            }

            this.$params('loading', false);
        },
        async handleReconcile(item) {
            const isConfirmed = await new Promise(resolve => {
                this.$program.alert(
                    'confirm',
                    this.$t('Transactions will be reconciled. Do you want to continue?'),
                    confirmed => {
                        resolve(confirmed);
                    }
                );
            });
            if (!isConfirmed) return;

            item = fastCopy(item);

            const company = this.$store.getters['session/company'];
            let reconciliationCode = '';
            let invoiceId = '';

            /*
            if (
                item.matchedRecords[0].currencyId !== company.currencyId &&
                !!item.remainingRecords &&
                item.remainingRecords.length > 0 &&
                (item.remainingRecords[0].debit > 0 || item.remainingRecords[0].credit > 0)
            ) {
                const amount =
                    item.remainingRecords[0].debit > 0
                        ? item.remainingRecords[0].debit
                        : item.remainingRecords[0].credit;
                const isDebit = item.remainingRecords[0].debit > 0;
                const partnerId = item.matchedRecords[0].partnerId;
                const partnerType = item.matchedRecords[0].partner.type;
                const component =
                    partnerType === 'customer'
                        ? 'accounting.sales.customer-invoices-detail'
                        : 'accounting.purchase.vendor-invoices-detail';
                const isReturn = partnerType === 'customer' ? !isDebit : isDebit;

                const numbering = await this.$collection('kernel.numbering').findOne({
                    code: 'reconciliationNumbering',
                    $select: ['_id'],
                    $disableInUseCheck: true,
                    $disableActiveCheck: true,
                    $disableSoftDelete: true
                });
                reconciliationCode = await this.$rpc('kernel.common.request-number', {
                    numberingId: numbering._id,
                    save: true
                });

                const invoice = await new Promise(resolve => {
                    this.$program.dialog({
                        component,
                        params: {
                            forcedPreview: false,
                            isReturn,
                            exchangeRateInvoicePayload: {
                                isReturn,
                                partnerId,
                                reference: reconciliationCode,
                                productId: this.$setting('accounting.reconciliationsExchangeRateServiceProductId'),
                                taxId: this.$setting('accounting.reconciliationsExchangeRateTaxId'),
                                amount: Math.abs(amount)
                            }
                        },
                        onSubmit: async payload => {
                            resolve(payload);
                        },
                        onClose() {
                            resolve(null);
                        }
                    });
                });
                if (!invoice) return;

                invoiceId = invoice._id;

                if (_.isPlainObject(invoice)) {
                    this.$params('loading', true);

                    try {
                        if (partnerType === 'customer') {
                            await this.$rpc('accounting.save-customer-invoice', {
                                id: invoice._id,
                                data: {
                                    ..._.omit(invoice, ['_id', 'extra']),
                                    status: 'approved'
                                }
                            });
                        } else {
                            await this.$rpc('accounting.save-vendor-invoice', {
                                id: invoice._id,
                                data: {
                                    ..._.omit(invoice, ['_id', 'extra']),
                                    status: 'approved'
                                }
                            });
                        }
                    } catch (error) {
                        this.$program.message('error', error.message);

                        this.$params('loading', false);
                        return;
                    }
                }
            }*/

            this.$params('loading', true);

            try {
                await this.$rpc('accounting.reconciliations-reconcile', {
                    ...item,
                    reconciliationCode,
                    exchangeDifferenceInvoiceId: invoiceId
                });

                this.transactions = fastCopy(this.transactions).filter(
                    record => record.reconciliationCode !== item._id
                );

                if (this.transactions.length < 1) {
                    this.selectedPartner = null;
                    this.$refs.partnerList.refresh();
                }

                this.editingItemId = null;

                this.$program.message('success', this.$t('Records successfully reconciled.'));
            } catch (error) {
                this.$program.message('error', error.message);
            }

            this.$params('loading', false);
        },
        async handleOpenDocument(type, transaction) {
            this.transactions = fastCopy(this.transactions).map(t => {
                if (t._id === transaction._id) {
                    t.isInfoShown = false;
                }

                return t;
            });

            if (type === 'journal-entry') {
                this.$shell.openProgram('accounting.adviser.journal-entries-detail', {
                    id: transaction.entryId,
                    isPreview: true
                });
            } else {
                this.$params('loading', true);

                let isFound = false;
                const customerInvoice = await this.$collection('accounting.customer-invoices').findOne({
                    journalEntryId: transaction.entryId,
                    $select: ['_id', 'isReturn']
                });
                if (!!customerInvoice) {
                    isFound = true;

                    if (!!customerInvoice.isReturn) {
                        this.$shell.openProgram('accounting.sales.customer-return-invoices-detail', {
                            id: customerInvoice._id,
                            isPreview: true
                        });
                    } else {
                        this.$shell.openProgram('accounting.sales.customer-invoices-detail', {
                            id: customerInvoice._id,
                            isPreview: true
                        });
                    }
                }
                if (!isFound) {
                    const vendorInvoice = await this.$collection('accounting.vendor-invoices').findOne({
                        journalEntryId: transaction.entryId,
                        $select: ['_id', 'isReturn']
                    });
                    if (!!vendorInvoice) {
                        isFound = true;

                        if (!!vendorInvoice.isReturn) {
                            this.$shell.openProgram('accounting.purchase.vendor-return-invoices-detail', {
                                id: vendorInvoice._id,
                                isPreview: true
                            });
                        } else {
                            this.$shell.openProgram('accounting.purchase.vendor-invoices-detail', {
                                id: vendorInvoice._id,
                                isPreview: true
                            });
                        }
                    }
                }
                if (!isFound) {
                    const financialEEntry = await this.$collection('finance.entries').findOne({
                        accountingJournalEntryId: transaction.entryId,
                        $select: ['_id', 'type']
                    });
                    if (!!financialEEntry) {
                        isFound = true;

                        if (financialEEntry.type === 'receipt') {
                            this.$shell.openProgram('finance.receivable.receipts-detail', {
                                id: financialEEntry._id,
                                type: 'receipt',
                                isPreview: true
                            });
                        } else {
                            this.$shell.openProgram('finance.payable.payments-detail', {
                                id: financialEEntry._id,
                                type: 'payment',
                                isPreview: true
                            });
                        }
                    }
                }
                if (!isFound) {
                    this.$program.message('error', this.$t('Document could not found!'));
                }

                this.$params('loading', false);
            }
        },
        async handleReconcileAutomatically() {
            const isConfirmed = await new Promise(resolve => {
                this.$program.alert(
                    'confirm',
                    this.$t('Transactions will be reconciled. Do you want to continue?'),
                    confirmed => {
                        resolve(confirmed);
                    }
                );
            });
            if (!isConfirmed) return;

            this.$params('loading', true);

            try {
                await this.$rpc('accounting.reconciliations-reconcile-all', {
                    query: {
                        partnerId: this.selectedPartner._id
                    }
                });

                this.editingItemId = null;

                await this.handleReloadTransactions();

                this.$program.message('success', this.$t('Records successfully reconciled.'));
            } catch (error) {
                this.$program.message('error', error.message);
            }

            this.$params('loading', false);
        },
        async handleReconcileAllAutomatically() {
            const isConfirmed = await new Promise(resolve => {
                this.$program.alert(
                    'confirm',
                    this.$t('All the transactions will be reconciled. Do you want to continue?'),
                    confirmed => {
                        resolve(confirmed);
                    }
                );
            });
            if (!isConfirmed) return;

            this.$params('loading', true);

            try {
                await this.$rpc('accounting.reconciliations-reconcile-all', {});

                this.selectedPartner = null;
                this.editingItemId = null;
                this.partnerListKey = _.uniqueId('partnerListKey_');

                this.$program.message('success', this.$t('Records successfully reconciled.'));
            } catch (error) {
                this.$program.message('error', error.message);
            }

            this.$params('loading', false);
        },
        getTransactions(items) {
            const transactions = [];

            for (const item of fastCopy(items)) {
                if (!!(item.reconciliationData || {}).isSaved) {
                    transactions.push(item);

                    items = items.filter(t => t._id !== item._id);
                }
            }

            const grouped = _.groupBy(items, 'reference');
            for (const reference of Object.keys(grouped)) {
                if (_.isString(reference) && trim(reference).length > 0 && grouped[reference].length > 1) {
                    const reconciliationCode = Random.id(8);
                    const groupedItems = grouped[reference];
                    let i = 1;

                    transactions.push(
                        ...groupedItems.map(t => {
                            t.reconciliationCode = reconciliationCode;
                            t.reconciliationData = {
                                order: i++
                            };

                            return t;
                        })
                    );
                    const ids = groupedItems.map(t => t._id);

                    items = items.filter(t => !ids.includes(t._id));
                }
            }

            const iterator = item => {
                let isDebit = item.debit > 0;
                let matching = items.find(
                    t =>
                        item._id !== t._id &&
                        ((isDebit && item.debit === t.credit) || (!isDebit && item.credit === t.debit))
                );

                if (!!matching) {
                    const reconciliationCode = Random.id(8);

                    transactions.push(
                        ...[
                            {
                                ...item,
                                reconciliationCode,
                                reconciliationData: {
                                    order: 1
                                }
                            },
                            {
                                ...matching,
                                reconciliationCode,
                                reconciliationData: {
                                    order: 2
                                }
                            }
                        ]
                    );

                    items = items.filter(t => t._id !== item._id && t._id !== matching._id);
                } else {
                    transactions.push(item);

                    items = items.filter(t => t._id !== item._id);
                }

                if (items.length > 0) {
                    iterator(items[0]);
                }
            };
            if (items.length > 0) {
                iterator(items[0]);
            }

            return transactions;
        },
        getFilteredReconciliationsUnMatchedRecords(item) {
            const query = toLower((this.reconciliationsUnMatchedRecordsQueryMap[item._id] || '').trim());

            return this.unMatchedRecords.filter(
                record =>
                    toLower(record.account.code).indexOf(query) > -1 ||
                    toLower(record.date).indexOf(query) > -1 ||
                    toLower(record.reference).indexOf(query) > -1 ||
                    toLower(record.description).indexOf(query) > -1 ||
                    toLower(record.debitFormatted).indexOf(query) > -1 ||
                    toLower(record.creditFormatted).indexOf(query) > -1 ||
                    toLower(record.debitFCFormatted).indexOf(query) > -1 ||
                    toLower(record.creditFCFormatted).indexOf(query) > -1
            );
        }
    },

    async created() {
        this.isInitialized = true;
    }
};
</script>

<style lang="scss">
//noinspection CssUnknownTarget
@import 'core';

.reconciliation-left {
    display: flex;
    flex-flow: column nowrap;
    width: 100%;
    height: 100%;
    overflow: hidden;

    .ui-list {
        flex: 1 1 0;
    }

    .reconcile-all {
        padding: 15px;
        border-top: 1px solid $border-color;
        background-color: white;

        .el-button {
            width: 100%;
        }
    }
}

.reconciliation-records-container {
    position: relative;
    width: 100%;
    height: 100%;
    padding-top: 75px;
    overflow: hidden;

    .reconciliation-records-header {
        position: absolute;
        display: flex;
        align-items: stretch;
        width: 100%;
        top: 0;
        left: 0;
        height: 75px;
        padding: 15px;
        background-color: #fff;
        border-bottom: 1px solid $border-color;

        .selected-partner-info {
            display: flex;
            flex: 1;
            min-width: 0;

            .selected-partner-avatar-container {
                display: flex;
                flex-flow: column nowrap;
                justify-content: center;
                align-items: center;
                width: 45px;
                height: 45px;

                .selected-partner-avatar {
                    display: flex;
                    flex-flow: column nowrap;
                    justify-content: center;
                    align-items: center;
                    width: 100%;
                    height: 100%;
                    border-radius: 50%;
                    background-color: $primary;
                    color: #fff;
                    font-size: 14px;
                    line-height: 1;

                    &.is-customer {
                        background-color: palate('orange', '500');
                    }

                    &.is-vendor {
                        background-color: palate('green', '500');
                    }

                    &.is-employee {
                        background-color: palate('purple', '500');
                    }
                }
            }

            .selected-partner-content {
                flex: 1;
                margin-left: 15px;
                line-height: 1;
                min-width: 0;

                .selected-partner-name {
                    width: 100%;
                    margin-bottom: 10px;
                    font-size: 16px;
                    font-weight: 700;
                    overflow: hidden;
                    @include text-truncate();
                }

                .selected-partner-details {
                    width: 100%;
                    font-size: 13px;
                    overflow: hidden;
                    @include text-truncate();
                }
            }
        }

        .reconciliations-progress {
            display: flex;
            flex-flow: row nowrap;
            justify-content: flex-end;
            align-items: center;
            padding: 4.5px 0;
            flex: 0 0 365px;

            .el-progress {
                flex: 0 0 290px;
            }

            .reconciliations-progress-text {
                display: flex;
                align-items: center;
                margin-left: 10px;
                white-space: nowrap;
            }
        }

        .reload-reconciliation-btn {
            display: flex;
            //align-items: center;
            margin-left: 15px;
            margin-top: 6px;
            height: 32px;

            .el-button {
                &:not(.is-disabled) {
                    border-color: $primary;
                    color: $primary;

                    &:hover,
                    &:focus {
                        background-color: $primary;
                        color: #fff;
                    }
                }
            }
        }

        .reconciliation-search {
            display: flex;
            align-items: center;
            margin-left: 30px;
        }
    }

    .reconciliation-records-content {
        position: relative;
        width: 100%;
        height: 100%;
        padding: 20px;
        overflow: hidden;

        .ui-card {
            margin-bottom: 15px;

            .reconciliation-card-title {
                font-size: 16px;
                font-weight: 700;
                line-height: 1;
            }

            .ui-card-header {
                border-bottom: 1px solid $border-color-light;
            }

            .ui-card-content {
                padding: 20px;

                .el-button.btn-reconcile {
                    margin-bottom: 10px;
                    float: right;

                    &:not(.is-disabled) {
                        border-color: $primary;
                        color: $primary;

                        &:hover,
                        &:focus {
                            background-color: $primary;
                            color: #fff;
                        }
                    }
                }

                .el-button.btn-save-reconciliation {
                    margin-bottom: 10px;
                    float: right;

                    &:not(.is-disabled) {
                        border-color: $success;
                        color: $success;

                        &:hover,
                        &:focus {
                            background-color: $success;
                            color: #fff;
                        }
                    }
                }
            }

            &:last-child {
                margin-bottom: 0;
            }

            .el-table {
                tr {
                    cursor: pointer;
                }

                tr:hover > td {
                    background-color: $light-25;
                }

                td {
                    padding: 0 !important;
                    transition: none !important;

                    .cell {
                        height: 24px !important;
                        line-height: 24px !important;
                        font-size: 12px !important;
                        font-weight: 700;
                        @include text-truncate();
                    }
                }

                .el-table__empty-block {
                    border-top: 1px solid $border-color-light;
                    min-height: 45px;
                }
            }

            .reconcile-item-action {
                width: 100%;
                height: 24px;
                font-size: 14px;
                line-height: 24px;
                cursor: pointer;

                &:hover i {
                    font-weight: 900;

                    &:before {
                        content: '\f055';
                    }
                }
            }

            .item-info {
                color: #bbb;
                cursor: pointer;

                &:hover {
                    color: $info;
                }
            }

            .matched-table {
                tr:first-child {
                    td {
                        &.t-cell {
                            border-top: 1px solid darken($border-color, 5%);
                        }
                    }
                }

                td {
                    &:nth-child(7) {
                        border-left: 1px solid darken($border-color, 5%);
                    }
                }

                .reconcile-item-action {
                    &:hover i {
                        color: $danger;
                        font-weight: 900;

                        &:before {
                            content: '\f056';
                        }
                    }
                }
            }

            .remaining-table {
                tr {
                    td {
                        background-color: #fff !important;
                        cursor: pointer;
                    }

                    .cell {
                        color: #bbb !important;
                    }
                }
            }

            .write-off-form {
                margin-top: 15px;
                padding: 15px;
                border: 1px solid $border-color-light;
                background-color: $light-25;
            }

            .matches-table {
                tr:first-child td {
                    border-top: 1px solid $border-color-light;
                }

                .reconcile-item-action {
                    &:hover i {
                        color: $success;
                    }
                }

                td {
                    &:nth-child(7) {
                        border-left: 1px solid darken($border-color, 5%);
                    }
                }
            }
        }
    }
}

.reconciliations-empty-state {
    position: relative;
    background-color: #fff;
}

.reconciliations-partner-cell {
    display: flex;
    padding: 10px;

    .partner-cell-avatar-container {
        display: flex;
        flex-flow: column nowrap;
        justify-content: center;
        align-items: center;
        width: 40px;
        height: 40px;
    }

    .partner-cell-avatar {
        display: flex;
        flex-flow: column nowrap;
        justify-content: center;
        align-items: center;
        width: 100%;
        height: 100%;
        border-radius: 50%;
        background-color: $primary;
        color: #fff;
        font-size: 14px;
        line-height: 1;

        &.is-customer {
            background-color: palate('orange', '500');
        }

        &.is-vendor {
            background-color: palate('green', '500');
        }

        &.is-employee {
            background-color: palate('purple', '500');
        }
    }

    .partner-cell-content {
        display: flex;
        flex-flow: column;
        justify-content: center;
        flex: 1;
        margin-left: 10px;
        min-width: 0;
    }

    .partner-cell-name {
        width: 100%;
        overflow: hidden;
        @include text-truncate();
        font-size: 14px;
        line-height: 1;
        margin-bottom: 8px;
    }

    .partner-cell-details {
        width: 100%;
        overflow: hidden;
        @include text-truncate();
        font-size: 12px;
        color: #7f878a;
        line-height: 1;
    }
}

.reconciliation-info-popover {
    td:last-child {
        -webkit-user-select: text;
        -moz-user-select: text;
        -ms-user-select: text;
        -o-user-select: text;
        user-select: text;
    }

    .reconciliation-info-popover-actions {
        display: flex;
        margin-top: 10px;
        margin-left: -5px;
        margin-right: -5px;

        .el-button {
            flex: 1 1 0;
            margin: 0 5px;
        }
    }
}
</style>
