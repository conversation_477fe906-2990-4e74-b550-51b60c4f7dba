<template>
    <ui-view
        type="table"
        collection="accounting.journal-entries"
        :columns="columns"
        :filters="filters"
        :extra-actions="extraActions"
        actions="create,remove"
        progress-id="accounting.adviser.journal-entries"
        @create="handleCreate"
        @remove="handleRemove"
        v-if="initialized"
    >
        <template slot="top-panel">
            <ui-scope
                id="accounting.adviser.journal-entries"
                :filters="scopeApplicableFilters"
                :applied-items="scopeAppliedItems"
                @changed="handleScopeChange"
            />

            <el-uploader
                accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                @started="$params('loading', true)"
                @completed="handleImport"
                v-show="false"
            >
                <el-button ref="importBtn" size="mini" icon="far fa-cloud-upload" plain>
                    {{ 'Import' | t }}
                </el-button>
            </el-uploader>
        </template>
    </ui-view>
</template>

<script>
import _ from 'lodash';
import saveAs from 'framework/save-as';

export default {
    data: () => ({
        scopeQuery: {},
        fiscalYear: null,
        isPeriodEndEntry: false,
        initialized: false
    }),

    computed: {
        filters() {
            let filters = _.cloneDeep(this.scopeQuery);

            if (!!this.$params('periodEndEntry')) {
                filters.periodEndEntry = !!this.$params('periodEndEntry');
            } else {
                if (!Array.isArray(filters.$or)) {
                    filters.$or = [];
                }

                filters.$or.push({
                    periodEndEntry: {$exists: false}
                });
                filters.$or.push({
                    periodEndEntry: {$ne: true}
                });
            }

            return filters;
        },
        columns() {
            return [
                {
                    type: 'integer',
                    field: 'entryNo',
                    label: 'Journal No',
                    width: 120
                },
                {
                    field: 'recordDate',
                    label: 'Record Date',
                    format: 'date',
                    width: 120
                },
                {
                    field: 'issueDate',
                    label: 'Issue Date',
                    sort: 'desc',
                    format: 'date',
                    width: 120
                },
                {
                    field: 'dueDate',
                    label: 'Due Date',
                    format: 'date',
                    width: 120,
                    visible: false
                },
                {
                    field: 'voucherNo',
                    label: 'Voucher No',
                    width: 150
                },
                {
                    field: 'documentNo',
                    label: 'Document No',
                    width: 150
                },
                {
                    field: 'branch.name',
                    label: 'Branch Office',
                    hidden: !this.$setting('system.multiBranch')
                },
                {
                    field: 'reference',
                    label: 'Reference'
                },
                {
                    field: 'description',
                    label: 'Description',
                    visible: false
                },
                {
                    field: 'journal.name',
                    label: 'Journal'
                },
                {
                    field: 'total',
                    label: 'Total',
                    format: 'currency',
                    width: 150
                },
                {
                    field: 'status',
                    label: 'Status',
                    tagsCell: true,
                    translateLabels: true,
                    tagLabels: [
                        {label: 'Draft', value: 'draft', color: 'default'},
                        {label: 'Posted', value: 'posted', color: 'primary'},
                        {label: 'Canceled', value: 'canceled', color: 'danger'}
                    ],
                    width: 120
                }
            ];
        },
        scopeAppliedItems() {
            return [
                {
                    type: 'filter',
                    payload: {
                        label: 'Fiscal year',
                        field: 'period.fiscalYearId',
                        value: {
                            value: this.fiscalYear._id,
                            label: this.fiscalYear.name
                        },
                        collection: 'kernel.periods',
                        isRemovable: false,
                        singleSelect: true
                    }
                }
            ];
        },
        scopeApplicableFilters() {
            const self = this;
            return [
                // Pre-defined
                {code: 'thisMonth', label: 'This month', query: 'recordDate|thisMonth'},
                {code: 'lastMonth', label: 'Last month', query: 'recordDate|lastMonth'},
                {code: 'thisQuarter', label: 'This quarter', query: 'recordDate|thisQuarter'},
                {code: 'lastQuarter', label: 'Last quarter', query: 'recordDate|lastQuarter'},

                // Filters.
                {field: 'entryNo', label: 'Entry no', type: 'integer'},
                {field: 'voucherNo', label: 'Voucher no'},
                {field: 'documentNo', label: 'Document no'},
                {
                    field: 'recordDate',
                    code: 'recordDate',
                    label: 'Record date',
                    type: 'date'
                },
                {
                    field: 'issueDate',
                    code: 'issueDate',
                    label: 'Issue date',
                    type: 'date'
                },
                {
                    field: 'dueDate',
                    code: 'dueDate',
                    label: 'Due date',
                    type: 'date'
                },
                {
                    field: 'branchId',
                    label: 'Branch office',
                    collection: 'kernel.branches',
                    filters: {
                        _id: {$in: this.$user.branchIds},
                        $sort: {name: 1}
                    },
                    condition() {
                        return self.$setting('system.multiBranch');
                    }
                },
                {field: 'reference', label: 'Reference'},
                {field: 'description', label: 'Description'},
                {
                    field: 'journalId',
                    label: 'Journal',
                    collection: 'accounting.journals',
                    filters: {
                        $sort: {name: 1}
                    }
                },
                {field: 'total', label: 'Total', type: 'integer'},
                {
                    field: 'status',
                    label: 'Status',
                    translateLabels: true,
                    items: [
                        {value: 'draft', label: 'Draft'},
                        {value: 'posted', label: 'Posted'},
                        {label: 'Canceled', value: 'canceled'}
                    ]
                }
            ];
        },
        extraActions() {
            const actions = [];

            if (!this.isPeriodEndEntry) {
                actions.push({
                    name: 'import-journal-entries',
                    title: 'Import Journal Entries',
                    icon: 'fal fa-cloud-upload',
                    handler: () => {
                        this.$refs.importBtn.$el.click();
                    }
                });
                actions.push({
                    name: 'download-sample-data',
                    title: 'Download Sample Data',
                    icon: 'fal fa-cloud-download',
                    handler: this.handleDownloadSample
                });
            }

            return actions;
        }
    },

    methods: {
        handleScopeChange(model) {
            this.scopeQuery = model.query;
        },
        async handleCreate() {
            if (!!this.$params('periodEndEntry')) {
                const payload = await new Promise(resolve => {
                    this.$program.dialog({
                        component: 'accounting.adviser.journal-entries.period-end',
                        params: {
                            title: this.$t('Period End Journal')
                        },
                        onSubmit: payload => resolve(payload),
                        onClose: () => resolve(null)
                    });
                });
                if (!payload) return;

                this.$params('loading', true);

                try {
                    const id = await this.$rpc('accounting.create-period-end-journal', payload);

                    this.$program.go('accounting.adviser.journal-entries-detail', {
                        id,
                        isPreview: false
                    });
                } catch (error) {
                    this.$program.message('error', error.message);
                }

                this.$params('loading', false);
            } else {
                this.$program.go('accounting.adviser.journal-entries-detail', {
                    isPreview: false
                });
            }
        },
        async handleRemove(entryIds) {
            this.$params('loading', true);

            const user = this.$user;
            const entries = await this.$collection('accounting.journal-entries').find({
                _id: {$in: entryIds},
                $select: ['status']
            });
            if (!user.isRoot && entries.some(entry => entry.status === 'posted')) {
                this.$params('loading', false);
                this.$program.message('error', this.$t('You cannot delete posted journal entries!'));
                return;
            }

            try {
                await this.$rpc('accounting.remove-journal-entries', entryIds);
            } catch (error) {
                this.$program.message('error', error.message);
            }

            this.$params('loading', false);
        },
        async handleImport(payload) {
            this.$params('loading', true);

            try {
                await this.$rpc('accounting.import-journal-entries', {
                    fileId: payload._id
                });
            } catch (error) {
                this.$program.message('error', error.message);
            }

            this.$params('loading', false);
        },
        async handleDownloadSample() {
            this.$params('loading', true);

            const buffer = await this.$rpc('accounting.download-journal-entry-sample');
            const blob = new Blob([buffer], {
                type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            });

            saveAs(blob, `${this.$t('Entries')}.xlsx`);

            this.$params('loading', false);
        }
    },

    async created() {
        this.$params('loading', true);

        this.fiscalYear = await this.$collection('kernel.periods').findOne({
            fiscalYear: this.$datetime.local().year,
            $select: ['_id', 'name']
        });

        this.isPeriodEndEntry = !!this.$params('periodEndEntry');

        this.initialized = true;

        this.$nextTick(() => {
            this.$params('loading', false);
        });
    }
};
</script>
