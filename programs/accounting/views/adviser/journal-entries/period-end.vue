<template>
    <ui-view
        type="form"
        :model="model"
        :schema="schema"
        :before-init="beforeInit"
        :before-validate="beforeValidate"
        :before-submit="beforeSubmit"
        actions="edit,cancel"
        :dialog-width="920"
        :dialog-height="295"
        @changed="handleChange"
        v-if="initialized"
    >
        <div class="columns">
            <div class="column is-half">
                <ui-field name="templateId" collection="accounting.period-end-templates" />
                <ui-field name="fiscalYearId" collection="kernel.periods" />
                <ui-field name="startPeriod" :options="periodOptions" />
                <ui-field name="endPeriod" :options="periodOptions" />
                <ui-field name="branchBased" />
                <kernel-common-branch-select />
                <ui-field name="recordBased" v-show="!!template && template.type === 'partner-advance-payment'" />
                <ui-field name="applyDueConstraintInPeriod" v-show="!!model.recordBased" />
            </div>

            <div class="column is-half">
                <ui-field name="scope" :options="scopeOptions" v-show="isScopeShown" />
                <ui-field
                    name="journalId"
                    collection="accounting.journals"
                    view="accounting.configuration.journals"
                    :filters="journalIdFilters"
                    :update-params="updateJournalIdParams"
                />
                <ui-field
                    name="financialProjectId"
                    collection="kernel.financial-projects"
                    view="system.management.configuration.financial-projects"
                    disable-detail
                    disable-create
                    :extra-fields="['code']"
                    :template="'{{code}} - {{name}}'"
                />
                <ui-field name="issueDate" />
                <ui-field name="reference" />
                <ui-field name="description" />
            </div>
        </div>
    </ui-view>
</template>

<script>
import _ from 'lodash';

export default {
    data: () => ({
        model: {},
        periodOptions: [],
        template: null,
        initialized: false
    }),

    computed: {
        schema() {
            return {
                templateId: {
                    type: 'string',
                    label: 'Template'
                },
                fiscalYearId: {
                    type: 'string',
                    label: 'Fiscal year'
                },
                startPeriod: {
                    type: 'string',
                    label: 'Start period',
                    required: false
                },
                endPeriod: {
                    type: 'string',
                    label: 'End period',
                    required: false
                },
                journalId: {
                    type: 'string',
                    label: 'Journal'
                },
                branchBased: {
                    type: 'boolean',
                    label: 'Branch based',
                    default: false
                },
                branchId: {
                    type: 'string',
                    label: 'Branch office'
                },
                financialProjectId: {
                    type: 'string',
                    label: 'Financial project',
                    required: false
                },
                scope: {
                    type: 'string',
                    label: 'Scope',
                    default: '1'
                },
                recordBased: {
                    type: 'boolean',
                    label: 'Record based',
                    default: false
                },
                applyDueConstraintInPeriod: {
                    type: 'boolean',
                    label: 'Apply due constraint in period',
                    default: false
                },
                issueDate: {
                    type: 'date',
                    label: 'Issue date',
                    default: 'date:now'
                },
                reference: {
                    type: 'string',
                    label: 'Reference',
                    required: false
                },
                description: {
                    type: 'string',
                    label: 'Description',
                    required: false
                }
            };
        },
        scopeOptions() {
            return [
                {value: '1', label: this.$setting('system.scope1Label')},
                {value: '2', label: this.$setting('system.scope2Label')}
            ];
        },
        isScopeShown() {
            return !!this.$setting('system.scopes') && this.model.documentType !== 'pos';
        },
        journalIdFilters() {
            return {
                type: 'period-end'
            };
        }
    },

    methods: {
        async beforeInit(model) {
            const journal = await this.$collection('accounting.journals').findOne({
                type: 'period-end',
                $select: ['_id']
            });
            if (!!journal) {
                model.journalId = journal._id;
            }

            return model;
        },
        async beforeValidate(model) {
            return model;
        },
        async beforeSubmit(model) {
            return model;
        },
        async handleChange(model, field) {
            if (field === 'templateId') {
                const template = await this.$collection('accounting.period-end-templates').findOne({
                    _id: model.templateId,
                    $select: ['_id', 'type', 'journalId']
                });

                this.model.journalId = template.journalId ?? '';
                this.template = template;
            }

            if (field === 'fiscalYearId') {
                const fiscalYear = await this.$collection('kernel.periods').findOne({
                    _id: model.fiscalYearId
                });
                if (_.isPlainObject(fiscalYear)) {
                    const periods = _.orderBy(fiscalYear.periods, ['issueDateStart'], ['asc']) || [];
                    const po = [];

                    for (const period of periods) {
                        po.push({value: period.code, label: period.code});
                    }

                    this.periodOptions = po;
                    this.model.issueDate = this.$datetime
                        .local()
                        .set({year: fiscalYear.fiscalYear})
                        .endOf('year')
                        .toJSDate();
                }

                this.model.startPeriod = '';
                this.model.endPeriod = '';
            }

            if (field === 'endPeriod' && !!model.endPeriod) {
                const fiscalYear = await this.$collection('kernel.periods').findOne({
                    _id: model.fiscalYearId
                });
                const period = ((fiscalYear ?? {}).periods ?? []).find(p => p.code === model.endPeriod);

                if (!!period) {
                    this.model.issueDate = period.issueDateEnd;
                }
            }
        },
        updateJournalIdParams(params) {
            params.model = {
                type: 'period-end'
            };

            return params;
        }
    },

    async created() {
        this.$params('loading', true);

        this.initialized = true;

        this.$params('loading', false);
    }
};
</script>
