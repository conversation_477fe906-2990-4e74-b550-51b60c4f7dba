export default [
    // {name: 'panel', title: 'Panel', icon: 'desktop'},
    {
        name: 'sales',
        title: 'Sales',
        icon: 'chart-line',
        items: [
            {
                name: 'customer-invoices',
                title: 'Customer Invoices'
            },
            {
                name: 'customer-invoices-detail',
                params: {model: {module: 'sale'}}
            },
            {
                name: 'customer-return-invoices',
                title: 'Customer Return Invoices',
                view: 'accounting.sales.customer-invoices.master',
                params: {isReturn: true}
            },
            {
                name: 'customer-return-invoices-detail',
                view: 'accounting.sales.customer-invoices.detail',
                params: {isReturn: true}
            },
            {
                name: 'receivable-receipts',
                title: 'Receivable Receipts',
                view: 'finance.receipts.master',
                params: {type: 'receipt'}
            },
            {
                name: 'receivable-receipts-detail',
                view: 'finance.receipts.detail',
                params: {type: 'receipt'}
            },
            // {name: 'refunds', title: 'Refunds'},
            // {name: 'deliveries', title: 'Deliveries'},
            {
                name: 'customers',
                title: 'Customers',
                view: 'partners.partners.master',
                params: {filters: {type: 'customer'}}
            },
            {
                name: 'customers-detail',
                view: 'partners.partners.detail',
                params: {model: {type: 'customer'}}
            },
            {
                name: 'sellable-products',
                title: 'Sellable Products',
                view: 'inventory.catalog.products.master',
                params: {filters: {canBeSold: true}}
            },
            {
                name: 'sellable-products-detail',
                view: 'inventory.catalog.products.detail',
                params: {model: {canBeSold: true}}
            },
            {
                name: 'invoices-for-approval',
                title: 'Invoices for Approval',
                view: 'accounting.sales.customer-invoices.master',
                params: {isBulkApproval: true}
            },
            {
                name: 'invoices-for-approval-detail',
                view: 'accounting.sales.customer-invoices.detail'
            }
        ]
    },
    {
        name: 'purchase',
        title: 'Purchase',
        icon: 'shopping-cart',
        items: [
            {name: 'vendor-invoices', title: 'Vendor Invoices'},
            {name: 'vendor-invoices-detail'},
            {
                name: 'vendor-return-invoices',
                title: 'Vendor Return Invoices',
                view: 'accounting.purchase.vendor-invoices.master',
                params: {isReturn: true}
            },
            {
                name: 'vendor-return-invoices-detail',
                view: 'accounting.purchase.vendor-invoices.detail',
                params: {isReturn: true}
            },
            {
                name: 'payment-receipts',
                title: 'Payment Receipts',
                view: 'finance.receipts.master',
                params: {type: 'payment'}
            },
            {
                name: 'payment-receipts-detail',
                view: 'finance.receipts.detail',
                params: {type: 'payment'}
            },
            // {name: 'refunds', title: 'Refunds'},
            // {name: 'deliveries', title: 'Deliveries'},
            {
                name: 'vendors',
                title: 'Vendors',
                view: 'partners.partners.master',
                params: {filters: {type: 'vendor'}}
            },
            {
                name: 'vendors-detail',
                view: 'partners.partners.detail',
                params: {model: {type: 'vendor'}}
            },
            {
                name: 'purchasable-products',
                title: 'Purchasable Products',
                view: 'inventory.catalog.products.master',
                params: {filters: {canBePurchased: true}}
            },
            {
                name: 'purchasable-products-detail',
                view: 'inventory.catalog.products.detail',
                params: {model: {canBePurchased: true}}
            }
        ]
    },
    // {
    //     name: 'declarations', title: 'Declarations', icon: 'file-alt', items: [
    //         {name: 'value-added-tax-1', title: 'Value-added Tax 1'},
    //         {name: 'value-added-tax-2', title: 'Value-added Tax 2'},
    //         {name: 'corporation-tax', title: 'Corporation Tax'},
    //         {name: 'provisional-tax', title: 'Provisional Tax'},
    //         {name: 'withholding-tax', title: 'Withholding Tax'}
    //     ],
    // },
    {
        name: 'adviser',
        title: 'Adviser',
        icon: 'book',
        items: [
            {name: 'reconciliations', title: 'Reconciliations'},
            {name: 'journal-entries', title: 'Journal Entries'},
            {name: 'journal-entries-detail'},
            {
                name: 'period-end-entries',
                title: 'Period End Entries',
                view: 'accounting.adviser.journal-entries.master',
                params: {periodEndEntry: true}
            },
            {name: 'stock-counts', title: 'Stock Counts'},
            {
                name: 'stock-counts-detail',
                view: 'inventory.operations.counts.detail'
            },
            {name: 'stock-valuations', title: 'Stock Valuations', view: 'inventory.operations.valuations.master'},
            {
                name: 'stock-valuations-detail',
                view: 'inventory.operations.valuations.detail'
            },
            // {name: 'tax-adjustments', title: 'Tax Adjustments'},
            // {name: 'fixture-management', title: 'Fixture Management'},
            // {name: 'declarations', title: 'Declarations'},
            {name: 'opening-records', title: 'Opening Records'},
            {name: 'opening-records-detail'},
            {name: 'closing-records', title: 'Closing Records'},
            {name: 'closing-records-detail'},
            {
                name: 'exchange-rate-simulation',
                title: 'Exchange Rate Simulation',
                view: 'finance.operations.exchange-rate-simulation.master'
            },
            {
                name: 'exchange-difference-valuations',
                title: 'Exchange Difference Valuations'
            },
            {
                name: 'periodic-inventory-recommendations',
                title: 'Periodic Inventory Recommendations'
            }
        ]
    },
    {
        name: 'reports',
        title: 'Reports',
        icon: 'chart-bar',
        items: [
            {name: 'budget', title: 'Budget'},
            {name: 'balance-sheet', title: 'Balance Sheet'},
            {name: 'profit-and-loss', title: 'Profit and Loss'},
            {name: 'document-journal', title: 'Document Journal'},
            {name: 'general-ledger', title: 'General Ledger'},
            {name: 'trial-balance', title: 'Trial Balance'},
            {name: 'detailed-trial-balance', title: 'Detailed Trial Balance'},
            {name: 'consolidated-journals', title: 'Consolidated Journals'},
            {name: 'partner-balances', title: 'Partner Balances'},
            {name: 'partner-ledger', title: 'Partner Ledger'},
            {name: 'account-ledger', title: 'Account Ledger'},
            {name: 'tax', title: 'Tax'},
            {
                name: 'aged-receivables',
                title: 'Aged Receivables',
                params: {type: 'receivables'},
                view: 'accounting.reports.aged'
            },
            {
                name: 'aged-payments',
                title: 'Aged Payments',
                params: {type: 'payments'},
                view: 'accounting.reports.aged'
            },
            {name: 'product-sales-analysis', title: 'Product Sales Analysis'}
            // {name: 'fixed-assets', title: 'Fixed Assets'},
            // {name: 'invoices', title: 'Invoices'},
        ]
    },
    {
        name: 'configuration',
        title: 'Configuration',
        icon: 'wrench',
        items: [
            {
                name: 'chart-of-accounts',
                title: 'Chart Of Accounts',
                view: 'system.management.configuration.chart-of-accounts.master'
            },
            {name: 'journals', title: 'Journals'},
            {name: 'journals-detail'},
            {name: 'budget-items', title: 'Budget Items'},
            {name: 'budget-tags', title: 'Budget Tags'},
            {
                name: 'taxes',
                title: 'Taxes',
                view: 'system.management.configuration.taxes.master'
            },
            {
                name: 'taxes-detail',
                view: 'system.management.configuration.taxes.detail'
            },
            {
                name: 'accounting-periods',
                title: 'Accounting Periods',
                view: 'system.management.configuration.accounting-periods.master'
            },
            {
                name: 'accounting-periods-detail',
                view: 'system.management.configuration.accounting-periods.detail'
            },
            {
                name: 'account-set-groups',
                title: 'Account Set Groups',
                view: 'system.management.configuration.account-set-groups.master'
            },
            {
                name: 'account-set-groups-detail',
                view: 'system.management.configuration.account-set-groups.detail'
            },
            {
                name: 'default-accounts',
                title: 'Default Accounts',
                view: 'system.management.configuration.default-accounts'
            },
            {
                name: 'financial-projects',
                title: 'Financial Projects',
                view: 'system.management.configuration.financial-projects.master'
            },
            {name: 'period-end-templates', title: 'Period End Templates'},
            {name: 'period-end-templates-detail'},
            {name: 'reconciliation-models', title: 'Reconciliation Models'},
            {name: 'reconciliation-models-detail'},
            {name: 'bank-accounts', title: 'Bank Accounts'},
            {name: 'credit-cards', title: 'Credit Cards'},
            {name: 'credit-cards-detail'},
            {name: 'pos', title: 'POS'},
            {name: 'pos-detail'}
            // {name: 'pos-wizard', title: 'POS Wizard'}
        ]
    },

    // Settings
    {
        name: 'settings',
        title: 'Settings',
        icon: 'cog',
        params: {isPreview: true}
    }
];
