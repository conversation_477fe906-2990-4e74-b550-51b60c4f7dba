import _ from 'lodash';
import * as Excel from 'exceljs';
import path from 'path';
import Random from 'framework/random';
import {firstUpperAll} from 'framework/helpers';
import fastCopy from 'fast-copy';

export default {
    name: 'export-detailed-trail-balance',
    async action({filters, filterEmptyAccounts}, params) {
        const app = this.app;
        const t = app.translate;
        const extension = 'xlsx';
        const file = `${Random.id(16)}.${extension}`;
        const progressId = `accounting.reports.detailed-trial-balance-${params.user._id}`;

        // Get account report.
        const result = await app.rpc(
            'accounting.reports-trail-balance',
            {filters, filterEmptyAccounts},
            {user: params.user}
        );

        // Get partner account ids.
        let partnerAccountIds = [];
        const partnerAccounts = await app.collection('kernel.accounts').find({
            isPartnerAccount: true,
            $select: ['_id']
        });
        if (partnerAccounts.length > 0) {
            partnerAccountIds = partnerAccounts.map(account => account._id);
        } else {
            partnerAccountIds = await app.collection('kernel.partners').distinct('accountingAccountId');
        }
        partnerAccountIds = _.uniq(partnerAccountIds);

        // Partner report pipeline.
        const partnerReportPipeline = [
            {
                $match: {
                    ...filters,
                    partnerId: {$exists: true}
                }
            },
            {
                $group: {
                    _id: '$partnerId',
                    partner: {$first: '$partner'},
                    debit: {$sum: '$debit'},
                    credit: {$sum: '$credit'},
                    debitFC: {$sum: '$debitFC'},
                    creditFC: {$sum: '$creditFC'}
                }
            },
            {
                $project: {
                    partner: 1,
                    debit: 1,
                    credit: 1,
                    balance: {$subtract: ['$debit', '$credit']}
                }
            },
            {$sort: {'partner.name': 1}}
        ];

        // Total partner records.
        let totalPartnerRecordsCount = 0;
        const countResult = await app
            .collection('accounting.transactions')
            .aggregate(partnerReportPipeline.concat([{$count: 'count'}]));
        if (countResult.length > 0) totalPartnerRecordsCount = countResult[0].count;

        // Start progress.
        app.progress({
            id: progressId,
            status: 'started'
        });

        // Filter zero items.
        // result.items = _.orderBy(
        //     result.items.filter(item => item.balance !== 0),
        //     ['code'],
        //     ['asc']
        // );

        // Prepare workbook.
        const workbook = new Excel.stream.xlsx.WorkbookWriter({
            filename: path.join(app.config('paths.temp'), file),
            useStyles: true
        });
        workbook.creator = 'EnterERP';
        workbook.lastModifiedBy = 'EnterERP';
        workbook.created = app.datetime.local().toJSDate();
        workbook.modified = app.datetime.local().toJSDate();
        workbook.lastPrinted = app.datetime.local().toJSDate();
        const worksheet = workbook.addWorksheet(t('Detailed Trial Balance'), {
            views: [{state: 'frozen', xSplit: 0, ySplit: 1}]
        });

        // Prepare columns.
        worksheet.columns = [
            {
                header: firstUpperAll(t('Account code')),
                key: 'code',
                width: 30
            },
            {
                header: firstUpperAll(t('Account name')),
                key: 'name',
                width: 60
            },
            {
                header: firstUpperAll(t('Status')),
                key: 'redBalance',
                width: 30
            },
            {header: firstUpperAll(t('Debit')), key: 'debit', width: 15, style: {numFmt: '#,##0.00'}},
            {header: firstUpperAll(t('Credit')), key: 'credit', width: 15, style: {numFmt: '#,##0.00'}},
            {header: firstUpperAll(t('Debit Balance')), key: 'debitBalance', width: 15, style: {numFmt: '#,##0.00'}},
            {header: firstUpperAll(t('Credit Balance')), key: 'creditBalance', width: 15, style: {numFmt: '#,##0.00'}},
            {header: firstUpperAll(t('Balance')), key: 'balance', width: 15, style: {numFmt: '#,##0.00'}}
        ].map(c => {
            if (
                c.key !== 'balance' &&
                c.key !== 'debit' &&
                c.key !== 'credit' &&
                c.key !== 'debitBalance' &&
                c.key !== 'creditBalance'
            ) {
                c.style = {numFmt: '@'};
            }

            return c;
        });

        // Set header styles.
        const headerRow = worksheet.getRow(1);
        headerRow.font = {
            color: {argb: 'FFFFFFFF'},
            bold: true
        };
        headerRow.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: {argb: 'FF0052CC'}
        };

        for (const item of result.items) {
            if (partnerAccountIds.includes(item._id)) {
                const pipeline = fastCopy(partnerReportPipeline);
                pipeline[0].$match.accountId = item._id;

                let total = 0;
                const countResult = await app
                    .collection('accounting.transactions')
                    .aggregate(pipeline.concat([{$count: 'count'}]));
                if (countResult.length > 0) total = countResult[0].count;

                const limit = 100;
                let page = 0;
                let rowCount = 0;
                do {
                    const report = (
                        await app
                            .collection('accounting.transactions')
                            .aggregate(pipeline.concat([{$skip: page * limit}, {$limit: limit}]))
                    ).map(r => {
                        r.debit = r.debit || 0;
                        r.credit = r.credit || 0;
                        r.balance = r.balance || 0;

                        return r;
                    });

                    // Commit rows.
                    for (const r of report) {
                        let row = {};

                        row.code = (r.partner || {}).code;
                        row.name = (r.partner || {}).name;
                        row.debit = r.debit;
                        row.credit = r.credit;
                        row.balance = r.balance;

                        if (item.type === 'assets' && !item.regulatory && row.credit > row.debit) {
                            row.redBalance = true;
                        }
                        if (item.type === 'assets' && item.regulatory && row.debit > row.credit) {
                            row.redBalance = true;
                        }
                        if (
                            (item.type === 'liabilities' ||
                                item.type === 'equity' ||
                                item.type === 'sales' ||
                                item.type === 'costOfSales' ||
                                item.type === 'extraordinaryRevenuesAndExpenses') &&
                            !item.regulatory &&
                            row.debit > row.credit
                        ) {
                            row.redBalance = true;
                        }
                        if (
                            (item.type === 'liabilities' ||
                                item.type === 'equity' ||
                                item.type === 'sales' ||
                                item.type === 'costOfSales' ||
                                item.type === 'extraordinaryRevenuesAndExpenses') &&
                            item.regulatory &&
                            row.credit > row.debit
                        ) {
                            row.redBalance = true;
                        }
                        if (item.type === 'operatingExpenses' && row.credit > row.debit) {
                            row.redBalance = true;
                        }
                        if (item.type === 'financialExpenses') {
                            if (item.inverseExpense) {
                                row.redBalance = row.credit > row.debit;
                            } else {
                                if (!item.regulatory && row.debit > row.credit) {
                                    row.redBalance = true;
                                }
                                if (item.regulatory && row.credit > row.debit) {
                                    row.redBalance = true;
                                }
                            }
                        }

                        if (row.debit > row.credit) {
                            row.debitBalance = row.debit - row.credit;
                        } else if (row.credit > row.debit) {
                            row.creditBalance = row.credit - row.debit;
                        }

                        row.redBalance = !!row.redBalance ? t('Red Balance') : '';

                        if (!(row.balance === 0 && row.debit === 0 && row.credit === 0)) {
                            await worksheet.addRow(row).commit();
                        }

                        rowCount++;
                    }

                    // Send progress percentage.
                    if (totalPartnerRecordsCount !== 0) {
                        const percentage = (rowCount / totalPartnerRecordsCount) * 100;
                        app.progress({
                            id: progressId,
                            status: 'info',
                            percentage
                        });
                    }

                    page++;
                } while (total > 0 && rowCount < total);
            } else {
                const row = worksheet.addRow({
                    code: item.code,
                    name: item.name,
                    redBalance: !!item.redBalance ? t('Red Balance') : '',
                    debit: item.debit,
                    credit: item.credit,
                    debitBalance: item.debitBalance,
                    creditBalance: item.creditBalance,
                    balance: item.balance
                });

                const depthColorMap = {
                    0: 'FFa6aab3',
                    1: 'FFb5b9c0',
                    2: 'FFc4c7cc',
                    3: 'FFd3d5d9',
                    4: 'FFe1e3e6',
                    5: 'FFf0f1f2'
                };

                row.fill = {
                    type: 'pattern',
                    pattern: 'solid',
                    fgColor: {argb: depthColorMap[item.tree.depth] ?? 'FFFFFFFF'}
                };

                await row.commit();
            }
        }

        // Summary.
        const summaryRow = worksheet.addRow({
            code: '',
            name: t('Total'),
            redBalance: '',
            debit: result.totalDebit,
            credit: result.totalCredit,
            debitBalance: result.totalDebitBalance,
            creditBalance: result.totalCreditBalance,
            balance: ''
        });
        summaryRow.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: {argb: 'FFE9E9ED'}
        };
        await summaryRow.commit();

        // Finished adding data. Commit the worksheet
        await worksheet.commit();

        // Finished the workbook.
        await workbook.commit();

        app.progress({
            id: progressId,
            status: 'success',
            percentage: 100
        });

        return {file, name: `${t('Detailed Trial Balance')}.${extension}`};
    }
};
