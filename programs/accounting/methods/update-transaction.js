import _ from 'lodash';

export default {
    name: 'update-transaction',
    async action({id, payload, voucherNo}, params) {
        const app = this.app;
        const user = params.user;
        const partnersCollection = app.collection('kernel.partners');
        const accountsCollection = app.collection('kernel.accounts');
        const branchesCollection = app.collection('kernel.branches');
        const journalsCollection = app.collection('accounting.journals');
        const transactionsCollection = app.collection('accounting.transactions');
        const now = app.datetime.local().toJSDate();
        const data = {};

        const originalTransaction = await transactionsCollection.findOne({_id: id, $select: ['_id', 'partnerId']});

        const account = await accountsCollection.findOne({
            _id: payload.accountId,
            $select: ['code', 'name', 'type', 'regulatory', 'currencyId', 'reconciliation', 'tree'],
            $disableActiveCheck: true,
            $disableSoftDelete: true
        });
        const branch = await branchesCollection.findOne({
            _id: payload.branchId,
            $select: ['code', 'name'],
            $disableActiveCheck: true,
            $disableSoftDelete: true
        });
        let partner = null;
        if (!!payload.partnerId) {
            partner = await partnersCollection.findOne({
                _id: payload.partnerId,
                $select: [
                    'code',
                    'name',
                    'type',
                    'identity',
                    'tin',
                    'groupId',
                    'tags',
                    'mainPartner',
                    'connectedPartnerIds'
                ],
                $disableActiveCheck: true,
                $disableSoftDelete: true
            });
        }
        const journal = await journalsCollection.get(payload.journalId);

        data.documentNo = payload.documentNo;
        data.reference = payload.reference;
        data.description = payload.description;
        data.accountId = payload.accountId;
        data.partnerId = payload.partnerId;
        data.recordDate = payload.recordDate;
        data.issueDate = payload.issueDate;
        data.dueDate = payload.dueDate;
        data.journalId = payload.journalId;
        if (!!payload.declaredEntryNo) {
            data.declaredEntryNo = payload.declaredEntryNo;
        }
        data.account = _.omit(account, ['currency']);
        data.branch = branch;

        if (payload.partnerId && partner) {
            data.partner = _.omit(partner, ['mainPartner', 'connectedPartnerIds', 'tags']);
            data.partner.tinIdentity = !!partner.tin ? partner.tin : partner.identity;
            data.partner.tags = Array.isArray(partner.tags) ? partner.tags.map(tag => tag.label) : [];

            if (
                Array.isArray(partner.connectedPartnerIds) &&
                partner.connectedPartnerIds.length > 0 &&
                !partner.mainPartner
            ) {
                const mainPartner = await partnersCollection.findOne({
                    _id: {$in: partner.connectedPartnerIds},
                    mainPartner: true,
                    $select: [
                        '_id',
                        'code',
                        'name',
                        'type',
                        'identity',
                        'tin',
                        'groupId',
                        'mainPartner',
                        'connectedPartnerIds'
                    ],
                    $disableActiveCheck: true,
                    $disableSoftDelete: true
                });

                if (!!mainPartner) {
                    data.partnerId = mainPartner._id;
                    data.partner = _.omit(mainPartner, ['mainPartner', 'connectedPartnerIds']);
                    data.partner.tinIdentity = !!mainPartner.tin ? mainPartner.tin : mainPartner.identity;
                }
            }
        }

        data.journal = _.pick(journal, ['_id', 'name', 'type', 'numberingId', 'debitAccountId', 'creditAccountId']);

        if (data.account.tree.depth === 1) {
            data.mainAccount = data.account;
        } else if (data.account.tree.depth === 2) {
            data.mainAccount = data.account;
        } else if (data.account.tree.depth > 2) {
            const parentAccounts = await accountsCollection.ancestors(data.account.tree.path);

            data.mainAccount = _.pick(
                parentAccounts.find(a => a.tree.depth === 2),
                ['_id', 'code', 'name', 'type', 'regulatory', 'currencyId', 'reconciliation', 'tree']
            );
            data.mainAccount = _.omit(data.mainAccount, ['currency']);
        }

        // Fix dates.
        data.recordDate = app.datetime
            .fromJSDate(data.recordDate)
            .set({
                hour: now.getHours(),
                minute: now.getMinutes(),
                second: now.getSeconds()
            })
            .toJSDate();
        data.issueDate = app.datetime
            .fromJSDate(data.issueDate)
            .set({
                hour: now.getHours(),
                minute: now.getMinutes(),
                second: now.getSeconds()
            })
            .toJSDate();
        data.dueDate = app.datetime
            .fromJSDate(data.dueDate)
            .set({
                hour: now.getHours(),
                minute: now.getMinutes(),
                second: now.getSeconds()
            })
            .toJSDate();

        await transactionsCollection.patch({_id: id}, data, {user});

        let partnerIds = [];
        if (!!originalTransaction.partnerId) {
            partnerIds.push(originalTransaction.partnerId);
        }
        if (!!data.partnerId) {
            partnerIds.push(data.partnerId);
        }
        partnerIds = _.uniq(partnerIds);
        await app.rpc('accounting.partner-balances-sync', {partnerIds});

        const log = {};
        log.collection = 'accounting.transactions';
        log.documentId = id;
        log.documentIdentifier = voucherNo;
        log.method = 'update';
        log.collectionName = `${app.translate('Accounting')} / ${app.translate('Transactions')}`;
        log.userId = user._id;
        log.user = {
            name: user.name,
            email: user.email
        };
        await app.log(log);
    }
};
