import fs from 'fs-extra';
import path from 'path';
import archiver from 'archiver';
import _ from 'lodash';
import Random from 'framework/random';
import {trim} from 'framework/helpers';

export default {
    name: 'create-e-journal',
    async action(payload, params) {
        const app = this.app;
        const {startDate, endDate, nextEntryNo, isBranchBased} = payload;
        const company = await app.collection('kernel.company').findOne({});
        const fileName = Random.id(16);
        const textFilePath = path.join(app.config('paths.temp'), `${fileName}.txt`);
        const zipFilePath = path.join(app.config('paths.temp'), `${fileName}.zip`);
        const progressId = `accounting.reports.document-journal-${params.user._id}`;
        const stream = fs.createWriteStream(textFilePath);

        // Start progress.
        app.progress({
            id: progressId,
            status: 'started'
        });

        // Get users.
        const users = await app.collection('kernel.users').find({
            $select: ['_id', 'name'],
            $disableActiveCheck: true,
            $disableSoftDelete: true
        });
        const usersMap = {};
        for (const user of users) {
            usersMap[user._id] = user;
        }

        try {
            // Get $match.
            const query = {
                issueDate: {$gte: startDate, $lte: endDate},
                scope: '1',
                mainAccount: {$exists: true},
                balance: {$ne: 0},
                $and: [{'account.code': {$not: {$regex: '^8.*'}}}, {'account.code': {$not: {$regex: '^9.*'}}}]
            };

            if (!!payload.subCompanyId) {
                const companyBranches = await app.collection('kernel.branches').find({
                    companyId: payload.subCompanyId,
                    $select: ['_id']
                });

                if (companyBranches.length > 0) {
                    query.$and.push({
                        branchId: {$in: companyBranches.map(branch => branch._id)}
                    });
                }
            }

            // Opening record entry record count
            const openingEntryCountReport = await app.collection('accounting.transactions').aggregate([
                {$match: {...query, belongsToOpeningRecord: true, 'journal.type': {$ne: 'period-end'}}},
                {
                    $group: {
                        _id: '$entryId'
                    }
                },
                {
                    $count: 'count'
                }
            ]);
            const totalOpeningEntryCount = openingEntryCountReport.length > 0 ? openingEntryCountReport[0].count : 0;

            // Journal entry record count
            const normalEntryCountReport = await app.collection('accounting.transactions').aggregate([
                {
                    $match: {
                        ...query,
                        belongsToOpeningRecord: {$ne: true},
                        belongsToClosingRecord: {$ne: true},
                        'journal.type': {$ne: 'period-end'}
                    }
                },
                {
                    $group: {
                        _id: '$entryId'
                    }
                },
                {
                    $count: 'count'
                }
            ]);
            const totalNormalEntryCount = normalEntryCountReport.length > 0 ? normalEntryCountReport[0].count : 0;

            // Period end journal entry record count
            const periodEndEntryCountReport = await app.collection('accounting.transactions').aggregate([
                {
                    $match: {
                        ...query,
                        belongsToOpeningRecord: {$ne: true},
                        belongsToClosingRecord: {$ne: true},
                        'journal.type': 'period-end'
                    }
                },
                {
                    $group: {
                        _id: '$entryId'
                    }
                },
                {
                    $count: 'count'
                }
            ]);
            const totalPeriodEndEntryCount = periodEndEntryCountReport.length > 0 ? normalEntryCountReport[0].count : 0;

            // Closing record entry record count
            const closingEntryCountReport = await app.collection('accounting.transactions').aggregate([
                {$match: {...query, belongsToClosingRecord: true, 'journal.type': {$ne: 'period-end'}}},
                {
                    $group: {
                        _id: '$entryId'
                    }
                },
                {
                    $count: 'count'
                }
            ]);
            const totalClosingEntryCount = closingEntryCountReport.length > 0 ? closingEntryCountReport[0].count : 0;

            // Check count.
            if (
                totalOpeningEntryCount + totalNormalEntryCount + totalPeriodEndEntryCount + totalClosingEntryCount <
                1
            ) {
                return;
            }

            // Get total.
            let totalEntryCount = totalOpeningEntryCount > 0 ? totalNormalEntryCount + 1 : totalNormalEntryCount;
            if (totalClosingEntryCount > 0) {
                totalEntryCount += 1;
            }
            if (totalPeriodEndEntryCount > 0) {
                totalEntryCount += totalPeriodEndEntryCount;
            }

            // Get opening transactions count.
            const openingTransactionsCount = await app
                .collection('accounting.transactions')
                .count({...query, belongsToOpeningRecord: true, 'journal.type': {$ne: 'period-end'}});

            // Get normal transactions count.
            const normalTransactionsCount = await app.collection('accounting.transactions').count({
                ...query,
                belongsToOpeningRecord: {$ne: true},
                belongsToClosingRecord: {$ne: true},
                'journal.type': {$ne: 'period-end'}
            });

            // Get period end transactions count.
            const periodEndTransactionsCount = await app.collection('accounting.transactions').count({
                ...query,
                belongsToOpeningRecord: {$ne: true},
                belongsToClosingRecord: {$ne: true},
                'journal.type': 'period-end'
            });

            // Get closing transactions count.
            const closingTransactionsCount = await app
                .collection('accounting.transactions')
                .count({...query, belongsToClosingRecord: true, 'journal.type': {$ne: 'period-end'}});

            // Total row count.
            let totalRowCount = 0;

            // Current entry no.
            let currentEntryNo = nextEntryNo;

            // L row.
            const lRow = [];
            lRow.push('L');
            lRow.push(company.tin);
            lRow.push('');
            lRow.push(app.datetime.fromJSDate(startDate).toFormat('yyyy-LL-dd'));
            lRow.push(app.datetime.fromJSDate(endDate).toFormat('yyyy-LL-dd'));
            lRow.push(totalEntryCount);
            await stream.write(`${lRow.join('|')}\n`);

            // Process opening records.
            await (async () => {
                const limit = 1000;
                const total = openingTransactionsCount;
                let page = 0;
                let rowCount = 0;
                let headerWritten = false;
                let headerVoucherNo = '';

                do {
                    const transactionOperations = [];
                    const filters = {
                        ...query,
                        belongsToOpeningRecord: true,
                        'journal.type': {$ne: 'period-end'},
                        $sort: {issueDate: 1}
                    };
                    let result = null;

                    // Adjust page.
                    filters.$skip = page * limit;
                    filters.$limit = limit;

                    // Get rows.
                    result = await app.collection('accounting.transactions').find(filters);

                    for (const item of result) {
                        const createdBy = usersMap[item.createdBy];

                        totalRowCount++;

                        // H row.
                        if (!headerWritten) {
                            const hRow = [];
                            hRow.push('H');
                            hRow.push(!!createdBy ? createdBy.name : '');
                            hRow.push(app.datetime.fromJSDate(item.issueDate).toFormat('yyyy-LL-dd'));
                            hRow.push(item.voucherNo);
                            hRow.push(currentEntryNo);
                            hRow.push(item.description || '');
                            hRow.push(total);
                            await stream.write(`${hRow.join('|')}\n`);

                            headerVoucherNo = item.voucherNo;
                            headerWritten = true;
                        }

                        let documentNo = trim(item.documentNo || '');
                        let documentType = '';
                        let documentTypeDescription = '';
                        let documentDate = '';
                        if (!!documentNo && documentNo.length > 0) {
                            if (item.journal.type === 'sale' || item.journal.type === 'purchase') {
                                documentType = 'invoice';
                            } else if (
                                item.journal.type === 'opening-record' ||
                                item.journal.type === 'closing-record' ||
                                item.journal.type === 'period-end'
                            ) {
                                documentType = 'voucher';
                            } else if (item.journal.type === 'sale-return') {
                                documentType = 'order-customer';
                            } else if (item.journal.type === 'purchase-return') {
                                documentType = 'order-vendor';
                            } else if (
                                item.journal.type === 'cash-safe' ||
                                item.journal.type === 'bank' ||
                                item.journal.type === 'promissory-note' ||
                                item.journal.type === 'credit-card' ||
                                item.journal.type === 'pos' ||
                                item.journal.type === 'guarantee' ||
                                item.journal.type === 'loan' ||
                                item.journal.type === 'salary' ||
                                item.journal.type === 'expense' ||
                                item.journal.type === 'income' ||
                                item.journal.type === 'exchange-rate-difference'
                            ) {
                                documentType = 'receipt';
                            } else if (item.journal.type === 'cheque') {
                                documentType = 'check';
                            } else if (item.journal.type === 'stock') {
                                documentType = 'shipment';
                            } else {
                                documentType = 'other';
                                documentTypeDescription = item.journal.name;
                            }

                            documentDate = app.datetime.fromJSDate(item.issueDate).toFormat('yyyy-LL-dd');
                        }

                        // D row.
                        const dRow = [];
                        dRow.push('D');
                        dRow.push(item.mainAccount.code);
                        dRow.push(item.mainAccount.name);
                        dRow.push(item.account.code);
                        dRow.push(item.account.name);
                        dRow.push(item.debit > 0 ? item.debit : item.credit);
                        dRow.push(item.debit > 0 ? 'D' : 'C');
                        dRow.push(documentType); // documentType
                        dRow.push(documentTypeDescription); // documentTypeDescription
                        dRow.push(documentNo);
                        dRow.push(documentDate);
                        dRow.push(headerVoucherNo);
                        dRow.push(''); // paymentMethod
                        dRow.push(item.description || '');

                        if (
                            totalRowCount ===
                            normalTransactionsCount +
                                openingTransactionsCount +
                                periodEndTransactionsCount +
                                closingTransactionsCount
                        ) {
                            await stream.write(`${dRow.join('|')}`);
                        } else {
                            await stream.write(`${dRow.join('|')}\n`);
                        }

                        transactionOperations.push({
                            updateOne: {
                                filter: {_id: item._id.toString()},
                                update: {$set: {declaredEntryNo: currentEntryNo}}
                            }
                        });

                        rowCount++;
                    }

                    if (transactionOperations.length > 0) {
                        await app.collection('accounting.transactions').bulkWrite(transactionOperations);
                    }

                    // Send progress percentage.
                    if (total !== 0) {
                        const percentage = (rowCount / total) * 100;
                        app.progress({
                            id: progressId,
                            status: 'info',
                            percentage
                        });
                    }

                    page++;
                } while (total > 0 && rowCount < total);

                if (openingTransactionsCount > 0) {
                    currentEntryNo++;
                }
            })();

            // Process normal entries.
            await (async () => {
                const evaluatedEntriesMap = {};
                const limit = 1000;
                const total = normalTransactionsCount;
                let page = 0;
                let rowCount = 0;

                do {
                    const transactionOperations = [];
                    const filters = {
                        ...query,
                        belongsToOpeningRecord: {$ne: true},
                        belongsToClosingRecord: {$ne: true},
                        'journal.type': {$ne: 'period-end'},
                        $sort: {issueDate: 1}
                    };
                    let result = null;

                    // Adjust page.
                    filters.$skip = page * limit;
                    filters.$limit = limit;

                    // Get rows.
                    result = await app.collection('accounting.transactions').find(filters);

                    const rows = await app.collection('accounting.transactions').find({
                        ...query,
                        belongsToOpeningRecord: {$ne: true},
                        entryId: {$in: _.uniq(result.map(item => item.entryId))},
                        $select: ['entryId']
                    });
                    const entryItemCounts = {};
                    for (const row of rows) {
                        if (!_.isNumber(entryItemCounts[row.entryId])) {
                            entryItemCounts[row.entryId] = 0;
                        }

                        entryItemCounts[row.entryId]++;
                    }

                    for (const item of result) {
                        const createdBy = usersMap[item.createdBy];

                        totalRowCount++;

                        // H row.
                        if (!evaluatedEntriesMap[item.entryId]) {
                            const count = entryItemCounts[item.entryId];

                            const hRow = [];
                            hRow.push('H');
                            hRow.push(!!createdBy ? createdBy.name : '');
                            hRow.push(app.datetime.fromJSDate(item.issueDate).toFormat('yyyy-LL-dd'));
                            hRow.push(item.voucherNo);
                            hRow.push(currentEntryNo);
                            hRow.push(item.description || '');
                            hRow.push(count);
                            await stream.write(`${hRow.join('|')}\n`);

                            evaluatedEntriesMap[item.entryId] = true;
                            currentEntryNo++;
                        }

                        let documentNo = trim(item.documentNo || '');
                        let documentType = '';
                        let documentTypeDescription = '';
                        let documentDate = '';
                        if (!!documentNo && documentNo.length > 0) {
                            if (item.journal.type === 'sale' || item.journal.type === 'purchase') {
                                documentType = 'invoice';
                            } else if (
                                item.journal.type === 'opening-record' ||
                                item.journal.type === 'closing-record' ||
                                item.journal.type === 'period-end'
                            ) {
                                documentType = 'voucher';
                            } else if (item.journal.type === 'sale-return') {
                                documentType = 'order-customer';
                            } else if (item.journal.type === 'purchase-return') {
                                documentType = 'order-vendor';
                            } else if (
                                item.journal.type === 'cash-safe' ||
                                item.journal.type === 'bank' ||
                                item.journal.type === 'promissory-note' ||
                                item.journal.type === 'credit-card' ||
                                item.journal.type === 'pos' ||
                                item.journal.type === 'guarantee' ||
                                item.journal.type === 'loan' ||
                                item.journal.type === 'salary' ||
                                item.journal.type === 'expense' ||
                                item.journal.type === 'income' ||
                                item.journal.type === 'exchange-rate-difference'
                            ) {
                                documentType = 'receipt';
                            } else if (item.journal.type === 'cheque') {
                                documentType = 'check';
                            } else if (item.journal.type === 'stock') {
                                documentType = 'shipment';
                            } else {
                                documentType = 'other';
                                documentTypeDescription = item.journal.name;
                            }

                            documentDate = app.datetime.fromJSDate(item.issueDate).toFormat('yyyy-LL-dd');
                        }

                        // D row.
                        const dRow = [];
                        dRow.push('D');
                        dRow.push(item.mainAccount.code);
                        dRow.push(item.mainAccount.name);
                        dRow.push(item.account.code);
                        dRow.push(item.account.name);
                        dRow.push(item.debit > 0 ? item.debit : item.credit);
                        dRow.push(item.debit > 0 ? 'D' : 'C');
                        dRow.push(documentType); // documentType
                        dRow.push(documentTypeDescription); // documentTypeDescription
                        dRow.push(documentNo);
                        dRow.push(documentDate);
                        dRow.push(item.voucherNo);
                        dRow.push(''); // paymentMethod
                        dRow.push(item.description || '');

                        if (
                            totalRowCount ===
                            normalTransactionsCount +
                                openingTransactionsCount +
                                periodEndTransactionsCount +
                                closingTransactionsCount
                        ) {
                            await stream.write(`${dRow.join('|')}`);
                        } else {
                            await stream.write(`${dRow.join('|')}\n`);
                        }

                        transactionOperations.push({
                            updateOne: {
                                filter: {_id: item._id.toString()},
                                update: {$set: {declaredEntryNo: currentEntryNo - 1}}
                            }
                        });

                        rowCount++;
                    }

                    if (transactionOperations.length > 0) {
                        await app.collection('accounting.transactions').bulkWrite(transactionOperations);
                    }

                    // Send progress percentage.
                    if (total !== 0) {
                        const percentage = (rowCount / total) * 100;
                        app.progress({
                            id: progressId,
                            status: 'info',
                            percentage
                        });
                    }

                    page++;
                } while (total > 0 && rowCount < total);
            })();

            // Process period end records.
            await (async () => {
                const evaluatedEntriesMap = {};
                const limit = 1000;
                const total = periodEndTransactionsCount;
                let page = 0;
                let rowCount = 0;

                do {
                    const transactionOperations = [];
                    const filters = {
                        ...query,
                        belongsToOpeningRecord: {$ne: true},
                        belongsToClosingRecord: {$ne: true},
                        'journal.type': 'period-end',
                        $sort: {issueDate: 1}
                    };
                    let result = null;

                    // Adjust page.
                    filters.$skip = page * limit;
                    filters.$limit = limit;

                    // Get rows.
                    result = await app.collection('accounting.transactions').find(filters);

                    const rows = await app.collection('accounting.transactions').find({
                        ...query,
                        belongsToOpeningRecord: {$ne: true},
                        entryId: {$in: _.uniq(result.map(item => item.entryId))},
                        $select: ['entryId']
                    });
                    const entryItemCounts = {};
                    for (const row of rows) {
                        if (!_.isNumber(entryItemCounts[row.entryId])) {
                            entryItemCounts[row.entryId] = 0;
                        }

                        entryItemCounts[row.entryId]++;
                    }

                    for (const item of result) {
                        const createdBy = usersMap[item.createdBy];

                        totalRowCount++;

                        // H row.
                        if (!evaluatedEntriesMap[item.entryId]) {
                            const count = entryItemCounts[item.entryId];

                            const hRow = [];
                            hRow.push('H');
                            hRow.push(!!createdBy ? createdBy.name : '');
                            hRow.push(app.datetime.fromJSDate(item.issueDate).toFormat('yyyy-LL-dd'));
                            hRow.push(item.voucherNo);
                            hRow.push(currentEntryNo);
                            hRow.push(item.description || '');
                            hRow.push(count);
                            await stream.write(`${hRow.join('|')}\n`);

                            evaluatedEntriesMap[item.entryId] = true;
                            currentEntryNo++;
                        }

                        let documentNo = trim(item.documentNo || '');
                        let documentType = '';
                        let documentTypeDescription = '';
                        let documentDate = '';
                        if (!!documentNo && documentNo.length > 0) {
                            if (item.journal.type === 'sale' || item.journal.type === 'purchase') {
                                documentType = 'invoice';
                            } else if (
                                item.journal.type === 'opening-record' ||
                                item.journal.type === 'closing-record' ||
                                item.journal.type === 'period-end'
                            ) {
                                documentType = 'voucher';
                            } else if (item.journal.type === 'sale-return') {
                                documentType = 'order-customer';
                            } else if (item.journal.type === 'purchase-return') {
                                documentType = 'order-vendor';
                            } else if (
                                item.journal.type === 'cash-safe' ||
                                item.journal.type === 'bank' ||
                                item.journal.type === 'promissory-note' ||
                                item.journal.type === 'credit-card' ||
                                item.journal.type === 'pos' ||
                                item.journal.type === 'guarantee' ||
                                item.journal.type === 'loan' ||
                                item.journal.type === 'salary' ||
                                item.journal.type === 'expense' ||
                                item.journal.type === 'income' ||
                                item.journal.type === 'exchange-rate-difference'
                            ) {
                                documentType = 'receipt';
                            } else if (item.journal.type === 'cheque') {
                                documentType = 'check';
                            } else if (item.journal.type === 'stock') {
                                documentType = 'shipment';
                            } else {
                                documentType = 'other';
                                documentTypeDescription = item.journal.name;
                            }

                            documentDate = app.datetime.fromJSDate(item.issueDate).toFormat('yyyy-LL-dd');
                        }

                        // D row.
                        const dRow = [];
                        dRow.push('D');
                        dRow.push(item.mainAccount.code);
                        dRow.push(item.mainAccount.name);
                        dRow.push(item.account.code);
                        dRow.push(item.account.name);
                        dRow.push(item.debit > 0 ? item.debit : item.credit);
                        dRow.push(item.debit > 0 ? 'D' : 'C');
                        dRow.push(documentType); // documentType
                        dRow.push(documentTypeDescription); // documentTypeDescription
                        dRow.push(documentNo);
                        dRow.push(documentDate);
                        dRow.push(item.voucherNo);
                        dRow.push(''); // paymentMethod
                        dRow.push(item.description || '');

                        if (
                            totalRowCount ===
                            normalTransactionsCount +
                                openingTransactionsCount +
                                periodEndTransactionsCount +
                                closingTransactionsCount
                        ) {
                            await stream.write(`${dRow.join('|')}`);
                        } else {
                            await stream.write(`${dRow.join('|')}\n`);
                        }

                        transactionOperations.push({
                            updateOne: {
                                filter: {_id: item._id.toString()},
                                update: {$set: {declaredEntryNo: currentEntryNo - 1}}
                            }
                        });

                        rowCount++;
                    }

                    if (transactionOperations.length > 0) {
                        await app.collection('accounting.transactions').bulkWrite(transactionOperations);
                    }

                    // Send progress percentage.
                    if (total !== 0) {
                        const percentage = (rowCount / total) * 100;
                        app.progress({
                            id: progressId,
                            status: 'info',
                            percentage
                        });
                    }

                    page++;
                } while (total > 0 && rowCount < total);
            })();

            // Process closing records.
            await (async () => {
                const limit = 1000;
                const total = closingTransactionsCount;
                let page = 0;
                let rowCount = 0;
                let headerWritten = false;
                let headerVoucherNo = '';

                do {
                    const transactionOperations = [];
                    const filters = {
                        ...query,
                        belongsToClosingRecord: true,
                        'journal.type': {$ne: 'period-end'},
                        $sort: {issueDate: 1}
                    };
                    let result = null;

                    // Adjust page.
                    filters.$skip = page * limit;
                    filters.$limit = limit;

                    // Get rows.
                    result = await app.collection('accounting.transactions').find(filters);

                    for (const item of result) {
                        const createdBy = usersMap[item.createdBy];

                        totalRowCount++;

                        // H row.
                        if (!headerWritten) {
                            const hRow = [];
                            hRow.push('H');
                            hRow.push(!!createdBy ? createdBy.name : '');
                            hRow.push(app.datetime.fromJSDate(item.issueDate).toFormat('yyyy-LL-dd'));
                            hRow.push(item.voucherNo);
                            hRow.push(currentEntryNo);
                            hRow.push(item.description || '');
                            hRow.push(total);
                            await stream.write(`${hRow.join('|')}\n`);

                            headerVoucherNo = item.voucherNo;
                            headerWritten = true;
                        }

                        let documentNo = trim(item.documentNo || '');
                        let documentType = '';
                        let documentTypeDescription = '';
                        let documentDate = '';
                        if (!!documentNo && documentNo.length > 0) {
                            if (item.journal.type === 'sale' || item.journal.type === 'purchase') {
                                documentType = 'invoice';
                            } else if (
                                item.journal.type === 'opening-record' ||
                                item.journal.type === 'closing-record' ||
                                item.journal.type === 'period-end'
                            ) {
                                documentType = 'voucher';
                            } else if (item.journal.type === 'sale-return') {
                                documentType = 'order-customer';
                            } else if (item.journal.type === 'purchase-return') {
                                documentType = 'order-vendor';
                            } else if (
                                item.journal.type === 'cash-safe' ||
                                item.journal.type === 'bank' ||
                                item.journal.type === 'promissory-note' ||
                                item.journal.type === 'credit-card' ||
                                item.journal.type === 'pos' ||
                                item.journal.type === 'guarantee' ||
                                item.journal.type === 'loan' ||
                                item.journal.type === 'salary' ||
                                item.journal.type === 'expense' ||
                                item.journal.type === 'income' ||
                                item.journal.type === 'exchange-rate-difference'
                            ) {
                                documentType = 'receipt';
                            } else if (item.journal.type === 'cheque') {
                                documentType = 'check';
                            } else if (item.journal.type === 'stock') {
                                documentType = 'shipment';
                            } else {
                                documentType = 'other';
                                documentTypeDescription = item.journal.name;
                            }

                            documentDate = app.datetime.fromJSDate(item.issueDate).toFormat('yyyy-LL-dd');
                        }

                        // D row.
                        const dRow = [];
                        dRow.push('D');
                        dRow.push(item.mainAccount.code);
                        dRow.push(item.mainAccount.name);
                        dRow.push(item.account.code);
                        dRow.push(item.account.name);
                        dRow.push(item.debit > 0 ? item.debit : item.credit);
                        dRow.push(item.debit > 0 ? 'D' : 'C');
                        dRow.push(documentType); // documentType
                        dRow.push(documentTypeDescription); // documentTypeDescription
                        dRow.push(documentNo);
                        dRow.push(documentDate);
                        dRow.push(headerVoucherNo);
                        dRow.push(''); // paymentMethod
                        dRow.push(item.description || '');

                        if (
                            totalRowCount ===
                            normalTransactionsCount +
                                openingTransactionsCount +
                                periodEndTransactionsCount +
                                closingTransactionsCount
                        ) {
                            await stream.write(`${dRow.join('|')}`);
                        } else {
                            await stream.write(`${dRow.join('|')}\n`);
                        }

                        transactionOperations.push({
                            updateOne: {
                                filter: {_id: item._id.toString()},
                                update: {$set: {declaredEntryNo: currentEntryNo}}
                            }
                        });

                        rowCount++;
                    }

                    if (transactionOperations.length > 0) {
                        await app.collection('accounting.transactions').bulkWrite(transactionOperations);
                    }

                    // Send progress percentage.
                    if (total !== 0) {
                        const percentage = (rowCount / total) * 100;
                        app.progress({
                            id: progressId,
                            status: 'info',
                            percentage
                        });
                    }

                    page++;
                } while (total > 0 && rowCount < total);

                if (closingTransactionsCount > 0) {
                    currentEntryNo++;
                }
            })();

            // End stream.
            stream.end();

            await new Promise((resolve, reject) => {
                const output = fs.createWriteStream(zipFilePath);
                const archive = archiver('zip', {
                    zlib: {level: 9}
                });

                output.on('close', resolve);
                output.on('error', reject);
                archive.pipe(output);
                archive.append(fs.createReadStream(textFilePath), {
                    name: `${app.translate('E-Journal')}-${app.datetime
                        .fromJSDate(startDate)
                        .toFormat('yyyyLLdd')}-${app.datetime.fromJSDate(endDate).toFormat('yyyyLLdd')}.txt`
                });
                archive.finalize();
            });
            await fs.unlink(textFilePath);
        } catch (error) {
            app.progress({
                id: progressId,
                status: 'error',
                message: error.message,
                percentage: 0
            });
            return;
        }

        // Finalize progress.
        app.progress({
            id: progressId,
            status: 'success',
            percentage: 100
        });

        return {
            file: `${fileName}.zip`,
            name: `${app.translate('E-Journal')}-${app.datetime
                .fromJSDate(startDate)
                .toFormat('yyyyLLdd')}-${app.datetime.fromJSDate(endDate).toFormat('yyyyLLdd')}.zip`
        };
    }
};
