import _ from 'lodash';
import {Workbook} from 'exceljs';
import {firstUpper, firstUpperAll, toLower} from '../../../framework/helpers';

export default {
    name: 'download-journal-entry-sample',
    async action() {
        const app = this.app;
        const t = app.translate;

        const workbook = new Workbook();
        workbook.creator = 'EnterERP';
        workbook.lastModifiedBy = 'EnterERP';
        workbook.created = app.datetime.local().toJSDate();
        workbook.modified = app.datetime.local().toJSDate();
        workbook.lastPrinted = app.datetime.local().toJSDate();

        const worksheet = workbook.addWorksheet(t('Entries'), {
            views: [{state: 'frozen', xSplit: 0, ySplit: 1}]
        });

        worksheet.columns = [
            {
                header: firstUpper(toLower(t('Voucher no'))),
                key: 'voucherNo',
                width: 25,
                style: {numFmt: '@'}
            },
            {
                header: firstUpper(toLower(t('Document no'))),
                key: 'documentNo',
                width: 25,
                style: {numFmt: '@'}
            },
            {
                header: firstUpper(toLower(t('Reference'))),
                key: 'reference',
                width: 25,
                style: {numFmt: '@'}
            },
            {
                header: firstUpper(toLower(t('Journal'))),
                key: 'journalName',
                width: 30,
                style: {numFmt: '@'}
            },
            {
                header: firstUpper(toLower(t('Account code'))),
                key: 'accountCode',
                width: 25,
                style: {numFmt: '@'}
            },
            {
                header: firstUpper(toLower(t('Partner code'))),
                key: 'partnerCode',
                width: 20,
                style: {numFmt: '@'}
            },
            {
                header: firstUpper(toLower(t('Description'))),
                key: 'description',
                width: 30,
                style: {numFmt: '@'}
            },
            {
                header: firstUpper(toLower(t('Branch code'))),
                key: 'branchCode',
                width: 10,
                style: {numFmt: '@'}
            },
            {
                header: firstUpper(toLower(t('Project code'))),
                key: 'financialProjectCode',
                width: 15,
                style: {numFmt: '@'}
            },
            {
                header: firstUpper(toLower(t('Cash flow item code'))),
                key: 'cashFlowItemCode',
                width: 15,
                style: {numFmt: '@'}
            },
            {
                header: firstUpper(toLower(t('Tax scope'))),
                key: 'taxScope',
                width: 15,
                style: {numFmt: '@'}
            },
            {
                header: firstUpper(toLower(t('Tax name'))),
                key: 'taxName',
                width: 15,
                style: {numFmt: '@'}
            },
            {
                header: firstUpper(toLower(t('Tax assessment'))),
                key: 'taxAssessment',
                width: 15,
                style: {numFmt: '#,##0.00'}
            },
            {
                header: firstUpper(toLower(t('Scope'))),
                key: 'scope',
                width: 10,
                style: {numFmt: '@'}
            },
            {
                header: firstUpperAll(t('Record date')),
                key: 'recordDate',
                width: 10
            },
            {
                header: firstUpperAll(t('Issue date')),
                key: 'issueDate',
                width: 10
            },
            {
                header: firstUpperAll(t('Due date')),
                key: 'dueDate',
                width: 10
            },
            {
                header: firstUpperAll(t('Currency')),
                key: 'currencyName',
                width: 10,
                style: {numFmt: '@'}
            },
            {
                header: firstUpper(toLower(t('Debit (FC)'))),
                key: 'debitFC',
                width: 15,
                style: {numFmt: '#,##0.00'}
            },
            {
                header: firstUpper(toLower(t('Credit (FC)'))),
                key: 'creditFC',
                width: 15,
                style: {numFmt: '#,##0.00'}
            },
            {
                header: firstUpper(toLower(t('Debit'))),
                key: 'debit',
                width: 15,
                style: {numFmt: '#,##0.00'}
            },
            {
                header: firstUpper(toLower(t('Credit'))),
                key: 'credit',
                width: 15,
                style: {numFmt: '#,##0.00'}
            }
        ];

        const headerRow = worksheet.getRow(1);
        headerRow.font = {
            color: {argb: 'FFFFFFFF'},
            bold: true
        };
        headerRow.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: {argb: 'FF0052CC'}
        };

        await worksheet
            .addRow(
                fixDates({
                    voucherNo: 'STI/000/********',
                    documentNo: 'Test document',
                    reference: 'Test ref',
                    journalName: t('Miscellaneous Transactions'),
                    accountCode: '100.001.001',
                    partnerCode: 'M********',
                    description: 'Sample movement',
                    branchCode: 'MS',
                    currencyName: 'TL',
                    scope: app.setting('system.scope1Label'),
                    recordDate: app.datetime.local().toJSDate(),
                    issueDate: app.datetime.local().toJSDate(),
                    dueDate: app.datetime.local().toJSDate(),
                    debit: 0,
                    credit: 1000
                })
            )
            .commit();
        await worksheet
            .addRow(
                fixDates({
                    voucherNo: 'STI/000/********',
                    documentNo: 'Test document',
                    reference: 'Test ref',
                    journalName: t('Miscellaneous Transactions'),
                    accountCode: '120.001.001',
                    partnerCode: 'M********',
                    description: 'Sample movement',
                    branchCode: 'MS',
                    currencyName: 'TL',
                    scope: app.setting('system.scope1Label'),
                    recordDate: app.datetime.local().toJSDate(),
                    issueDate: app.datetime.local().toJSDate(),
                    dueDate: app.datetime.local().toJSDate(),
                    debit: 1000,
                    credit: 0
                })
            )
            .commit();
        await worksheet
            .addRow(
                fixDates({
                    voucherNo: 'STI/000/********',
                    documentNo: 'Test document',
                    reference: 'Test ref',
                    journalName: t('Miscellaneous Transactions'),
                    accountCode: '391.001.001',
                    partnerCode: 'M********',
                    description: 'Sample movement',
                    branchCode: 'MS',
                    taxScope: t('Sale'),
                    taxName: 'KDV %20',
                    taxAssessment: 1000,
                    currencyName: 'TL',
                    scope: app.setting('system.scope1Label'),
                    recordDate: app.datetime.local().toJSDate(),
                    issueDate: app.datetime.local().toJSDate(),
                    dueDate: app.datetime.local().toJSDate(),
                    debit: 0,
                    credit: 200
                })
            )
            .commit();
        await worksheet
            .addRow(
                fixDates({
                    voucherNo: 'STI/000/********',
                    documentNo: 'Test document',
                    reference: 'Test ref',
                    journalName: t('Miscellaneous Transactions'),
                    accountCode: '191.001.001',
                    partnerCode: 'M********',
                    description: 'Sample movement',
                    branchCode: 'MS',
                    taxScope: t('Purchase'),
                    taxName: 'KDV %20',
                    taxAssessment: 1000,
                    currencyName: 'TL',
                    scope: app.setting('system.scope1Label'),
                    recordDate: app.datetime.local().toJSDate(),
                    issueDate: app.datetime.local().toJSDate(),
                    dueDate: app.datetime.local().toJSDate(),
                    debit: 200,
                    credit: 0
                })
            )
            .commit();

        return await workbook.xlsx.writeBuffer();
    }
};

function fixDates(row) {
    for (const key of Object.keys(row)) {
        if (_.isDate(row[key])) {
            const date = row[key];

            row[key] = new Date(
                Date.UTC(
                    date.getFullYear(),
                    date.getMonth(),
                    date.getDate(),
                    date.getHours(),
                    date.getMinutes(),
                    date.getSeconds()
                )
            );
        }
    }

    return row;
}
