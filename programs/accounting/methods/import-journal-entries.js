import {Workbook} from 'exceljs';
import _ from 'lodash';

export default {
    name: 'import-journal-entries',
    async action(payload, params) {
        const app = this.app;
        const t = app.translate;
        const taxScopeOptions = [
            {value: 'sale', label: t('Sale')},
            {value: 'purchase', label: t('Purchase')}
        ];
        const scopeOptions = [
            {value: '1', label: app.setting('system.scope1Label')},
            {value: '2', label: app.setting('system.scope2Label')}
        ];
        const matches = [
            {col: 'A', field: 'voucherNo'},
            {col: 'B', field: 'documentNo'},
            {col: 'C', field: 'reference'},
            {col: 'D', field: 'journalName'},
            {col: 'E', field: 'accountCode'},
            {col: 'F', field: 'partnerCode'},
            {col: 'G', field: 'description'},
            {col: 'H', field: 'branchCode'},
            {col: 'I', field: 'financialProjectCode'},
            {col: 'J', field: 'cashFlowItemCode'},
            {col: 'K', field: 'taxScope'},
            {col: 'L', field: 'taxName'},
            {col: 'M', field: 'taxAssessment'},
            {col: 'N', field: 'scope'},
            {col: 'O', field: 'recordDate'},
            {col: 'P', field: 'issueDate'},
            {col: 'Q', field: 'dueDate'},
            {col: 'R', field: 'currencyName'},
            {col: 'S', field: 'debitFC'},
            {col: 'T', field: 'creditFC'},
            {col: 'U', field: 'debit'},
            {col: 'V', field: 'credit'}
        ];
        const schema = {
            voucherNo: {
                type: 'string',
                label: 'Voucher no'
            },
            documentNo: {
                type: 'string',
                label: 'Document no',
                required: false
            },
            reference: {
                type: 'string',
                label: 'Reference',
                required: false
            },
            journalName: {
                type: 'string',
                label: 'Journal'
            },
            accountCode: {
                type: 'string',
                label: 'Account code'
            },
            partnerCode: {
                type: 'string',
                label: 'Partner code',
                required: false
            },
            description: {
                type: 'string',
                label: 'Description'
            },
            branchCode: {
                type: 'string',
                label: 'Branch code'
            },
            financialProjectCode: {
                type: 'string',
                label: 'Project code',
                required: false
            },
            cashFlowItemCode: {
                type: 'string',
                label: 'Cash flow item code',
                required: false
            },
            taxScope: {
                type: 'string',
                label: 'Tax scope',
                allowed: taxScopeOptions.map(o => o.label),
                required: false
            },
            taxName: {
                type: 'string',
                label: 'Tax name',
                required: false
            },
            taxAssessment: {
                type: 'decimal',
                label: 'Tax assessment',
                required: false
            },
            scope: {
                type: 'string',
                label: 'Scope',
                allowed: scopeOptions.map(o => o.label)
            },
            recordDate: {
                type: 'date',
                label: 'Record date'
            },
            issueDate: {
                type: 'date',
                label: 'Issue date'
            },
            dueDate: {
                type: 'date',
                label: 'Due date'
            },
            currencyName: {
                type: 'string',
                label: 'Currency'
            },
            debitFC: {
                type: 'decimal',
                label: 'Debit (FC)',
                required: false
            },
            creditFC: {
                type: 'decimal',
                label: 'Credit (FC)',
                required: false
            },
            debit: {
                type: 'decimal',
                label: 'Debit',
                default: 0
            },
            credit: {
                type: 'decimal',
                label: 'Credit',
                default: 0
            }
        };
        const accounts = await app.collection('kernel.accounts').find({
            $select: ['_id', 'code', 'name']
        });
        const accountsMap = {};
        for (const account of accounts) {
            accountsMap[account.code] = account;
        }
        const branches = await app.collection('kernel.branches').find({
            $select: ['_id', 'code', 'name']
        });
        const branchesMap = {};
        for (const branch of branches) {
            branchesMap[branch.code] = branch;
        }
        const journals = await app.collection('accounting.journals').find({
            $select: ['_id', 'name']
        });
        const journalsMap = {};
        for (const journal of journals) {
            journalsMap[journal.name] = journal;
        }
        const currencies = await app.collection('kernel.currencies').find({
            $select: ['_id', 'name']
        });
        const currenciesMap = {};
        for (const currency of currencies) {
            currenciesMap[currency.name] = currency;
        }
        const financialProjects = await app.collection('kernel.financial-projects').find({
            $select: ['_id', 'code', 'name']
        });
        const financialProjectsMap = {};
        for (const financialProject of financialProjects) {
            financialProjectsMap[financialProject.code] = financialProject;
        }
        const cashFlowItems = await app.collection('finance.cash-flow-items').find({
            $select: ['_id', 'code', 'name']
        });
        const cashFlowItemsMap = {};
        for (const cashFlowItem of cashFlowItems) {
            cashFlowItemsMap[cashFlowItem.code] = cashFlowItem;
        }
        const taxes = await app.collection('kernel.taxes').find({
            $select: ['_id', 'scope', 'name']
        });
        const taxesMap = {};
        for (const tax of taxes) {
            taxesMap[`${tax.scope}/${tax.name}`] = tax;
        }
        const workbook = new Workbook();
        await workbook.xlsx.load(await app.files.read(payload.fileId));
        const worksheet = workbook.getWorksheet(t('Entries'));
        if (!worksheet) {
            throw new app.errors.Unprocessable(app.translate('Worksheet not found!'));
        }

        const rows = [];
        worksheet.eachRow((row, rowNumber) => {
            if (rowNumber > 1) {
                let item = {};

                row.eachCell((cell, colNumber) => {
                    const match = matches.find(m => m.col === cell.address.replace(rowNumber.toString(), ''));

                    if (_.isPlainObject(match)) {
                        item[match.field] = cell.value;
                    }
                });

                rows.push(item);
            }
        });

        const items = [];
        let index = 2;
        for (let item of rows) {
            item = await app.schema.clean(schema, item, {
                isModifier: false
            });

            const validation = await app.schema.validate(schema, item, {
                modifier: false
            });
            if (!validation.isValid) {
                // noinspection ExceptionCaughtLocallyJS
                throw new app.errors.Unprocessable(
                    t('Invalid row! Reason: {{reason}} Row: {{row}}', {
                        reason: validation.errors[0].message,
                        row: index
                    })
                );
            }

            // Get journal.
            const journal = journalsMap[item.journalName];
            if (!!journal) {
                item.journalId = journal._id;
                delete item.journalName;
            } else {
                // noinspection ExceptionCaughtLocallyJS
                throw new app.errors.Unprocessable(
                    t('Invalid row! Reason: {{reason}} Row: {{row}}', {
                        reason: t('{{label}} not found!', {
                            label: t('Journal')
                        }),
                        row: index
                    })
                );
            }

            // Get account.
            const account = accountsMap[item.accountCode];
            if (!!account) {
                item.accountId = account._id;
                delete item.accountCode;
            } else {
                // noinspection ExceptionCaughtLocallyJS
                throw new app.errors.Unprocessable(
                    t('Invalid row! Reason: {{reason}} Row: {{row}}', {
                        reason: t('{{label}} not found!', {
                            label: t('Account')
                        }),
                        row: index
                    })
                );
            }

            // Get partner.
            if (!!item.partnerCode) {
                const partner = await app.collection('kernel.partners').findOne({
                    code: item.partnerCode,
                    $select: ['_id']
                });

                if (!!partner) {
                    item.partnerId = partner._id;
                    delete item.partnerCode;
                } else {
                    // noinspection ExceptionCaughtLocallyJS
                    throw new app.errors.Unprocessable(
                        t('Invalid row! Reason: {{reason}} Row: {{row}}', {
                            reason: t('{{label}} not found!', {
                                label: t('Partner')
                            }),
                            row: index
                        })
                    );
                }
            }

            // Get branch.
            const branch = branchesMap[item.branchCode];
            if (!!branch) {
                item.branchId = branch._id;

                delete item.branchCode;
            } else {
                // noinspection ExceptionCaughtLocallyJS
                throw new app.errors.Unprocessable(
                    t('Invalid row! Reason: {{reason}} Row: {{row}}', {
                        reason: t('{{label}} not found!', {
                            label: t('Branch office')
                        }),
                        row: index
                    })
                );
            }

            // Get financial project.
            if (!!item.financialProjectCode) {
                const financialProject = financialProjectsMap[item.financialProjectCode];
                if (!!financialProject) {
                    item.financialProjectId = financialProject._id;

                    delete item.financialProjectCode;
                } else {
                    // noinspection ExceptionCaughtLocallyJS
                    throw new app.errors.Unprocessable(
                        t('Invalid row! Reason: {{reason}} Row: {{row}}', {
                            reason: t('{{label}} not found!', {
                                label: t('Project')
                            }),
                            row: index
                        })
                    );
                }
            }

            // Get cash flow item.
            if (!!item.cashFlowItemCode) {
                const cashFlowItem = cashFlowItemsMap[item.cashFlowItemCode];
                if (!!cashFlowItem) {
                    item.cashFlowItemId = cashFlowItem._id;

                    delete item.cashFlowItemCode;
                } else {
                    // noinspection ExceptionCaughtLocallyJS
                    throw new app.errors.Unprocessable(
                        t('Invalid row! Reason: {{reason}} Row: {{row}}', {
                            reason: t('{{label}} not found!', {
                                label: t('Cash flow items')
                            }),
                            row: index
                        })
                    );
                }
            }

            // Get scope.
            if (!!item.scope) {
                const scopeOption = scopeOptions.find(o => o.label === item.scope);

                if (!!scopeOption) {
                    item.scope = scopeOption.value;
                } else {
                    // noinspection ExceptionCaughtLocallyJS
                    throw new app.errors.Unprocessable(
                        t('Invalid row! Reason: {{reason}} Row: {{row}}', {
                            reason: t('{{label}} not found!', {
                                label: t('Scope')
                            }),
                            row: index
                        })
                    );
                }
            }

            // Get tax.
            if (!!item.taxScope && !!item.taxName) {
                const scope = taxScopeOptions.find(o => o.label === item.taxScope);
                const tax = taxesMap[`${scope.value}/${item.taxName}`];

                if (!!tax) {
                    item.taxId = tax._id;

                    delete item.taxScope;
                    delete item.taxName;
                } else {
                    throw new app.errors.Unprocessable(
                        t('Invalid row! Reason: {{reason}} Row: {{row}}', {
                            reason: t('{{label}} not found!', {
                                label: t('Tax')
                            }),
                            row: index
                        })
                    );
                }
            }

            // Get currency.
            const currency = currenciesMap[item.currencyName];
            if (!!currency) {
                item.currencyId = currency._id;

                delete item.currencyName;
            } else {
                throw new app.errors.Unprocessable(
                    t('Invalid row! Reason: {{reason}} Row: {{row}}', {
                        reason: t('{{label}} not found!', {
                            label: t('Currency')
                        }),
                        row: index
                    })
                );
            }

            items.push(item);

            index++;
        }

        const entryIds = [];
        const grouped = _.groupBy(items, 'voucherNo');
        for (const voucherNo of Object.keys(grouped)) {
            const firstItem = grouped[voucherNo][0];
            const entry = {items: []};

            if (firstItem.documentNo) entry.documentNo = firstItem.documentNo;
            entry.voucherNo = voucherNo;
            entry.reference = firstItem.reference;
            entry.description = firstItem.description;
            entry.branchId = firstItem.branchId;
            entry.recordDate = firstItem.recordDate;
            entry.issueDate = firstItem.issueDate;
            entry.dueDate = firstItem.dueDate;
            entry.journalId = firstItem.journalId;
            if (_.isPlainObject(params.user)) entry.createdBy = params.user._id;

            entry.items = grouped[voucherNo].map(item =>
                _.omit(item, ['voucherNo', 'documentNo', 'reference', 'journalName'])
            );

            const journalEntry = await app.rpc(
                'accounting.save-journal-entry',
                {
                    data: entry,
                    dontSetVoucherNo: true
                },
                {user: params.user}
            );

            entryIds.push(journalEntry._id);
        }

        if (entryIds.length > 0) {
            for (const entryId of entryIds) {
                await this.app.rpc('accounting.post-journal-entry', entryId, {
                    user: params.user
                });
            }
        }
    }
};
