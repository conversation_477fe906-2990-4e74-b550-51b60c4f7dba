import _ from 'lodash';
import fastCopy from 'fast-copy';
import {trim} from 'framework/helpers';
import Random from 'framework/random';

export default [
    {
        name: 'reconciliations-get-partners',
        async action(payload, params) {
            const app = this.app;
            const {query, skip, limit} = payload;

            if (!Array.isArray(query.$and)) {
                query.$and = [];
            }

            query.$and.push({
                $or: [{reconciled: {$exists: false}}, {reconciled: {$ne: true}}]
            });

            const pipeline = [
                {
                    $match: {
                        ...query,
                        'account.reconciliation': true,
                        partnerId: {$exists: true},
                        'partner.type': {$in: ['customer', 'vendor']},
                        reconciliationBalance: {$ne: 0}
                    }
                },
                {
                    $group: {
                        _id: '$partnerId',
                        partner: {$first: '$partner'}
                    }
                }
            ];

            const transactions = await app.collection('accounting.transactions').aggregate([
                ...pipeline,
                {
                    $sort: {
                        'partner.name': 1
                    }
                },
                {$skip: skip},
                {$limit: limit},
                {
                    $project: {
                        _id: 1,
                        code: '$partner.code',
                        name: '$partner.name',
                        type: '$partner.type'
                    }
                }
            ]);
            const countResult = await app.collection('accounting.transactions').aggregate([
                ...pipeline,
                {
                    $count: 'total'
                }
            ]);
            let total = countResult.length > 0 ? countResult[0].total : transactions.length;

            return {total, data: transactions};
        }
    },
    {
        name: 'reconciliations-get-transactions',
        async action(payload, params) {
            const app = this.app;
            const {partnerId} = payload;
            const transactions = await app.collection('accounting.transactions').find({
                partnerId,
                $or: [{reconciled: {$exists: false}}, {reconciled: {$ne: true}}],
                'account.reconciliation': true,
                reconciliationBalance: {$ne: 0},
                $sort: {dueDate: 1}
            });
            const currencies = await app.collection('kernel.currencies').find({
                _id: {$in: transactions.map(transaction => transaction.currencyId)},
                $disableSoftDelete: true,
                $disableActiveCheck: true
            });
            const currenciesMap = {};
            for (const currency of currencies) {
                currenciesMap[currency._id] = currency;
            }

            return transactions.map(transaction => {
                transaction.currency = currenciesMap[transaction.currencyId];

                transaction.originalDebit = transaction.debit;
                transaction.originalCredit = transaction.credit;
                transaction.originalDebitFC = transaction.debitFC;
                transaction.originalCreditFC = transaction.creditFC;
                transaction.originalBalance = transaction.balance;
                transaction.originalBalanceFC = transaction.balanceFC;

                if (transaction.debit > 0) {
                    transaction.debit = Math.abs(transaction.reconciliationBalance);
                } else {
                    transaction.credit = Math.abs(transaction.reconciliationBalance);
                }
                if (transaction.debitFC > 0) {
                    const currencyRate = transaction.debit / transaction.debitFC;

                    transaction.currencyRate = currencyRate;
                    transaction.debitFC = Math.abs(transaction.reconciliationBalance) / currencyRate;
                } else if (transaction.creditFC > 0) {
                    const currencyRate = transaction.credit / transaction.creditFC;

                    transaction.currencyRate = currencyRate;
                    transaction.creditFC = Math.abs(transaction.reconciliationBalance) / currencyRate;
                }
                transaction.balance = transaction.reconciliationBalance ?? 0;

                if (_.isFinite(transaction.currencyRate) && transaction.currencyRate !== 0) {
                    transaction.balanceFC = transaction.balance / transaction.currencyRate;
                }
                if (_.isFinite(transaction.reconciliationBalanceFC)) {
                    transaction.balanceFC = transaction.reconciliationBalanceFC;
                }

                return transaction;
            });
        }
    },
    {
        name: 'reconciliations-save-reconciliation',
        async action(reconciliation, params) {
            const app = this.app;
            const company = await app.collection('kernel.company').findOne({$select: ['currencyId']});
            const numbering = await this.app.collection('kernel.numbering').findOne(
                {
                    code: 'reconciliationNumbering',
                    $select: ['_id']
                },
                {
                    disableInUseCheck: true,
                    disableActiveCheck: true,
                    disableSoftDelete: true
                }
            );
            const transactionOperations = [];
            const customerInvoiceOperations = [];
            const vendorInvoiceOperations = [];
            const transactionIds = [];
            const customerInvoiceIds = [];
            const vendorInvoiceIds = [];

            const creditRecords = [];
            const debitRecords = [];
            for (const record of fastCopy(reconciliation.matchedRecords)) {
                record.reconciled = false;
                record.reconciliationCode = null;
                record.reconciliationData = null;

                if (record.credit > 0) {
                    creditRecords.push(record);
                } else if (record.debit > 0) {
                    debitRecords.push(record);
                }
            }

            // Assign reconciliation code to transactions.
            const code = !!reconciliation.invoiceReconciliationCode
                ? reconciliation.invoiceReconciliationCode
                : await app.rpc('kernel.common.request-number', {
                      numberingId: numbering._id,
                      save: true
                  });

            for (const debitRecord of debitRecords) {
                let debitRemaining = Math.abs(debitRecord.reconciliationBalance ?? 0);

                for (const creditRecord of creditRecords) {
                    let creditRemaining = Math.abs(creditRecord.reconciliationBalance);
                    if (!(creditRemaining > 0)) {
                        continue;
                    }

                    creditRecord.reconciliationBalance += debitRemaining;
                    creditRecord.reconciliationBalance = app.round(
                        Math.min(0, creditRecord.reconciliationBalance),
                        'currency'
                    );
                    if (creditRecord.reconciliationBalance === 0) {
                        creditRecord.reconciled = true;
                        creditRecord.reconciliationCode = code;
                        creditRecord.reconciliationData = {};
                    }

                    debitRemaining -= creditRemaining;
                    debitRemaining = app.round(Math.max(0, debitRemaining), 'currency');

                    if (!(debitRemaining > 0)) {
                        break;
                    }
                }

                debitRecord.reconciliationBalance = debitRemaining;
                if (debitRecord.reconciliationBalance === 0) {
                    debitRecord.reconciled = true;
                    debitRecord.reconciliationCode = code;
                    debitRecord.reconciliationData = {};
                }
            }
            for (const record of creditRecords.concat(debitRecords)) {
                transactionOperations.push({
                    updateOne: {
                        filter: {_id: record._id},
                        update: {
                            $set: {
                                ..._.pick(record, [
                                    'reconciled',
                                    'reconciliationCode',
                                    'reconciliationData',
                                    'reconciliationBalance'
                                ])
                            }
                        }
                    }
                });
                transactionIds.push(record._id);
            }

            if (transactionOperations.length > 0) {
                await app.collection('accounting.transactions').bulkWrite(transactionOperations);
            }

            const updatedRecords = await app.collection('accounting.transactions').find({
                _id: {$in: transactionIds},
                $select: ['_id', 'entryId', 'balance', 'reconciliationBalance'],
                $sort: {dueDate: 1}
            });
            for (const record of updatedRecords) {
                const customerInvoice = await app.collection('accounting.customer-invoices').findOne({
                    journalEntryId: record.entryId,
                    $select: ['_id', 'code', 'grandTotal', 'currencyId', 'currencyRate'],
                    $populate: ['currency']
                });
                if (!!customerInvoice) {
                    let paidTotal = app.round(
                        Math.abs(record.balance ?? 0) - Math.abs(record.reconciliationBalance ?? 0),
                        'total'
                    );
                    if (
                        customerInvoice.currencyId !== company.currencyId &&
                        _.isFinite(customerInvoice.currencyRate) &&
                        customerInvoice.currencyRate !== 0
                    ) {
                        paidTotal = app.round(paidTotal / customerInvoice.currencyRate, 'total');
                    }

                    customerInvoiceOperations.push({
                        updateOne: {
                            filter: {_id: customerInvoice._id},
                            update: {
                                $set: {
                                    paymentStatus:
                                        paidTotal >= customerInvoice.grandTotal
                                            ? 'paid'
                                            : paidTotal > 0
                                            ? 'partially-paid'
                                            : 'not-paid',
                                    paidTotal: Math.min(paidTotal, customerInvoice.grandTotal),
                                    reconciliationCode: code
                                }
                            }
                        }
                    });
                    customerInvoiceIds.push(customerInvoice._id);
                }

                const vendorInvoice = await app.collection('accounting.vendor-invoices').findOne({
                    journalEntryId: record.entryId,
                    $select: ['_id', 'grandTotal', 'currencyId'],
                    $populate: ['currency']
                });
                if (!!vendorInvoice) {
                    let paidTotal = app.round(
                        Math.abs(record.balance ?? 0) - Math.abs(record.reconciliationBalance ?? 0),
                        'total'
                    );
                    if (
                        vendorInvoice.currencyId !== company.currencyId &&
                        _.isFinite(vendorInvoice.currencyRate) &&
                        vendorInvoice.currencyRate !== 0
                    ) {
                        paidTotal = app.round(paidTotal / vendorInvoice.currencyRate, 'total');
                    }

                    vendorInvoiceOperations.push({
                        updateOne: {
                            filter: {_id: vendorInvoice._id},
                            update: {
                                $set: {
                                    paymentStatus:
                                        paidTotal >= vendorInvoice.grandTotal
                                            ? 'paid'
                                            : paidTotal > 0
                                            ? 'partially-paid'
                                            : 'not-paid',
                                    paidTotal: Math.min(paidTotal, vendorInvoice.grandTotal),
                                    reconciliationCode: code
                                }
                            }
                        }
                    });
                    vendorInvoiceIds.push(vendorInvoice._id);
                }
            }

            if (customerInvoiceOperations.length > 0) {
                await app.collection('accounting.customer-invoices').bulkWrite(customerInvoiceOperations);
            }
            if (vendorInvoiceOperations.length > 0) {
                await app.collection('accounting.vendor-invoices').bulkWrite(vendorInvoiceOperations);
            }

            // For analytic.
            const now = app.datetime.local().toJSDate();
            if (transactionIds.length > 0) {
                await app.collection('accounting.transactions').patch({_id: {$in: transactionIds}}, {updatedAt: now});
            }
            if (customerInvoiceIds.length > 0) {
                await app
                    .collection('accounting.customer-invoices')
                    .patch({_id: {$in: customerInvoiceIds}}, {updatedAt: now});
            }
            if (vendorInvoiceIds.length > 0) {
                await app
                    .collection('accounting.vendor-invoices')
                    .patch({_id: {$in: vendorInvoiceIds}}, {updatedAt: now});
            }
        }
    },
    {
        name: 'reconciliations-reconcile',
        async action(reconciliation, params) {
            const app = this.app;

            let code = reconciliation.reconciliationCode;
            if (!code) {
                const numbering = await this.app.collection('kernel.numbering').findOne(
                    {
                        code: 'reconciliationNumbering',
                        $select: ['_id']
                    },
                    {
                        disableInUseCheck: true,
                        disableActiveCheck: true,
                        disableSoftDelete: true
                    }
                );

                code = await app.rpc('kernel.common.request-number', {
                    numberingId: numbering._id,
                    save: true
                });
            }

            const transactionOperations = [];
            const customerInvoiceOperations = [];
            const vendorInvoiceOperations = [];
            const transactionIds = [];
            const customerInvoiceIds = [];
            const vendorInvoiceIds = [];

            if (!!reconciliation.exchangeDifferenceInvoiceId) {
                const partnerType = reconciliation.matchedRecords[0].partner.type;
                const invoice = await app
                    .collection(
                        partnerType === 'customer' ? 'accounting.customer-invoices' : 'accounting.vendor-invoices'
                    )
                    .findOne({
                        _id: reconciliation.exchangeDifferenceInvoiceId,
                        $select: ['journalEntryId']
                    });
                const transactions = await app.collection('accounting.transactions').find({
                    entryId: invoice.journalEntryId,
                    partnerId: reconciliation.matchedRecords[0].partnerId,
                    reconciled: false,
                    'account.reconciliation': true,
                    $sort: {issueDate: -1}
                });
                const currencies = await app.collection('kernel.currencies').find({
                    _id: {$in: transactions.map(transaction => transaction.currencyId)},
                    $disableSoftDelete: true,
                    $disableActiveCheck: true
                });
                const currenciesMap = {};
                for (const currency of currencies) {
                    currenciesMap[currency._id] = currency;
                }

                reconciliation.matchedRecords.push(
                    ...transactions.map(transaction => {
                        transaction.currency = currenciesMap[transaction.currencyId];

                        return transaction;
                    })
                );
            }

            let debitTotal = 0;
            let creditTotal = 0;
            let debitFCTotal = 0;
            let creditFCTotal = 0;
            for (const record of reconciliation.matchedRecords) {
                debitTotal += record.debit || 0;
                creditTotal += record.credit || 0;
                debitFCTotal += record.debitFC || 0;
                creditFCTotal += record.creditFC || 0;
            }
            debitTotal = app.round(debitTotal, 'currency');
            creditTotal = app.round(creditTotal, 'currency');
            debitFCTotal = app.round(debitFCTotal, 'currency');
            creditFCTotal = app.round(creditFCTotal, 'currency');
            if (debitTotal !== creditTotal) {
                throw new app.errors.Unprocessable(
                    app.translate('Records could not reconciled because the total debit and credit are not balanced!')
                );
            }

            for (const record of reconciliation.matchedRecords) {
                transactionOperations.push({
                    updateOne: {
                        filter: {_id: record._id},
                        update: {
                            $set: {
                                reconciled: true,
                                reconciliationCode: code,
                                reconciliationData: record.reconciliationData || {},
                                reconciliationBalance: 0
                            }
                        }
                    }
                });
                transactionIds.push(record._id);

                const customerInvoice = await app.collection('accounting.customer-invoices').findOne({
                    journalEntryId: record.entryId,
                    $select: ['_id', 'grandTotal', 'currencyId'],
                    $populate: ['currency']
                });
                if (!!customerInvoice) {
                    customerInvoiceOperations.push({
                        updateOne: {
                            filter: {_id: customerInvoice._id},
                            update: {
                                $set: {
                                    paymentStatus: 'paid',
                                    paidTotal: customerInvoice.grandTotal,
                                    reconciliationCode: code
                                }
                            }
                        }
                    });
                    customerInvoiceIds.push(customerInvoice._id);
                }

                const vendorInvoice = await app.collection('accounting.vendor-invoices').findOne({
                    journalEntryId: record.entryId,
                    $select: ['_id', 'grandTotal', 'currencyId'],
                    $populate: ['currency']
                });
                if (!!vendorInvoice) {
                    vendorInvoiceOperations.push({
                        updateOne: {
                            filter: {_id: vendorInvoice._id},
                            update: {
                                $set: {
                                    paymentStatus: 'paid',
                                    paidTotal: vendorInvoice.grandTotal,
                                    reconciliationCode: code
                                }
                            }
                        }
                    });
                    vendorInvoiceIds.push(vendorInvoice._id);
                }
            }

            if (transactionOperations.length > 0) {
                await app.collection('accounting.transactions').bulkWrite(transactionOperations);
            }
            if (customerInvoiceOperations.length > 0) {
                await app.collection('accounting.customer-invoices').bulkWrite(customerInvoiceOperations);
            }
            if (vendorInvoiceOperations.length > 0) {
                await app.collection('accounting.vendor-invoices').bulkWrite(vendorInvoiceOperations);
            }

            // For analytic.
            const now = app.datetime.local().toJSDate();
            if (transactionIds.length > 0) {
                await app.collection('accounting.transactions').patch({_id: {$in: transactionIds}}, {updatedAt: now});
            }
            if (customerInvoiceIds.length > 0) {
                await app
                    .collection('accounting.customer-invoices')
                    .patch({_id: {$in: customerInvoiceIds}}, {updatedAt: now});
            }
            if (vendorInvoiceIds.length > 0) {
                await app
                    .collection('accounting.vendor-invoices')
                    .patch({_id: {$in: vendorInvoiceIds}}, {updatedAt: now});
            }
        }
    },
    {
        name: 'reconciliations-reset-reconciliations',
        async action({partnerId, onlySaved}, params) {
            const app = this.app;
            const transactions = await app.collection('accounting.transactions').find({
                partnerId,
                'account.reconciliation': true,
                ...(!!onlySaved
                    ? {
                          $or: [{reconciled: {$exists: false}}, {reconciled: {$ne: true}}]
                      }
                    : {}),
                $sort: {dueDate: 1}
            });
            const transactionOperations = [];
            const customerInvoiceOperations = [];
            const vendorInvoiceOperations = [];
            const transactionIds = [];
            const customerInvoiceIds = [];
            const vendorInvoiceIds = [];

            for (const record of transactions) {
                transactionOperations.push({
                    updateOne: {
                        filter: {_id: record._id},
                        update: {
                            $set: {
                                reconciled: false,
                                reconciliationCode: null,
                                reconciliationData: null,
                                reconciliationBalance: record.balance ?? 0,
                                reconciliationBalanceFC: null
                            }
                        }
                    }
                });
                transactionIds.push(record._id);

                const customerInvoices = await app.collection('accounting.customer-invoices').find({
                    journalEntryId: record.entryId,
                    $select: ['_id']
                });
                const vendorInvoices = await app.collection('accounting.vendor-invoices').find({
                    journalEntryId: record.entryId,
                    $select: ['_id']
                });
                for (const customerInvoice of customerInvoices) {
                    customerInvoiceOperations.push({
                        updateOne: {
                            filter: {_id: customerInvoice._id},
                            update: {
                                $set: {
                                    paymentStatus: 'not-paid',
                                    paidTotal: 0,
                                    reconciliationCode: null
                                }
                            }
                        }
                    });
                    customerInvoiceIds.push(customerInvoice._id);
                }
                for (const vendorInvoice of vendorInvoices) {
                    vendorInvoiceOperations.push({
                        updateOne: {
                            filter: {_id: vendorInvoice._id},
                            update: {
                                $set: {
                                    paymentStatus: 'not-paid',
                                    paidTotal: 0,
                                    reconciliationCode: null
                                }
                            }
                        }
                    });
                    vendorInvoiceIds.push(vendorInvoice._id);
                }

                if (transactionOperations.length > 0) {
                    await app.collection('accounting.transactions').bulkWrite(transactionOperations);
                }
                if (customerInvoiceOperations.length > 0) {
                    await app.collection('accounting.customer-invoices').bulkWrite(customerInvoiceOperations);
                }
                if (vendorInvoiceOperations.length > 0) {
                    await app.collection('accounting.vendor-invoices').bulkWrite(vendorInvoiceOperations);
                }
            }

            // For analytic.
            const now = app.datetime.local().toJSDate();
            if (transactionIds.length > 0) {
                await app.collection('accounting.transactions').patch({_id: {$in: transactionIds}}, {updatedAt: now});
            }
            if (customerInvoiceIds.length > 0) {
                await app
                    .collection('accounting.customer-invoices')
                    .patch({_id: {$in: customerInvoiceIds}}, {updatedAt: now});
            }
            if (vendorInvoiceIds.length > 0) {
                await app
                    .collection('accounting.vendor-invoices')
                    .patch({_id: {$in: vendorInvoiceIds}}, {updatedAt: now});
            }
        }
    },
    {
        name: 'reconciliations-reconcile-all',
        async action(payload, params) {
            const app = this.app;
            const progressId = `accounting.adviser.reconciliations-${params.user._id}`;
            const locale = app.config('app.locale');

            const {query = {}} = payload;
            if (!Array.isArray(query.$and)) {
                query.$and = [];
            }
            query.$and.push({
                $or: [{reconciled: {$exists: false}}, {reconciled: {$ne: true}}]
            });
            query.$and.push({
                'account.reconciliation': true,
                partnerId: {$exists: true},
                'partner.type': {$in: ['customer', 'vendor']}
            });

            app.progress({
                id: progressId,
                status: 'started',
                message: this.translate('Please wait.. Partner information is being collected.')
            });

            const partnersPipeline = [
                {
                    $match: query
                },
                {
                    $group: {
                        _id: '$partnerId',
                        partner: {$first: '$partner'}
                    }
                }
            ];
            const countResult = await app.collection('accounting.transactions').aggregate([
                ...partnersPipeline,
                {
                    $count: 'total'
                }
            ]);
            const total = countResult.length > 0 ? countResult[0].total : 0;
            if (total < 1) {
                return;
            }

            const company = await app.collection('kernel.company').findOne({});
            const currencies = await app.collection('kernel.currencies').find({
                $disableSoftDelete: true,
                $disableActiveCheck: true
            });
            const currenciesMap = {};
            for (const currency of currencies) {
                currenciesMap[currency._id] = currency;
            }

            const tempCollectionName = `accounting_temp-reconcile-all-${Random.id(4)}`;
            try {
                await app.db.dropCollection(tempCollectionName);
            } catch (error) {}
            await app.db.createCollection(tempCollectionName, {
                collation: {locale}
            });

            let limit = 100;
            let page = 0;
            let rowCount = 0;

            do {
                const partners = await app.collection('accounting.transactions').aggregate([
                    ...partnersPipeline,
                    {
                        $sort: {
                            'partner.code': 1
                        }
                    },
                    {$skip: page * limit},
                    {$limit: limit},
                    {
                        $project: {
                            _id: 1
                        }
                    }
                ]);

                await app.db.collection(tempCollectionName).insertMany(partners);

                rowCount += partners.length;
                page++;

                const percentage = (rowCount / total) * 100;
                app.progress({
                    id: progressId,
                    status: 'info',
                    percentage,
                    message: this.translate('Please wait.. Partner information is being collected.')
                });
            } while (total > 0 && rowCount < total);

            limit = 10;
            page = 0;
            rowCount = 0;

            app.progress({
                id: progressId,
                status: 'info',
                percentage: 0,
                message: this.translate('Please wait.. The reconciliations are underway.')
            });

            try {
                do {
                    const partners = await app.db
                        .collection(tempCollectionName)
                        .find({})
                        .sort({_id: 1})
                        .skip(page * limit)
                        .limit(limit)
                        .toArray();

                    for (const partner of partners) {
                        let transactions = await getTransactionsForReconcileAll(app, company, partner._id);

                        const reconciliations = await getReconciliationsForReconcileAll(app, transactions);
                        for (const reconciliation of reconciliations.filter(r => !!r.canReconcile)) {
                            await app.rpc('accounting.reconciliations-reconcile', reconciliation);

                            transactions = transactions.filter(
                                transaction => transaction.reconciliationCode !== reconciliation._id
                            );
                        }

                        const unMatchedRecords = [...transactions];
                        if (unMatchedRecords.length > 0) {
                            const firstCreditRow = unMatchedRecords.find(record => record.balance < 0);

                            if (!!firstCreditRow) {
                                const reconciliationCode = Random.id(8);

                                transactions = transactions.map(t => {
                                    if (t._id === firstCreditRow._id) {
                                        t.reconciliationCode = reconciliationCode;
                                        t.reconciliationData = {
                                            order: 1
                                        };
                                    }

                                    return t;
                                });

                                let creditTotal = firstCreditRow.credit;
                                for (const transaction of unMatchedRecords.filter(
                                    record => record._id !== firstCreditRow._id && record.balance < 0
                                )) {
                                    const reconciliation = (
                                        await getReconciliationsForReconcileAll(app, transactions)
                                    ).find(r => r._id === reconciliationCode);

                                    creditTotal += transaction.credit;

                                    const matchedRecords = _.orderBy(
                                        transactions.filter(t => t.reconciliationCode === reconciliation._id),
                                        ['reconciliationData.order'],
                                        ['asc']
                                    );
                                    const index = transactions.findIndex(t => t._id === transaction._id);

                                    transactions[index].reconciliationCode = reconciliation._id;
                                    transactions[index].reconciliationData = {
                                        ...(transactions[index].reconciliationData || {}),
                                        currencyId: company.currencyId,
                                        currency: company.currency,
                                        order: matchedRecords.length + 1
                                    };
                                }
                                creditTotal = app.round(creditTotal, 'currency');

                                for (const transaction of unMatchedRecords.filter(
                                    record => record._id !== firstCreditRow._id && record.balance > 0
                                )) {
                                    const reconciliation = (
                                        await getReconciliationsForReconcileAll(app, transactions)
                                    ).find(r => r._id === reconciliationCode);

                                    const matchedRecords = _.orderBy(
                                        transactions.filter(t => t.reconciliationCode === reconciliation._id),
                                        ['reconciliationData.order'],
                                        ['asc']
                                    );
                                    const index = transactions.findIndex(t => t._id === transaction._id);

                                    transactions[index].reconciliationCode = reconciliation._id;
                                    transactions[index].reconciliationData = {
                                        ...(transactions[index].reconciliationData || {}),
                                        currencyId: company.currencyId,
                                        currency: company.currency,
                                        order: matchedRecords.length + 1
                                    };

                                    creditTotal -= transaction.debit;
                                    creditTotal = app.round(creditTotal, 'currency');

                                    if (creditTotal < 0) {
                                        break;
                                    }
                                }

                                const reconciliation = (
                                    await getReconciliationsForReconcileAll(app, transactions)
                                ).find(r => r._id === reconciliationCode);
                                await app.rpc('accounting.reconciliations-save-reconciliation', reconciliation);
                            }
                        }

                        rowCount++;

                        const percentage = (rowCount / total) * 100;
                        app.progress({
                            id: progressId,
                            status: 'info',
                            percentage,
                            message: this.translate('Please wait.. The reconciliations are underway.')
                        });
                    }

                    page++;
                } while (total > 0 && rowCount < total);
            } catch (error) {
                const percentage = (rowCount / total) * 100;
                app.progress({
                    id: progressId,
                    status: 'error',
                    message: error.message,
                    percentage
                });
            }

            await app.db.dropCollection(tempCollectionName);

            app.progress({
                id: progressId,
                status: 'success',
                percentage: 100
            });
        }
    }
];

async function getTransactionsForReconcileAll(app, company, partnerId) {
    const records = await app.collection('accounting.transactions').find({
        partnerId,
        $or: [{reconciled: {$exists: false}}, {reconciled: {$ne: true}}],
        'account.reconciliation': true,
        reconciliationBalance: {$ne: 0},
        $sort: {dueDate: 1}
    });
    let items = records.map(transaction => {
        if (_.isPlainObject(transaction.reconciliationData) && !_.isEmpty(transaction.reconciliationData)) {
            transaction = {
                ...transaction,
                ...transaction.reconciliationData
            };
        }

        if (transaction.debit > 0) {
            transaction.debit = Math.abs(transaction.reconciliationBalance);
        } else {
            transaction.credit = Math.abs(transaction.reconciliationBalance);
        }
        if (transaction.debitFC > 0) {
            const currencyRate = transaction.debit / transaction.debitFC;

            transaction.currencyRate = currencyRate;
            transaction.debitFC = Math.abs(transaction.reconciliationBalance) / currencyRate;
        } else if (transaction.creditFC > 0) {
            const currencyRate = transaction.credit / transaction.creditFC;

            transaction.currencyRate = currencyRate;
            transaction.creditFC = Math.abs(transaction.reconciliationBalance) / currencyRate;
        }
        transaction.balance = transaction.reconciliationBalance ?? 0;

        if (_.isFinite(transaction.debitFC) && _.isFinite(transaction.creditFC)) {
            transaction.balanceFC = transaction.debitFC - transaction.creditFC;
        }

        return transaction;
    });
    const transactions = [];

    for (const item of items) {
        if (!!(item.reconciliationData || {}).isSaved) {
            transactions.push(item);

            items = items.filter(t => t._id !== item._id);
        }
    }

    const grouped = _.groupBy(items, 'reference');
    for (const reference of Object.keys(grouped)) {
        if (_.isString(reference) && trim(reference).length > 0 && grouped[reference].length > 1) {
            const reconciliationCode = Random.id(8);
            const groupedItems = grouped[reference];
            let i = 1;

            transactions.push(
                ...groupedItems.map(t => {
                    t.reconciliationCode = reconciliationCode;
                    t.reconciliationData = {
                        order: i++
                    };

                    return t;
                })
            );
            const ids = groupedItems.map(t => t._id);

            items = items.filter(t => !ids.includes(t._id));
        }
    }

    const iterator = (items, item) => {
        let isDebit = item.debit > 0;
        let matching = items.find(
            t => item._id !== t._id && ((isDebit && item.debit === t.credit) || (!isDebit && item.credit === t.debit))
        );

        if (!!matching) {
            const reconciliationCode = Random.id(8);

            transactions.push(
                ...[
                    {
                        ...item,
                        reconciliationCode,
                        reconciliationData: {
                            order: 1
                        }
                    },
                    {
                        ...matching,
                        reconciliationCode,
                        reconciliationData: {
                            order: 2
                        }
                    }
                ]
            );

            items = items.filter(t => t._id !== item._id && t._id !== matching._id);
        } else {
            transactions.push(item);

            items = items.filter(t => t._id !== item._id);
        }

        if (items.length > 0) {
            iterator(items, items[0]);
        }
    };
    if (items.length > 0) {
        iterator(items, items[0]);
    }

    return transactions;
}

async function getReconciliationsForReconcileAll(app, transactions) {
    const records = transactions.filter(transaction => !!transaction.reconciliationCode);
    const grouped = _.groupBy(records, 'reconciliationCode');
    const items = [];

    for (const reconciliationCode of Object.keys(grouped)) {
        const item = {};

        item._id = reconciliationCode;
        item.canReconcile = false;
        item.matchedRecords = _.sortBy(grouped[reconciliationCode], 'reconciliationData.order');

        let debitTotal = 0;
        let creditTotal = 0;
        let debitFCTotal = 0;
        let creditFCTotal = 0;
        for (const record of item.matchedRecords) {
            debitTotal += record.debit || 0;
            creditTotal += record.credit || 0;
            debitFCTotal += record.debitFC || 0;
            creditFCTotal += record.creditFC || 0;
        }
        debitTotal = app.round(debitTotal, 'currency');
        creditTotal = app.round(creditTotal, 'currency');
        debitFCTotal = app.round(debitFCTotal, 'currency');
        creditFCTotal = app.round(creditFCTotal, 'currency');

        item.canReconcile = debitTotal === creditTotal;

        items.push(item);
    }

    return items;
}
