import _ from 'lodash';
import path from 'path';
import * as Excel from 'exceljs';
import fastCopy from 'fast-copy';
import Random from 'framework/random';
import {firstUpper, firstUpperAll, getMongoSearchQuery, toLower} from 'framework/helpers';

export default [
    {
        name: 'partner-ledger-get-partners',
        async action(payload, params) {
            const app = this.app;
            const user = params.user;
            const {skip, limit, partnerType, partnerGroupIds, searchQuery} = payload;
            const result = {
                total: 0,
                data: []
            };

            let canOnlySeeOwnRecordsInPartnerLedger = false;
            try {
                canOnlySeeOwnRecordsInPartnerLedger = await app.checkPermission({
                    type: 'permission',
                    name: 'accounting.canOnlySeeOwnRecordsInPartnerLedger',
                    user
                });
            } catch (error) {}

            const partnersQuery = {
                $and: [],
                $limit: limit,
                $skip: skip,
                $sort: {createdAt: -1},
                $select: ['_id', 'type', 'code', 'name', 'isCompany', 'tin', 'identity']
                // $disableActiveCheck: true,
                // $disableSoftDelete: true
            };
            if (!!canOnlySeeOwnRecordsInPartnerLedger && !user.isRoot) {
                partnersQuery.$and.push({
                    $or: [{createdBy: user._id}, {assignedBy: user._id}, {assignedTo: user._id}]
                });
            }
            if (!!partnerType) {
                partnersQuery.type = partnerType;
            } else {
                partnersQuery.type = {$in: ['customer', 'vendor']};
            }
            if (Array.isArray(partnerGroupIds) && partnerGroupIds.length > 0) {
                partnersQuery.groupId = {$in: partnerGroupIds};
            }
            if (!!searchQuery) {
                if (searchQuery.indexOf(' ') !== -1) {
                    const parts = searchQuery.split(' ');

                    for (const searchText of parts) {
                        partnersQuery.$and.push(getMongoSearchQuery('searchText', toLower(searchText)));
                    }
                } else {
                    partnersQuery.$and.push(getMongoSearchQuery('searchText', searchQuery));
                }
            }
            if (
                !(user.isRoot && !!app.setting('system.rootsAuthorizedOnAllBranches')) &&
                !!user &&
                Array.isArray(user.branchIds)
            ) {
                partnersQuery.$and.push({
                    branchIds: {$in: user.branchIds}
                });
            }
            if (partnersQuery.$and.length < 1) {
                delete partnersQuery.$and;
            }

            const res = await app.collection('kernel.partners').find(partnersQuery, {
                paginate: {
                    default: limit
                }
            });
            result.total = res.total;
            result.data = res.data.map(r => ({
                _id: r._id,
                type: r.type,
                code: r.code,
                name: r.name,
                tinIdentity: !!r.isCompany ? r.tin : r.identity,
                debit: 0,
                credit: 0,
                balance: 0
            }));
            const report = await app.rpc(
                'accounting.partner-ledger-get-partner-balances',
                {
                    partnerIds: result.data.map(d => d._id)
                },
                {user}
            );
            result.data = result.data.map(d => {
                const r = report.find(r => r.partnerId === d._id);

                if (!!r) {
                    d.debit = r.debit;
                    d.credit = r.credit;
                    d.balance = r.balance;
                }

                return d;
            });

            return result;
        }
    },
    {
        name: 'partner-ledger-get-partner-balances',
        async action(payload, params) {
            const app = this.app;
            const user = params.user;

            const partnersQuery = {
                $and: [{partnerId: {$in: payload.partnerIds ?? []}}]
            };
            if (
                !(user.isRoot && !!app.setting('system.rootsAuthorizedOnAllBranches')) &&
                !!user &&
                Array.isArray(user.branchIds)
            ) {
                partnersQuery.$and.push({
                    branchId: {$in: user.branchIds}
                });
            }

            return (
                await app.collection('accounting.partner-balances').aggregate([
                    {$match: partnersQuery},
                    {
                        $group: {
                            _id: '$partnerId',
                            debit: {$sum: '$debit'},
                            credit: {$sum: '$credit'},
                            balance: {$sum: '$balance'}
                        }
                    }
                ])
            ).map(r => {
                r.partnerId = r._id;
                delete r._id;

                return r;
            });
        }
    },
    {
        name: 'partner-ledger-get-partner-balance',
        async action(payload, params) {
            const app = this.app;
            const user = params.user;
            const {partnerId, scopeQuery} = payload;
            const partnersCollection = app.collection('kernel.partners');
            const transactionsCollection = app.collection('accounting.transactions');
            const partner = await partnersCollection.findOne({
                _id: partnerId,
                $select: [
                    '_id',
                    'type',
                    'isCompany',
                    'code',
                    'name',
                    'tin',
                    'identity',
                    'accountingAccountId',
                    'additionalAllowanceAccount',
                    'employeeExpenseAccountId',
                    'connectedPartnerIds'
                ],
                $disableSoftDelete: true
            });
            const includeConnectedPartners = !!payload.includeConnectedPartners;
            const connectedPartnerIds = (partner ?? {}).connectedPartnerIds ?? [];
            const result = {
                type: '',
                code: '',
                name: '',
                tinIdentity: '',
                debit: 0,
                credit: 0,
                balance: 0
            };

            if (!!partner) {
                result.type = partner.type;
                result.code = partner.code;
                result.name = partner.name;
                result.tinIdentity = !!partner.isCompany ? partner.tin : partner.identity;

                // const accountIds = await getPartnerAccounts(app, {excludeAdditionalAllowances: false});
                let accountIds = [];
                if (!!partner.accountingAccountId) {
                    accountIds.push(partner.accountingAccountId);
                }
                if (!!partner.additionalAllowanceAccount) {
                    accountIds.push(partner.additionalAllowanceAccount);
                }
                if (!!partner.employeeExpenseAccountId) {
                    accountIds.push(partner.employeeExpenseAccountId);
                }
                const query = {$and: []};
                // if (
                //     !(user.isRoot && !!app.setting('system.rootsAuthorizedOnAllBranches')) &&
                //     !!user &&
                //     Array.isArray(user.branchIds)
                // ) {
                //     query.$and.push({
                //         branchId: {$in: user.branchIds}
                //     });
                // }
                if (!!includeConnectedPartners) {
                    query.$and.push(
                        ...[
                            {
                                partnerId: {
                                    $in: [partnerId].concat(connectedPartnerIds)
                                }
                            },
                            {accountId: {$in: accountIds}}
                        ]
                    );
                } else {
                    query.$and.push(...[{partnerId}, {accountId: {$in: accountIds}}]);
                }
                if (!_.isEmpty(scopeQuery)) {
                    query.$and.push(scopeQuery);
                }

                const pipeline = [
                    {$match: query},
                    {
                        $group: {
                            _id: null,
                            debit: {$sum: '$debit'},
                            credit: {$sum: '$credit'}
                        }
                    },
                    {$project: {debit: 1, credit: 1, balance: {$subtract: ['$debit', '$credit']}}}
                ];
                let r = await transactionsCollection.aggregate(pipeline);
                if (r.length > 0) {
                    r = r[0];
                    result.debit = r.debit;
                    result.credit = r.credit;
                    result.balance = r.balance;
                }
            }

            return result;
        }
    },
    {
        name: 'partner-ledger-get-records',
        async action(payload, params) {
            const app = this.app;
            const user = params.user;
            const company = await app.collection('kernel.company').findOne({});
            const {query} = payload;
            const partnersCollection = app.collection('kernel.partners');
            const transactionsCollection = app.collection('accounting.transactions');

            // const accountIds = await getPartnerAccounts(app, {excludeAdditionalAllowances: false});
            const partner = await partnersCollection.findOne({
                _id: payload.partnerId,
                $select: [
                    '_id',
                    'accountingAccountId',
                    'additionalAllowanceAccount',
                    'employeeExpenseAccountId',
                    'connectedPartnerIds'
                ],
                $disableSoftDelete: true,
                $disableActiveCheck: true
            });
            let accountIds = [];
            if (!!partner.accountingAccountId) {
                accountIds.push(partner.accountingAccountId);
            }
            if (!!partner.additionalAllowanceAccount) {
                accountIds.push(partner.additionalAllowanceAccount);
            }
            if (!!partner.employeeExpenseAccountId) {
                accountIds.push(partner.employeeExpenseAccountId);
            }
            const includeConnectedPartners = !!payload.includeConnectedPartners;
            const connectedPartnerIds = (partner ?? {}).connectedPartnerIds ?? [];

            if (!Array.isArray(query.$and)) {
                query.$and = [];
            }

            if (!!includeConnectedPartners) {
                delete query.partnerId;

                query.$and.push({
                    partnerId: {
                        $in: [payload.partnerId].concat(connectedPartnerIds)
                    }
                });
            }

            // if (
            //     !(user.isRoot && !!app.setting('system.rootsAuthorizedOnAllBranches')) &&
            //     !!user &&
            //     Array.isArray(user.branchIds)
            // ) {
            //     query.$and.push({
            //         branchId: {$in: user.branchIds}
            //     });
            // }
            query.$and.push(
                ...[
                    {partnerId: {$exists: true}},
                    {partnerId: {$ne: null}},
                    {partner: {$exists: true}},
                    {partner: {$ne: null}},
                    {accountId: {$in: accountIds}}
                ]
            );

            let result = await transactionsCollection.find({
                ...query,
                $sort: {issueDate: 1}
            });

            let initialBalance = null;
            if (result.length > 0) {
                initialBalance = await app.rpc('accounting.get-last-balance-before-transaction', {
                    transactionId: result[0]._id,
                    query,
                    issueDate: result[0].issueDate
                });
            }
            if (!_.isFinite(initialBalance)) {
                initialBalance = 0;
            }

            const currencies = await app.collection('kernel.currencies').find({
                _id: {$ne: company.currencyId},
                $select: ['_id'],
                $disableActiveCheck: true,
                $disableSoftDelete: true
            });
            const fcInitialBalancesMap = {};
            if (result.length > 0) {
                for (const currency of currencies) {
                    fcInitialBalancesMap[currency._id] = await app.rpc(
                        'accounting.get-last-balance-before-transaction',
                        {
                            transactionId: result[0]._id,
                            query,
                            currencyId: currency._id,
                            issueDate: result[0].issueDate
                        }
                    );
                }
            }

            result = result.map(item => {
                item.initialBalance = initialBalance;
                item.balance = item.initialBalance + (item.debit - item.credit);

                initialBalance = item.balance;

                if (item.currencyId !== company.currencyId) {
                    item.initialBalanceFC = fcInitialBalancesMap[item.currencyId] || 0;
                    item.balanceFC = item.initialBalanceFC + ((item.debitFC || 0) - (item.creditFC || 0));

                    if (item.debitFC > 0) {
                        item.currencyRate = item.debit / item.debitFC;
                    } else if (item.creditFC > 0) {
                        item.currencyRate = item.credit / item.creditFC;
                    }

                    fcInitialBalancesMap[item.currencyId] = item.balanceFC;
                } else {
                    delete item.debitFC;
                    delete item.creditFC;
                    delete item.balanceFC;
                }

                if (item.debit > 0) {
                    delete item.credit;
                }
                if (item.credit > 0) {
                    delete item.debit;
                }
                if (item.debitFC > 0) {
                    delete item.creditFC;
                }
                if (item.creditFC > 0) {
                    delete item.debitFC;
                }

                return item;
            });

            result = _.orderBy(result, ['issueDate'], ['desc']);

            return result;
        }
    },
    {
        name: 'partner-ledger-export',
        async action(payload, params) {
            const app = this.app;
            const user = params.user;
            const t = app.translate;
            const company = await app.collection('kernel.company').findOne({});
            const customCurrency = !!payload.currencyId
                ? await app.collection('kernel.currencies').findOne({_id: payload.currencyId})
                : null;
            const formatOptions = {
                ...payload.formatOptions,
                currency: !!customCurrency
                    ? {
                          ...(payload.formatOptions.currency || {}),
                          symbol: customCurrency.symbol,
                          format: customCurrency.symbolPosition === 'before' ? '%s %v' : '%v %s'
                      }
                    : {
                          ...(payload.formatOptions.currency || {}),
                          symbol: company.currency.symbol,
                          format: company.currency.symbolPosition === 'before' ? '%s %v' : '%v %s'
                      }
            };
            const format = (value, format) => app.format(value, format, formatOptions);
            const transactionsCollection = app.collection('accounting.transactions');
            const partnerId = payload.partnerId;
            const includeConnectedPartners = payload.includeConnectedPartners;
            const connectedPartnerIds = payload.connectedPartnerIds;
            const documentType = payload.documentType;
            const startDate = payload.startDate;
            const endDate = payload.endDate;
            const groupByCurrency = payload.groupByCurrency;
            const hideInitialBalance = payload.hideInitialBalance;
            const hideDebitRows = payload.hideDebitRows;
            const hideZeroBalances = payload.hideZeroBalances;
            const hideCreditRows = payload.hideCreditRows;
            const hideReconciledRows = payload.hideReconciledRows;
            const excludeAdditionalAllowances = !!payload.excludeAdditionalAllowances;
            const excludedJournalTypes = payload.excludedJournalTypes ?? [];
            const type = payload.type;
            const horizontalPrint = !!payload.horizontalPrint;

            // Get document title.
            let documentTitle = 'Partner Ledger';
            if (!!type) {
                if (type === 'customer') {
                    documentTitle = 'Customer Ledger';
                } else if (type === 'vendor') {
                    documentTitle = 'Vendor Ledger';
                } else if (type === 'employee') {
                    documentTitle = 'Employee Ledger';
                }
            }

            // Get partner label.
            let partnerLabel = 'Partner';
            if (!!type) {
                if (type === 'customer') {
                    partnerLabel = 'Customer';
                } else if (type === 'vendor') {
                    partnerLabel = 'Vendor';
                } else if (type === 'employee') {
                    partnerLabel = 'Employee';
                }
            }

            // Get partner code and name label.
            let partnerCodeLabel = 'Partner code';
            let partnerNameLabel = 'Partner name';
            if (!!type) {
                if (type === 'customer') {
                    partnerCodeLabel = 'Customer code';
                    partnerNameLabel = 'Customer name';
                } else if (type === 'vendor') {
                    partnerCodeLabel = 'Vendor code';
                    partnerNameLabel = 'Vendor name';
                } else if (type === 'employee') {
                    partnerCodeLabel = 'Employee code';
                    partnerNameLabel = 'Employee name';
                }
            }

            if (documentType === 'pdf') {
                const partner = await app.collection('kernel.partners').findOne({
                    _id: partnerId,
                    $select: [
                        'isCompany',
                        'name',
                        'legalName',
                        'tin',
                        'identity',
                        'accountingAccountId',
                        'additionalAllowanceAccount',
                        'employeeExpenseAccountId'
                    ]
                });
                const partnerData = {
                    stack: [
                        {
                            text: t(partnerLabel),
                            fontSize: 9,
                            bold: true,
                            color: '#0052cc',
                            margin: [0, 0, 0, 4]
                        },
                        {
                            text: partner.isCompany && !!partner.legalName ? partner.legalName : partner.name,
                            fontSize: 9
                        },
                        ...(partner.isCompany && !!partner.tin
                            ? [
                                  {
                                      columns: [
                                          {
                                              text: t('TN:'),
                                              fontSize: 9,
                                              bold: true,
                                              width: 'auto',
                                              margin: [0, 10, 3, 0]
                                          },
                                          {
                                              text: partner.tin,
                                              fontSize: 9,
                                              marginTop: 10
                                          },
                                          {}
                                      ]
                                  }
                              ]
                            : []),
                        ...(!partner.isCompany && !!partner.identity
                            ? [
                                  {
                                      columns: [
                                          {
                                              text: t('IN:'),
                                              fontSize: 9,
                                              bold: true,
                                              width: 'auto',
                                              margin: [0, 10, 3, 0]
                                          },
                                          {
                                              text: partner.identity,
                                              fontSize: 9,
                                              marginTop: 10
                                          },
                                          {}
                                      ]
                                  }
                              ]
                            : [])
                    ],
                    width: 320
                };
                const startDateData = {
                    stack: [
                        {
                            text: t('Start Date'),
                            fontSize: 9,
                            bold: true,
                            color: '#0052cc',
                            margin: [0, 0, 0, 4]
                        },
                        {
                            text: format(startDate, 'date'),
                            fontSize: 9
                        },
                        {
                            text: format(startDate, 'time'),
                            fontSize: 9
                        }
                    ],
                    width: 80
                };
                const endDateData = {
                    stack: [
                        {
                            text: t('End Date'),
                            fontSize: 9,
                            bold: true,
                            color: '#0052cc',
                            margin: [0, 0, 0, 4]
                        },
                        {
                            text: format(endDate, 'date'),
                            fontSize: 9
                        },
                        {
                            text: format(endDate, 'time'),
                            fontSize: 9
                        }
                    ],
                    width: 80
                };

                // Prepare items table.
                const itemsTable = {
                    table: {
                        headerRows: 1,
                        dontBreakRows: true,
                        widths: !!hideInitialBalance
                            ? ['auto', 'auto', 'auto', '*', 'auto', 'auto', 'auto', 'auto']
                            : ['auto', 'auto', 'auto', '*', 'auto', 'auto', 'auto', 'auto', 'auto'],
                        body: [
                            [
                                {
                                    text: firstUpperAll(t('Issue date')),
                                    style: 'itemsHeader',
                                    border: [false, false, false, true],
                                    noWrap: true
                                },
                                {
                                    text: firstUpperAll(t('Due date')),
                                    style: 'itemsHeader',
                                    border: [false, false, false, true],
                                    noWrap: true
                                },
                                {
                                    text: firstUpperAll(t('Document no')),
                                    style: 'itemsHeader',
                                    border: [false, false, false, true],
                                    noWrap: true
                                },
                                {
                                    text: firstUpperAll(t('Description')),
                                    style: 'itemsHeader',
                                    border: [false, false, false, true],
                                    noWrap: true
                                },
                                {
                                    text: firstUpperAll(t('C. Rate')),
                                    style: 'itemsHeader',
                                    border: [false, false, false, true],
                                    noWrap: true
                                },
                                ...(!hideInitialBalance
                                    ? [
                                          {
                                              text: firstUpperAll(t('I. Balance')),
                                              style: 'itemsHeader',
                                              border: [false, false, false, true],
                                              noWrap: true
                                          }
                                      ]
                                    : []),
                                {
                                    text: firstUpperAll(t('Debit')),
                                    style: 'itemsHeader',
                                    border: [false, false, false, true],
                                    noWrap: true
                                },
                                {
                                    text: firstUpperAll(t('Credit')),
                                    style: 'itemsHeader',
                                    border: [false, false, false, true],
                                    noWrap: true
                                },
                                {
                                    text: firstUpperAll(t('Balance')),
                                    style: 'itemsHeader',
                                    border: [false, false, false, true],
                                    noWrap: true
                                }
                            ]
                        ]
                    },
                    layout: {
                        hLineWidth(i, node) {
                            return i === 1 ? 1 : 0.5;
                        },
                        vLineWidth(i, node) {
                            return 0.5;
                        },
                        hLineColor(i, node) {
                            return '#bdc3c7';
                        },
                        vLineColor(i, node) {
                            return '#bdc3c7';
                        },
                        fillColor(rowIndex, node, columnIndex) {
                            return rowIndex % 2 === 1 ? '#fcfcfc' : null;
                        }
                    },
                    marginBottom: 30
                };

                // Prepare query.
                // const accountIds = await getPartnerAccounts(app, {excludeAdditionalAllowances});
                let accountIds = [];
                if (!!partner.accountingAccountId) {
                    accountIds.push(partner.accountingAccountId);
                }
                if (!!partner.additionalAllowanceAccount) {
                    accountIds.push(partner.additionalAllowanceAccount);
                }
                if (!!partner.employeeExpenseAccountId) {
                    accountIds.push(partner.employeeExpenseAccountId);
                }
                const query = {
                    partnerId,
                    accountId: {$in: accountIds},
                    ...(!!customCurrency ? {currencyId: customCurrency._id} : {})
                };
                if (!!includeConnectedPartners) {
                    query.partnerId = {
                        $in: [partnerId].concat(connectedPartnerIds)
                    };

                    const connectedPartners = await app.collection('kernel.partners').find({
                        _id: {$in: connectedPartnerIds},
                        $select: ['accountingAccountId', 'additionalAllowanceAccount']
                    });
                    let connectedAccountIds = [...accountIds];
                    for (const connectedPartner of connectedPartners) {
                        connectedAccountIds.push(connectedPartner.accountingAccountId);

                        if (!!connectedPartner.additionalAllowanceAccount) {
                            connectedAccountIds.push(connectedPartner.additionalAllowanceAccount);
                        }
                    }
                    connectedAccountIds = _.uniq(connectedAccountIds);
                    query.accountId = {$in: connectedAccountIds};
                }
                if (hideDebitRows) {
                    query.debit = {$not: {$gt: 0}};
                }
                if (hideCreditRows) {
                    query.credit = {$not: {$gt: 0}};
                }
                if (hideReconciledRows) {
                    query.reconciled = {$ne: true};
                }
                if (Array.isArray(excludedJournalTypes) && excludedJournalTypes.length > 0) {
                    query['journal.type'] = {$nin: excludedJournalTypes};
                }
                if (!!payload.scope) {
                    query.scope = payload.scope;
                }
                if (payload.branchId) {
                    query.branchId = payload.branchId;
                }

                // Find initial balance.
                let initialBalance = 0;
                let ir = await transactionsCollection.aggregate([
                    {
                        $match: {
                            ...query,
                            issueDate: {$lt: startDate}
                        }
                    },
                    {
                        $group: {
                            _id: null,
                            debit: {$sum: '$debit'},
                            credit: {$sum: '$credit'},
                            debitFC: {$sum: '$debitFC'},
                            creditFC: {$sum: '$creditFC'}
                        }
                    },
                    {
                        $project: {
                            _id: 1,
                            debit: 1,
                            credit: 1,
                            debitFC: 1,
                            creditFC: 1,
                            balance: {$subtract: ['$debit', '$credit']},
                            balanceFC: {$subtract: ['$debitFC', '$creditFC']}
                        }
                    }
                ]);
                if (!!customCurrency && payload.currencyId !== company.currencyId) {
                    ir = ir.map(r => {
                        r.debit = r.debitFC;
                        r.credit = r.creditFC;
                        r.balance = r.balanceFC;
                        delete r.debitFC;
                        delete r.creditFC;
                        delete r.balanceFC;

                        return r;
                    });
                }
                if (ir.length > 0) {
                    initialBalance = ir[0].balance;
                }

                // Get items.
                const items = (
                    await transactionsCollection.find({
                        ...query,
                        issueDate: {$gte: startDate, $lte: endDate},
                        $sort: {issueDate: 1, _id: 1}
                    })
                ).map(item => {
                    if (!!customCurrency && payload.currencyId !== company.currencyId) {
                        if (item.debitFC > 0) {
                            item.currencyRate = item.debit / item.debitFC;
                        } else if (item.creditFC > 0) {
                            item.currencyRate = item.credit / item.creditFC;
                        }

                        item.debit = item.debitFC;
                        item.credit = item.creditFC;
                        item.balance = item.balanceFC;
                        delete item.debitFC;
                        delete item.creditFC;
                        delete item.balanceFC;
                    }

                    item.debit = item.debit || 0;
                    item.credit = item.credit || 0;
                    item.balance = item.balance || 0;

                    item.initialBalance = initialBalance;
                    item.balance = item.initialBalance + (item.debit - item.credit);

                    initialBalance = item.balance;

                    return item;
                });

                // Prepare items table rows.
                for (const item of items) {
                    if (hideZeroBalances && item.balance === 0) {
                        continue;
                    }

                    // Get balance text.
                    let balanceText = format(Math.abs(item.balance), 'currency');
                    if (item.balance !== 0) {
                        if (item.balance > 0) {
                            balanceText = t('{{balance}} (D)', {
                                balance: balanceText
                            });
                        } else if (item.balance < 0) {
                            balanceText = t('{{balance}} (C)', {
                                balance: balanceText
                            });
                        }
                    }

                    itemsTable.table.body.push([
                        {
                            text: format(item.issueDate, 'date'),
                            style: 'itemText',
                            noWrap: true
                        },
                        {
                            text: format(item.dueDate, 'date'),
                            style: 'itemText',
                            noWrap: true
                        },
                        {text: item.documentNo, style: 'itemText', noWrap: true},
                        {text: item.description, style: 'itemText'},
                        {
                            text: _.isNumber(item.currencyRate) ? format(item.currencyRate, 'decimal') : '-',
                            style: 'itemNumber',
                            noWrap: true
                        },
                        ...(!hideInitialBalance
                            ? [
                                  {
                                      text: format(item.initialBalance, 'currency'),
                                      style: 'itemNumber',
                                      noWrap: true
                                  }
                              ]
                            : []),
                        {
                            text: format(item.debit, 'currency'),
                            style: 'itemNumber',
                            noWrap: true
                        },
                        {
                            text: format(item.credit, 'currency'),
                            style: 'itemNumber',
                            noWrap: true
                        },
                        {text: balanceText, style: 'itemNumber', noWrap: true}
                    ]);
                }

                // Prepare totals table.
                const totalsTable = {
                    table: {
                        headerRows: 0,
                        widths: ['auto', 'auto'],
                        body: []
                    },
                    layout: {
                        hLineWidth(i, node) {
                            return 0.5;
                        },
                        hLineColor(i, node) {
                            return '#bdc3c7';
                        }
                    },
                    width: 'auto'
                };
                const totals = {
                    initialBalance: 0,
                    debit: 0,
                    credit: 0,
                    balance: 0,
                    balanceText: ''
                };
                for (const item of items) {
                    totals.debit += item.debit || 0;
                    totals.credit += item.credit || 0;
                }
                if (items.length > 0) {
                    totals.initialBalance = items[items.length - 1].initialBalance || 0;
                    totals.balance = items[items.length - 1].balance || 0;

                    let balanceText = format(Math.abs(totals.balance), 'currency');
                    if (totals.balance !== 0) {
                        if (totals.debit > totals.credit) {
                            balanceText = t('{{balance}} (D)', {
                                balance: balanceText
                            });
                        } else if (totals.debit < totals.credit) {
                            balanceText = t('{{balance}} (C)', {
                                balance: balanceText
                            });
                        }
                    }
                    totals.balanceText = balanceText;
                }
                totalsTable.table.body.push([
                    {
                        text: t('Debit'),
                        noWrap: true,
                        border: [false, false, false, true],
                        margin: [0, 1, 5, 1],
                        fontSize: 7
                    },
                    {
                        text: format(totals.debit, 'currency'),
                        noWrap: true,
                        border: [false, false, false, true],
                        margin: [0, 1, 0, 1],
                        fontSize: 7,
                        alignment: 'right'
                    }
                ]);
                totalsTable.table.body.push([
                    {
                        text: t('Credit'),
                        noWrap: true,
                        border: [false, false, false, true],
                        margin: [0, 1, 5, 1],
                        fontSize: 7
                    },
                    {
                        text: format(totals.credit, 'currency'),
                        noWrap: true,
                        border: [false, false, false, true],
                        margin: [0, 1, 0, 1],
                        fontSize: 7,
                        alignment: 'right'
                    }
                ]);
                totalsTable.table.body.push([
                    {
                        text: t('Balance'),
                        noWrap: true,
                        border: [false, false, false, false],
                        margin: [0, 2, 5, 2],
                        fontSize: 8,
                        bold: true,
                        fillColor: '#fcfcfc'
                    },
                    {
                        text: totals.balanceText,
                        noWrap: true,
                        border: [false, false, false, false],
                        margin: [0, 2, 0, 2],
                        fontSize: 8,
                        bold: true,
                        alignment: 'right',
                        fillColor: '#fcfcfc'
                    }
                ]);

                // Prepare document.
                const document = {};
                document.title = t(documentTitle);
                document.landscape = horizontalPrint;
                document.definition = {
                    content: [
                        {
                            columns: [partnerData, {}, startDateData, endDateData],
                            marginBottom: 20
                        },
                        itemsTable,
                        {columns: [{}, totalsTable], unbreakable: true}
                    ],
                    styles: {
                        // Items Header
                        itemsHeader: {
                            margin: 1,
                            fontSize: 6,
                            bold: true
                        },

                        // Item.
                        itemNumber: {
                            fontSize: 6,
                            margin: 1,
                            alignment: 'right'
                        },
                        itemText: {
                            fontSize: 6,
                            margin: 1
                        },
                        itemTotal: {
                            fontSize: 6,
                            margin: 1,
                            bold: true,
                            alignment: 'right'
                        },

                        left: {
                            alignment: 'left'
                        }
                    }
                };

                return this.app.print(document);
            } else {
                const extension = 'xlsx';
                const file = `${Random.id(16)}.${extension}`;
                const progressId = `accounting.reports.partner-ledger-${params.user._id}`;

                // Start progress.
                app.progress({
                    id: progressId,
                    status: 'started'
                });

                // Prepare workbook.
                const workbook = new Excel.stream.xlsx.WorkbookWriter({
                    filename: path.join(app.config('paths.temp'), file),
                    useStyles: true
                });
                workbook.creator = 'EnterERP';
                workbook.lastModifiedBy = 'EnterERP';
                workbook.created = app.datetime.local().toJSDate();
                workbook.modified = app.datetime.local().toJSDate();
                workbook.lastPrinted = app.datetime.local().toJSDate();
                const worksheet = workbook.addWorksheet(t(documentTitle), {
                    views: [{state: 'frozen', xSplit: 0, ySplit: 1}]
                });

                if (!partnerId) {
                    const accountIds = await getPartnerAccounts(app, {excludeAdditionalAllowances});

                    // Prepare columns.
                    worksheet.columns = [
                        {
                            header: firstUpperAll(t(partnerCodeLabel)),
                            key: 'partnerCode',
                            width: 30,
                            style: {numFmt: '@'}
                        },
                        {
                            header: firstUpperAll(t(partnerNameLabel)),
                            key: 'partnerName',
                            width: 60,
                            style: {numFmt: '@'}
                        },
                        {
                            header: firstUpperAll(t('Salesperson')),
                            key: 'salesperson',
                            width: 60,
                            style: {numFmt: '@'}
                        },
                        {
                            header: firstUpperAll(t('Currency')),
                            key: 'currencyName',
                            width: 20,
                            style: {numFmt: '@'}
                        },
                        ...(!hideInitialBalance
                            ? [
                                  {
                                      header: firstUpperAll(t('Initial Balance')),
                                      key: 'initialBalance',
                                      width: 20,
                                      style: {numFmt: '#,##0.00'}
                                  }
                              ]
                            : []),
                        {
                            header: firstUpperAll(t('Debit')),
                            key: 'debit',
                            width: 20,
                            style: {numFmt: '#,##0.00'}
                        },
                        {
                            header: firstUpperAll(t('Credit')),
                            key: 'credit',
                            width: 20,
                            style: {numFmt: '#,##0.00'}
                        },
                        {
                            header: firstUpperAll(t('Balance')),
                            key: 'balance',
                            width: 20,
                            style: {numFmt: '#,##0.00'}
                        },
                        ...(groupByCurrency
                            ? [
                                  ...(!hideInitialBalance
                                      ? [
                                            {
                                                header: firstUpperAll(t('Initial balance (FC)')),
                                                key: 'initialBalanceFC',
                                                width: 20,
                                                style: {numFmt: '#,##0.00'}
                                            }
                                        ]
                                      : []),
                                  {
                                      header: firstUpperAll(t('Debit (FC)')),
                                      key: 'debitFC',
                                      width: 20,
                                      style: {numFmt: '#,##0.00'}
                                  },
                                  {
                                      header: firstUpperAll(t('Credit (FC)')),
                                      key: 'creditFC',
                                      width: 20,
                                      style: {numFmt: '#,##0.00'}
                                  },
                                  {
                                      header: firstUpperAll(t('Balance (FC)')),
                                      key: 'balanceFC',
                                      width: 20,
                                      style: {numFmt: '#,##0.00'}
                                  }
                              ]
                            : []),
                        {
                            header: firstUpperAll(t('Balance type')),
                            key: 'balanceType',
                            width: 15,
                            style: {numFmt: '@'}
                        }
                    ];

                    // Set header styles.
                    const headerRow = worksheet.getRow(1);
                    headerRow.font = {
                        color: {argb: 'FFFFFFFF'},
                        bold: true
                    };
                    headerRow.fill = {
                        type: 'pattern',
                        pattern: 'solid',
                        fgColor: {argb: 'FF0052CC'}
                    };

                    // Get query.
                    const query = {
                        partnerId: {$exists: true},
                        accountId: {$in: accountIds},
                        issueDate: {$gte: startDate, $lte: endDate},
                        ...(!!customCurrency ? {currencyId: customCurrency._id} : {}),
                        createdAt: {$lte: app.datetime.local().toJSDate()}
                    };
                    if (!!payload.partnerType) {
                        query['partner.type'] = payload.partnerType;
                    }
                    if (hideDebitRows) {
                        query.debit = {$not: {$gt: 0}};
                    }
                    if (hideCreditRows) {
                        query.credit = {$not: {$gt: 0}};
                    }
                    if (hideReconciledRows) {
                        query.reconciled = {$ne: true};
                    }
                    if (Array.isArray(excludedJournalTypes) && excludedJournalTypes.length > 0) {
                        query['journal.type'] = {$nin: excludedJournalTypes};
                    }
                    if (
                        !(user.isRoot && !!app.setting('system.rootsAuthorizedOnAllBranches')) &&
                        !!user &&
                        Array.isArray(user.branchIds)
                    ) {
                        if (!Array.isArray(query.$and)) query.$and = [];

                        query.$and.push({
                            branchId: {$in: user.branchIds}
                        });
                    }
                    if (!!payload.scope) {
                        query.scope = payload.scope;
                    }
                    if (payload.branchId) {
                        query.branchId = payload.branchId;
                    }

                    const partnerGroupPipeline = [
                        {
                            $addFields: {
                                partnerObjectId: {
                                    $convert: {
                                        input: '$partnerId',
                                        to: 'objectId',
                                        onError: '',
                                        onNull: ''
                                    }
                                }
                            }
                        },
                        {
                            $lookup: {
                                from: 'kernel_partners',
                                localField: 'partnerObjectId',
                                foreignField: '_id',
                                as: 'partnerInfo'
                            }
                        },
                        {
                            $unwind: '$partnerInfo'
                        },
                        {
                            $match: {
                                'partnerInfo.groupId': {$in: payload.partnerGroupIds}
                            }
                        }
                    ];

                    if (groupByCurrency) {
                        // Get initial balances.
                        const currentInitial = {};
                        let ir = await transactionsCollection.aggregate([
                            {
                                $match: {
                                    ...query,
                                    issueDate: {$lt: startDate}
                                }
                            },
                            ...(Array.isArray(payload.partnerGroupIds) && payload.partnerGroupIds.length > 0
                                ? partnerGroupPipeline
                                : []),
                            {
                                $group: {
                                    _id: {partnerId: '$partnerId', currencyId: '$currencyId'},
                                    debit: {$sum: '$debit'},
                                    credit: {$sum: '$credit'},
                                    debitFC: {$sum: '$debitFC'},
                                    creditFC: {$sum: '$creditFC'}
                                }
                            },
                            {
                                $project: {
                                    _id: 1,
                                    debit: 1,
                                    credit: 1,
                                    debitFC: 1,
                                    creditFC: 1,
                                    balance: {$subtract: ['$debit', '$credit']},
                                    balanceFC: {$subtract: ['$debitFC', '$creditFC']}
                                }
                            }
                        ]);
                        for (const item of ir) {
                            currentInitial[`${item._id.partnerId}|${item._id.currencyId}`] = {
                                balance: item.balance,
                                balanceFC: item.balanceFC
                            };
                        }

                        // Get pipeline.
                        const pipeline = [
                            {$match: query},
                            ...(Array.isArray(payload.partnerGroupIds) && payload.partnerGroupIds.length > 0
                                ? partnerGroupPipeline
                                : []),
                            {
                                $sort: {createdAt: 1}
                            },
                            {
                                $group: {
                                    _id: {partnerId: '$partnerId', currencyId: '$currencyId'},
                                    partner: {$first: '$partner'},
                                    currency: {$first: '$currency'},
                                    debit: {$sum: '$debit'},
                                    credit: {$sum: '$credit'},
                                    debitFC: {$sum: '$debitFC'},
                                    creditFC: {$sum: '$creditFC'}
                                }
                            },
                            {
                                $project: {
                                    partner: 1,
                                    currency: 1,
                                    debit: 1,
                                    credit: 1,
                                    debitFC: 1,
                                    creditFC: 1,
                                    balance: {$subtract: ['$debit', '$credit']},
                                    balanceFC: {$subtract: ['$debitFC', '$creditFC']}
                                }
                            },
                            {$sort: {'partner.code': 1}}
                        ];

                        // Get total.
                        let total = 0;
                        const countResult = await transactionsCollection.aggregate(
                            pipeline.concat([{$count: 'count'}])
                        );
                        if (countResult.length > 0) total = countResult[0].count;

                        // Get rows.
                        const limit = 100;
                        let page = 0;
                        let rowCount = 0;
                        do {
                            // Get report.
                            let result = (
                                await transactionsCollection.aggregate(
                                    pipeline.concat([{$skip: page * limit}, {$limit: limit}])
                                )
                            ).map(item => {
                                const initial = currentInitial[`${item._id.partnerId}|${item._id.currencyId}`] || {};

                                item.debit = item.debit || 0;
                                item.credit = item.credit || 0;
                                item.balance = item.balance || 0;

                                item.initialBalance = initial.balance || 0;
                                item.balance = item.initialBalance + (item.debit - item.credit);

                                if (item._id.currencyId !== company.currencyId) {
                                    item.debitFC = item.debitFC || 0;
                                    item.creditFC = item.creditFC || 0;
                                    item.balanceFC = item.balanceFC || 0;

                                    item.initialBalanceFC = initial.balanceFC || 0 || 0;
                                    item.balanceFC = item.initialBalanceFC + (item.debitFC - item.creditFC);
                                } else {
                                    delete item.debitFC;
                                    delete item.creditFC;
                                    delete item.balanceFC;
                                }

                                for (const key of Object.keys(item)) {
                                    if (_.isNumber(item[key]) && !_.isInteger(item[key])) {
                                        item[key] = app.roundNumber(item[key], 2);
                                    }
                                }

                                if (item.balance > 0) {
                                    item.balanceType = t('Debit');
                                } else if (item.balance < 0) {
                                    item.balanceType = t('Credit');
                                }

                                return item;
                            });

                            const partners = await app.collection('kernel.partners').find({
                                code: {$in: result.map(item => item.partner?.code)},
                                $select: ['_id', 'code', 'name', 'salespersonId'],
                                $disableActiveCheck: true,
                                $disableSoftDelete: true
                            });
                            const salespersonIds = [];
                            for (const partner of partners) {
                                if (!!partner.salespersonId && !salespersonIds.includes(partner.salespersonId)) {
                                    salespersonIds.push(partner.salespersonId);
                                }
                            }
                            if (salespersonIds.length > 0) {
                                const salespersons = await app.collection('kernel.partners').find({
                                    _id: {$in: salespersonIds},
                                    $select: ['_id', 'code', 'name'],
                                    $disableActiveCheck: true,
                                    $disableSoftDelete: true
                                });

                                result = result.map(item => {
                                    const partner = partners.find(partner => partner.code === item.partner?.code);

                                    if (!!partner && !!partner.salespersonId) {
                                        const salesperson = salespersons.find(
                                            salesperson => salesperson._id === partner.salespersonId
                                        );

                                        if (!!salesperson) {
                                            item.salesperson = salesperson.name;
                                        }
                                    }

                                    return item;
                                });
                            }

                            // Commit rows.
                            for (const item of result) {
                                let row = {};
                                row.partnerCode = item.partner?.code;
                                row.partnerName = item.partner?.name;
                                row.currencyName = item.currency.name;
                                row.salesperson = item.salesperson;
                                row.initialBalance = item.initialBalance;
                                row.debit = item.debit;
                                row.credit = item.credit;
                                row.balance = item.balance;
                                row.balanceType = item.balanceType;

                                row.initialBalanceFC = item.initialBalanceFC;
                                row.debitFC = item.debitFC;
                                row.creditFC = item.creditFC;
                                row.balanceFC = item.balanceFC;

                                if (hideZeroBalances) {
                                    if (item.partner && item.balance !== 0) {
                                        await worksheet.addRow(row).commit();
                                    }
                                } else {
                                    await worksheet.addRow(row).commit();
                                }
                                rowCount++;
                            }

                            // Send progress percentage.
                            if (total !== 0) {
                                const percentage = (rowCount / total) * 100;
                                app.progress({
                                    id: progressId,
                                    status: 'info',
                                    percentage
                                });
                            }

                            page++;
                        } while (total > 0 && rowCount < total);
                    } else {
                        // Get initial balances.
                        const currentInitial = {};
                        let ir = await transactionsCollection.aggregate([
                            {
                                $match: {
                                    ...query,
                                    issueDate: {$lt: startDate}
                                }
                            },
                            ...(Array.isArray(payload.partnerGroupIds) && payload.partnerGroupIds.length > 0
                                ? partnerGroupPipeline
                                : []),
                            {
                                $group: {
                                    _id: '$partnerId',
                                    debit: {$sum: '$debit'},
                                    credit: {$sum: '$credit'}
                                }
                            },
                            {
                                $project: {
                                    _id: 1,
                                    debit: 1,
                                    credit: 1,
                                    balance: {$subtract: ['$debit', '$credit']}
                                }
                            }
                        ]);
                        for (const item of ir) {
                            currentInitial[item._id] = item.balance;
                        }

                        // Get pipeline.
                        const pipeline = [
                            {$match: query},
                            ...(Array.isArray(payload.partnerGroupIds) && payload.partnerGroupIds.length > 0
                                ? partnerGroupPipeline
                                : []),
                            {
                                $sort: {createdAt: 1}
                            },
                            {
                                $group: {
                                    _id: '$partnerId',
                                    partner: {$first: '$partner'},
                                    debit: {$sum: '$debit'},
                                    credit: {$sum: '$credit'}
                                }
                            },
                            {
                                $project: {
                                    partner: 1,
                                    debit: 1,
                                    credit: 1,
                                    balance: {$subtract: ['$debit', '$credit']}
                                }
                            },
                            {$sort: {'partner.code': 1}}
                        ];

                        // Get total.
                        let total = 0;
                        const countResult = await transactionsCollection.aggregate(
                            pipeline.concat([{$count: 'count'}])
                        );
                        if (countResult.length > 0) total = countResult[0].count;

                        // Get rows.
                        const limit = 100;
                        let page = 0;
                        let rowCount = 0;
                        do {
                            // Get report.
                            let result = (
                                await transactionsCollection.aggregate(
                                    pipeline.concat([{$skip: page * limit}, {$limit: limit}])
                                )
                            ).map(item => {
                                const initial = currentInitial[item._id] || 0;

                                item.debit = item.debit || 0;
                                item.credit = item.credit || 0;
                                item.balance = item.balance || 0;

                                item.initialBalance = initial || 0;
                                item.balance = item.initialBalance + (item.debit - item.credit);

                                for (const key of Object.keys(item)) {
                                    if (_.isNumber(item[key]) && !_.isInteger(item[key])) {
                                        item[key] = app.roundNumber(item[key], 2);
                                    }
                                }

                                if (item.balance > 0) {
                                    item.balanceType = t('Debit');
                                } else if (item.balance < 0) {
                                    item.balanceType = t('Credit');
                                }

                                return item;
                            });

                            const partners = await app.collection('kernel.partners').find({
                                code: {$in: result.map(item => item.partner?.code)},
                                $select: ['_id', 'code', 'name', 'salespersonId'],
                                $disableActiveCheck: true,
                                $disableSoftDelete: true
                            });
                            const salespersonIds = [];
                            for (const partner of partners) {
                                if (!!partner.salespersonId && !salespersonIds.includes(partner.salespersonId)) {
                                    salespersonIds.push(partner.salespersonId);
                                }
                            }
                            if (salespersonIds.length > 0) {
                                const salespersons = await app.collection('kernel.partners').find({
                                    _id: {$in: salespersonIds},
                                    $select: ['_id', 'code', 'name'],
                                    $disableActiveCheck: true,
                                    $disableSoftDelete: true
                                });

                                result = result.map(item => {
                                    const partner = partners.find(partner => partner.code === item.partner?.code);

                                    if (!!partner && !!partner.salespersonId) {
                                        const salesperson = salespersons.find(
                                            salesperson => salesperson._id === partner.salespersonId
                                        );

                                        if (!!salesperson) {
                                            item.salesperson = salesperson.name;
                                        }
                                    }

                                    return item;
                                });
                            }

                            // Commit rows.
                            for (const item of result) {
                                let row = {};
                                row.partnerCode = item.partner?.code;
                                row.partnerName = item.partner?.name;
                                row.currencyName = company.currency.name;
                                row.salesperson = item.salesperson;
                                row.initialBalance = item.initialBalance;
                                row.debit = item.debit;
                                row.credit = item.credit;
                                row.balance = item.balance;
                                row.balanceType = item.balanceType;

                                if (hideZeroBalances) {
                                    if (item.partner && item.balance !== 0) {
                                        await worksheet.addRow(row).commit();
                                    }
                                } else {
                                    await worksheet.addRow(row).commit();
                                }
                                rowCount++;
                            }

                            // Send progress percentage.
                            if (total !== 0) {
                                const percentage = (rowCount / total) * 100;
                                app.progress({
                                    id: progressId,
                                    status: 'info',
                                    percentage
                                });
                            }

                            page++;
                        } while (total > 0 && rowCount < total);
                    }

                    // Finished adding data. Commit the worksheet
                    await worksheet.commit();

                    // Finished the workbook.
                    await workbook.commit();

                    // Finalize progress.
                    app.progress({
                        id: progressId,
                        status: 'success',
                        percentage: 100
                    });

                    return {file, name: `${t(documentTitle)}.${extension}`};
                } else {
                    // Prepare columns.
                    const columns = [];
                    for (const c of payload.columns) {
                        const column = {};

                        column.header = t(c.label)
                            .split(' ')
                            .map(str => firstUpper(str))
                            .join(' ');
                        column.key = c.field;

                        if (_.isNumber(c.width)) {
                            column.width = c.width;
                        }

                        columns.push(column);
                    }
                    worksheet.columns = columns.map(c => {
                        if (
                            c.key !== 'issueDate' &&
                            c.key !== 'dueDate' &&
                            c.key !== 'balance' &&
                            c.key !== 'initialBalance' &&
                            c.key !== 'debit' &&
                            c.key !== 'credit' &&
                            c.key !== 'balanceFC' &&
                            c.key !== 'initialBalanceFC' &&
                            c.key !== 'debitFC' &&
                            c.key !== 'creditFC'
                        ) {
                            c.style = {numFmt: '@'};
                        }

                        if (
                            c.key === 'balance' &&
                            c.key === 'initialBalance' &&
                            c.key === 'debit' &&
                            c.key === 'credit' &&
                            c.key === 'balanceFC' &&
                            c.key === 'initialBalanceFC' &&
                            c.key === 'debitFC' &&
                            c.key === 'creditFC'
                        ) {
                            c.style = {numFmt: '#,##0.00'};
                        }

                        return c;
                    });

                    // Get fields.
                    const fields = payload.columns.map(column => column.field);
                    const deepFields = fields.filter(field => field.indexOf('.') !== -1);

                    // Set header styles.
                    const headerRow = worksheet.getRow(1);
                    headerRow.font = {
                        color: {argb: 'FFFFFFFF'},
                        bold: true
                    };
                    headerRow.fill = {
                        type: 'pattern',
                        pattern: 'solid',
                        fgColor: {argb: 'FF0052CC'}
                    };

                    // Prepare query.
                    const accountIds = await getPartnerAccounts(app, {excludeAdditionalAllowances});
                    const query = {
                        partnerId,
                        accountId: {$in: accountIds},
                        ...(!!customCurrency ? {currencyId: customCurrency._id} : {}),
                        createdAt: {$lte: app.datetime.local().toJSDate()}
                    };
                    if (!!includeConnectedPartners) {
                        query.partnerId = {
                            $in: [partnerId].concat(connectedPartnerIds)
                        };

                        const connectedPartners = await app.collection('kernel.partners').find({
                            _id: {$in: connectedPartnerIds},
                            $select: ['accountingAccountId', 'additionalAllowanceAccount']
                        });
                        let connectedAccountIds = [...accountIds];
                        for (const connectedPartner of connectedPartners) {
                            connectedAccountIds.push(connectedPartner.accountingAccountId);

                            if (!!connectedPartner.additionalAllowanceAccount) {
                                accountIds.push(connectedPartner.additionalAllowanceAccount);
                            }
                        }
                        connectedAccountIds = _.uniq(connectedAccountIds);
                        query.accountId = {$in: connectedAccountIds};
                    }
                    if (hideDebitRows) {
                        query.debit = {$not: {$gt: 0}};
                    }
                    if (hideCreditRows) {
                        query.credit = {$not: {$gt: 0}};
                    }
                    if (hideReconciledRows) {
                        query.reconciled = {$ne: true};
                    }
                    if (Array.isArray(excludedJournalTypes) && excludedJournalTypes.length > 0) {
                        query['journal.type'] = {$nin: excludedJournalTypes};
                    }
                    if (!!payload.scope) {
                        query.scope = payload.scope;
                    }
                    if (payload.branchId) {
                        query.branchId = payload.branchId;
                    }

                    // Find initial balance.
                    let initialBalance = 0;
                    const ir = await transactionsCollection.aggregate([
                        {
                            $match: {
                                ...query,
                                issueDate: {$lt: startDate}
                            }
                        },
                        {
                            $group: {
                                _id: null,
                                debit: {$sum: '$debit'},
                                credit: {$sum: '$credit'}
                            }
                        },
                        {
                            $project: {
                                _id: 1,
                                debit: 1,
                                credit: 1,
                                balance: {$subtract: ['$debit', '$credit']}
                            }
                        }
                    ]);
                    if (ir.length > 0) {
                        initialBalance = ir[0].balance;
                    }

                    const fcInitialBalancesMap = {};
                    const cir = await transactionsCollection.aggregate([
                        {
                            $match: {
                                ...query,
                                issueDate: {$lt: startDate},
                                currencyId: {$ne: company.currencyId}
                            }
                        },
                        {
                            $group: {
                                _id: 'currencyId',
                                debitFC: {$sum: '$debitFC'},
                                creditFC: {$sum: '$creditFC'}
                            }
                        },
                        {
                            $project: {
                                _id: 1,
                                debitFC: 1,
                                creditFC: 1,
                                balanceFC: {$subtract: ['$debitFC', '$creditFC']}
                            }
                        }
                    ]);
                    for (const r of cir) {
                        fcInitialBalancesMap[r._id] = r.balanceFC;
                    }

                    // Get items.
                    const items = (
                        await transactionsCollection.find({
                            ...query,
                            issueDate: {$gte: startDate, $lte: endDate},
                            $sort: {issueDate: 1, _id: 1}
                        })
                    ).map(item => {
                        item.initialBalance = initialBalance;
                        item.balance = item.initialBalance + (item.debit - item.credit);

                        initialBalance = item.balance;

                        if (item.currencyId !== company.currencyId) {
                            item.initialBalanceFC = fcInitialBalancesMap[item.currencyId] || 0;
                            item.balanceFC = item.initialBalanceFC + ((item.debitFC || 0) - (item.creditFC || 0));

                            if (item.debitFC > 0) {
                                item.currencyRate = item.debit / item.debitFC;
                            } else if (item.creditFC > 0) {
                                item.currencyRate = item.credit / item.creditFC;
                            }

                            fcInitialBalancesMap[item.currencyId] = item.balanceFC;
                        } else {
                            delete item.debitFC;
                            delete item.creditFC;
                            delete item.balanceFC;
                        }

                        if (item.debit > 0) {
                            delete item.credit;
                        }
                        if (item.credit > 0) {
                            delete item.debit;
                        }
                        if (item.debitFC > 0) {
                            delete item.creditFC;
                        }
                        if (item.creditFC > 0) {
                            delete item.debitFC;
                        }

                        for (const key of Object.keys(item)) {
                            if (_.isNumber(item[key]) && !_.isInteger(item[key])) {
                                item[key] = app.roundNumber(item[key], 2);
                            }
                        }

                        return item;
                    });

                    // Get rows.
                    for (let row of items) {
                        // Fix dates.
                        row = fixDates(row);

                        for (const field of deepFields) {
                            if (_.isUndefined(row[field])) {
                                row[field] = _.get(row, field);
                            }
                        }

                        await worksheet.addRow(row).commit();
                    }

                    // Finished adding data. Commit the worksheet
                    await worksheet.commit();

                    // Finished the workbook.
                    await workbook.commit();

                    // Finalize progress.
                    app.progress({
                        id: progressId,
                        status: 'success',
                        percentage: 100
                    });

                    return {file, name: `${t(documentTitle)}.${extension}`};
                }
            }
        }
    },

    {
        name: 'partner-ledger-export-grouped',
        async action(payload, params) {
            const app = this.app;
            const t = app.translate;
            const company = await app.collection('kernel.company').findOne({});
            const transactionsCollection = app.collection('accounting.transactions');
            const partnerId = payload.partnerId;
            const includeConnectedPartners = payload.includeConnectedPartners;
            const connectedPartnerIds = payload.connectedPartnerIds;
            const startDate = payload.startDate;
            const endDate = payload.endDate;
            const hideInitialBalance = payload.hideInitialBalance;
            const hideDebitRows = payload.hideDebitRows;
            const hideZeroBalances = payload.hideZeroBalances;
            const hideCreditRows = payload.hideCreditRows;
            const hideReconciledRows = payload.hideReconciledRows;
            const excludeAdditionalAllowances = !!payload.excludeAdditionalAllowances;
            const excludedJournalTypes = payload.excludedJournalTypes ?? [];
            const type = payload.type;
            const horizontalPrint = !!payload.horizontalPrint;

            // Get document title.
            let documentTitle = 'Partner Ledger';
            if (!!type) {
                if (type === 'customer') {
                    documentTitle = 'Customer Ledger';
                } else if (type === 'vendor') {
                    documentTitle = 'Vendor Ledger';
                } else if (type === 'employee') {
                    documentTitle = 'Employee Ledger';
                }
            }

            // Get partner label.
            let partnerLabel = 'Partner';
            if (!!type) {
                if (type === 'customer') {
                    partnerLabel = 'Customer';
                } else if (type === 'vendor') {
                    partnerLabel = 'Vendor';
                } else if (type === 'employee') {
                    partnerLabel = 'Employee';
                }
            }

            // Get partner code and name label.
            let partnerCodeLabel = 'Partner code';
            let partnerNameLabel = 'Partner name';
            if (!!type) {
                if (type === 'customer') {
                    partnerCodeLabel = 'Customer code';
                    partnerNameLabel = 'Customer name';
                } else if (type === 'vendor') {
                    partnerCodeLabel = 'Vendor code';
                    partnerNameLabel = 'Vendor name';
                } else if (type === 'employee') {
                    partnerCodeLabel = 'Employee code';
                    partnerNameLabel = 'Employee name';
                }
            }

            const partner = await app.collection('kernel.partners').findOne({
                _id: partnerId,
                $select: [
                    'isCompany',
                    'name',
                    'legalName',
                    'tin',
                    'identity',
                    'accountingAccountId',
                    'additionalAllowanceAccount',
                    'employeeExpenseAccountId'
                ]
            });
            const partnerData = {
                stack: [
                    {
                        text: t(partnerLabel),
                        fontSize: 9,
                        bold: true,
                        color: '#0052cc',
                        margin: [0, 0, 0, 4]
                    },
                    {
                        text: partner.isCompany && !!partner.legalName ? partner.legalName : partner.name,
                        fontSize: 9
                    },
                    ...(partner.isCompany && !!partner.tin
                        ? [
                              {
                                  columns: [
                                      {
                                          text: t('TN:'),
                                          fontSize: 9,
                                          bold: true,
                                          width: 'auto',
                                          margin: [0, 10, 3, 0]
                                      },
                                      {
                                          text: partner.tin,
                                          fontSize: 9,
                                          marginTop: 10
                                      },
                                      {}
                                  ]
                              }
                          ]
                        : []),
                    ...(!partner.isCompany && !!partner.identity
                        ? [
                              {
                                  columns: [
                                      {
                                          text: t('IN:'),
                                          fontSize: 9,
                                          bold: true,
                                          width: 'auto',
                                          margin: [0, 10, 3, 0]
                                      },
                                      {
                                          text: partner.identity,
                                          fontSize: 9,
                                          marginTop: 10
                                      },
                                      {}
                                  ]
                              }
                          ]
                        : [])
                ],
                width: 320
            };
            const startDateData = {
                stack: [
                    {
                        text: t('Start Date'),
                        fontSize: 9,
                        bold: true,
                        color: '#0052cc',
                        margin: [0, 0, 0, 4]
                    },
                    {
                        text: app.format(startDate, 'date'),
                        fontSize: 9
                    },
                    {
                        text: app.format(startDate, 'time'),
                        fontSize: 9
                    }
                ],
                width: 80
            };
            const endDateData = {
                stack: [
                    {
                        text: t('End Date'),
                        fontSize: 9,
                        bold: true,
                        color: '#0052cc',
                        margin: [0, 0, 0, 4]
                    },
                    {
                        text: app.format(endDate, 'date'),
                        fontSize: 9
                    },
                    {
                        text: app.format(endDate, 'time'),
                        fontSize: 9
                    }
                ],
                width: 80
            };

            // Prepare document.
            const document = {};
            document.title = t(documentTitle);
            document.landscape = horizontalPrint;
            document.definition = {
                content: [
                    {
                        columns: [partnerData, {}, startDateData, endDateData],
                        marginBottom: 20
                    }
                ],
                styles: {
                    // Items Header
                    itemsHeader: {
                        margin: 1,
                        fontSize: 6,
                        bold: true
                    },

                    // Item.
                    itemNumber: {
                        fontSize: 6,
                        margin: 1,
                        alignment: 'right'
                    },
                    itemText: {
                        fontSize: 6,
                        margin: 1
                    },
                    itemTotal: {
                        fontSize: 6,
                        margin: 1,
                        bold: true,
                        alignment: 'right'
                    },

                    left: {
                        alignment: 'left'
                    }
                }
            };

            // Get currencies.
            const currencies = await app.collection('kernel.currencies').find({
                $disableActiveCheck: true,
                $disableSoftDelete: true
            });
            let currencyIndex = 0;

            for (const currency of currencies) {
                currencyIndex++;

                const isForeignCurrency = currency._id !== company.currencyId;
                const formatOptions = {
                    ...payload.formatOptions,
                    currency: {
                        ...(payload.formatOptions.currency || {}),
                        symbol: currency.symbol,
                        format: currency.symbolPosition === 'before' ? '%s %v' : '%v %s'
                    }
                };
                const format = (value, format) => app.format(value, format, formatOptions);

                // Prepare items table.
                let itemsTable;
                if (isForeignCurrency) {
                    itemsTable = {
                        table: {
                            headerRows: 2,
                            dontBreakRows: true,
                            widths: !!hideInitialBalance
                                ? ['auto', 'auto', 'auto', '*', 'auto', 'auto', 'auto', 'auto', 'auto', 'auto', 'auto']
                                : [
                                      'auto',
                                      'auto',
                                      'auto',
                                      '*',
                                      'auto',
                                      'auto',
                                      'auto',
                                      'auto',
                                      'auto',
                                      'auto',
                                      'auto',
                                      'auto',
                                      'auto'
                                  ],
                            body: [
                                [
                                    {
                                        text: currency.name,
                                        style: 'itemsHeader',
                                        border: [true, true, false, true],
                                        noWrap: true
                                    },
                                    {
                                        text: '',
                                        style: 'itemsHeader',
                                        border: [false, true, false, true],
                                        noWrap: true
                                    },
                                    {
                                        text: '',
                                        style: 'itemsHeader',
                                        border: [false, true, false, true],
                                        noWrap: true
                                    },
                                    {
                                        text: '',
                                        style: 'itemsHeader',
                                        border: [false, true, false, true],
                                        noWrap: true
                                    },
                                    {
                                        text: '',
                                        style: 'itemsHeader',
                                        border: [false, true, false, true],
                                        noWrap: true
                                    },
                                    {
                                        text: '',
                                        style: 'itemsHeader',
                                        border: [false, true, false, true],
                                        noWrap: true
                                    },
                                    {
                                        text: '',
                                        style: 'itemsHeader',
                                        border: [false, true, false, true],
                                        noWrap: true
                                    },
                                    {
                                        text: '',
                                        style: 'itemsHeader',
                                        border: [false, true, false, true],
                                        noWrap: true
                                    },
                                    {
                                        text: '',
                                        style: 'itemsHeader',
                                        border: [false, true, false, true],
                                        noWrap: true
                                    },
                                    {
                                        text: '',
                                        style: 'itemsHeader',
                                        border: [false, true, false, true],
                                        noWrap: true
                                    },
                                    ...(!hideInitialBalance
                                        ? [
                                              {
                                                  text: '',
                                                  style: 'itemsHeader',
                                                  border: [false, true, false, true],
                                                  noWrap: true
                                              },
                                              {
                                                  text: '',
                                                  style: 'itemsHeader',
                                                  border: [false, true, false, true],
                                                  noWrap: true
                                              }
                                          ]
                                        : []),
                                    {
                                        text: '',
                                        style: 'itemsHeader',
                                        border: [false, true, true, true],
                                        noWrap: true
                                    }
                                ],
                                [
                                    {
                                        text: firstUpperAll(t('Issue date')),
                                        style: 'itemsHeader',
                                        border: [true, true, true, true],
                                        noWrap: true
                                    },
                                    {
                                        text: firstUpperAll(t('Due date')),
                                        style: 'itemsHeader',
                                        border: [false, true, true, true],
                                        noWrap: true
                                    },
                                    {
                                        text: firstUpperAll(t('Document no')),
                                        style: 'itemsHeader',
                                        border: [false, true, true, true],
                                        noWrap: true
                                    },
                                    {
                                        text: firstUpperAll(t('Description')),
                                        style: 'itemsHeader',
                                        border: [false, true, true, true],
                                        noWrap: true
                                    },
                                    {
                                        text: firstUpperAll(t('C. Rate')),
                                        style: 'itemsHeader',
                                        border: [false, true, true, true],
                                        noWrap: true
                                    },

                                    ...(!hideInitialBalance
                                        ? [
                                              {
                                                  text: firstUpperAll(t('I. Balance (SC)')),
                                                  style: 'itemsHeader',
                                                  border: [false, true, true, true],
                                                  noWrap: true
                                              }
                                          ]
                                        : []),
                                    {
                                        text: firstUpperAll(t('Debit (SC)')),
                                        style: 'itemsHeader',
                                        border: [false, true, true, true],
                                        noWrap: true
                                    },
                                    {
                                        text: firstUpperAll(t('Credit (SC)')),
                                        style: 'itemsHeader',
                                        border: [false, true, true, true],
                                        noWrap: true
                                    },
                                    {
                                        text: firstUpperAll(t('Balance (SC)')),
                                        style: 'itemsHeader',
                                        border: [false, true, true, true],
                                        noWrap: true
                                    },

                                    ...(!hideInitialBalance
                                        ? [
                                              {
                                                  text: firstUpperAll(t('I. Balance')),
                                                  style: 'itemsHeader',
                                                  border: [false, true, true, true],
                                                  noWrap: true
                                              }
                                          ]
                                        : []),
                                    {
                                        text: firstUpperAll(t('Debit')),
                                        style: 'itemsHeader',
                                        border: [false, true, true, true],
                                        noWrap: true
                                    },
                                    {
                                        text: firstUpperAll(t('Credit')),
                                        style: 'itemsHeader',
                                        border: [false, true, true, true],
                                        noWrap: true
                                    },
                                    {
                                        text: firstUpperAll(t('Balance')),
                                        style: 'itemsHeader',
                                        border: [false, true, true, true],
                                        noWrap: true
                                    }
                                ]
                            ]
                        },
                        layout: {
                            hLineWidth(i, node) {
                                return i === 1 ? 1 : 0.5;
                            },
                            vLineWidth(i, node) {
                                return 0.5;
                            },
                            hLineColor(i, node) {
                                return '#bdc3c7';
                            },
                            vLineColor(i, node) {
                                return '#bdc3c7';
                            },
                            fillColor(rowIndex, node, columnIndex) {
                                return rowIndex % 2 === 1 ? '#fcfcfc' : null;
                            }
                        },
                        marginBottom: 0
                    };
                } else {
                    itemsTable = {
                        table: {
                            headerRows: 2,
                            dontBreakRows: true,
                            widths: !!hideInitialBalance
                                ? ['auto', 'auto', 'auto', '*', 'auto', 'auto', 'auto']
                                : ['auto', 'auto', 'auto', '*', 'auto', 'auto', 'auto', 'auto'],
                            body: [
                                [
                                    {
                                        text: currency.name,
                                        style: 'itemsHeader',
                                        border: [true, true, false, true],
                                        noWrap: true
                                    },
                                    {
                                        text: '',
                                        style: 'itemsHeader',
                                        border: [false, true, false, true],
                                        noWrap: true
                                    },
                                    {
                                        text: '',
                                        style: 'itemsHeader',
                                        border: [false, true, false, true],
                                        noWrap: true
                                    },
                                    {
                                        text: '',
                                        style: 'itemsHeader',
                                        border: [false, true, false, true],
                                        noWrap: true
                                    },
                                    {
                                        text: '',
                                        style: 'itemsHeader',
                                        border: [false, true, false, true],
                                        noWrap: true
                                    },
                                    {
                                        text: '',
                                        style: 'itemsHeader',
                                        border: [false, true, false, true],
                                        noWrap: true
                                    },
                                    ...(!hideInitialBalance
                                        ? [
                                              {
                                                  text: '',
                                                  style: 'itemsHeader',
                                                  border: [false, true, false, true],
                                                  noWrap: true
                                              }
                                          ]
                                        : []),
                                    {
                                        text: '',
                                        style: 'itemsHeader',
                                        border: [false, true, true, true],
                                        noWrap: true
                                    }
                                ],
                                [
                                    {
                                        text: firstUpperAll(t('Issue date')),
                                        style: 'itemsHeader',
                                        border: [true, true, true, true],
                                        noWrap: true
                                    },
                                    {
                                        text: firstUpperAll(t('Due date')),
                                        style: 'itemsHeader',
                                        border: [false, true, true, true],
                                        noWrap: true
                                    },
                                    {
                                        text: firstUpperAll(t('Document no')),
                                        style: 'itemsHeader',
                                        border: [false, true, true, true],
                                        noWrap: true
                                    },
                                    {
                                        text: firstUpperAll(t('Description')),
                                        style: 'itemsHeader',
                                        border: [false, true, true, true],
                                        noWrap: true
                                    },
                                    ...(!hideInitialBalance
                                        ? [
                                              {
                                                  text: firstUpperAll(t('I. Balance')),
                                                  style: 'itemsHeader',
                                                  border: [false, true, true, true],
                                                  noWrap: true
                                              }
                                          ]
                                        : []),
                                    {
                                        text: firstUpperAll(t('Debit')),
                                        style: 'itemsHeader',
                                        border: [false, true, true, true],
                                        noWrap: true
                                    },
                                    {
                                        text: firstUpperAll(t('Credit')),
                                        style: 'itemsHeader',
                                        border: [false, true, true, true],
                                        noWrap: true
                                    },
                                    {
                                        text: firstUpperAll(t('Balance')),
                                        style: 'itemsHeader',
                                        border: [false, true, true, true],
                                        noWrap: true
                                    }
                                ]
                            ]
                        },
                        layout: {
                            hLineWidth(i, node) {
                                return i === 1 ? 1 : 0.5;
                            },
                            vLineWidth(i, node) {
                                return 0.5;
                            },
                            hLineColor(i, node) {
                                return '#bdc3c7';
                            },
                            vLineColor(i, node) {
                                return '#bdc3c7';
                            },
                            fillColor(rowIndex, node, columnIndex) {
                                return rowIndex % 2 === 1 ? '#fcfcfc' : null;
                            }
                        },
                        marginBottom: 0
                    };
                }

                // Prepare query.
                // const accountIds = await getPartnerAccounts(app, {excludeAdditionalAllowances});
                let accountIds = [];
                if (!!partner.accountingAccountId) {
                    accountIds.push(partner.accountingAccountId);
                }
                if (!!partner.additionalAllowanceAccount) {
                    accountIds.push(partner.additionalAllowanceAccount);
                }
                if (!!partner.employeeExpenseAccountId) {
                    accountIds.push(partner.employeeExpenseAccountId);
                }
                const query = {
                    partnerId,
                    accountId: {$in: accountIds},
                    currencyId: currency._id
                };
                if (!!includeConnectedPartners) {
                    query.partnerId = {
                        $in: [partnerId].concat(connectedPartnerIds)
                    };

                    const connectedPartners = await app.collection('kernel.partners').find({
                        _id: {$in: connectedPartnerIds},
                        $select: ['accountingAccountId', 'additionalAllowanceAccount']
                    });
                    let connectedAccountIds = [...accountIds];
                    for (const connectedPartner of connectedPartners) {
                        connectedAccountIds.push(connectedPartner.accountingAccountId);

                        if (!!connectedPartner.additionalAllowanceAccount) {
                            connectedAccountIds.push(connectedPartner.additionalAllowanceAccount);
                        }
                    }
                    connectedAccountIds = _.uniq(connectedAccountIds);
                    query.accountId = {$in: connectedAccountIds};
                }
                if (hideDebitRows) {
                    query.debit = {$not: {$gt: 0}};
                }
                if (hideCreditRows) {
                    query.credit = {$not: {$gt: 0}};
                }
                if (hideReconciledRows) {
                    query.reconciled = {$ne: true};
                }
                if (Array.isArray(excludedJournalTypes) && excludedJournalTypes.length > 0) {
                    query['journal.type'] = {$nin: excludedJournalTypes};
                }
                if (!!payload.scope) {
                    query.scope = payload.scope;
                }
                if (payload.branchId) {
                    query.branchId = payload.branchId;
                }

                // Find initial balance.
                let initialBalance = 0;
                let initialBalanceSC = 0;
                let ir = await transactionsCollection.aggregate([
                    {
                        $match: {
                            ...query,
                            issueDate: {$lt: startDate}
                        }
                    },
                    {
                        $group: {
                            _id: null,
                            debit: {$sum: '$debit'},
                            credit: {$sum: '$credit'},
                            debitFC: {$sum: '$debitFC'},
                            creditFC: {$sum: '$creditFC'}
                        }
                    },
                    {
                        $project: {
                            _id: 1,
                            debit: 1,
                            credit: 1,
                            debitFC: 1,
                            creditFC: 1,
                            balance: {$subtract: ['$debit', '$credit']},
                            balanceFC: {$subtract: ['$debitFC', '$creditFC']}
                        }
                    }
                ]);
                // if (ir.length < 1) continue;
                if (currency._id !== company.currencyId) {
                    ir = ir.map(r => {
                        r.balanceSC = r.balance || 0;
                        r.debit = r.debitFC;
                        r.credit = r.creditFC;
                        r.balance = r.balanceFC;
                        delete r.debitFC;
                        delete r.creditFC;
                        delete r.balanceFC;

                        return r;
                    });
                }
                if (ir.length > 0) {
                    initialBalance = ir[0].balance || 0;
                    initialBalanceSC = ir[0].balanceSC || 0;
                }

                // Get items.
                const items = (
                    await transactionsCollection.find({
                        ...query,
                        issueDate: {$gte: startDate, $lte: endDate},
                        $sort: {issueDate: 1, _id: 1}
                    })
                ).map(item => {
                    item.debitSC = item.debit || 0;
                    item.creditSC = item.credit || 0;
                    item.balanceSC = item.balance || 0;

                    if (currency._id !== company.currencyId) {
                        if (item.debitFC > 0) {
                            item.currencyRate = item.debit / item.debitFC;
                        } else if (item.creditFC > 0) {
                            item.currencyRate = item.credit / item.creditFC;
                        }

                        item.debit = item.debitFC;
                        item.credit = item.creditFC;
                        item.balance = item.balanceFC;
                        delete item.debitFC;
                        delete item.creditFC;
                        delete item.balanceFC;
                    }

                    item.debit = item.debit || 0;
                    item.credit = item.credit || 0;
                    item.balance = item.balance || 0;

                    item.initialBalance = initialBalance;
                    item.initialBalanceSC = initialBalanceSC;
                    item.balanceSC = app.round(item.initialBalanceSC + (item.debitSC - item.creditSC), 'currency');
                    item.balance = app.round(item.initialBalance + (item.debit - item.credit), 'currency');

                    initialBalanceSC = item.balanceSC;
                    initialBalance = item.balance;

                    return item;
                });
                if (items.length < 1) continue;

                // Prepare items table rows.
                for (const item of items) {
                    if (hideZeroBalances && item.balance === 0) {
                        continue;
                    }

                    // Get balance text.
                    let balanceText = format(Math.abs(item.balance), 'currency');
                    if (item.balance !== 0) {
                        if (item.balance > 0) {
                            balanceText = t('{{balance}} (D)', {
                                balance: balanceText
                            });
                        } else if (item.balance < 0) {
                            balanceText = t('{{balance}} (C)', {
                                balance: balanceText
                            });
                        }
                    }
                    let balanceTextSC = '';
                    if (_.isNumber(item.balanceSC)) {
                        balanceTextSC = app.format(Math.abs(item.balanceSC), 'currency');

                        if (item.balanceSC !== 0) {
                            if (item.balanceSC > 0) {
                                balanceTextSC = t('{{balance}} (D)', {
                                    balance: balanceTextSC
                                });
                            } else if (item.balance < 0) {
                                balanceTextSC = t('{{balance}} (C)', {
                                    balance: balanceTextSC
                                });
                            }
                        }
                    }

                    if (isForeignCurrency) {
                        itemsTable.table.body.push([
                            {
                                text: format(item.issueDate, 'date'),
                                style: 'itemText',
                                noWrap: true
                            },
                            {
                                text: format(item.dueDate, 'date'),
                                style: 'itemText',
                                noWrap: true
                            },
                            {text: item.documentNo, style: 'itemText', noWrap: true},
                            {text: item.description, style: 'itemText'},
                            {
                                text: _.isNumber(item.currencyRate) ? format(item.currencyRate, 'decimal') : '-',
                                style: 'itemNumber',
                                noWrap: true
                            },

                            ...(!hideInitialBalance
                                ? [
                                      {
                                          text: app.format(item.initialBalanceSC, 'currency'),
                                          style: 'itemNumber',
                                          noWrap: true
                                      }
                                  ]
                                : []),
                            {
                                text: app.format(item.debitSC, 'currency'),
                                style: 'itemNumber',
                                noWrap: true
                            },
                            {
                                text: app.format(item.creditSC, 'currency'),
                                style: 'itemNumber',
                                noWrap: true
                            },
                            {text: balanceTextSC, style: 'itemNumber', noWrap: true},

                            ...(!hideInitialBalance
                                ? [
                                      {
                                          text: format(item.initialBalance, 'currency'),
                                          style: 'itemNumber',
                                          noWrap: true
                                      }
                                  ]
                                : []),
                            {
                                text: format(item.debit, 'currency'),
                                style: 'itemNumber',
                                noWrap: true
                            },
                            {
                                text: format(item.credit, 'currency'),
                                style: 'itemNumber',
                                noWrap: true
                            },
                            {text: balanceText, style: 'itemNumber', noWrap: true}
                        ]);
                    } else {
                        itemsTable.table.body.push([
                            {
                                text: format(item.issueDate, 'date'),
                                style: 'itemText',
                                noWrap: true
                            },
                            {
                                text: format(item.dueDate, 'date'),
                                style: 'itemText',
                                noWrap: true
                            },
                            {text: item.documentNo, style: 'itemText', noWrap: true},
                            {text: item.description, style: 'itemText'},
                            ...(!hideInitialBalance
                                ? [
                                      {
                                          text: format(item.initialBalance, 'currency'),
                                          style: 'itemNumber',
                                          noWrap: true
                                      }
                                  ]
                                : []),
                            {
                                text: format(item.debit, 'currency'),
                                style: 'itemNumber',
                                noWrap: true
                            },
                            {
                                text: format(item.credit, 'currency'),
                                style: 'itemNumber',
                                noWrap: true
                            },
                            {text: balanceText, style: 'itemNumber', noWrap: true}
                        ]);
                    }
                }

                // Prepare totals table.
                const totalsTable = {
                    table: {
                        headerRows: 0,
                        widths: ['auto', 'auto'],
                        body: []
                    },
                    layout: {
                        hLineWidth(i, node) {
                            return 0.5;
                        },
                        hLineColor(i, node) {
                            return '#bdc3c7';
                        }
                    },
                    marginBottom: currencyIndex === currencies.length ? 0 : 30,
                    width: 'auto'
                };
                const totalsSCTable = {
                    table: {
                        headerRows: 0,
                        widths: ['auto', 'auto'],
                        body: []
                    },
                    layout: {
                        hLineWidth(i, node) {
                            return 0.5;
                        },
                        hLineColor(i, node) {
                            return '#bdc3c7';
                        }
                    },
                    marginRight: 30,
                    marginBottom: currencyIndex === currencies.length ? 0 : 30,
                    width: 'auto'
                };
                const totals = {
                    initialBalance: 0,
                    initialBalanceSC: 0,
                    debit: 0,
                    credit: 0,
                    balance: 0,
                    balanceText: '',
                    debitSC: 0,
                    creditSC: 0,
                    balanceSC: 0,
                    balanceTextSC: ''
                };
                for (const item of items) {
                    totals.debit += item.debit || 0;
                    totals.credit += item.credit || 0;
                    totals.debitSC += item.debitSC || 0;
                    totals.creditSC += item.creditSC || 0;
                }
                if (items.length > 0) {
                    totals.initialBalance = items[items.length - 1].initialBalance || 0;
                    totals.initialBalanceSC = items[items.length - 1].initialBalanceSC || 0;
                    totals.balance = items[items.length - 1].balance || 0;
                    totals.balanceSC = items[items.length - 1].balanceSC || 0;

                    let balanceText = format(Math.abs(totals.balance), 'currency');
                    if (totals.balance !== 0) {
                        if (totals.debit > totals.credit) {
                            balanceText = t('{{balance}} (D)', {
                                balance: balanceText
                            });
                        } else if (totals.debit < totals.credit) {
                            balanceText = t('{{balance}} (C)', {
                                balance: balanceText
                            });
                        }
                    }
                    totals.balanceText = balanceText;

                    let balanceTextSC = app.format(Math.abs(totals.balanceSC), 'currency');
                    if (totals.balanceSC !== 0) {
                        if (totals.debitSC > totals.creditSC) {
                            balanceTextSC = t('{{balance}} (D)', {
                                balance: balanceTextSC
                            });
                        } else if (totals.debitSC < totals.creditSC) {
                            balanceTextSC = t('{{balance}} (C)', {
                                balance: balanceTextSC
                            });
                        }
                    }
                    totals.balanceTextSC = balanceTextSC;
                }
                totalsTable.table.body.push([
                    {
                        text: t('Debit'),
                        noWrap: true,
                        border: [false, false, false, true],
                        margin: [0, 1, 5, 1],
                        fontSize: 7
                    },
                    {
                        text: format(totals.debit, 'currency'),
                        noWrap: true,
                        border: [false, false, false, true],
                        margin: [0, 1, 0, 1],
                        fontSize: 7,
                        alignment: 'right'
                    }
                ]);
                totalsTable.table.body.push([
                    {
                        text: t('Credit'),
                        noWrap: true,
                        border: [false, false, false, true],
                        margin: [0, 1, 5, 1],
                        fontSize: 7
                    },
                    {
                        text: format(totals.credit, 'currency'),
                        noWrap: true,
                        border: [false, false, false, true],
                        margin: [0, 1, 0, 1],
                        fontSize: 7,
                        alignment: 'right'
                    }
                ]);
                totalsTable.table.body.push([
                    {
                        text: t('Balance'),
                        noWrap: true,
                        border: [false, false, false, false],
                        margin: [0, 2, 5, 2],
                        fontSize: 8,
                        bold: true,
                        fillColor: '#fcfcfc'
                    },
                    {
                        text: totals.balanceText,
                        noWrap: true,
                        border: [false, false, false, false],
                        margin: [0, 2, 0, 2],
                        fontSize: 8,
                        bold: true,
                        alignment: 'right',
                        fillColor: '#fcfcfc'
                    }
                ]);

                totalsSCTable.table.body.push([
                    {
                        text: t('Debit (SC)'),
                        noWrap: true,
                        border: [false, false, false, true],
                        margin: [0, 1, 5, 1],
                        fontSize: 7
                    },
                    {
                        text: app.format(totals.debitSC, 'currency'),
                        noWrap: true,
                        border: [false, false, false, true],
                        margin: [0, 1, 0, 1],
                        fontSize: 7,
                        alignment: 'right'
                    }
                ]);
                totalsSCTable.table.body.push([
                    {
                        text: t('Credit (SC)'),
                        noWrap: true,
                        border: [false, false, false, true],
                        margin: [0, 1, 5, 1],
                        fontSize: 7
                    },
                    {
                        text: app.format(totals.creditSC, 'currency'),
                        noWrap: true,
                        border: [false, false, false, true],
                        margin: [0, 1, 0, 1],
                        fontSize: 7,
                        alignment: 'right'
                    }
                ]);
                totalsSCTable.table.body.push([
                    {
                        text: t('Balance (SC)'),
                        noWrap: true,
                        border: [false, false, false, false],
                        margin: [0, 2, 5, 2],
                        fontSize: 8,
                        bold: true,
                        fillColor: '#fcfcfc'
                    },
                    {
                        text: totals.balanceTextSC,
                        noWrap: true,
                        border: [false, false, false, false],
                        margin: [0, 2, 0, 2],
                        fontSize: 8,
                        bold: true,
                        alignment: 'right',
                        fillColor: '#fcfcfc'
                    }
                ]);

                document.definition.content.push(itemsTable);

                if (isForeignCurrency) {
                    document.definition.content.push({columns: [{}, totalsSCTable, totalsTable], unbreakable: true});
                } else {
                    document.definition.content.push({columns: [{}, totalsTable], unbreakable: true});
                }
            }

            return this.app.print(document);
        }
    }
];

export async function getPartnerAccounts(app, payload) {
    const partnerAccountsSelection = app.setting('accounting.partnerAccounts');
    const excludeAdditionalAllowances = !!payload.excludeAdditionalAllowances;
    const cacheKey = `distinct-partner-accounting-accounts-${partnerAccountsSelection}-${excludeAdditionalAllowances}`;
    let accountIds = await app.cache.get(cacheKey);

    if (!Array.isArray(accountIds) || accountIds.length < 1) {
        accountIds = [];

        if (
            partnerAccountsSelection === 'chart-of-accounts-and-partner-based' ||
            partnerAccountsSelection === 'chart-of-accounts-based'
        ) {
            const partnerAccounts = await app.collection('kernel.accounts').find({
                isPartnerAccount: true,
                $select: ['_id']
            });
            if (partnerAccounts.length > 0) {
                accountIds.push(...partnerAccounts.map(account => account._id));
            }
        }

        if (
            partnerAccountsSelection === 'chart-of-accounts-and-partner-based' ||
            partnerAccountsSelection === 'partner-based'
        ) {
            accountIds.push(...(await app.collection('kernel.partners').distinct('accountingAccountId')));

            if (!excludeAdditionalAllowances) {
                accountIds.push(...(await app.collection('kernel.partners').distinct('additionalAllowanceAccount')));
            }
        }

        accountIds = _.uniq(accountIds.filter(accountId => _.isString(accountId) && accountId.length > 0));

        await app.cache.set(cacheKey, accountIds, 60 * 60);
    }

    return accountIds;
}

function fixDates(row) {
    for (const key of Object.keys(row)) {
        if (_.isDate(row[key])) {
            const date = row[key];

            row[key] = new Date(
                Date.UTC(
                    date.getFullYear(),
                    date.getMonth(),
                    date.getDate(),
                    date.getHours(),
                    date.getMinutes(),
                    date.getSeconds()
                )
            );
        }
    }

    return row;
}
