import _ from 'lodash';
import fastCopy from 'fast-copy';

export default [
    {
        name: 'partner-balances-sync',
        async action({partnerIds}, params) {
            const app = this.app;

            const partners = await app.collection('kernel.partners').find({
                _id: {$in: partnerIds},
                $select: ['_id', 'accountingAccountId', 'additionalAllowanceAccount', 'employeeExpenseAccountId']
            });

            await app.db.collection('accounting_partner-balances').deleteMany(
                {partnerId: {$in: partnerIds}},
                {
                    collation: {locale: app.config('app.locale')}
                }
            );

            const operations = [];

            for (const partner of partners) {
                let accountIds = [];
                if (!!partner.accountingAccountId) {
                    accountIds.push(partner.accountingAccountId);
                }
                if (!!partner.additionalAllowanceAccount) {
                    accountIds.push(partner.additionalAllowanceAccount);
                }
                if (!!partner.employeeExpenseAccountId) {
                    accountIds.push(partner.employeeExpenseAccountId);
                }

                const $match = {partnerId: partner._id, accountId: {$in: accountIds}};
                const $group = {
                    _id: {
                        currencyId: '$currencyId',
                        branchId: '$branchId',
                        financialProjectId: '$financialProjectId',
                        salespersonId: '$salespersonId',
                        scope: '$scope',
                        day: {
                            $dayOfMonth: {
                                date: '$issueDate',
                                timezone: app.config('app.timezone')
                            }
                        },
                        month: {
                            $month: {
                                date: '$issueDate',
                                timezone: app.config('app.timezone')
                            }
                        },
                        year: {
                            $year: {
                                date: '$issueDate',
                                timezone: app.config('app.timezone')
                            }
                        }
                    },
                    currency: {$first: '$currency'},
                    partner: {$first: '$partner'},
                    branch: {$first: '$branch'},
                    financialProject: {$first: '$financialProject'},
                    salesperson: {$first: '$salesperson'},
                    debitFC: {$sum: '$debitFC'},
                    creditFC: {$sum: '$creditFC'},
                    debit: {$sum: '$debit'},
                    credit: {$sum: '$credit'},
                    balance: {$sum: '$balance'},
                    balanceFC: {$sum: '$balanceFC'}
                };

                const report = await app.collection('accounting.transactions').aggregate([{$match}, {$group}]);

                for (const r of report) {
                    const d = {};

                    d.partnerId = partner._id;
                    d.partnerType = (r.partner ?? {}).type ?? '';
                    d.partnerCode = (r.partner ?? {}).code ?? '';
                    d.partnerName = (r.partner ?? {}).name ?? '';
                    d.partnerTinIdentity = (r.partner ?? {}).tinIdentity ?? '';
                    d.partnerGroupId = (r.partner ?? {}).groupId ?? '';
                    d.partnerTags = (r.partner ?? {}).tags ?? [];

                    const day = r._id.day;
                    const month = r._id.month;
                    const year = r._id.year;
                    d.date = app.datetime
                        .local()
                        .set({
                            day,
                            month,
                            year
                        })
                        .startOf('day')
                        .toJSDate();

                    d.currencyId = r._id.currencyId ?? '';
                    d.currencyName = (r.currency ?? {}).name ?? '';

                    d.branchId = r._id.branchId ?? '';
                    d.branchCode = (r.branch ?? {}).code ?? '';
                    d.branchName = (r.branch ?? {}).name ?? '';

                    d.financialProjectId = r._id.financialProjectId ?? '';
                    d.financialProjectCode = (r.financialProject ?? {}).code ?? '';
                    d.financialProjectName = (r.financialProject ?? {}).name ?? '';

                    d.salespersonId = r._id.salespersonId ?? '';
                    d.salespersonCode = (r.salesperson ?? {}).code ?? '';
                    d.salespersonName = (r.salesperson ?? {}).name ?? '';

                    d.scope = r._id.scope ?? '1';

                    d.debit = r.debit ?? 0;
                    d.credit = r.credit ?? 0;
                    d.debitFC = r.debitFC ?? 0;
                    d.creditFC = r.creditFC ?? 0;
                    d.balance = r.balance ?? 0;
                    d.balanceFC = r.balanceFC ?? 0;
                    if (d.balance > 0) {
                        d.balanceType = 'debit';
                    } else if (d.balance < 0) {
                        d.balanceType = 'credit';
                    }

                    operations.push({
                        insertOne: {
                            document: d
                        }
                    });
                }
            }

            if (operations.length > 0) {
                await app.collection('accounting.partner-balances').bulkWrite(operations);
            }
        }
    },
    {
        name: 'partner-balances-records',
        async action(payload, params) {
            const app = this.app;
            const {
                // startDate,
                // endDate,
                partnerTypes,
                partnerGroupIds,
                partnerTags,
                partnerId,
                currencyIds,
                branchIds,
                financialProjectIds,
                salespersonId,
                scope,
                groupByCurrency,
                groupByBranch,
                groupByProject,
                groupBySalesperson,
                hideZeroBalances,
                query = {},
                sort = {partnerCode: -1},
                skip = 0,
                limit = 75
            } = payload;
            let startDate = payload.startDate;
            let endDate = payload.endDate;
            const result = {tableResult: {total: 0, data: []}, summaryResult: {}};

            const $match = {$and: []};
            if (!_.isEmpty(query)) {
                $match.$and.push({...query});
            }
            if (Array.isArray(partnerTypes) && partnerTypes.length > 0) {
                $match.$and.push({partnerType: {$in: partnerTypes}});
            }
            if (Array.isArray(partnerGroupIds) && partnerGroupIds.length > 0) {
                $match.$and.push({partnerGroupId: {$in: partnerGroupIds}});
            }
            if (Array.isArray(partnerTags) && partnerTags.length > 0) {
                $match.$and.push({partnerTags: {$in: partnerTags}});
            }
            if (!!partnerId) {
                $match.$and.push({partnerId});
            }
            if (Array.isArray(currencyIds) && currencyIds.length > 0) {
                $match.$and.push({currencyId: {$in: currencyIds}});
            }
            if (Array.isArray(branchIds) && branchIds.length > 0) {
                $match.$and.push({branchId: {$in: branchIds}});
            }
            if (Array.isArray(financialProjectIds) && financialProjectIds.length > 0) {
                $match.$and.push({financialProjectId: {$in: financialProjectIds}});
            }
            if (!!salespersonId) {
                $match.$and.push({salespersonId});
            }
            if (!!scope) {
                $match.$and.push({scope});
            }

            if (_.isDate(endDate)) {
                endDate = app.datetime.fromJSDate(endDate).endOf('day').toJSDate();

                $match.$and.push({date: {$lte: endDate}});
            }
            const $matchWithoutStartDate = fastCopy($match);
            if (_.isDate(startDate)) {
                startDate = app.datetime.fromJSDate(startDate).startOf('day').toJSDate();

                $match.$and.push({date: {$gte: startDate}});
            }

            if ($matchWithoutStartDate.$and.length < 1) {
                delete $matchWithoutStartDate.$and;
            }
            if ($match.$and.length < 1) {
                delete $match.$and;
            }

            const $group = {_id: {partnerId: '$partnerId'}};
            $group.partnerCode = {$first: '$partnerCode'};
            $group.partnerName = {$first: '$partnerName'};
            $group.partnerTinIdentity = {$first: '$partnerTinIdentity'};
            if (!!groupByCurrency) {
                $group._id.currencyId = '$currencyId';
                $group.currencyId = {$first: '$currencyId'};
                $group.currencyName = {$first: '$currencyName'};
            }
            if (!!groupByBranch) {
                $group._id.branchId = '$branchId';
                $group.branchName = {$first: '$branchName'};
            }
            if (!!groupByProject) {
                $group._id.financialProjectId = '$financialProjectId';
                $group.financialProjectName = {$first: '$financialProjectName'};
            }
            if (!!groupBySalesperson) {
                $group._id.salespersonId = '$salespersonId';
                $group.salespersonName = {$first: '$salespersonName'};
            }
            $group.debit = {$sum: '$debit'};
            $group.credit = {$sum: '$credit'};
            $group.balance = {$sum: '$balance'};
            $group.debitFC = {$sum: '$debitFC'};
            $group.creditFC = {$sum: '$creditFC'};
            $group.balanceFC = {$sum: '$balanceFC'};

            const aggregationPipeline = [
                ...(!_.isEmpty($matchWithoutStartDate) ? [{$match: $matchWithoutStartDate}] : []),
                {$group},
                ...(hideZeroBalances ? [{$match: {balance: {$ne: 0}}}] : []),
                {$sort: sort}
            ];

            result.tableResult.data = (
                await app
                    .collection('accounting.partner-balances')
                    .aggregate([...aggregationPipeline, {$skip: skip}, {$limit: limit}])
            ).map(item => {
                item._id = Object.values(item._id)
                    .map(p => p.toString())
                    .join('');

                return item;
            });

            const countReport = await app
                .collection('accounting.partner-balances')
                .aggregate([...aggregationPipeline, {$count: 'count'}]);
            if (Array.isArray(countReport) && countReport.length > 0) {
                const r = countReport[0];

                result.tableResult.total = r.count ?? 0;
            }

            const summaryReport = await app.collection('accounting.partner-balances').aggregate([
                ...(!_.isEmpty($matchWithoutStartDate) ? [{$match: $matchWithoutStartDate}] : []),
                {
                    $group: {
                        _id: null,
                        debit: {$sum: '$debit'},
                        credit: {$sum: '$credit'},
                        balance: {$sum: '$balance'},
                        debitFC: {$sum: '$debitFC'},
                        creditFC: {$sum: '$creditFC'},
                        balanceFC: {$sum: '$balanceFC'}
                    }
                }
            ]);
            const summary = {
                rowCount: 0,
                debit: 0,
                credit: 0,
                balance: 0,
                debitFC: 0,
                creditFC: 0,
                balanceFC: 0
            };
            if (summaryReport.length > 0) {
                const r = summaryReport[0];

                summary.rowCount = result.tableResult.total;
                summary.debit = r.debit;
                summary.credit = r.credit;
                summary.balance = r.balance;
                summary.debitFC = r.debitFC;
                summary.creditFC = r.creditFC;
                summary.balanceFC = r.balanceFC;
            }
            if (!(Array.isArray(currencyIds) && currencyIds.length === 1)) {
                delete summary.debitFC;
                delete summary.creditFC;
                delete summary.balanceFC;
            }
            result.summaryResult = summary;

            if (_.isDate(startDate)) {
                const partnerCodes = _.uniq(result.tableResult.data.map(item => item.partnerCode));
                const $periodMatch = fastCopy($match);
                if (partnerCodes.length > 0) {
                    if (!Array.isArray($periodMatch.$and)) {
                        $periodMatch.$and = [];
                    }

                    $periodMatch.$and.push({partnerCode: {$in: partnerCodes}});
                }
                const periodItems = (
                    await app
                        .collection('accounting.partner-balances')
                        .aggregate([...(!_.isEmpty($periodMatch) ? [{$match: $periodMatch}] : []), {$group}])
                ).map(item => {
                    item._id = Object.values(item._id)
                        .map(p => p.toString())
                        .join('');

                    return item;
                });
                result.tableResult.data = result.tableResult.data.map(item => {
                    const periodItem = periodItems.find(pi => pi._id === item._id);

                    item.totalBalance = item.balance ?? 0;
                    item.totalBalanceFC = item.balanceFC ?? 0;
                    item.periodBalance = 0;
                    item.periodBalanceFC = 0;
                    item.debit = 0;
                    item.credit = 0;
                    item.debitFC = 0;
                    item.creditFC = 0;

                    if (!!periodItem) {
                        item.periodBalance = periodItem.balance ?? 0;
                        item.periodBalanceFC = periodItem.balanceFC ?? 0;

                        item.debit = periodItem.debit ?? 0;
                        item.credit = periodItem.credit ?? 0;
                        item.debitFC = periodItem.debitFC ?? 0;
                        item.creditFC = periodItem.creditFC ?? 0;
                    }

                    item.initialBalance = item.totalBalance - item.periodBalance;
                    item.initialBalanceFC = item.totalBalanceFC - item.periodBalanceFC;

                    return item;
                });

                const periodSummaryReport = await app.collection('accounting.partner-balances').aggregate([
                    ...(!_.isEmpty($match) ? [{$match}] : []),
                    {
                        $group: {
                            _id: null,
                            debit: {$sum: '$debit'},
                            credit: {$sum: '$credit'},
                            balance: {$sum: '$balance'},
                            debitFC: {$sum: '$debitFC'},
                            creditFC: {$sum: '$creditFC'},
                            balanceFC: {$sum: '$balanceFC'}
                        }
                    }
                ]);
                summary.totalBalance = summary.balance ?? 0;
                summary.totalBalanceFC = summary.balanceFC ?? 0;
                summary.periodBalance = 0;
                summary.periodBalanceFC = 0;
                summary.debit = 0;
                summary.credit = 0;
                summary.debitFC = 0;
                summary.creditFC = 0;
                if (periodSummaryReport.length > 0) {
                    const r = periodSummaryReport[0];

                    summary.periodBalance = r.balance ?? 0;
                    summary.periodBalanceFC = r.balanceFC ?? 0;

                    summary.debit = r.debit ?? 0;
                    summary.credit = r.credit ?? 0;
                    summary.debitFC = r.debitFC ?? 0;
                    summary.creditFC = r.creditFC ?? 0;
                }
                summary.initialBalance = summary.totalBalance - summary.periodBalance;
                summary.initialBalanceFC = summary.totalBalanceFC - summary.periodBalanceFC;
                if (!(Array.isArray(currencyIds) && currencyIds.length === 1)) {
                    delete summary.initialBalanceFC;
                    delete summary.periodBalanceFC;
                    delete summary.totalBalanceFC;
                    delete summary.debitFC;
                    delete summary.creditFC;
                    delete summary.balanceFC;
                }
            }

            return result;
        }
    }
];
