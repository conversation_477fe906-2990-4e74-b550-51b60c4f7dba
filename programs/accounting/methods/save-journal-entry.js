import _ from 'lodash';
import microtime from 'microtime';

export default {
    name: 'save-journal-entry',
    async action({data, id, dontSetVoucherNo = false}, params) {
        const app = this.app;
        const accountsCollection = app.collection('kernel.accounts');
        const partnersCollection = app.collection('kernel.partners');
        const periodsCollection = app.collection('kernel.periods');
        const entriesCollection = app.collection('accounting.journal-entries');
        const company = await app.collection('kernel.company').findOne({});
        const roundingDebitAccountId = app.defaultAccountingAccount('roundingDebitAccount');
        const roundingCreditAccountId = app.defaultAccountingAccount('roundingCreditAccount');
        const isCheck = !!params.isCheck;
        let debitTotal = 0;
        let creditTotal = 0;

        // Fix missing item.
        if (app.setting('system.scopes')) {
            data.items = data.items.map(item => {
                if (!item.scope) {
                    item.scope = '1';
                }

                if (
                    item.financialProjectId === 'undefined' ||
                    item.financialProjectId === null ||
                    item.financialProjectId === undefined
                ) {
                    delete item.financialProjectId;
                }

                return item;
            });
        }

        // Check if credit and debit are balanced.
        // const hasMixedScopes = (data.items || []).some(item => item.scope === '1') && (data.items || []).some(item => item.scope === '2');
        if (app.setting('system.scopes')) {
            let scope1Debit = 0;
            let scope1Credit = 0;
            let scope1Count = 0;
            let scope2Debit = 0;
            let scope2Credit = 0;
            let scope2Count = 0;

            for (const item of data.items) {
                if (item.scope === '1') {
                    scope1Debit += app.round(item.debit || 0, 'currency');
                    scope1Credit += app.round(item.credit || 0, 'currency');
                    scope1Count++;
                } else if (item.scope === '2') {
                    scope2Debit += app.round(item.debit || 0, 'currency');
                    scope2Credit += app.round(item.credit || 0, 'currency');
                    scope2Count++;
                }
            }

            scope1Debit = app.round(scope1Debit, 'currency');
            scope1Credit = app.round(scope1Credit, 'currency');
            scope2Debit = app.round(scope2Debit, 'currency');
            scope2Credit = app.round(scope2Credit, 'currency');

            if (!params.provider) {
                const scope1Diff = scope1Debit - scope1Credit;
                const scope1RoundingRow = {};
                if (scope1Diff < 0) {
                    if (!roundingDebitAccountId) {
                        throw new app.errors.Unprocessable(this.translate('Rounding debit account is required!'));
                    }

                    scope1RoundingRow.accountId = roundingDebitAccountId;
                    scope1RoundingRow.description = data.description;
                    scope1RoundingRow.branchId = data.branchId;
                    scope1RoundingRow.currencyId = company.currencyId;
                    scope1RoundingRow.scope = '1';
                    scope1RoundingRow.debit = app.round(Math.abs(scope1Diff), 'currency');
                } else if (scope1Count > 0) {
                    if (!roundingCreditAccountId) {
                        throw new app.errors.Unprocessable(this.translate('Rounding credit account is required!'));
                    }

                    scope1RoundingRow.accountId = roundingCreditAccountId;
                    scope1RoundingRow.description = data.description;
                    scope1RoundingRow.branchId = data.branchId;
                    scope1RoundingRow.currencyId = company.currencyId;
                    scope1RoundingRow.scope = '1';
                    scope1RoundingRow.credit = app.round(Math.abs(scope1Diff), 'currency');
                }
                if (!_.isEmpty(scope1RoundingRow)) {
                    data.items.push(scope1RoundingRow);
                }

                const scope2Diff = scope2Debit - scope2Credit;
                const scope2RoundingRow = {};
                if (scope2Diff < 0) {
                    if (!roundingDebitAccountId) {
                        throw new app.errors.Unprocessable(this.translate('Rounding debit account is required!'));
                    }

                    scope2RoundingRow.accountId = roundingDebitAccountId;
                    scope2RoundingRow.description = data.description;
                    scope2RoundingRow.branchId = data.branchId;
                    scope2RoundingRow.currencyId = company.currencyId;
                    scope2RoundingRow.scope = '2';
                    scope2RoundingRow.debit = app.round(Math.abs(scope2Diff), 'currency');
                } else if (scope2Count > 0) {
                    if (!roundingCreditAccountId) {
                        throw new app.errors.Unprocessable(this.translate('Rounding credit account is required!'));
                    }

                    scope2RoundingRow.accountId = roundingCreditAccountId;
                    scope2RoundingRow.description = data.description;
                    scope2RoundingRow.branchId = data.branchId;
                    scope2RoundingRow.currencyId = company.currencyId;
                    scope2RoundingRow.scope = '2';
                    scope2RoundingRow.credit = app.round(Math.abs(scope2Diff), 'currency');
                }
                if (!_.isEmpty(scope2RoundingRow)) {
                    data.items.push(scope2RoundingRow);
                }
            }

            debitTotal = 0;
            creditTotal = 0;
            for (const item of data.items) {
                if (_.isNumber(item.debit)) {
                    debitTotal += app.round(item.debit, 'currency');
                }

                if (_.isNumber(item.credit)) {
                    creditTotal += app.round(item.credit, 'currency');
                }
            }
            debitTotal = app.round(debitTotal, 'currency');
            creditTotal = app.round(creditTotal, 'currency');

            if (debitTotal !== creditTotal) {
                throw new app.errors.Unprocessable(
                    this.translate(
                        'Journal entry could not created because the total debit and credit are not balanced!'
                    )
                );
            }
        } else {
            debitTotal = 0;
            creditTotal = 0;
            for (const item of data.items) {
                if (_.isNumber(item.debit)) {
                    debitTotal += app.round(item.debit, 'currency');
                }

                if (_.isNumber(item.credit)) {
                    creditTotal += app.round(item.credit, 'currency');
                }
            }
            debitTotal = app.round(debitTotal, 'currency');
            creditTotal = app.round(creditTotal, 'currency');

            if (!params.provider) {
                if (debitTotal !== creditTotal) {
                    const diff = debitTotal - creditTotal;
                    const roundingRow = {};

                    if (diff < 0) {
                        if (!roundingDebitAccountId) {
                            throw new app.errors.Unprocessable(this.translate('Rounding debit account is required!'));
                        }

                        roundingRow.accountId = roundingDebitAccountId;
                        roundingRow.description = data.description;
                        roundingRow.branchId = data.branchId;
                        roundingRow.currencyId = company.currencyId;
                        roundingRow.debit = app.round(Math.abs(diff), 'currency');
                    } else {
                        if (!roundingCreditAccountId) {
                            throw new app.errors.Unprocessable(this.translate('Rounding credit account is required!'));
                        }

                        roundingRow.accountId = roundingCreditAccountId;
                        roundingRow.description = data.description;
                        roundingRow.branchId = data.branchId;
                        roundingRow.currencyId = company.currencyId;
                        roundingRow.credit = app.round(Math.abs(diff), 'currency');
                    }

                    data.items.push(roundingRow);
                }
            } else if (debitTotal !== creditTotal) {
                throw new app.errors.Unprocessable(
                    this.translate(
                        'Journal entry could not created because the total debit and credit are not balanced!'
                    )
                );
            }
        }

        // Remove zero rows.
        data.items = data.items.filter(item => {
            if (_.isPlainObject(item.tax) && item.tax.unTaxedAmount > 0) {
                return true;
            }

            return !(
                (_.isUndefined(item.credit) && item.debit === 0) ||
                (_.isUndefined(item.debit) && item.credit === 0)
            );
        });

        // Check debit and credit.
        if (
            data.items
                .filter(item => typeof item.debit === 'number' && !isNaN(item.debit))
                .some(item => !(item.debit >= 0)) ||
            data.items
                .filter(item => typeof item.credit === 'number' && !isNaN(item.credit))
                .some(item => !(item.credit >= 0))
        ) {
            throw new app.errors.Unprocessable(this.translate('Debit or credit cannot be lower than zero!'));
        }

        // Double check debit and credit total.
        let finalDebitTotal = 0;
        let finalCreditTotal = 0;
        for (const item of data.items) {
            if (typeof item.debit === 'number' && !isNaN(item.debit)) {
                finalDebitTotal += item.debit;
            }

            if (typeof item.credit === 'number' && !isNaN(item.credit)) {
                finalCreditTotal += item.credit;
            }
        }
        if (app.round(finalDebitTotal, 'currency') !== app.round(finalCreditTotal, 'currency')) {
            throw new app.errors.Unprocessable(
                this.translate('Journal entry could not created because the total debit and credit are not balanced!')
            );
        }

        if (app.setting('system.multiBranch') && app.setting('accounting.creditDebitBalanceAtBranchLevel')) {
            const branchGroupedItems = _.groupBy(data.items, 'branchId');

            for (const branchId of Object.keys(branchGroupedItems)) {
                const branchItems = branchGroupedItems[branchId];
                let debitTotal = 0;
                let creditTotal = 0;

                for (const item of branchItems) {
                    if (_.isNumber(item.debit)) {
                        debitTotal += app.round(item.debit, 'currency');
                    }

                    if (_.isNumber(item.credit)) {
                        creditTotal += app.round(item.credit, 'currency');
                    }
                }

                debitTotal = app.round(debitTotal, 'currency');
                creditTotal = app.round(creditTotal, 'currency');

                if (debitTotal !== creditTotal) {
                    throw new app.errors.Unprocessable(
                        this.translate(
                            'Journal entry could not created because the total debit and credit are not balanced at branch level!'
                        )
                    );
                }
            }
        }

        // Get and check fiscal year.
        const fiscalYear = await periodsCollection.findOne({
            'periods.recordDateStart': {$lte: data.recordDate},
            'periods.recordDateEnd': {$gte: data.recordDate}
        });
        if (!_.isObject(fiscalYear)) {
            throw new app.errors.Unprocessable(
                this.translate('No relevant fiscal year found according to the supplied record date!')
            );
        }

        // Get and check accounting period.
        const period = fiscalYear.periods.find(period => {
            return (
                period.recordDateStart.getTime() <= data.recordDate.getTime() &&
                period.recordDateEnd.getTime() >= data.recordDate.getTime()
            );
        });
        if (!_.isObject(period)) {
            throw new app.errors.Unprocessable(
                this.translate('No relevant accounting period found according to the supplied record date!')
            );
        }

        // Check accounting period status.
        if (period.status === 'blockedExceptForAdvisers') {
            if (!!params.user) {
                if (!params.user.isAdviser) {
                    throw new app.errors.Unprocessable(
                        this.translate('The relevant accounting period has been blocked!')
                    );
                }
            } else {
                throw new app.errors.Unprocessable(this.translate('The relevant accounting period has been blocked!'));
            }
        } else if (period.status !== 'unBlocked') {
            throw new app.errors.Unprocessable(this.translate('The relevant accounting period has been blocked!'));
        }

        // Check issue date.
        if (
            !(
                period.issueDateStart.getTime() <= data.issueDate.getTime() &&
                period.issueDateEnd.getTime() >= data.issueDate.getTime()
            )
        ) {
            throw new app.errors.Unprocessable(
                this.translate(
                    'The issue date you entered is invalid for the {{periodName}} period. Please update the date and try again.',
                    {periodName: period.name}
                )
            );
        }

        // Check due date.
        if (
            !(
                period.dueDateStart.getTime() <= data.dueDate.getTime() &&
                period.dueDateEnd.getTime() >= data.dueDate.getTime()
            )
        ) {
            throw new app.errors.Unprocessable(
                this.translate(
                    'The due date you entered is invalid for the {{periodName}} period. Please update the date and try again.',
                    {
                        periodName: period.name
                    }
                )
            );
        }

        for (let item of data.items || []) {
            if (!item.accountId) {
                throw new app.errors.Unprocessable(this.translate('No account found for journal entry item!'));
            }

            const account = await accountsCollection.findOne({
                _id: item.accountId,
                $select: ['code', 'name', 'currencyId', 'scope'],
                $disableActiveCheck: true,
                $disableSoftDelete: true
            });

            if (!account) {
                throw new app.errors.Unprocessable(this.translate('No account found for journal entry item!'));
            }

            if (
                account.currencyId &&
                item.currencyId &&
                account.currencyId !== company.currency._id &&
                item.currencyId !== account.currencyId
            ) {
                throw new app.errors.Unprocessable(
                    this.translate(
                        `The currency selected for account '{{account}}' is invalid! Please update the currency and try again!`,
                        {account: `${account.code} - ${account.name}`}
                    )
                );
            }

            // Check scope.
            if (app.setting('system.scopes') && !!item.scope) {
                if (!!account.scope && account.scope !== '1+2' && item.scope !== account.scope) {
                    throw new app.errors.Unprocessable(
                        this.translate(
                            `The scope selected for account '{{account}}' is invalid! Please update the scope and try again!`,
                            {
                                account: `${account.code} - ${account.name}`
                            }
                        )
                    );
                }

                if (!!item.partnerId) {
                    const partner = await partnersCollection.findOne({
                        _id: item.partnerId,
                        $select: ['code', 'name', 'scope']
                    });

                    if (!!partner.scope && partner.scope !== '1+2' && item.scope !== partner.scope) {
                        throw new app.errors.Unprocessable(
                            this.translate(
                                `The scope selected for partner '{{partner}}' is invalid! Please update the scope and try again!`,
                                {partner: `${partner.code} - ${partner.name}`}
                            )
                        );
                    }
                }
            }
        }

        // Check if entry has item.
        if (!(Array.isArray(data.items) && data.items.length > 0)) {
            throw new app.errors.Unprocessable(this.translate('Journal entry does not have items!'));
        }

        if (isCheck) {
            return true;
        }

        // Fiscal year and period.
        data.period = _.pick(period, ['name', 'code']);
        data.period.fiscalYearId = fiscalYear._id;
        data.period.fiscalYear = fiscalYear.fiscalYear;

        // Evaluate Tax.
        const taxEvaluatedItems = [];
        for (const item of data.items) {
            if (!!item.taxId && !item.tax) {
                const tax = await app.collection('kernel.taxes').findOne({
                    _id: item.taxId
                });
                const taxGroup = await app.collection('kernel.tax-groups').findOne({
                    _id: tax.groupId,
                    $select: ['name']
                });

                item.tax = _.pick(tax, [
                    '_id',
                    'scope',
                    'code',
                    'name',
                    'label',
                    'computation',
                    'amount',
                    'isDeduction'
                ]);
                item.tax.unTaxedAmount = item.taxAssessment || 0;
                item.tax.groupId = taxGroup._id;
                item.tax.groupName = taxGroup.name;
            } else if (!!item.tax && !item.taxId) {
                item.taxId = item.tax._id;
                item.taxAssessment = item.tax.unTaxedAmount || 0;
            }

            taxEvaluatedItems.push(item);
        }
        data.items = taxEvaluatedItems;

        // Account projections.
        const accountProjections = app.setting('system.accountProjections') || [];
        if (Array.isArray(accountProjections) && accountProjections.length > 0) {
            data.items = data.items.map(item => {
                item.scope = item.scope || '1';

                const projection = accountProjections.find(
                    ap =>
                        !!ap.scope &&
                        !!ap.sourceAccountId &&
                        !!ap.destinationAccountId &&
                        ap.sourceAccountId === item.accountId &&
                        ap.scope === item.scope
                );
                if (!!projection) {
                    item.accountId = projection.destinationAccountId;
                }

                return item;
            });
        }

        if (id) {
            // Get entry.
            const entry = await entriesCollection.get(id);

            // Check entry is posted.
            const canUpdate = await app.checkPermission({
                type: 'permission',
                name: 'accounting.canUpdatePostedJournalEntries',
                user: params.user
            });
            if (entry.status !== 'draft' && !params.user.isRoot && !canUpdate) {
                throw new app.errors.Unprocessable(this.translate('Posted entry cannot be updated!'));
            }

            // Entry status.
            // data.status = 'draft';

            // Calculate total.
            data.total = Math.abs((debitTotal + creditTotal) / 2);

            // Normalize.
            data.items = data.items.map(item => {
                if (data.description && !item.description) {
                    item.description = data.description;
                }

                if (!_.isDate(item.recordDate)) item.recordDate = data.recordDate;
                if (!_.isDate(item.issueDate)) item.issueDate = data.issueDate;
                if (!_.isDate(item.dueDate)) item.dueDate = data.dueDate;

                if (_.isUndefined(item.currencyId)) item.currencyId = company.currencyId;

                if (item.currencyId === company.currencyId) {
                    delete item.debitFC;
                    delete item.creditFC;
                }

                if (typeof item.debit !== 'number' || isNaN(item.debit)) item.debit = 0;
                if (typeof item.credit !== 'number' || isNaN(item.credit)) item.credit = 0;
                if (typeof item.debitFC !== 'number' || isNaN(item.debitFC)) item.debitFC = 0;
                if (typeof item.creditFC !== 'number' || isNaN(item.creditFC)) item.creditFC = 0;

                if (_.isNumber(item.debit)) item.debit = app.round(item.debit, 'currency');
                if (_.isNumber(item.credit)) item.credit = app.round(item.credit, 'currency');
                if (_.isNumber(item.debitFC)) item.debitFC = app.round(item.debitFC, 'currency');
                if (_.isNumber(item.creditFC)) item.creditFC = app.round(item.creditFC, 'currency');

                return item;
            });

            if (!!params.user) {
                data.updatedBy = params.user._id;
            }

            if (!!data.journalId) {
                const journal = await app.collection('accounting.journals').findOne({
                    _id: data.journalId,
                    $select: ['_id', 'preventCreationOfPastRecord'],
                    $disableSoftDelete: true,
                    $disableActiveCheck: true
                });

                if (!!journal) {
                    const hasPastRecords = data.items.some(
                        item => item.issueDate.getTime() < app.datetime.local().startOf('day').toJSDate().getTime()
                    );

                    if (!!hasPastRecords) {
                        const preventCreationOfPastRecord = !!journal.preventCreationOfPastRecord;
                        const canCreatePastRecords = await app.checkPermission({
                            type: 'permission',
                            name: 'accounting.canCreatePastRecords',
                            user: params.user
                        });

                        if (preventCreationOfPastRecord && !canCreatePastRecords) {
                            throw new Error(
                                app.translate(
                                    'You are not authorized to create a record for the past date in the current journal!'
                                )
                            );
                        }
                    }
                }
            }

            const result = await entriesCollection.patch({_id: id}, data, {
                user: params.user,
                disablePermissionCheck: true
            });

            if (entry.status !== 'draft' && (!!params.user.isRoot || canUpdate)) {
                const existingTransaction = await app.collection('accounting.transactions').findOne({
                    entryId: id,
                    $select: ['_id', 'declaredEntryNo']
                });
                if (!!existingTransaction && !!existingTransaction.declaredEntryNo) {
                    const items = data.items.map(item => {
                        item.declaredEntryNo = existingTransaction.declaredEntryNo;

                        return item;
                    });

                    await entriesCollection.patch(
                        {_id: id},
                        {
                            items
                        },
                        {user: params.user, disablePermissionCheck: true}
                    );
                }

                await app.collection('accounting.transactions').remove(
                    {
                        entryId: id
                    },
                    {disableSoftDelete: true}
                );

                await app.rpc('accounting.post-journal-entry', id, {
                    user: params.user,
                    isUpdatingPostedEntry: true
                });
            }

            return result;
        } else {
            // Find next draft voucher no.
            // const lastDraftEntry = await entriesCollection.findOne({
            //     status: 'draft',
            //     $sort: {createdAt: -1},
            //     $limit: 1,
            //     $select: ['voucherNo']
            // });
            // let voucherNo = '* 1';
            // if (_.isObject(lastDraftEntry)) voucherNo = `* ${parseInt(lastDraftEntry.voucherNo.replace('* ', '')) + 1}`;
            if (!dontSetVoucherNo) {
                data.voucherNo = `* ${microtime.now()}`;
            }

            // Entry status.
            data.status = 'draft';

            // Calculate total.
            data.total = Math.abs((debitTotal + creditTotal) / 2);

            // Normalize.
            data.items = data.items.map(item => {
                if (data.description && !item.description) {
                    item.description = data.description;
                }

                if (!_.isDate(item.recordDate)) item.recordDate = data.recordDate;
                if (!_.isDate(item.issueDate)) item.issueDate = data.issueDate;
                if (!_.isDate(item.dueDate)) item.dueDate = data.dueDate;

                if (_.isUndefined(item.currencyId)) item.currencyId = company.currencyId;

                if (item.currencyId === company.currencyId) {
                    delete item.debitFC;
                    delete item.creditFC;
                }

                if (_.isNumber(item.debit)) item.debit = app.round(item.debit, 'currency');
                if (_.isNumber(item.credit)) item.credit = app.round(item.credit, 'currency');
                if (_.isNumber(item.debitFC)) item.debitFC = app.round(item.debitFC, 'currency');
                if (_.isNumber(item.creditFC)) item.creditFC = app.round(item.creditFC, 'currency');

                return item;
            });

            if (!!params.user) {
                data.createdBy = params.user._id;
            }

            if (!!data.journalId) {
                const journal = await app.collection('accounting.journals').findOne({
                    _id: data.journalId,
                    $select: ['_id', 'preventCreationOfPastRecord'],
                    $disableSoftDelete: true,
                    $disableActiveCheck: true
                });

                if (!!journal) {
                    const hasPastRecords = data.items.some(
                        item => item.issueDate.getTime() < app.datetime.local().startOf('day').toJSDate().getTime()
                    );

                    if (!!hasPastRecords) {
                        const preventCreationOfPastRecord = !!journal.preventCreationOfPastRecord;
                        const canCreatePastRecords = await app.checkPermission({
                            type: 'permission',
                            name: 'accounting.canCreatePastRecords',
                            user: params.user
                        });

                        if (preventCreationOfPastRecord && !canCreatePastRecords) {
                            throw new Error(
                                app.translate(
                                    'You are not authorized to create a record for the past date in the current journal!'
                                )
                            );
                        }
                    }
                }
            }

            // Create and return entry.
            return await entriesCollection.create(data, {user: params.user, disablePermissionCheck: true});
        }
    }
};
