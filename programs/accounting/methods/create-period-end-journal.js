import _ from 'lodash';
import fastCopy from 'fast-copy';

export default {
    name: 'create-period-end-journal',
    async action(payload, params) {
        const app = this.app;
        const company = await app.collection('kernel.company').findOne({});
        const template = await app.collection('accounting.period-end-templates').findOne({
            _id: payload.templateId
        });
        const fiscalYear = await app.collection('kernel.periods').get(payload.fiscalYearId);

        const entry = {items: []};
        entry.reference = payload.reference ?? '';
        entry.description = payload.description ?? '';
        entry.branchId = payload.branchId;
        entry.recordDate = payload.issueDate;
        entry.issueDate = payload.issueDate;
        entry.dueDate = payload.issueDate;
        entry.journalId = payload.journalId;
        entry.periodEndEntry = true;
        if (_.isPlainObject(params.user)) entry.createdBy = params.user._id;

        if (template.type === 'partner-advance-payment' && !template.isInverse) {
            const partnerAccountIds = await app.collection('kernel.partners').distinct('accountingAccountId');

            const $match = {
                'partner.type': template.partnerType,
                balance: {$ne: 0},
                'period.fiscalYear': fiscalYear.fiscalYear,
                accountId: {
                    $in: partnerAccountIds
                },
                $and: []
            };
            if (!!payload.startPeriod) {
                const periods = _.orderBy(fiscalYear.periods ?? [], ['issueDateStart'], ['asc']);
                const period = periods.find(p => p.code === payload.startPeriod);

                if (!!period) {
                    $match.$and.push({
                        issueDate: {$gte: app.datetime.fromJSDate(period.issueDateStart).startOf('day').toJSDate()}
                    });

                    if (!!payload.applyDueConstraintInPeriod) {
                        $match.$and.push({
                            dueDate: {$gte: app.datetime.fromJSDate(period.issueDateStart).startOf('day').toJSDate()}
                        });
                    }
                }
            }
            if (!!payload.endPeriod) {
                const periods = _.orderBy(fiscalYear.periods ?? [], ['issueDateEnd'], ['desc']);
                const period = periods.find(p => p.code === payload.endPeriod);

                if (!!period) {
                    $match.$and.push({
                        issueDate: {$lte: app.datetime.fromJSDate(period.issueDateEnd).endOf('day').toJSDate()}
                    });

                    if (!!payload.applyDueConstraintInPeriod) {
                        $match.$and.push({
                            dueDate: {$lte: app.datetime.fromJSDate(period.issueDateEnd).endOf('day').toJSDate()}
                        });
                    }
                }
            }
            if (payload.scope === '1' || payload.scope === '2') {
                $match.$and.push({scope: payload.scope});
            }
            if (!!payload.branchBased) {
                $match.$and.push({branchId: payload.branchId});
            }
            if ($match.$and.length < 1) {
                delete $match.$and;
            }

            const accountsReport = await app.collection('accounting.transactions').aggregate([
                {$match},
                ...(!payload.recordBased
                    ? [
                          {
                              $group: {
                                  _id: {accountId: '$accountId', partnerId: '$partnerId', currencyId: '$currencyId'},
                                  balance: {$sum: '$balance'},
                                  balanceFC: {$sum: '$balanceFC'}
                              }
                          },
                          {
                              $project: {
                                  _id: 0,
                                  accountId: '$_id.accountId',
                                  partnerId: '$_id.partnerId',
                                  currencyId: '$_id.currencyId',
                                  balance: 1,
                                  balanceFC: 1
                              }
                          }
                      ]
                    : []),
                {
                    $match: {
                        balance: template.partnerType === 'vendor' ? {$gt: 0} : {$lt: 0}
                    }
                }
            ]);

            const partners = await app.collection('kernel.partners').find({
                _id: {$in: _.uniq(accountsReport.map(r => r.partnerId))},
                $select: ['_id', 'additionalAllowanceAccount'],
                $disableSoftDelete: true,
                $disableActiveCheck: true
            });
            const partnersMap = {};
            for (const partner of partners) {
                partnersMap[partner._id] = partner;
            }

            for (const report of accountsReport) {
                const partner = partnersMap[report.partnerId];
                if (!partner || !partner.additionalAllowanceAccount) continue;
                const amount = app.round(Math.abs(report.balance ?? 0), 'currency');
                const amountFC = app.round(Math.abs(report.balanceFC ?? 0), 'currency');
                if (amount === 0) continue;

                const firstItem = {};
                firstItem.accountId = report.accountId;
                firstItem.description = payload.description;
                firstItem.partnerId = report.partnerId;
                firstItem.branchId = payload.branchId;
                firstItem.currencyId = report.currencyId;
                firstItem.scope = payload.scope || '1';
                firstItem.financialProjectId = payload.financialProjectId || '';
                if (template.partnerType === 'customer') {
                    firstItem.debit = amount;

                    if (report.currencyId !== company.currencyId) {
                        firstItem.debitFC = amountFC;
                    }
                } else {
                    firstItem.credit = amount;

                    if (report.currencyId !== company.currencyId) {
                        firstItem.creditFC = amountFC;
                    }
                }
                if (_.isDate(report.dueDate)) {
                    firstItem.recordDate = report.dueDate;
                    firstItem.issueDate = report.dueDate;
                    firstItem.dueDate = report.dueDate;
                }
                entry.items.push(firstItem);

                const secondItem = {};
                secondItem.accountId = partner.additionalAllowanceAccount;
                secondItem.description = payload.description;
                secondItem.partnerId = report.partnerId;
                secondItem.branchId = payload.branchId;
                secondItem.currencyId = report.currencyId;
                secondItem.scope = payload.scope || '1';
                secondItem.financialProjectId = payload.financialProjectId || '';
                if (template.partnerType === 'customer') {
                    secondItem.credit = amount;

                    if (report.currencyId !== company.currencyId) {
                        secondItem.creditFC = amountFC;
                    }
                } else {
                    secondItem.debit = amount;

                    if (report.currencyId !== company.currencyId) {
                        secondItem.debitFC = amountFC;
                    }
                }
                if (_.isDate(report.dueDate)) {
                    secondItem.recordDate = report.dueDate;
                    secondItem.issueDate = report.dueDate;
                    secondItem.dueDate = report.dueDate;
                }
                entry.items.push(secondItem);
            }
        } else if (template.type === 'partner-advance-payment' && !!template.isInverse) {
            const partnerAccountIds = await app.collection('kernel.partners').distinct('additionalAllowanceAccount');

            const $match = {
                'partner.type': template.partnerType,
                balance: {$ne: 0},
                'period.fiscalYear': fiscalYear.fiscalYear,
                accountId: {
                    $in: partnerAccountIds
                },
                $and: []
            };
            if (!!payload.startPeriod) {
                const periods = _.orderBy(fiscalYear.periods ?? [], ['issueDateStart'], ['asc']);
                const period = periods.find(p => p.code === payload.startPeriod);

                if (!!period) {
                    $match.$and.push({
                        issueDate: {$gte: app.datetime.fromJSDate(period.issueDateStart).startOf('day').toJSDate()}
                    });

                    if (!!payload.applyDueConstraintInPeriod) {
                        $match.$and.push({
                            dueDate: {$gte: app.datetime.fromJSDate(period.issueDateStart).startOf('day').toJSDate()}
                        });
                    }
                }
            }
            if (!!payload.endPeriod) {
                const periods = _.orderBy(fiscalYear.periods ?? [], ['issueDateEnd'], ['desc']);
                const period = periods.find(p => p.code === payload.endPeriod);

                if (!!period) {
                    $match.$and.push({
                        issueDate: {$lte: app.datetime.fromJSDate(period.issueDateEnd).endOf('day').toJSDate()}
                    });

                    if (!!payload.applyDueConstraintInPeriod) {
                        $match.$and.push({
                            dueDate: {$lte: app.datetime.fromJSDate(period.issueDateEnd).endOf('day').toJSDate()}
                        });
                    }
                }
            }
            if (payload.scope === '1' || payload.scope === '2') {
                $match.$and.push({scope: payload.scope});
            }
            if (!!payload.branchBased) {
                $match.$and.push({branchId: payload.branchId});
            }
            if ($match.$and.length < 1) {
                delete $match.$and;
            }

            const accountsReport = await app.collection('accounting.transactions').aggregate([
                {$match},
                ...(!payload.recordBased
                    ? [
                          {
                              $group: {
                                  _id: {accountId: '$accountId', partnerId: '$partnerId', currencyId: '$currencyId'},
                                  balance: {$sum: '$balance'},
                                  balanceFC: {$sum: '$balanceFC'}
                              }
                          },
                          {
                              $project: {
                                  _id: 0,
                                  accountId: '$_id.accountId',
                                  partnerId: '$_id.partnerId',
                                  currencyId: '$_id.currencyId',
                                  balance: 1,
                                  balanceFC: 1
                              }
                          }
                      ]
                    : []),
                {
                    $match: {
                        balance: template.partnerType === 'vendor' ? {$gt: 0} : {$lt: 0}
                    }
                }
            ]);

            const partners = await app.collection('kernel.partners').find({
                _id: {$in: _.uniq(accountsReport.map(r => r.partnerId))},
                $select: ['_id', 'accountingAccountId', 'additionalAllowanceAccount'],
                $disableSoftDelete: true,
                $disableActiveCheck: true
            });
            const partnersMap = {};
            for (const partner of partners) {
                partnersMap[partner._id] = partner;
            }

            for (const report of accountsReport) {
                const partner = partnersMap[report.partnerId];
                if (!partner || !partner.additionalAllowanceAccount) continue;
                const amount = app.round(Math.abs(report.balance ?? 0), 'currency');
                const amountFC = app.round(Math.abs(report.balanceFC ?? 0), 'currency');
                if (amount === 0) continue;

                const firstItem = {};
                firstItem.accountId = report.accountId;
                firstItem.description = payload.description;
                firstItem.partnerId = report.partnerId;
                firstItem.branchId = payload.branchId;
                firstItem.currencyId = report.currencyId;
                firstItem.scope = payload.scope || '1';
                firstItem.financialProjectId = payload.financialProjectId || '';
                if (template.partnerType === 'customer') {
                    firstItem.credit = amount;

                    if (report.currencyId !== company.currencyId) {
                        firstItem.creditFC = amountFC;
                    }
                } else {
                    firstItem.debit = amount;

                    if (report.currencyId !== company.currencyId) {
                        firstItem.debitFC = amountFC;
                    }
                }
                if (_.isDate(report.dueDate)) {
                    firstItem.recordDate = report.dueDate;
                    firstItem.issueDate = report.dueDate;
                    firstItem.dueDate = report.dueDate;
                }
                entry.items.push(firstItem);

                const secondItem = {};
                secondItem.accountId = partner.accountingAccountId;
                secondItem.description = payload.description;
                secondItem.partnerId = report.partnerId;
                secondItem.branchId = payload.branchId;
                secondItem.currencyId = report.currencyId;
                secondItem.scope = payload.scope || '1';
                secondItem.financialProjectId = payload.financialProjectId || '';
                if (template.partnerType === 'customer') {
                    secondItem.debit = amount;

                    if (report.currencyId !== company.currencyId) {
                        secondItem.debitFC = amountFC;
                    }
                } else {
                    secondItem.credit = amount;

                    if (report.currencyId !== company.currencyId) {
                        secondItem.creditFC = amountFC;
                    }
                }
                if (_.isDate(report.dueDate)) {
                    secondItem.recordDate = report.dueDate;
                    secondItem.issueDate = report.dueDate;
                    secondItem.dueDate = report.dueDate;
                }
                entry.items.push(secondItem);
            }
        } else if (template.type === 'partner-transfer') {
            const partnerAccountIds = await app.collection('kernel.partners').distinct('accountingAccountId');

            const $match = {
                'partner.type': template.partnerType,
                'partner.groupId': template.sourcePartnerGroupId,
                partnerId: {$ne: template.destinationPartnerId},
                balance: {$ne: 0},
                'period.fiscalYear': fiscalYear.fiscalYear,
                accountId: {
                    $in: partnerAccountIds
                },
                $and: []
            };
            if (!!payload.startPeriod) {
                const periods = _.orderBy(fiscalYear.periods ?? [], ['issueDateStart'], ['asc']);
                const period = periods.find(p => p.code === payload.startPeriod);

                if (!!period) {
                    $match.$and.push({
                        issueDate: {$gte: app.datetime.fromJSDate(period.issueDateStart).startOf('day').toJSDate()}
                    });
                }
            }
            if (!!payload.endPeriod) {
                const periods = _.orderBy(fiscalYear.periods ?? [], ['issueDateEnd'], ['desc']);
                const period = periods.find(p => p.code === payload.endPeriod);

                if (!!period) {
                    $match.$and.push({
                        issueDate: {$lte: app.datetime.fromJSDate(period.issueDateEnd).endOf('day').toJSDate()}
                    });
                }
            }
            if (payload.scope === '1' || payload.scope === '2') {
                $match.$and.push({scope: payload.scope});
            }
            if (!!payload.branchBased) {
                $match.$and.push({branchId: payload.branchId});
            }
            if ($match.$and.length < 1) {
                delete $match.$and;
            }

            const accountsReport = await app.collection('accounting.transactions').aggregate([
                {$match},
                {
                    $group: {
                        _id: {accountId: '$accountId', partnerId: '$partnerId', currencyId: '$currencyId'},
                        balance: {$sum: '$balance'},
                        balanceFC: {$sum: '$balanceFC'}
                    }
                },
                {
                    $project: {
                        _id: 0,
                        accountId: '$_id.accountId',
                        partnerId: '$_id.partnerId',
                        currencyId: '$_id.currencyId',
                        balance: 1,
                        balanceFC: 1
                    }
                }
                // {
                //     $match: {
                //         balance: template.partnerType === 'vendor' ? {$lt: 0} : {$gt: 0}
                //     }
                // }
            ]);
            const partners = await app.collection('kernel.partners').find({
                _id: {$in: _.uniq(accountsReport.map(r => r.partnerId).concat(template.destinationPartnerId))},
                $select: ['_id', 'accountingAccountId'],
                $disableSoftDelete: true,
                $disableActiveCheck: true
            });
            const partnersMap = {};
            for (const partner of partners) {
                partnersMap[partner._id] = partner;
            }
            const destinationPartner = partnersMap[template.destinationPartnerId];

            let totalsMap = {};
            for (const report of accountsReport) {
                const partner = partnersMap[report.partnerId];
                if (!partner || !partner.accountingAccountId) continue;
                if (report.balance === 0) continue;

                const firstItem = {};
                firstItem.accountId = report.accountId;
                firstItem.description = payload.description;
                firstItem.partnerId = report.partnerId;
                firstItem.branchId = payload.branchId;
                firstItem.currencyId = report.currencyId;
                firstItem.scope = payload.scope || '1';
                firstItem.financialProjectId = payload.financialProjectId || '';
                if (report.balance > 0) {
                    firstItem.credit = app.round(Math.abs(report.balance ?? 0), 'currency');

                    if (report.currencyId !== company.currencyId) {
                        firstItem.creditFC = app.round(Math.abs(report.balanceFC ?? 0), 'currency');
                    }
                } else {
                    firstItem.debit = app.round(Math.abs(report.balance ?? 0), 'currency');

                    if (report.currencyId !== company.currencyId) {
                        firstItem.debitFC = app.round(Math.abs(report.balanceFC ?? 0), 'currency');
                    }
                }
                entry.items.push(firstItem);

                if (report.currencyId !== company.currencyId) {
                    if (!_.isPlainObject(totalsMap[report.currencyId])) {
                        totalsMap[report.currencyId] = {balance: 0, balanceFC: 0};
                    }

                    totalsMap[report.currencyId].balance += app.round(report.balance ?? 0, 'currency');
                    totalsMap[report.currencyId].balanceFC += app.round(report.balanceFC ?? 0, 'currency');
                } else {
                    if (!_.isFinite(totalsMap[company.currencyId])) {
                        totalsMap[company.currencyId] = 0;
                    }

                    totalsMap[company.currencyId] += app.round(report.balance ?? 0, 'currency');
                }
            }

            for (const currencyId of Object.keys(totalsMap)) {
                const total = totalsMap[currencyId];

                const secondItem = {};
                secondItem.accountId = destinationPartner.accountingAccountId;
                secondItem.description = payload.description;
                secondItem.partnerId = destinationPartner._id;
                secondItem.branchId = payload.branchId;
                secondItem.currencyId = currencyId;
                secondItem.scope = payload.scope || '1';
                secondItem.financialProjectId = payload.financialProjectId || '';

                if (currencyId !== company.currencyId) {
                    const balance = total.balance;
                    const balanceFC = total.balanceFC;

                    if (balance < 0) {
                        secondItem.credit = Math.abs(balance);
                        secondItem.creditFC = Math.abs(balanceFC);
                    } else {
                        secondItem.debit = Math.abs(balance);
                        secondItem.debitFC = Math.abs(balanceFC);
                    }
                } else {
                    const balance = total;

                    if (balance < 0) {
                        secondItem.credit = Math.abs(balance);
                    } else {
                        secondItem.debit = Math.abs(balance);
                    }
                }

                entry.items.push(secondItem);
            }
        } else if (template.type === 'account') {
            const $match = {$and: []};
            $match.$and.push({
                'period.fiscalYear': fiscalYear.fiscalYear,
                accountId: {
                    $in: _.uniq(
                        template.accounts.filter(item => !!item.sourceAccountId).map(item => item.sourceAccountId)
                    )
                }
            });
            if (!!payload.startPeriod) {
                const periods = _.orderBy(fiscalYear.periods ?? [], ['issueDateStart'], ['asc']);
                const period = periods.find(p => p.code === payload.startPeriod);

                if (!!period) {
                    $match.$and.push({issueDate: {$gte: period.issueDateStart}});
                }
            }
            if (!!payload.endPeriod) {
                const periods = _.orderBy(fiscalYear.periods ?? [], ['issueDateEnd'], ['desc']);
                const period = periods.find(p => p.code === payload.endPeriod);

                if (!!period) {
                    $match.$and.push({
                        issueDate: {$lte: app.datetime.fromJSDate(period.issueDateEnd).endOf('day').toJSDate()}
                    });
                }
            }
            if (payload.scope === '1' || payload.scope === '2') {
                $match.$and.push({scope: payload.scope});
            }
            if (!!payload.branchBased) {
                $match.$and.push({branchId: payload.branchId});
            }
            const report = await app.collection('accounting.transactions').aggregate([
                {$match},
                {
                    $group: {
                        _id: {currencyId: '$currencyId', accountId: '$accountId'},
                        balance: {$sum: '$balance'},
                        balanceFC: {$sum: '$balanceFC'}
                    }
                },
                {
                    $project: {
                        _id: 0,
                        currencyId: '$_id.currencyId',
                        accountId: '$_id.accountId',
                        balance: 1,
                        balanceFC: 1
                    }
                }
            ]);
            const currencyGrouped = _.groupBy(report, 'currencyId');

            const accounts = await app.collection('kernel.accounts').find({
                $select: ['_id', 'code', 'currencyId']
            });
            const accountsMap = _.keyBy(accounts, '_id');

            for (const currencyId of Object.keys(currencyGrouped)) {
                const accountsReport = currencyGrouped[currencyId];

                for (const templateItem of template.accounts) {
                    if (!templateItem.sourceAccountId) continue;
                    if (!templateItem.sourceDebitOrCredit) continue;
                    const account = accountsMap[templateItem.sourceAccountId];
                    if (!account) {
                        continue;
                    }
                    const accountCurrencyId = account.currencyId ?? company.currencyId;
                    const amount = app.round(
                        Math.abs(
                            ((accountsReport ?? []).find(item => item.accountId === templateItem.sourceAccountId) ?? {})
                                .balance ?? 0
                        ),
                        'currency'
                    );
                    const amountFC = app.round(
                        Math.abs(
                            ((accountsReport ?? []).find(item => item.accountId === templateItem.sourceAccountId) ?? {})
                                .balanceFC ?? 0
                        ),
                        'currency'
                    );
                    if (amount === 0) continue;

                    let journalItem = {};
                    journalItem.accountId = templateItem.sourceAccountId;
                    journalItem.description = payload.description;
                    journalItem.branchId = payload.branchId;
                    journalItem.currencyId = accountCurrencyId;
                    journalItem.scope = payload.scope || '1';
                    journalItem.financialProjectId = payload.financialProjectId || '';

                    if (templateItem.sourceDebitOrCredit === 'debit') {
                        journalItem.debit = amount;

                        if (accountCurrencyId !== company.currencyId) {
                            journalItem.debitFC = amountFC;
                        }
                    } else {
                        journalItem.credit = amount;

                        if (accountCurrencyId !== company.currencyId) {
                            journalItem.creditFC = amountFC;
                        }
                    }

                    journalItem.finalGroupKey = `${journalItem.accountId}|${journalItem.currencyId}|${
                        templateItem.sourceDebitOrCredit === 'debit' ? 'debit' : 'credit'
                    }`;

                    entry.items.push(journalItem);
                }

                const grouped = _.groupBy(
                    template.accounts.map(item => {
                        item.key = `${item.destinationDebitOrCredit}|${item.destinationAccountId}`;

                        return item;
                    }),
                    'key'
                );
                for (const key of Object.keys(grouped)) {
                    const [destinationDebitOrCredit, destinationAccountId] = key.split('|');

                    const account = accountsMap[destinationAccountId];
                    if (!account) {
                        continue;
                    }
                    const accountCurrencyId = account.currencyId ?? company.currencyId;

                    let amount = 0;
                    for (const templateItem of template.accounts.filter(
                        item => item.destinationAccountId === destinationAccountId
                    )) {
                        if (!templateItem.sourceAccountId) continue;
                        if (!templateItem.sourceDebitOrCredit) continue;

                        amount += app.round(
                            Math.abs(
                                (
                                    (accountsReport ?? []).find(
                                        item => item.accountId === templateItem.sourceAccountId
                                    ) ?? {}
                                ).balance ?? 0
                            ),
                            'currency'
                        );
                    }
                    if (amount === 0) continue;

                    let amountFC = 0;
                    for (const templateItem of template.accounts.filter(
                        item => item.destinationAccountId === destinationAccountId
                    )) {
                        if (!templateItem.sourceAccountId) continue;
                        if (!templateItem.sourceDebitOrCredit) continue;

                        amountFC += app.round(
                            Math.abs(
                                (
                                    (accountsReport ?? []).find(
                                        item => item.accountId === templateItem.sourceAccountId
                                    ) ?? {}
                                ).balanceFC ?? 0
                            ),
                            'currency'
                        );
                    }

                    let journalItem = {};
                    journalItem.accountId = destinationAccountId;
                    journalItem.description = payload.description;
                    journalItem.branchId = payload.branchId;
                    journalItem.currencyId = accountCurrencyId;
                    journalItem.scope = payload.scope || '1';
                    journalItem.financialProjectId = payload.financialProjectId || '';

                    if (destinationDebitOrCredit === 'debit') {
                        journalItem.debit = amount;

                        if (accountCurrencyId !== company.currencyId) {
                            journalItem.debitFC = amountFC;
                        }
                    } else {
                        journalItem.credit = amount;

                        if (accountCurrencyId !== company.currencyId) {
                            journalItem.creditFC = amountFC;
                        }
                    }

                    journalItem.finalGroupKey = `${journalItem.accountId}|${journalItem.currencyId}|${
                        destinationDebitOrCredit === 'debit' ? 'debit' : 'credit'
                    }`;

                    entry.items.push(journalItem);
                }
            }

            const finalGrouped = _.groupBy(fastCopy(entry.items), 'finalGroupKey');
            const finalItems = [];
            for (const key of Object.keys(finalGrouped)) {
                const [accountId, currencyId, debitOrCredit] = key.split('|');
                const groupedItems = finalGrouped[key];
                const itemTemplate = fastCopy(groupedItems[0]);
                const item = {...itemTemplate};

                item.accountId = accountId;
                item.currencyId = currencyId;
                item.debit = 0;
                item.credit = 0;
                item.debitFC = 0;
                item.creditFC = 0;

                if (debitOrCredit === 'debit') {
                    item.debit = _.sumBy(groupedItems, 'debit');

                    if (currencyId !== company.currencyId) {
                        item.debitFC = _.sumBy(groupedItems, 'debitFC');
                    }
                } else {
                    item.credit = _.sumBy(groupedItems, 'credit');

                    if (currencyId !== company.currencyId) {
                        item.creditFC = _.sumBy(groupedItems, 'creditFC');
                    }
                }

                finalItems.push(item);
            }

            entry.items = finalItems;
        }

        const journalEntry = await app.rpc('accounting.save-journal-entry', {data: entry}, {user: params.user});

        return journalEntry._id;
    }
};
