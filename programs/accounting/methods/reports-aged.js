import _ from 'lodash';

export default [
    {
        name: 'reports-aged-get-result',
        async action(payload, params) {
            const app = this.app;
            const {
                type,
                partnerGroupIds,
                currencyName,
                groupBySalesperson,
                startDate,
                endDate,
                branchId,
                search,
                limit,
                skip,
                sort
            } = payload;
            const db = app.reportDb;
            // const company = await app.collection('kernel.company').findOne({});
            const now = app.datetime.local();
            const startOfDay = now.startOf('day');
            // const isForeignCurrency = company.currency.name !== currencyName;
            const getISO = date => date.toISO({includeOffset: false, suppressMilliseconds: true});
            const result = {
                data: [],
                total: 0,
                summary: {
                    rowCount: 0
                }
            };
            const accountingAccountIds = await app.collection('kernel.partners').distinct('accountingAccountId', {
                type: type === 'payments' ? 'vendor' : 'customer'
            });
            const additionalAllowanceAccountIds = await app
                .collection('kernel.partners')
                .distinct('additionalAllowanceAccount', {
                    type: type === 'payments' ? 'vendor' : 'customer'
                });
            const accountIds = _.uniq(
                accountingAccountIds.concat(additionalAllowanceAccountIds).filter(accountId => !!accountId)
            );

            let partnerGroupSQL = '';
            if (Array.isArray(partnerGroupIds) && partnerGroupIds.length > 0) {
                partnerGroupSQL = `i.partner_group_id IN (${partnerGroupIds.map(id => `'${id}'`).join(', ')})`;
            }
            let searchSQL = '';
            if (!!search) {
                if (groupBySalesperson) {
                    searchSQL = `(i.salesperson_code ILIKE '%${search}%' OR i.salesperson_name ILIKE '%${search}%')`;
                } else {
                    searchSQL = `(i.partner_code ILIKE '%${search}%' OR i.partner_name ILIKE '%${search}%' OR i.partner_group ILIKE '%${search}%')`;
                }
            }
            const rawItemsSQL = `
SELECT
    ${
        groupBySalesperson
            ? `
    CONCAT(i.salesperson_code, i.salesperson_name) AS "_id",
    i.salesperson_code AS "salespersonCode",
    i.salesperson_name AS "salespersonName",
        `.trim()
            : `
    CONCAT(i.partner_code, i.partner_group) AS "_id",
    i.partner_code AS "partnerCode",
    i.partner_name AS "partnerName",
    i.partner_group AS "partnerGroup",
        `.trim()
    }
    '${currencyName}' AS "currencyName",
    SUM(
	    CASE 
	        WHEN i.due_date < '${getISO(startOfDay.minus({days: 1}).endOf('day'))}' THEN i.reconciliation_balance
	        ELSE 0
	    END
	) AS "pastDueTotal",
	SUM(
	    CASE 
	        WHEN i.due_date >= '${getISO(startOfDay)}' AND i.due_date <= '${startOfDay
                .plus({days: 30})
                .endOf('day')}' THEN i.reconciliation_balance
	        ELSE 0
	    END
	) AS "period_0_30",
	SUM(
	    CASE 
	        WHEN i.due_date >= '${getISO(startOfDay.plus({days: 31}))}' AND i.due_date <= '${startOfDay
                .plus({days: 60})
                .endOf('day')}' THEN i.reconciliation_balance
	        ELSE 0
	    END
	) AS "period_31_60",
	SUM(
	    CASE 
	        WHEN i.due_date >= '${getISO(startOfDay.plus({days: 61}))}' AND i.due_date <= '${startOfDay
                .plus({days: 90})
                .endOf('day')}' THEN i.reconciliation_balance
	        ELSE 0
	    END
	) AS "period_61_90",
	SUM(
	    CASE 
	        WHEN i.due_date >= '${getISO(startOfDay.plus({days: 91}))}' AND i.due_date <= '${startOfDay
                .plus({days: 120})
                .endOf('day')}' THEN i.reconciliation_balance
	        ELSE 0
	    END
	) AS "period_91_120",
	SUM(
	    CASE 
	        WHEN i.due_date >= '${getISO(startOfDay.plus({days: 121}))}' AND i.due_date <= '${startOfDay
                .plus({days: 150})
                .endOf('day')}' THEN i.reconciliation_balance
	        ELSE 0
	    END
	) AS "period_121_150",
	SUM(
	    CASE 
	        WHEN i.due_date >= '${getISO(startOfDay.plus({days: 151}))}' AND i.due_date <= '${startOfDay
                .plus({days: 180})
                .endOf('day')}' THEN i.reconciliation_balance
	        ELSE 0
	    END
	) AS "period_151_180",
	SUM(
	    CASE 
	        WHEN i.due_date >= '${getISO(startOfDay.plus({days: 181}))}' THEN i.reconciliation_balance
	        ELSE 0
	    END
	) AS "period_181+",
	SUM(i.reconciliation_balance) AS "total"
FROM accounting_records i
WHERE
	i.reconciliation_balance != 0 AND i.account_id IN (${accountIds.map(accountId => `'${accountId}'`).join(', ')}) ${
                !!partnerGroupSQL ? ` AND ${partnerGroupSQL}` : ''
            }${!!searchSQL ? ` AND ${searchSQL}` : ''} ${
                _.isDate(startDate) ? ` AND i.due_date >= '${app.datetime.fromJSDate(startDate).toISO()}'` : ''
            } ${_.isDate(endDate) ? ` AND i.issue_date <= '${app.datetime.fromJSDate(endDate).toISO()}'` : ''} ${
                _.isString(branchId) && branchId.length > 0 ? ` AND i.branch_id = '${branchId}'` : ''
            }
GROUP BY 
    ${
        groupBySalesperson
            ? `
    i.salesperson_code,
    i.salesperson_name
    `.trim()
            : `
    i.partner_code,
    i.partner_name,
    i.partner_group
    `.trim()
    }
`.trim();
            const qb = db.with('aged_items', db.raw(rawItemsSQL)).from('aged_items');
            const summaryQb = db.with('aged_items_summary', db.raw(rawItemsSQL)).from('aged_items_summary');

            if (_.isPlainObject(sort) && !_.isEmpty(sort)) {
                const field = Object.keys(sort)[0];
                const direction = Object.values(sort)[0];

                qb.orderBy(field, direction === -1 ? 'desc' : 'asc');
            } else {
                if (groupBySalesperson) {
                    qb.orderBy('salespersonCode');
                } else {
                    qb.orderBy('partnerCode');
                }
            }

            let items = await qb.limit(limit).offset(skip);
            if (!groupBySalesperson) {
                const partnerCodes = _.uniq(items.map(item => item.partnerCode));
                const partners = await app.collection('kernel.partners').find({
                    code: {$in: partnerCodes},
                    $select: ['_id', 'code', 'paymentTermId']
                });
                const paymentTermIds = _.uniq(partners.map(partner => partner.paymentTermId));
                const paymentTerms = await app.collection('finance.payment-terms').find({
                    _id: {$in: paymentTermIds},
                    $select: ['_id', 'code', 'name']
                });

                items = items.map(item => {
                    if (!!item.partnerCode) {
                        const partner = partners.find(partner => partner.code === item.partnerCode);

                        if (!!partner && !!partner.paymentTermId) {
                            const paymentTerm = paymentTerms.find(
                                paymentTerm => paymentTerm._id === partner.paymentTermId
                            );

                            if (!!paymentTerm) {
                                item.paymentTermCode = paymentTerm.code;
                                item.paymentTermName = paymentTerm.name;
                            }
                        }
                    }

                    return item;
                });
            }

            result.data = items.map(item => {
                if (groupBySalesperson) {
                    item._id = item.salespersonCode + item.salespersonName;
                } else {
                    item._id = item.partnerCode + item.partnerGroup;
                }

                if (item['pastDueTotal'] === 0) delete item['pastDueTotal'];
                if (item['period_0_30'] === 0) delete item['period_0_30'];
                if (item['period_31_60'] === 0) delete item['period_31_60'];
                if (item['period_61_90'] === 0) delete item['period_61_90'];
                if (item['period_91_120'] === 0) delete item['period_91_120'];
                if (item['period_121_150'] === 0) delete item['period_121_150'];
                if (item['period_151_180'] === 0) delete item['period_151_180'];
                if (item['period_181+'] === 0) delete item['period_181+'];
                if (item['total'] === 0) delete item['total'];

                return item;
            });

            const summaryResult = await summaryQb.select(
                db.raw('COUNT(*) AS "rowCount"'),
                db.raw('SUM("pastDueTotal") AS "pastDueTotal"'),
                db.raw('SUM("period_0_30") AS "period_0_30"'),
                db.raw('SUM("period_31_60") AS "period_31_60"'),
                db.raw('SUM("period_61_90") AS "period_61_90"'),
                db.raw('SUM("period_91_120") AS "period_91_120"'),
                db.raw('SUM("period_121_150") AS "period_121_150"'),
                db.raw('SUM("period_151_180") AS "period_151_180"'),
                db.raw('SUM("period_181+") AS "period_181+"'),
                db.raw('SUM("total") AS "total"')
            );
            if (summaryResult.length > 0) {
                const summary = summaryResult[0];

                summary.rowCount = parseInt(summary.rowCount);
                summary.currencyName = currencyName;

                result.summary = summary;
            }

            result.total = result.summary.rowCount;

            return result;
        }
    }
];
