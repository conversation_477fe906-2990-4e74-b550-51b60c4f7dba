<template>
    <ui-view
        ref="view"
        type="form"
        collection="inventory.transfers"
        method="inventory.transfers-save-transfer"
        progress-id="inventory.transfers-detail"
        :class="{
            'inventory-transfers-detail-form': true,
            'no-stages': stages.length < 1
        }"
        :model="model"
        :schema="schema"
        :title="title"
        :extra-fields="extraFields"
        :before-init="beforeInit"
        :before-validate="beforeValidate"
        :before-submit="beforeSubmit"
        :after-submit="afterSubmit"
        :print-payload="printPayload"
        :actions="actions"
        :extra-actions="extraActions"
        @changed="handleChange"
        v-if="isInitialized"
    >
        <template slot="form-top">
            <ui-status :statuses="statuses" :value="status">
                <div class="transfer-integration-container" v-show="isIntegrationTransfer">
                    <div class="transfer-integration">
                        <span>{{ 'Integration Transfer' | t }}</span>
                    </div>
                </div>
            </ui-status>

            <integration-transfer-status :status="integrationTransferStatus" />

            <el-badge
                :value="model.integrationErrorCount"
                v-if="model.integrationErrorCount > 0 && isIntegrationTransfer"
            >
                <el-button plain @click="handleOpenLogs">
                    {{ 'Errors' | t }}
                </el-button>
            </el-badge>

            <ui-related-documents :documents="finalRelatedDocuments" />

            <system-components-print-labels-btn
                ref="printLabelBtn"
                :target-document="printLabelsTargetDocument"
                v-show="!!$app.hasModule('health')"
            />

            <el-dropdown @command="handlePrintAction" trigger="click" placement="bottom-start">
                <el-button size="mini" icon="far fa-print" plain>
                    {{ 'Print' | t }}<i class="el-icon-arrow-down el-icon--right"></i>
                </el-button>
                <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item
                        command="waybill"
                        :disabled="
                            !(
                                !!$params('isPreview') &&
                                model.shippingDocumentType === 'waybill' &&
                                (operationType.type === 'outgoing' ||
                                    (operationType.type === 'incoming' && model.documentType === 'return-transfers'))
                            )
                        "
                    >
                        {{ 'Waybill' | t }}
                    </el-dropdown-item>
                    <el-dropdown-item
                        command="label"
                        :disabled="!$params('isPreview') || !$params('id') || !printLabelsTargetDocument"
                    >
                        {{ 'Label' | t }}
                    </el-dropdown-item>
                    <el-dropdown-item command="bill-of-lading" :disabled="!$params('isPreview')">
                        {{ 'Bill Of Lading' | t }}
                    </el-dropdown-item>
                    <el-dropdown-item command="check-list" :disabled="!$params('isPreview')">
                        {{ 'Check List' | t }}
                    </el-dropdown-item>
                    <el-dropdown-item command="items" :disabled="!$params('isPreview')">
                        {{ 'Items' | t }}
                    </el-dropdown-item>

                    <el-dropdown-item
                        command="product-locations"
                        :disabled="!$params('isPreview')"
                        v-if="!!operationType && operationType.type === 'outgoing'"
                    >
                        {{ 'Product Locations' | t }}
                    </el-dropdown-item>
                </el-dropdown-menu>
            </el-dropdown>

            <el-button
                :loading="$params('loading')"
                type="primary"
                icon="far fa-truck-container"
                :disabled="!$params('isPreview')"
                v-if="canCreateShippingOrder"
                @click="handleCreateShippingOrder"
            >
                {{ $t('Create Shipping Order') }}
            </el-button>

            <el-button
                :loading="$params('loading')"
                type="primary"
                icon="far fa-thumbs-up"
                :disabled="
                    !$params('isPreview') ||
                    !$params('id') ||
                    status === 'approved' ||
                    status === 'canceled' ||
                    $params('loading') ||
                    (isIntegrationTransfer &&
                        integrationTransferType === 'sale-order' &&
                        (integrationTransferStatus === 'failed' || integrationTransferStatus === 'canceled'))
                "
                @click="handleApprove"
            >
                {{ 'Approve Transfer' | t }}
            </el-button>

            <el-button
                :loading="$params('loading')"
                type="success"
                icon="far fa-paper-plane"
                :disabled="!$params('isPreview') || isSendEWaybillDisabled || $params('loading')"
                v-show="isSendEWaybillShown"
                @click="handleSendEWaybill"
            >
                {{ 'Send E-Waybill' | t }}
            </el-button>
        </template>

        <div class="transfer-header" v-show="stages.length > 0">
            <div class="transfer-probability">
                <el-progress type="circle" :percentage="model.probability || 0" :stroke-width="4" :width="40" />
            </div>

            <el-steps
                class="transfer-steps"
                :class="{'is-disabled': $params('isPreview')}"
                :active="activeStageIndex"
                align-center
                finish-status="success"
                v-show="stepStages && stepStages.length > 0"
            >
                <el-step v-for="(stage, index) in stepStages" :key="index" :title="stage.name" :data-index="index" />
            </el-steps>
        </div>

        <div class="columns">
            <div class="column">
                <ui-field name="code" disabled />
                <ui-field
                    name="documentType"
                    :options="documentTypeOptions"
                    translate-labels
                    :disabled="model.isLocked || !!$params('model.documentType')"
                />
                <ui-field name="shippingDocumentType" :options="shippingDocumentTypeOptions" translate-labels />
                <ui-field name="operationTypeId" :options="operationTypeOptions" :disabled="!model.documentType" />
                <ui-field
                    name="sourceLocationId"
                    :key="sourceLocationIdKey"
                    v-bind="sourceLocationIdSettings"
                    :disabled="model.isLocked"
                    v-show="$setting('inventory.storageLocations')"
                />
                <ui-field
                    name="destinationLocationId"
                    :key="destinationLocationIdKey"
                    v-bind="destinationLocationIdSettings"
                    :disabled="model.isLocked"
                    v-show="$setting('inventory.storageLocations')"
                />
                <ui-field
                    name="partnerId"
                    collection="kernel.partners"
                    view="partners.partners"
                    :extra-fields="['code']"
                    :template="'{{code}} - {{name}}'"
                    :filters="partnerIdFilters"
                    v-show="operationType.type && operationType.type !== 'internal'"
                />
                <ui-field name="reference" v-show="!!model.reference" />
                <ui-field
                    name="stageId"
                    :options="stageIdOptions"
                    :html-template="stageIdOptionsTemplate"
                    v-show="stages.length > 0"
                />
            </div>
            <div class="column">
                <kernel-common-branch-select :disabled="model.isLocked" />
                <ui-field
                    name="organizationId"
                    collection="kernel.organizations"
                    :extra-fields="['code']"
                    :template="'{{ code }} - {{ name }}'"
                    disabled
                    v-show="$setting('inventory.inventoryOrganizations')"
                />
                <div class="ui-inline-fields" v-show="$setting('system.multiCurrency')">
                    <div class="field-label">{{ $t('Currency') }}</div>
                    <div class="field-content">
                        <ui-field
                            name="currencyId"
                            collection="kernel.currencies"
                            label="hide"
                            :style="{flex: '1 1 0'}"
                        />
                        <ui-field
                            name="currencyRate"
                            label="hide"
                            :style="{flex: '1 1 0'}"
                            :precision="$setting('system.exchangeRatePrecision')"
                            :disabled="model.currencyId === systemCurrencyId"
                        />
                    </div>
                </div>
                <ui-field name="recordDate" />
                <ui-field name="issueDate" />
                <ui-field name="dueDate" v-show="false" />
                <ui-field name="scheduledDate" />
                <div style="display: flex; max-width: 450px">
                    <ui-field name="documentNo" style="flex: 1 1 0" />

                    <el-button
                        class="mb5 ml5"
                        style="flex: 0 0 25px"
                        icon="far fa-pencil"
                        :title="'Change Document No' | t"
                        v-show="status === 'approved'"
                        @click="handleChangeDocumentNo"
                    />
                </div>
            </div>
        </div>

        <el-tabs v-model="activeTab">
            <el-tab-pane name="items" :label="'Items' | t">
                <ui-field
                    name="items"
                    :id="itemsId"
                    :key="itemsKey"
                    class="mb0"
                    :min-empty-rows="!!model.isLocked ? 0 : 3"
                    :before-init="beforeItemsInit"
                    :before-create="beforeSaveItem"
                    :before-update="beforeSaveItem"
                    :after-create="afterSaveItem"
                    :after-update="afterSaveItem"
                    :after-remove="afterRemoveItem"
                    :context-menu-actions="itemsContextMenuActions"
                    :enable-enlarge="true"
                    resizable
                    :enable-add-remove="!model.isLocked"
                    :table-options="tableOptions"
                    :disabled="!operationType.type || !model.sourceLocationId || !model.destinationLocationId"
                    @selected="handleSelectItems"
                    v-show="modelInitialized"
                >
                    <template slot="actions">
                        <el-tooltip effect="dark" :content="'Check Availability' | t" placement="bottom">
                            <el-button
                                :loading="$params('loading')"
                                icon="far fa-check-circle"
                                :disabled="
                                    (model.items || []).length < 1 ||
                                    !(status === 'draft' || status === 'waiting' || status === 'ready') ||
                                    $params('isPreview')
                                "
                                @click="handleCheckItemsAvailability"
                            />
                        </el-tooltip>

                        <el-tooltip effect="dark" :content="'Transfer Item Label' | t" placement="bottom">
                            <el-button
                                :loading="$params('loading')"
                                icon="far fa-print"
                                :disabled="(selectedItems || []).length < 1 || !$params('isPreview')"
                                @click="handlePrintItemLabel"
                            />
                        </el-tooltip>

                        <el-button
                            :loading="$params('loading')"
                            plain
                            icon="far fa-layer-plus"
                            :disabled="
                                $params('isPreview') ||
                                !operationType.type ||
                                !model.sourceLocationId ||
                                !model.destinationLocationId ||
                                !!model.isLocked
                            "
                            @click="handleAddMultipleProducts"
                        >
                            {{ 'Add Multiple Products' | t }}
                        </el-button>

                        <el-button
                            :loading="$params('loading')"
                            plain
                            icon="far fa-layer-plus"
                            :disabled="
                                $params('isPreview') ||
                                !operationType.type ||
                                !model.sourceLocationId ||
                                !model.destinationLocationId ||
                                !!model.isLocked
                            "
                            @click="handleAddKitProducts"
                        >
                            {{ 'Add Kit Products' | t }}
                        </el-button>

                        <el-tooltip
                            effect="dark"
                            :content="'Import Items' | t"
                            placement="bottom"
                            v-show="!$params('isPreview')"
                        >
                            <el-uploader
                                accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                                @started="$params('loading', true)"
                                @completed="handleImportItems"
                                :disabled="$params('isPreview')"
                            >
                                <el-button
                                    :loading="$params('loading')"
                                    icon="far fa-cloud-upload"
                                    :disabled="$params('isPreview')"
                                />
                            </el-uploader>
                        </el-tooltip>

                        <el-button
                            :loading="$params('loading')"
                            icon="far fa-inventory"
                            style="float: right"
                            class="ml5"
                            :disabled="
                                !operationType.type ||
                                !model.sourceLocationId ||
                                !model.destinationLocationId ||
                                $params('isPreview')
                            "
                            @click="handleBulkEntry"
                            v-if="
                                $setting('inventory.useBulkEntryInTransfers') &&
                                $setting('inventory.storageLocations') &&
                                !!$params('id') &&
                                !hasSubItems
                            "
                        >
                            {{ 'Bulk Entry' | t }}
                        </el-button>
                        <!--                        <el-button-->
                        <!--                            :loading="$params('loading')"-->
                        <!--                            icon="far fa-box-alt"-->
                        <!--                            style="float: right"-->
                        <!--                            class="ml5"-->
                        <!--                            :disabled="-->
                        <!--                                !$params('id') ||-->
                        <!--                                !operationType.type ||-->
                        <!--                                !model.sourceLocationId ||-->
                        <!--                                !model.destinationLocationId ||-->
                        <!--                                (!!model.isLocked && (model.items || []).length < 1)-->
                        <!--                            "-->
                        <!--                            @click="handlePack"-->
                        <!--                        >-->
                        <!--                            {{ 'Pack' | t }}-->
                        <!--                        </el-button>-->
                        <el-button
                            :loading="$params('loading')"
                            icon="far fa-barcode"
                            style="float: right"
                            class="ml5"
                            :disabled="
                                $params('isPreview') ||
                                !operationType.type ||
                                !model.sourceLocationId ||
                                !model.destinationLocationId ||
                                (!!model.isLocked && (model.items || []).length < 1)
                            "
                            @click="handleBarcodeEntry"
                            v-if="!$app.setting('inventory.useBulkEntryInTransfers') || hasSubItems"
                        >
                            {{ 'Barcode Entry' | t }}
                        </el-button>
                    </template>
                </ui-field>

                <ui-totals
                    :totals="totalItems"
                    v-show="$checkPermission({type: 'permission', name: 'system.canSeeCosts'})"
                >
                    <ui-legend title="Exchange Rates" class="mb0" />
                    <ui-field
                        name="exchangeRates"
                        class="mb0"
                        style="max-width: 450px"
                        :key="model.grandTotal"
                        :min-empty-rows="0"
                        :enable-add-remove="false"
                        :enable-row-handle="false"
                    />
                </ui-totals>
            </el-tab-pane>

            <el-tab-pane name="details" :label="$t('Details')">
                <div class="columns mt10">
                    <div class="column is-half">
                        <ui-field name="partnerOrderReference" />
                        <ui-field name="partnerOrderDate" />
                    </div>

                    <div class="column is-half">
                        <ui-field
                            :key="partnerType"
                            name="priceSourceId"
                            v-bind="priceSourceIdOptions"
                            :disabled="!model.partnerId"
                            v-show="!!partnerType"
                        />
                    </div>
                </div>
            </el-tab-pane>

            <el-tab-pane name="stage-history" :label="'Stage History' | t" :disabled="stages.length < 1">
                <ui-table
                    :items="model.stageHistory || []"
                    :columns="stageHistoryColumns"
                    :enable-auto-height="true"
                    :enable-selection="false"
                />
            </el-tab-pane>

            <el-tab-pane name="logistics" :label="'Logistics' | t">
                <div class="columns mt10">
                    <div class="column is-half">
                        <ui-legend title="Delivery Address" />
                        <ui-field
                            name="deliveryReceiverId"
                            collection="kernel.contacts"
                            view="partners.contacts"
                            :filters="{partnerId: model.partnerId, type: 'contact'}"
                            :update-params="updateContactIdParams"
                            :disabled="!model.partnerId"
                        />
                        <ui-field
                            name="deliveryAddress"
                            field-type="compact-address"
                            :is-preview="$params('isPreview')"
                        >
                            <ui-field
                                name="deliveryAddressId"
                                label="hide"
                                collection="kernel.contacts"
                                view="partners.contacts"
                                :filters="{partnerId: model.partnerId, type: 'delivery-address'}"
                                :update-params="updateDeliveryAddressSelectParams"
                                :disabled="!model.partnerId"
                            />
                        </ui-field>
                        <ui-field name="deliveryAddressCode" />

                        <ui-legend title="Shipment Note" class="mt30" />
                        <ui-field name="shipmentNote" label="hide" :rows="3" />
                    </div>

                    <div class="column is-half">
                        <ui-legend title="Delivery Information" />
                        <ui-field name="deliveryPriority" :options="deliveryPriorityOptions" translate-labels />
                        <ui-field name="deliveryPolicy" :options="deliveryPolicyOptions" translate-labels />
                        <ui-field
                            name="deliveryConditionId"
                            collection="logistics.delivery-conditions"
                            view="logistics.configuration.delivery-conditions"
                            :extra-fields="['code']"
                            :template="'{{code}} - {{name}}'"
                            disable-detail
                            disable-create
                        />
                        <ui-field
                            name="deliveryMethodId"
                            collection="logistics.delivery-methods"
                            view="logistics.configuration.delivery-methods"
                            :extra-fields="['code']"
                            :template="'{{code}} - {{name}}'"
                            disable-detail
                            disable-create
                        />
                        <ui-field name="deliveryNote" :rows="3" />
                        <ui-field name="imoAndMmsiNo" v-show="deliveryMethodType === 'seaway'" />
                        <ui-field name="shipName" v-show="deliveryMethodType === 'seaway'" />
                        <ui-field name="shipRadioCallName" v-show="deliveryMethodType === 'seaway'" />
                        <ui-field name="shipRegistrationName" v-show="deliveryMethodType === 'seaway'" />
                        <ui-field name="shipNetWeight" v-show="deliveryMethodType === 'seaway'" />
                        <ui-field name="shipGrossWeight" v-show="deliveryMethodType === 'seaway'" />
                        <ui-field name="shipRequirements" v-show="deliveryMethodType === 'seaway'" />
                        <ui-field name="shipPortOfRegistration" v-show="deliveryMethodType === 'seaway'" />
                        <ui-field name="trainNo" v-show="deliveryMethodType === 'railroad'" />
                        <ui-field name="trainWagonNo" v-show="deliveryMethodType === 'railroad'" />
                        <ui-field name="licensePlateNo" v-show="deliveryMethodType === 'land-route'" />
                        <ui-field name="aircraftNo" v-show="deliveryMethodType === 'airline'" />
                        <ui-field
                            name="carrierId"
                            collection="logistics.carriers"
                            view="logistics.configuration.carriers"
                            :extra-fields="['code']"
                            :template="'{{code}} - {{name}}'"
                            disable-detail
                            disable-create
                        />
                        <ui-field name="shippingPaymentType" :options="shippingPaymentTypeOptions" translate-labels />
                        <ui-field name="cargoTrackingCode" />
                        <ui-field name="typeOfGoods" />
                        <ui-field name="loadingPort" />
                        <ui-field name="portOfDestination" />
                        <div style="display: flex; max-width: 450px">
                            <ui-field name="customsDeclarationNo" style="flex: 1 1 0" />

                            <el-button
                                class="mb5 ml5"
                                style="flex: 0 0 25px"
                                icon="far fa-pencil"
                                :title="'Change Customs Declaration No' | t"
                                v-show="status === 'approved'"
                                @click="handleCustomsDeclarationNo"
                            />
                        </div>
                        <ui-field name="customsConsultancyFirm" />
                        <ui-field name="customsConsultancyContactPerson" />
                        <ui-field name="transportationInsurance" />
                        <ui-field name="costOfGoods" v-show="!!model.transportationInsurance" />
                    </div>
                </div>
            </el-tab-pane>

            <el-tab-pane name="accounting" :label="'Accounting' | t">
                <div class="columns mt10">
                    <div class="column is-half">
                        <ui-legend title="General Information" />
                        <div style="display: flex; max-width: 450px">
                            <ui-field
                                name="financialProjectId"
                                collection="kernel.financial-projects"
                                view="system.management.configuration.financial-projects"
                                disable-detail
                                disable-create
                                :extra-fields="['code']"
                                :template="'{{code}} - {{name}}'"
                                style="flex: 1 1 0"
                            />
                            <el-button
                                class="mb5 ml5"
                                style="flex: 0 0 25px"
                                icon="far fa-check"
                                :title="'Apply To All' | t"
                                :disabled="$params('isPreview')"
                                @click="handleApplyFinancialProject"
                            />
                        </div>
                        <ui-field name="journalId" collection="accounting.journals" :filters="journalIdFilters" />
                        <ui-field name="journalDescription" />
                        <ui-field name="scope" :options="scopeOptions" v-show="$setting('system.scopes')" />
                    </div>

                    <div class="column is-half">
                        <ui-legend title="Invoice Address" />
                        <ui-field
                            name="invoiceResponsibleId"
                            collection="kernel.contacts"
                            view="partners.contacts"
                            :filters="{partnerId: model.partnerId, type: 'contact'}"
                            :update-params="updateContactIdParams"
                            :disabled="!model.partnerId"
                        />
                        <ui-field
                            name="invoiceAddress"
                            field-type="compact-address"
                            :is-preview="$params('isPreview')"
                            :disabled="!model.partnerId"
                        >
                            <ui-field
                                name="invoiceAddressId"
                                label="hide"
                                collection="kernel.contacts"
                                view="partners.contacts"
                                :filters="{partnerId: model.partnerId, type: 'invoice-address'}"
                                :update-params="updateInvoiceAddressSelectParams"
                                :disabled="!model.partnerId"
                            />
                        </ui-field>
                    </div>
                </div>
            </el-tab-pane>

            <el-tab-pane
                name="discoveries-observations"
                :label="$t('Discoveries & Observations')"
                :disabled="!$setting('crm.isDiscoveriesObservationsEnabled') || !$params('id')"
            >
                <crm-components-discoveries-observations-list
                    document="transfer"
                    :id="$params('id')"
                    v-if="activeTab === 'discoveries-observations'"
                />
            </el-tab-pane>

            <el-tab-pane name="transaction-logs" :label="'Transaction Logs' | t" v-if="isIntegrationTransfer">
                <ui-table
                    :items="model.transactions || []"
                    :columns="transactionsColumns"
                    :enable-auto-height="true"
                    :enable-selection="false"
                />
            </el-tab-pane>

            <el-tab-pane name="attachments" :label="'Attachments' | t">
                <ui-field name="attachments" field-type="attachments" auto-height />
            </el-tab-pane>

            <el-tab-pane
                name="additional-information"
                :label="$t('Additional Information')"
                v-if="showAdditionalInformationTab"
            >
                <system-components-additional-information
                    ref="additionalInformationForm"
                    type="inventory-transfer"
                    :wrapper-style="{padding: '20px 0px 0px 0px !important'}"
                    :payload="model.additionalInformation || {}"
                    :is-preview="$params('isPreview')"
                />
            </el-tab-pane>
        </el-tabs>
    </ui-view>
</template>

<script>
import data from './detail/data';
import computed from './detail/computed';
import watch from './detail/watch';
import methods from './detail/methods';
import created from './detail/created';
import IntegrationTransferStatus from './_integration-transfer-status';

export default {
    mixins: [data, computed, watch, methods, created],

    components: {
        IntegrationTransferStatus
    }
};
</script>

<style lang="scss">
//noinspection CssUnknownTarget
@import 'core';

.transfer-integration-container {
    padding-left: 30px;
    display: flex;
    align-items: center;
}

.transfer-integration {
    display: flex;
    align-items: center;
    height: 22px;
    padding: 0 15px;
    border-radius: 11px;
    background-color: $primary-lighter;
    color: $primary;
    border: 1px solid $primary;
    font-size: 10px;
    font-weight: bold;
}

.inventory-transfers-detail-form {
    .ui-form.has-form-top {
        padding-top: 130px;
    }

    &.no-stages .ui-form.has-form-top {
        padding-top: 60px;
    }

    .transfer-header {
        position: absolute;
        display: flex;
        flex-flow: row nowrap;
        top: 40px;
        left: 0;
        width: 100%;
        height: 65px;
        padding-top: 10px;
        border-bottom: 1px solid $border-color-light;
        background-color: #fff;
        overflow: hidden;

        .transfer-steps {
            flex: 1 1 0;

            .el-step__icon {
                width: 20px;
                height: 20px;
            }

            .el-step.is-horizontal .el-step__line {
                height: 1px;
                top: 10px;
            }

            .el-step__head {
                height: 20px !important;
            }

            .el-step__head.is-process {
                color: $primary;
                border-color: $primary;

                .el-step__icon {
                    position: relative;

                    &:after {
                        position: absolute;
                        left: 3px;
                        top: 3px;
                        width: 10px;
                        height: 10px;
                        border-radius: 50%;
                        background-color: $primary;
                        content: ' ';
                    }
                }
            }

            .el-step__head:not(.is-success) {
                .el-step__icon-inner {
                    display: none;
                }
            }

            .el-step__head.is-wait {
                .el-step__icon {
                    border-width: 1px;
                }
            }

            .el-step__title {
                font-size: 10px !important;
                line-height: 28px !important;

                &.is-process {
                    font-weight: $font-weight;
                    color: $primary;
                }
            }

            .el-step__description {
                display: none;
            }

            .el-step__icon-inner {
                font-size: 12px;
            }

            &:not(.is-disabled) .el-step__head:not(.is-process),
            &:not(.is-disabled) .el-step__title:not(.is-process) {
                cursor: pointer;
            }
        }

        .transfer-probability {
            position: relative;
            flex: 0 0 80px;
            padding: 2px 20px 0 20px;

            &:before {
                position: absolute;
                right: 0;
                top: 0;
                width: 1px;
                height: 45px;
                background-color: $border-color-light;
                content: ' ';
            }

            .el-progress--circle .el-progress__text {
                font-size: 9px !important;
            }
        }
    }
}
</style>
