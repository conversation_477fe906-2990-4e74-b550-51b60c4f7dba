import _ from 'lodash';

export default {
    data: () => ({
        model: {},
        extraFields: [
            'code',
            'type',
            'status',
            'branchId',
            'isLocked',
            'referenceId',
            'connectedTransferId',
            'relatedDocuments',
            'createPartialDelivery',
            'hasWaybill',
            'logisticId',
            'packageParams',
            'copiedDocumentRecords',
            'currencyRate',
            'subTotal',
            'discount',
            'discountAmount',
            'subTotalAfterDiscount',
            'freight',
            'grandTotal',
            'workflowApprovalStatus',
            'freight',
            'integrationPayload',
            'additionalInformation',
            'additionalInformationId',
            'probability',
            'stageHistory',
            'stages'
        ],
        modelInitialized: false,
        isInitialized: false,
        activeTab: 'items',
        operationType: {},
        operationTypes: [],
        customerLocationId: '',
        vendorLocationId: '',
        productionLocationId: '',
        projectLocationId: '',
        selectedSourceLocation: null,
        selectedDestinationLocation: null,
        shippingDocumentTypeOptions: [
            {value: 'none', label: 'None'},
            {value: 'waybill', label: 'Waybill'},
            {value: 'invoice', label: 'Invoice'}
        ],
        deliveryPolicyOptions: [
            {value: 'when-one-ready', label: 'When a product is ready'},
            {value: 'when-all-ready', label: 'When all products are ready'}
        ],
        deliveryPriorityOptions: [
            {value: 'not-urgent', label: 'Not urgent'},
            {value: 'normal', label: 'Normal'},
            {value: 'urgent', label: 'Urgent'},
            {value: 'very-urgent', label: 'Very urgent'}
        ],
        shippingPaymentTypeOptions: [
            {value: 'freight-prepaid', label: 'Freight prepaid'},
            {value: 'freight-collect', label: 'Freight collect'}
        ],
        sourceLocationIdKey: _.uniqueId('sourceLocationIdKey_'),
        destinationLocationIdKey: _.uniqueId('destinationLocationIdKey_'),
        itemsKey: _.uniqueId('itemsKey_'),
        deliveryMethodType: null,
        createPartialDelivery: false,
        currencyFormat: {},
        currentCurrencyRate: 1,
        systemCurrencyId: null,
        showAdditionalInformationTab: false,
        relatedDocuments: [],
        stages: [],
        activeStageIndex: null,
        partnerType: null,
        selectedItems: [],
        organizations: [],
        organizationTeam: [],
        organizationSettings: {}
    })
};
