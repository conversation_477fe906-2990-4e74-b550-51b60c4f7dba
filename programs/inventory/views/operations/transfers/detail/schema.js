import _ from 'lodash';

export default function (vm) {
    return {
        code: {
            type: 'string',
            label: 'Code',
            required: false
        },
        documentType: {
            type: 'string',
            label: 'Document type',
            allowed: [
                'delivery-transfers',
                'transport-transfers',
                'return-transfers',
                'production-transfers',
                'project-transfers',
                'service-transfers'
            ],
            default: 'delivery-transfers'
        },
        operationTypeId: {
            type: 'string',
            label: 'Operation type'
        },
        sourceLocationId: {
            type: 'string',
            label: 'Source location',
            required: false
        },
        destinationLocationId: {
            type: 'string',
            label: 'Destination location',
            required: false
        },
        partnerId: {
            type: 'string',
            label: 'Partner',
            required: false
        },
        reference: {
            type: 'string',
            label: 'Reference',
            required: false
        },
        shippingDocumentType: {
            type: 'string',
            label: 'Shipping document type',
            allowed: ['none', 'waybill', 'invoice'],
            default: 'none'
        },
        branchId: {
            type: 'string',
            label: 'Branch office'
        },
        currencyId: {
            type: 'string',
            label: 'Currency'
        },
        currencyRate: {
            type: 'decimal',
            label: 'Currency rate',
            default: 1
        },
        recordDate: {
            type: 'date',
            label: 'Record date',
            default: 'date:now'
        },
        issueDate: {
            type: 'datetime',
            label: 'Issue date',
            default: 'date:now'
        },
        dueDate: {
            type: 'date',
            label: 'Due date',
            default: 'date:now'
        },
        scheduledDate: {
            type: 'date',
            label: 'Scheduled date',
            default: 'date:now'
        },
        documentNo: {
            type: 'string',
            label: 'Document no',
            required: false
        },
        partnerOrderReference: {
            type: 'string',
            label: 'Partner order reference',
            required: false
        },
        partnerOrderDate: {
            type: 'date',
            label: 'Partner order date',
            required: false
        },
        deliveryReceiverId: {
            type: 'string',
            label: 'Delivery receiver',
            required: false
        },
        deliveryAddressId: {
            type: 'string',
            required: false
        },
        deliveryAddressCode: {
            type: 'string',
            label: 'Delivery address code',
            required: false
        },
        deliveryAddress: {
            type: 'object',
            label: 'Delivery address',
            blackbox: true,
            required: false
        },
        deliveryPolicy: {
            type: 'string',
            label: 'Delivery policy',
            allowed: ['when-one-ready', 'when-all-ready'],
            default: 'when-one-ready'
        },
        deliveryPriority: {
            type: 'string',
            label: 'Delivery priority',
            allowed: ['not-urgent', 'normal', 'urgent', 'very-urgent'],
            default: 'normal'
        },
        deliveryConditionId: {
            type: 'string',
            label: 'Delivery condition',
            required: false
        },
        deliveryMethodId: {
            type: 'string',
            label: 'Delivery method',
            required: false
        },
        deliveryNote: {
            type: 'string',
            label: 'Delivery note',
            required: false
        },
        imoAndMmsiNo: {
            type: 'string',
            label: 'IMO and MMSI no',
            required: false
        },
        shipName: {
            type: 'string',
            label: 'Ship name',
            required: false
        },
        shipRadioCallName: {
            type: 'string',
            label: 'Ship radio call name',
            required: false
        },
        shipRegistrationName: {
            type: 'string',
            label: 'Ship registration name',
            required: false
        },
        shipNetWeight: {
            type: 'decimal',
            label: 'Ship net weight',
            required: false
        },
        shipGrossWeight: {
            type: 'decimal',
            label: 'Ship gross weight',
            required: false
        },
        shipRequirements: {
            type: 'string',
            label: 'Ship requirements',
            required: false
        },
        shipPortOfRegistration: {
            type: 'string',
            label: 'Ship port of registration',
            required: false
        },
        trainNo: {
            type: 'string',
            label: 'Train no',
            required: false
        },
        trainWagonNo: {
            type: 'string',
            label: 'Train wagon no',
            required: false
        },
        licensePlateNo: {
            type: 'string',
            label: 'License plate no',
            required: false
        },
        aircraftNo: {
            type: 'string',
            label: 'Aircraft no',
            required: false
        },
        carrierId: {
            type: 'string',
            label: 'Carrier',
            required: false
        },
        cargoTrackingCode: {
            type: 'string',
            label: 'Cargo tracking code',
            required: false
        },
        shippingPaymentType: {
            type: 'string',
            label: 'Shipment payment type',
            required: false
        },
        typeOfGoods: {
            type: 'string',
            label: 'Type of goods',
            required: false
        },
        loadingPort: {
            type: 'string',
            label: 'Loading port',
            required: false
        },
        portOfDestination: {
            type: 'string',
            label: 'Port of destination',
            required: false
        },
        customsDeclarationNo: {
            type: 'string',
            label: 'Customs declaration no',
            required: false
        },
        customsConsultancyFirm: {
            type: 'string',
            label: 'Customs consultancy firm',
            required: false
        },
        customsConsultancyContactPerson: {
            type: 'string',
            label: 'Customs consultancy contact person',
            required: false
        },
        transportationInsurance: {
            type: 'boolean',
            label: 'Transportation insurance',
            default: false
        },
        costOfGoods: {
            type: 'decimal',
            label: 'Cost of goods',
            required: false
        },
        shipmentNote: {
            type: 'string',
            label: 'Shipment note',
            required: false
        },
        journalId: {
            type: 'string',
            label: 'Journal',
            required: false
        },
        journalDescription: {
            type: 'string',
            label: 'Journal description',
            required: false
        },
        invoiceResponsibleId: {
            type: 'string',
            label: 'Invoice responsible',
            required: false
        },
        invoiceAddressId: {
            type: 'string',
            required: false
        },
        invoiceAddress: {
            type: 'object',
            label: 'Invoice address',
            blackbox: true,
            required: false
        },
        scope: {
            type: 'string',
            label: 'Scope',
            default: '1'
        },
        financialProjectId: {
            type: 'string',
            label: 'Project',
            required: false
        },
        priceSourceId: {
            type: 'string',
            label: 'Price source',
            required: false
        },
        items: [
            {
                id: {
                    type: 'string',
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                productId: {
                    type: 'string',
                    label: 'Product',
                    column: {
                        populate: 'product',
                        visible: false,
                        minWidth: 180
                    },
                    editor: {
                        collection: 'inventory.products',
                        view: 'inventory.catalog.products',
                        labelFrom: 'displayName',
                        filters() {
                            const filters = {
                                type: {$ne: 'service'},
                                isSimple: true
                            };

                            if (
                                vm.$setting('inventory.inventoryOrganizations') &&
                                _.isPlainObject(vm.organizationSettings) &&
                                !_.isEmpty(vm.organizationSettings) &&
                                Array.isArray(vm.organizationSettings.productGroupIds)
                            ) {
                                filters.groupIds = {$in: vm.organizationSettings.productGroupIds};
                            }

                            return filters;
                        },
                        isEditable() {
                            return !vm.model.isLocked;
                        }
                    }
                },
                productCode: {
                    type: 'string',
                    label: 'Product code',
                    column: {
                        populate: 'product',
                        pinned: 'left',
                        minWidth: 120
                    },
                    editor: {
                        collection: 'inventory.products',
                        view: 'inventory.catalog.products',
                        labelFrom: 'code',
                        valueFrom: 'code',
                        idFrom: 'productId',
                        extraFields: ['displayName'],
                        htmlTemplate(item) {
                            return item.displayName;
                        },
                        filters() {
                            const filters = {
                                type: {$ne: 'service'},
                                isSimple: true
                            };

                            if (
                                vm.$setting('inventory.inventoryOrganizations') &&
                                _.isPlainObject(vm.organizationSettings) &&
                                !_.isEmpty(vm.organizationSettings) &&
                                Array.isArray(vm.organizationSettings.productGroupIds)
                            ) {
                                filters.groupIds = {$in: vm.organizationSettings.productGroupIds};
                            }

                            return filters;
                        },
                        isEditable() {
                            return !vm.model.isLocked;
                        }
                    }
                },
                productDefinition: {
                    type: 'string',
                    label: 'Product definition',
                    column: {
                        populate: 'product',
                        // visible: false,
                        minWidth: 150
                    },
                    editor: {
                        collection: 'inventory.products',
                        view: 'inventory.catalog.products',
                        labelFrom: 'definition',
                        valueFrom: 'definition',
                        idFrom: 'productId',
                        extraFields: ['displayName'],
                        htmlTemplate(item) {
                            return item.displayName;
                        },
                        filters() {
                            const filters = {
                                type: {$ne: 'service'},
                                isSimple: true
                            };

                            if (
                                vm.$setting('inventory.inventoryOrganizations') &&
                                _.isPlainObject(vm.organizationSettings) &&
                                !_.isEmpty(vm.organizationSettings) &&
                                Array.isArray(vm.organizationSettings.productGroupIds)
                            ) {
                                filters.groupIds = {$in: vm.organizationSettings.productGroupIds};
                            }

                            return filters;
                        },
                        isEditable() {
                            return !vm.model.isLocked;
                        }
                    }
                },
                description: {
                    type: 'string',
                    label: 'Description',
                    required: false,
                    column: {
                        minWidth: 150,
                        visible: false
                    }
                },
                barcode: {
                    type: 'string',
                    label: 'Barcode',
                    required: false,
                    column: {
                        width: 150,
                        visible: false
                    }
                },
                deliveryNote: {
                    type: 'string',
                    label: 'Delivery note',
                    required: false,
                    column: {
                        visible: false,
                        width: 210
                    }
                },
                financialProjectId: {
                    type: 'string',
                    label: 'Project',
                    required: false,
                    column: {
                        populate: 'financialProject',
                        visible: false,
                        width: 150
                    },
                    editor: {
                        collection: 'kernel.financial-projects',
                        view: 'system.management.configuration.financial-projects',
                        disableCreate: true,
                        disableDetail: true,
                        extraFields: ['code'],
                        template: '{{code}} - {{name}}',
                        filters: () => ({
                            $and: [
                                {
                                    $or: [
                                        {validFrom: {$exists: false}},
                                        {validFrom: {$eq: null}},
                                        {
                                            validFrom: {
                                                $lte: vm.model.issueDate
                                            }
                                        }
                                    ]
                                },
                                {
                                    $or: [
                                        {validTo: {$exists: false}},
                                        {validTo: {$eq: null}},
                                        {
                                            validTo: {
                                                $gte: vm.model.issueDate
                                            }
                                        }
                                    ]
                                }
                            ],
                            $sort: {code: 1}
                        })
                    }
                },
                unitId: {
                    type: 'string',
                    label: 'Unit',
                    column: {
                        populate: 'unit',
                        width: 60
                    },
                    editor: {
                        collection: 'kernel.units',
                        filters(data) {
                            let ids = [];

                            if (_.isObject(data.product)) {
                                if (_.isString(data.product.baseUnitId)) {
                                    ids.push(data.product.baseUnitId);
                                }

                                const unitConversions = data.product.unitConversions;
                                if (Array.isArray(unitConversions) && unitConversions.length > 0) {
                                    unitConversions.forEach(uc => {
                                        ids.push(uc.fromUnitId);
                                        ids.push(uc.toUnitId);
                                    });
                                }
                            }

                            return {_id: {$in: _.uniq(ids)}};
                        },
                        isEditable() {
                            return !vm.model.isLocked;
                        }
                    }
                },
                unitPrice: {
                    type: 'decimal',
                    label: 'Unit price',
                    default: 0,
                    column: {
                        format: 'unit-price',
                        formatOptions: () => {
                            return vm.currencyFormat;
                        },
                        hidden: () =>
                            !vm.$checkPermission({
                                type: 'permission',
                                name: 'system.canSeeCosts'
                            }),
                        headerComponentParams: () => {
                            const type = vm.$params('type');
                            let unitPriceLabel = vm.$t('Unit price');
                            if (!!type) {
                                if (type === 'outgoing') {
                                    unitPriceLabel = vm.$t('Sales unit price');
                                } else if (type === 'incoming') {
                                    unitPriceLabel = vm.$t('Unit cost');
                                }
                            }

                            return {
                                template:
                                    '<div class="ag-cell-label-container" role="presentation">' +
                                    '  <span ref="eMenu" class="ag-header-icon ag-header-cell-menu-button"></span>' +
                                    '  <div ref="eLabel" class="ag-header-cell-label" role="presentation">' +
                                    `     <span class="ag-header-cell-text" role="columnheader">${unitPriceLabel}</span>` +
                                    '  </div>' +
                                    '</div>'
                            };
                        },
                        width: 95
                    },
                    editor: {}
                },
                discount: {
                    type: 'decimal',
                    label: 'Discount %',
                    default: 0,
                    column: {
                        format: 'percentage',
                        width: 80,
                        formatOptions: () => {
                            return {
                                number: {
                                    precision: vm.$setting('system.percentagePrecision')
                                }
                            };
                        },
                        hidden:
                            !vm.$setting('sale.lineDiscounts') ||
                            !vm.$checkPermission({
                                type: 'permission',
                                name: 'system.canSeeCosts'
                            })
                    },
                    editor: {}
                },
                unitPriceAfterDiscount: {
                    type: 'decimal',
                    label: 'Unit price after discount',
                    default: 0,
                    column: {
                        format: 'unit-price',
                        formatOptions: () => {
                            return vm.currencyFormat;
                        },
                        visible: false,
                        hidden: () =>
                            !vm.$checkPermission({
                                type: 'permission',
                                name: 'system.canSeeCosts'
                            }),
                        width: 95
                    },
                    editor: {
                        disabled: () => true
                    }
                },
                freight: {
                    type: 'decimal',
                    label: 'Freight',
                    default: 0,
                    column: {
                        format: 'total',
                        formatOptions: () => {
                            return vm.currencyFormat;
                        },
                        hidden:
                            !vm.$setting('system.freight') ||
                            !vm.$checkPermission({
                                type: 'permission',
                                name: 'system.canSeeCosts'
                            }),
                        width: 95
                    },
                    editor: {
                        disabled: true
                    }
                },
                cost: {
                    type: 'decimal',
                    label: 'Cost',
                    default: 0,
                    column: {
                        format: 'total',
                        formatOptions: () => {
                            return vm.currencyFormat;
                        },
                        visible: false,
                        hidden: () =>
                            !vm.$checkPermission({
                                type: 'permission',
                                name: 'system.canSeeCosts'
                            }),
                        width: 95
                    },
                    editor: {
                        disabled: true
                    }
                },
                total: {
                    type: 'decimal',
                    label: 'Total',
                    default: 0,
                    column: {
                        format: 'currency',
                        formatOptions: () => {
                            return vm.currencyFormat;
                        },
                        visible: false,
                        hidden: () =>
                            !vm.$checkPermission({
                                type: 'permission',
                                name: 'system.canSeeCosts'
                            }),
                        width: 120
                    },
                    editor: {
                        disabled: true
                    }
                },
                stockQuantity: {
                    type: 'decimal',
                    label: 'Stock on Hand',
                    default: 0,
                    column: {
                        width: 90,
                        format: 'unit',
                        // visible: (vm.operationType || {}).type !== 'incoming'
                        visible: false
                    },
                    editor: {
                        disabled: () => true
                    }
                },
                orderedQuantity: {
                    type: 'decimal',
                    label: 'Ordered Quantity',
                    default: 0,
                    column: {
                        width: 75,
                        format: 'unit',
                        visible: false
                    },
                    editor: {
                        disabled: () => true
                    }
                },
                assignedQuantity: {
                    type: 'decimal',
                    label: 'Assigned Quantity',
                    default: 0,
                    column: {
                        width: 75,
                        format: 'unit',
                        visible: false
                    },
                    editor: {
                        disabled: () => true
                    }
                },
                availableQuantity: {
                    type: 'decimal',
                    label: 'Available Quantity',
                    default: 0,
                    column: {
                        width: 75,
                        format: 'unit',
                        visible: false
                    },
                    editor: {
                        disabled: () => true
                    }
                },
                requestedQty: {
                    type: 'decimal',
                    label: 'Requested quantity',
                    default: 0,
                    column: {
                        width: 110,
                        suppressMenu: true,
                        format: 'unit',
                        formatOptions(row) {
                            return {
                                number: {
                                    precision: vm.$setting('system.unitPrecision')
                                }
                            };
                        }
                    },
                    editor: {
                        // disabled(row) {
                        //     return true;
                        // },
                        isEditable() {
                            return !vm.model.isLocked;
                        }
                    }
                },
                actualQty: {
                    type: 'decimal',
                    label: 'Actual Quantity',
                    default: 0,
                    column: {
                        width: 150,
                        suppressMenu: true,
                        format: 'unit',
                        formatOptions(row) {
                            return {
                                number: {
                                    precision: vm.$setting('system.unitPrecision')
                                }
                            };
                        },
                        render(params) {
                            const row = params.data;
                            let isSimpleInventory = false;

                            if (
                                _.isPlainObject(row.product) &&
                                row.product.tracking !== 'serial' &&
                                row.product.tracking !== 'lot' &&
                                !vm.$setting('inventory.storageLocations')
                            ) {
                                isSimpleInventory = true;
                            }

                            if (_.isNumber(row.actualQty) && _.isPlainObject(row.product)) {
                                const value = vm.$format(row.actualQty, 'unit');

                                if (vm.$params('id') && !isSimpleInventory) {
                                    const div = document.createElement('div');
                                    div.className = 'ui-table-relation-renderer';
                                    div.innerHTML = `
                                            <div class="relation-renderer-icon text-normal text-primary-hover">
                                                <i class="fas fa-arrow-right"></i>
                                            </div>
                                            <div class="relation-renderer-label">${value}</div>
                                            `;

                                    const icon = div.querySelectorAll('.relation-renderer-icon')[0];
                                    icon.addEventListener('click', () => {
                                        if (vm.$setting('inventory.useBulkEntryInTransfers') && !vm.hasSubItems) {
                                            vm.handleBulkEntry(row.id);
                                        } else {
                                            vm.openItemDetail(row, params);
                                        }
                                    });

                                    return div;
                                } else {
                                    return value;
                                }
                            }

                            return '';
                        }
                    },
                    editor: {
                        disabled(row) {
                            let isSimpleInventory = false;

                            if (
                                _.isPlainObject(row.product) &&
                                row.product.tracking !== 'serial' &&
                                row.product.tracking !== 'lot' &&
                                !vm.$setting('inventory.storageLocations')
                            ) {
                                isSimpleInventory = true;
                            }

                            return !isSimpleInventory || row.requestedQty === 0;
                        }
                    }
                },
                subItems: {
                    type: [
                        {
                            type: 'object',
                            blackbox: true,
                            required: false
                        }
                    ],
                    column: {
                        hidden: true
                    },
                    default: []
                },
                pcmModelId: {
                    type: 'string',
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                pcmConfigurationId: {
                    type: 'string',
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                pcmHash: {
                    type: 'string',
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                copyableItemId: {
                    type: 'string',
                    required: false,
                    column: {
                        hidden: true
                    }
                }
            }
        ],

        subTotal: {
            type: 'decimal',
            label: 'Subtotal',
            default: 0
        },
        discount: {
            type: 'decimal',
            label: 'Discount %',
            default: 0
        },
        discountAmount: {
            type: 'decimal',
            label: 'Discount',
            default: 0
        },
        subTotalAfterDiscount: {
            type: 'decimal',
            label: 'Subtotal after discount',
            default: 0
        },
        grandTotal: {
            type: 'decimal',
            label: 'Grand total',
            default: 0
        },

        // Exchange rates.
        exchangeRates: {
            type: [
                {
                    currencyName: {
                        type: 'string',
                        label: 'Currency',
                        column: {
                            width: 150
                        },
                        editor: {
                            disabled: true
                        }
                    },
                    rate: {
                        type: 'decimal',
                        label: 'Rate',
                        default: 0,
                        column: {
                            format: 'amount',
                            formatOptions: () => {
                                return {
                                    number: {
                                        precision: vm.$setting('system.exchangeRatePrecision')
                                    }
                                };
                            }
                        }
                    },
                    grandTotal: {
                        type: 'decimal',
                        label: 'Grand total',
                        default: 0,
                        column: {
                            format: 'amount',
                            valueGetter: params => {
                                const {data} = params;

                                if (!!data && _.isNumber(data.rate) && data.rate > 0) {
                                    return (vm.model.grandTotal * vm.model.currencyRate) / data.rate;
                                }

                                return 0;
                            }
                        },
                        editor: {
                            disabled: () => true
                        }
                    }
                }
            ],
            default: []
        },
        exchangeRatesMap: {
            type: 'object',
            blackbox: true,
            required: false
        },

        // Organization
        organizationId: {
            type: 'string',
            label: 'Organization',
            required: false
        },

        // Attachments.
        attachments: {
            type: ['string'],
            default: []
        },

        copiedDocumentRecords: {
            type: [
                {
                    type: 'object',
                    blackbox: true,
                    required: false
                }
            ],
            default: []
        },
        freight: {
            type: 'object',
            blackbox: true,
            required: false
        },

        // Additional information
        additionalInformation: {
            type: 'object',
            blackbox: true,
            required: false
        },
        additionalInformationId: {
            type: 'string',
            label: 'Additional information',
            required: false
        },

        // Stages
        stageId: {
            type: 'string',
            label: 'Stage',
            required: false,
            index: true
        },
        stages: {
            type: [
                {
                    type: 'object',
                    blackbox: true,
                    required: false
                }
            ],
            default: []
        },
        stageHistory: {
            type: [
                {
                    type: 'object',
                    blackbox: true,
                    required: false
                }
            ],
            default: []
        },
        probability: {
            type: 'decimal',
            label: 'Probability',
            required: false
        }
    };
}
