import _ from 'lodash';
import fastCopy from 'fast-copy';
import Random from 'framework/random';
import {escapeRegExp, toLower, toUpper, trim} from 'framework/helpers';

export default {
    methods: {
        async handleCreateProductionOrder() {
            const isConfirmed = await new Promise(resolve => {
                this.$program.alert(
                    'confirm',
                    this.$t('Production order(s) will be created for the document. Do you want to continue?'),
                    confirmed => {
                        resolve(confirmed);
                    }
                );
            });
            if (!isConfirmed) return;

            this.$params('loading', true);

            // Check PCM configurations.
            if (
                this.$app.hasModule('pcm') &&
                (this.model.items || []).some(item => !!item.pcmModelId && !item.pcmConfigurationId)
            ) {
                this.$program.message(
                    'error',
                    this.$t('The operation cannot be performed because there are unconfigured products in the rows!')
                );
                this.$params('loading', false);
                return;
            }

            let isOrdersCreated = false;
            const orderItems = this.model.items || [];
            let productionModels = null;
            if (!!this.$app.hasModule('production')) {
                productionModels = await this.$collection('production.models').find({
                    productId: {$in: _.uniq(orderItems.map(orderItem => orderItem.productId))},
                    $select: ['_id', 'productId'],
                    $sort: {updatedAt: -1}
                });
            }

            const orderItemIds = _.uniq(orderItems.map(item => item.productId));

            const orderProducts = await this.$collection('inventory.products').find({
                _id: {$in: orderItemIds},

                $select: ['type', 'baseUnitId']
            });

            if (!!this.$app.hasModule('production') && Array.isArray(productionModels) && productionModels.length > 0) {
                try {
                    const ecos = await this.$collection('production.eco').find({
                        status: 'published',
                        modelId: {$in: _.uniq(productionModels.map(productionModel => productionModel._id))},
                        $select: [
                            '_id',
                            'modelId',
                            'payload.code',
                            'payload.name',
                            'payload.unitId',
                            'payload.warehouseId',
                            'payload.outputLocationId',
                            'payload.productionLocationId',
                            'payload.indirectProductCost',
                            'payload.indirectResourceCost',
                            'payload.salesAndAdministrationCost',
                            'payload.shippingCost',
                            'payload.parameters'
                        ],
                        $sort: {publishedAt: -1}
                    });
                    const numbering = await this.$collection('kernel.numbering').findOne({
                        code: 'productionOrderNumbering',
                        $select: ['_id'],
                        $disableInUseCheck: true,
                        $disableActiveCheck: true,
                        $disableSoftDelete: true
                    });
                    const relatedDocuments = [];

                    for (const orderItem of orderItems) {
                        const productType = orderProducts.find(product => product._id === orderItem.productId).type;
                        const productBaseUnitId = orderProducts.find(
                            product => product._id === orderItem.productId
                        ).baseUnitId;
                        const baseQty = orderItem.requestedQty;
                        if (productType !== 'stockable' || baseQty < 1) {
                            continue;
                        }

                        let productionModel = productionModels.find(
                            productionModel => productionModel.productId === orderItem.productId
                        );
                        if (!productionModel) {
                            continue;
                        }
                        const eco = ecos.find(eco => eco.modelId === productionModel._id);
                        if (!eco) {
                            continue;
                        }

                        productionModel = {
                            _id: eco.modelId,
                            ...eco.payload
                        };

                        const order = {};
                        order.status = 'draft';
                        order.code = await this.$rpc('kernel.common.request-number', {
                            numberingId: numbering._id,
                            save: true
                        });
                        order.productId = orderItem.productId;
                        order.modelId = productionModel._id;
                        order.ecoId = eco._id;
                        order.unitId = orderItem.unitId;
                        order.baseUnitId = productBaseUnitId;
                        order.quantity = orderItem.quantity;
                        order.baseQuantity = orderItem.requestedQty;
                        order.reference = this.model.code;
                        order.parameters = {};
                        order.recordDate = new Date();
                        order.orderDate = new Date();
                        order.financialProjectId = orderItem.financialProjectId || this.model.financialProjectId;
                        order.relatedPartnerId = this.model.partnerId;
                        order.useUpToDateCosts = true;
                        order.warehouseId = orderItem.warehouseId;
                        order.outputLocationId = '';
                        order.productionLocationId = '';
                        order.description = orderItem.description;
                        order.pcmModelId = orderItem.pcmModelId;
                        order.pcmConfigurationId = orderItem.pcmConfigurationId;
                        order.pcmHash = orderItem.pcmHash;
                        order.relatedDocuments = [
                            {
                                collection: 'inventory.transfers',
                                view: 'inventory.operations.transfers',
                                title: 'Transfers',
                                ids: [this.$params('id')]
                            }
                        ];

                        order.outputLocationId = eco.payload.outputLocationId;
                        order.productionLocationId = eco.payload.productionLocationId;
                        order.indirectProductCost = eco.payload.indirectProductCost;
                        order.indirectResourceCost = eco.payload.indirectResourceCost;
                        order.salesAndAdministrationCost = eco.payload.salesAndAdministrationCost;
                        order.shippingCost = eco.payload.shippingCost;

                        if (
                            !!productionModel &&
                            Array.isArray(productionModel.parameters) &&
                            productionModel.parameters.length > 0
                        ) {
                            const parametersPayload = {};

                            for (const parameter of productionModel.parameters) {
                                if (parameter.name === 'quantity') {
                                    continue;
                                }

                                if (parameter.fieldType === 'integer' && _.isFinite(parameter.integerDefaultValue)) {
                                    parametersPayload[parameter.name] = parameter.integerDefaultValue;
                                } else if (
                                    parameter.fieldType === 'decimal' &&
                                    _.isFinite(parameter.decimalDefaultValue)
                                ) {
                                    parametersPayload[parameter.name] = parameter.decimalDefaultValue;
                                } else if (parameter.fieldType === 'date' && _.isDate(parameter.dateDefaultValue)) {
                                    parametersPayload[parameter.name] = parameter.dateDefaultValue;
                                } else if (
                                    parameter.fieldType === 'datetime' &&
                                    _.isDate(parameter.dateTimeDefaultValue)
                                ) {
                                    parametersPayload[parameter.name] = parameter.dateTimeDefaultValue;
                                } else if (
                                    parameter.fieldType === 'boolean' &&
                                    _.isBoolean(parameter.booleanDefaultValue)
                                ) {
                                    parametersPayload[parameter.name] = parameter.booleanDefaultValue;
                                } else if (
                                    parameter.fieldType === 'yes-no' &&
                                    _.isString(parameter.yesNoDefaultValue)
                                ) {
                                    parametersPayload[parameter.name] = parameter.yesNoDefaultValue;
                                } else if (
                                    parameter.fieldType === 'select' &&
                                    _.isString(parameter.selectDefaultValue)
                                ) {
                                    parametersPayload[parameter.name] = parameter.selectDefaultValue;
                                } else if (_.isString(parameter.textDefaultValue)) {
                                    parametersPayload[parameter.name] = parameter.textDefaultValue;
                                }
                            }

                            order.parameters = parametersPayload;

                            if (!!orderItem.pcmConfigurationId) {
                                const pcmConfiguration = await this.$collection('pcm.configurations').findOne({
                                    _id: orderItem.pcmConfigurationId
                                });

                                if (
                                    !!pcmConfiguration &&
                                    _.isPlainObject(pcmConfiguration.payload) &&
                                    _.isPlainObject(pcmConfiguration.payload.values)
                                ) {
                                    order.parameters = {
                                        ...order.parameters,
                                        ...pcmConfiguration.payload.values
                                    };
                                }
                            }
                        }

                        const productionOrder = await this.$rpc('production.orders-save', {
                            data: order
                        });

                        relatedDocuments.push({
                            collection: 'production.orders',
                            view: 'production.production.orders',
                            title: 'Production Orders',
                            ids: [productionOrder._id]
                        });

                        isOrdersCreated = true;
                    }

                    if (relatedDocuments.length > 0) {
                        await this.$collection('inventory.transfers').patch(
                            {_id: this.$params('id')},
                            {
                                relatedDocuments: (this.model.relatedDocuments || []).concat(relatedDocuments)
                            }
                        );
                    }
                } catch (error) {
                    this.$program.message('error', error.message);
                }
            } else {
                try {
                    const stages = await this.$collection('basic-production.order-stages').find();
                    const productFields = [
                        'code',
                        'name',
                        'displayName',
                        'definition',
                        'type',
                        'baseUnitId',
                        'stockUnitId',
                        'barcode',
                        'unitRatios',
                        'unitConversions',
                        'lastCost',
                        'lastPurchasePrice'
                    ];

                    for (const orderItem of orderItems) {
                        const productType = orderProducts.find(product => product._id === orderItem.productId).type;
                        // const baseQty = orderItem.baseQuantity - orderItem.warehouseAvailableQuantity;
                        const baseQty = orderItem.requestedQty;

                        if (productType !== 'stockable' || baseQty < 1) {
                            continue;
                        }

                        const bom = await this.$collection('basic-production.bom').findOne({
                            productId: orderItem.productId,
                            $populate: [
                                {
                                    field: 'product',
                                    query: {
                                        $select: productFields
                                    }
                                }
                            ]
                        });

                        if (!bom) {
                            continue;
                        }
                        const numbering = await this.$collection('kernel.numbering').findOne({
                            code: 'basicProductionOrderNumbering',
                            $select: ['_id'],
                            $disableInUseCheck: true,
                            $disableActiveCheck: true,
                            $disableSoftDelete: true
                        });
                        let order = {};

                        order.status = 'draft';
                        order.code = await this.$rpc('kernel.common.request-number', {
                            numberingId: numbering._id,
                            save: true
                        });
                        order.type = 'standard';
                        order.bomId = bom._id;
                        order.unitId = orderItem.unitId;
                        order.quantity = baseQty;
                        order.reference = this.model.code;
                        order.warehouseId = this.model.warehouseId;
                        if (this.$setting('basic-production.defaultWarehouseId')) {
                            order.warehouseId = this.$setting('basic-production.defaultWarehouseId');
                        }
                        order.recordDate = new Date();
                        order.orderDate = new Date();
                        order.financialProjectId = orderItem.financialProjectId;
                        order.relatedPartnerId = this.model.partnerId;
                        order.relatedDocuments = [
                            {
                                collection: 'inventory.transfers',
                                view: 'inventory.operations.transfers',
                                title: 'Transfers',
                                ids: [this.$params('id')]
                            }
                        ];

                        if (stages.length > 0) {
                            order.stageId = stages[0]._id;
                        }

                        const products = await this.$collection('inventory.products').find({
                            _id: {$in: bom.items.map(item => item.productId)},
                            $select: productFields
                        });
                        const items = [];
                        for (const row of bom.items) {
                            const product = products.find(product => product._id === row.productId);
                            const item = {};

                            item.id = Random.id(8);
                            item.productId = row.productId;
                            item.productCode = row.productCode;
                            item.productDefinition = row.productDefinition;
                            item.barcode = product.barcode;
                            item.quantity = row.quantity * (order.quantity > 0 ? order.quantity : 1);
                            item.unitId = row.unitId;
                            item.baseQuantity = 0;
                            item.baseUnitId = product.baseUnitId;
                            item.warehouseId = order.warehouseId;
                            item.wastageType = row.wastageType;
                            item.wastage = row.wastage;
                            item.stockQuantity = 0;
                            item.orderedQuantity = 0;
                            item.assignedQuantity = 0;
                            item.availableQuantity = 0;
                            item.warehouseStockQuantity = 0;
                            item.warehouseOrderedQuantity = 0;
                            item.warehouseAssignedQuantity = 0;
                            item.warehouseAvailableQuantity = 0;
                            item.financialProjectId = order.financialProjectId;
                            item.unitCost = row.unitCost;

                            items.push(item);
                        }

                        order.items = await this.$rpc('basic-production.decorate-order-items', {
                            items,
                            field: 'quantity',
                            model: order,
                            productFields: productFields
                        });
                        order.byProducts = fastCopy(bom.byProducts ?? []).map(byProduct => {
                            byProduct.quantity = byProduct.quantity * (order.quantity > 0 ? order.quantity : 1);
                            byProduct.cost = byProduct.unitCost * byProduct.quantity;

                            return byProduct;
                        });
                        order.services = bom.services ?? [];
                        order.resources = bom.resources ?? [];
                        const productsTotal = _.sumBy(order.items, 'cost');
                        const byProductsTotal = _.sumBy(order.byProducts, 'cost');
                        const servicesTotal = _.sumBy(order.services, 'cost');
                        const resourcesTotal = _.sumBy(order.resources, 'cost');
                        order.cost = productsTotal - byProductsTotal + servicesTotal + resourcesTotal;

                        order = await this.$collection('basic-production.orders').create(order);

                        isOrdersCreated = true;

                        await this.$collection('inventory.transfers').patch(
                            {_id: this.$params('id')},
                            {
                                relatedDocuments: (this.model.relatedDocuments || []).concat([
                                    {
                                        collection: 'basic-production.orders',
                                        view: 'basic-production.production.orders',
                                        title: 'Production Orders',
                                        ids: [order._id]
                                    }
                                ])
                            }
                        );
                    }
                } catch (error) {
                    this.$program.message('error', error.message);
                }
            }

            if (isOrdersCreated) {
                this.$program.message('success', this.$t('Production order created successfully.'));
            }

            this.$params('loading', false);
        },

        async handleApprove() {
            const model = this.model;

            try {
                const result = await this.$rpc('kernel.common.get-document-under-edit', {
                    document: 'inventory.transfers',
                    documentId: this.$params('id')
                });

                if (result) {
                    this.$program.alert(
                        'error',
                        this.$t(
                            'The document is being updated by {{user}}. When the relevant user completes the update, the document will be allowed to update.',
                            {user: `${result.userCode} - ${result.userName}`}
                        )
                    );
                    return;
                }
            } catch (error) {}

            // Check issue date.
            const issueDate = this.$app.datetime.fromJSDate(model.issueDate).startOf('day').toJSDate();
            const scheduledDate = this.$app.datetime.fromJSDate(model.scheduledDate).startOf('day').toJSDate();
            if (scheduledDate.getTime() < issueDate.getTime()) {
                const isConfirmed = await new Promise(resolve => {
                    this.$program.alert(
                        'confirm',
                        this.$t(
                            'Scheduled date can not be before issue date! Scheduled date will be updated! Do you want to continue?'
                        ),
                        confirmed => {
                            resolve(confirmed);
                        }
                    );
                });

                if (!isConfirmed) return;

                this.model.scheduledDate = model.issueDate;
            }

            this.$params('loading', true);
            await this.handleCheckItemsAvailability();
            this.$params('loading', false);

            if (
                (model.deliveryPolicy === 'when-one-ready' || !model.deliveryPolicy) &&
                model.items.length > 0 &&
                model.items.some(item => item.actualQty !== item.requestedQty)
            ) {
                const isPartialDeliveryConfirmed = await new Promise(resolve => {
                    this.$program.alert(
                        'confirm',
                        this.$t(
                            'There are incomplete rows in the delivery. Partial delivery will be created. Do you want continue?'
                        ),
                        confirmed => {
                            resolve(confirmed);
                        }
                    );
                });

                if (!isPartialDeliveryConfirmed) return;

                this.createPartialDelivery = true;
            }

            const isConfirmed = await new Promise(resolve => {
                this.$program.alert(
                    'confirm',
                    this.$t('Related accounting records or moves will be created and posted. Do you want continue?'),
                    confirmed => {
                        resolve(confirmed);
                    }
                );
            });

            if (!isConfirmed) return;

            // Check serial numbers.
            if (!this.$setting('inventory.useBulkEntryInTransfers') || this.hasSubItems) {
                const serialNumbers = [];

                for (const item of model.items) {
                    for (const subItem of item.subItems || []) {
                        if (!!subItem.serialNumber && serialNumbers.includes(subItem.serialNumber)) {
                            this.$program.message(
                                'error',
                                this.$t(
                                    'The {{serialNumber}} serial number must be unique to the product with the {{code}} code!',
                                    {
                                        serialNumber: subItem.serialNumber,
                                        code: item.productCode
                                    }
                                )
                            );

                            return;
                        }

                        serialNumbers.push(subItem.serialNumber);
                    }
                }
            }

            // Run approval workflow.
            if (this.$app.hasModule('workflow')) {
                this.$params('isPreview', true);
                this.$params('loading', true);

                try {
                    const flowResult = await this.$rpc('workflow.run-workflow', {
                        name: 'inventory.transfers',
                        data: {
                            ..._.omit(this.model, 'workflowApprovalStatus')
                        },
                        id: this.$params('id'),
                        operation: 'update',
                        actionTypes: ['approval'],
                        approvalMethod: 'inventory.transfers-save-transfer',
                        approvalPayload: {
                            id: this.$params('id'),
                            data: {
                                ..._.omit(this.model, 'workflowApprovalStatus'),
                                status: 'approved'
                            }
                        }
                    });
                    if (!!flowResult.approvalDefinitionFound) {
                        if (flowResult.approvalIsAlreadyInProgress === true) {
                            this.$params('loading', false);
                            this.$program.alert(
                                'error',
                                this.$t(
                                    'The document is currently in the process of approval. When the relevant person or persons complete the approval process, the document will be automatically approved.'
                                )
                            );
                            return;
                        }

                        this.$set(this.model, 'workflowApprovalStatus', 'waiting-for-approval');
                        this.$params('loading', false);
                        this.$program.alert(
                            'success',
                            this.$t(
                                'The document has been successfully submitted to the workflow approval process. When the relevant person or persons complete the approval process, the document will be automatically approved.'
                            )
                        );
                        this.$program.emit('workflow-approval-params-changed', {
                            documentId: this.$params('id'),
                            documentCollection: 'inventory.transfers',
                            approvalStatus: 'waiting-for-approval'
                        });
                        return;
                    } else {
                        this.$params('loading', false);
                    }
                } catch (error) {
                    this.$program.message('error', error.message);
                    this.$params('loading', false);
                    return;
                }
            }

            this.$params('loading', true);

            this.model.status = 'approved';

            await this.$refs.view.submitForm({status: 'approved'});

            this.$params('loading', false);
        },
        async handleCreateShippingOrder() {
            this.$program.dialog({
                component: 'inventory.operations.transfers.new-shipping-order',
                params: {
                    title: this.$t('New Shipping Order'),
                    model: {
                        carrierId: this.model.carrierId,
                        deliveryAddress: this.model.deliveryAddress
                    }
                },
                onSubmit: async ({
                    carrierId,
                    packageTypeId,
                    cashOnDeliveryAmount,
                    shippingPaymentType,
                    packagingType,
                    weight,
                    volumetricWeight,
                    deliveryAddress
                }) => {
                    this.$params('loading', true);

                    try {
                        await this.$rpc('inventory.transfers-create-shipping-order', {
                            transferId: this.$params('id'),
                            carrierId,
                            packageTypeId,
                            cashOnDeliveryAmount,
                            shippingPaymentType,
                            packagingType,
                            weight,
                            volumetricWeight,
                            deliveryAddress
                        });

                        this.$program.message('success', this.$t('Shipping order created successfully.'));
                    } catch (e) {
                        this.$program.message('error', e.message);
                    }

                    this.$params('loading', false);
                }
            });
        },
        async handleCreateInvoice() {
            const updateCurrencies = await new Promise(resolve => {
                this.$program.alert('yesNo', this.$t('Would you like the exchange rates to be updated?'), confirmed => {
                    resolve(confirmed);
                });
            });

            this.$params('loading', true);
            this.$params('isPreview', true);

            const customerInvoices = await this.$collection('accounting.customer-invoices').findOne({
                transferIds: this.$params('id'),
                status: {$ne: 'canceled'},
                $select: ['_id']
            });
            if (!!customerInvoices) {
                this.$program.message('error', this.$t('There is already an invoice for this transfer!'));
                this.$params('loading', false);
                return;
            }
            const vendorInvoices = await this.$collection('accounting.vendor-invoices').findOne({
                transferIds: this.$params('id'),
                status: {$ne: 'canceled'},
                $select: ['_id']
            });
            if (!!vendorInvoices) {
                this.$program.message('error', this.$t('There is already an invoice for this transfer!'));
                this.$params('loading', false);
                return;
            }

            try {
                await this.$rpc('inventory.create-invoices-form-transfer', {
                    transferId: this.$params('id'),
                    updateCurrencies
                });

                this.$program.message('success', this.$t('Invoice created successfully.'));
            } catch (e) {
                this.$program.message('error', e.message);
            }

            this.$params('loading', false);
        },
        async handleCopyFromSource() {
            if (!this.$params('id')) {
                const isSucceed = await new Promise(async resolve => {
                    try {
                        await this.$refs.view.submitForm();
                        await this.$refs.view.refreshModel();

                        this.$nextTick(() => {
                            this.$params('isPreview', false);

                            resolve(true);
                        });
                    } catch (error) {
                        resolve(false);
                    }
                });
                if (!isSucceed) return;
            }

            const operationType = this.operationType;

            let type = 'delivery-order';
            let allowedTypes = ['sale-order'];
            if (operationType.type === 'incoming' && operationType.documentType !== 'return-transfers') {
                type = 'goods-receipt';
                allowedTypes = ['purchase-order'];
            }
            if (operationType.type === 'incoming' && operationType.documentType === 'return-transfers') {
                type = 'return-goods-receipt';
                allowedTypes = ['delivery-order'];
            }
            if (operationType.type === 'outgoing' && operationType.documentType === 'return-transfers') {
                type = 'return-delivery-order';
                allowedTypes = ['goods-receipt'];
            }

            const result = await new Promise(resolve => {
                this.$program.dialog({
                    component: 'system.components.copy-from-source',
                    params: {
                        title: this.$t('Copy From Source'),
                        type,
                        allowedTypes,
                        documentId: this.$params('id'),
                        documentCollection: 'inventory.transfers',
                        partnerId: this.model.partnerId,
                        branchId: this.model.branchId,
                        currencyId: this.model.currencyId,
                        currencyRate: this.model.currencyRate || 1
                    },
                    onSubmit: data => resolve(data),
                    onClose: () => resolve(null)
                });
            });
            if (!result) return;

            this.$params('loading', true);

            try {
                const items = [];

                for (const i of fastCopy(result.items)) {
                    const item = await this.beforeSaveItem({
                        row: {
                            productId: i.productId,
                            unitId: i.unitId,
                            requestedQty: i.quantity,
                            unitPrice: i.unitPrice,
                            discount: i.discount || 0,

                            stockQuantity: 0,
                            orderedQuantity: 0,
                            assignedQuantity: 0,
                            availableQuantity: 0,
                            actualQty: 0,
                            unitPriceAfterDiscount: 0,
                            freight: 0,
                            cost: 0,
                            total: 0,
                            copyableItemId: i.id
                        },
                        params: {
                            colDef: {field: 'productId'},
                            dontSetUnitId: true,
                            dontSetUnitPrice: true
                        }
                    });

                    items.push(item);
                }

                this.$set(this.model, 'items', fastCopy(this.model.items).concat(items));
                await this.afterSaveItem();
            } catch (error) {
                this.$program.message('error', error.message);
            }

            this.$params('loading', false);
        },
        async handleSendEWaybill() {
            this.$params('loading', true);

            const ew = await this.$collection('logistics.logistics').findOne({
                transferId: this.$params('id'),
                $select: ['_id', 'branchId', 'waybillStatus', 'waybillDocumentNo', 'issueDate', 'partnerTinIdentity']
            });

            if (!ew) {
                this.$program.message('error', 'E-Waybill is not found!');

                this.$params('loading', false);

                return;
            }

            if (ew.waybillStatus !== 'draft') {
                this.$program.message('error', 'E-Waybill is already sent!');

                this.$params('loading', false);

                return;
            }

            if (!!ew.waybillDocumentNo) {
            } else {
                let pbLabel = null;
                let users = [];
                if (_.isString(ew.partnerTinIdentity) && ew.partnerTinIdentity.length === 10) {
                    this.$params('loading', true);

                    try {
                        users = await this.$rpc('eops.e-waybills-get-users', ew.partnerTinIdentity);
                    } catch (error) {
                        const user = await new Promise(resolve => {
                            this.$program.dialog({
                                component: 'eops.waybills.e-waybills.new-pb-label',
                                params: {
                                    forcedPreview: false,
                                    title: this.$t('New Postbox Label'),
                                    tinIdentity: ew.partnerTinIdentity
                                },
                                onSubmit: async user => {
                                    resolve(user);
                                },
                                onClose() {
                                    resolve(null);
                                }
                            });
                        });

                        if (!!user) {
                            users = [
                                {
                                    tinIdentity: ew.partnerTinIdentity,
                                    label: user.pbLabel,
                                    role: 'PK'
                                }
                            ];
                        }
                    }

                    this.$params('loading', false);
                }

                if (users.length > 0) {
                    if (users.length > 1) {
                        pbLabel = await new Promise(resolve => {
                            this.$program.dialog({
                                component: 'eops.waybills.e-waybills.select-pb',
                                params: {
                                    forcedPreview: false,
                                    title: this.$t('Post Boxes'),
                                    users
                                },
                                onSubmit: async ({pb}) => {
                                    resolve(pb);
                                },
                                onClose() {
                                    resolve(null);
                                }
                            });
                        });
                    } else {
                        pbLabel = users[0].label;
                    }
                }

                this.$program.dialog({
                    component: 'eops.waybills.e-waybills.send',
                    params: {
                        eWaybillId: ew._id,
                        forcedPreview: false,
                        title: this.$t('Document No'),
                        branchId: ew.branchId,
                        issueDate: ew.issueDate
                    },
                    onSubmit: async ({numberingId, waybillDocumentNo, issueDate, generatedCode, waybillTemplateId}) => {
                        this.$params('loading', true);

                        try {
                            await this.$rpc('eops.e-waybills-send', {
                                eWaybillId: ew._id,
                                waybillDocumentNo,
                                issueDate,
                                numberingId,
                                saveNumbering: waybillDocumentNo === generatedCode,
                                waybillTemplateId,
                                pbLabel
                            });

                            this.$program.message('success', this.$t('Waybill(s) has been sent successfully.'));
                        } catch (error) {
                            this.$program.message('error', error.message);
                        }

                        this.$params('loading', false);
                    }
                });
            }

            this.$params('loading', false);
        },
        async handlePrintAction(action) {
            if (action === 'waybill') {
                await this.handlePreviewEWaybill();
            } else if (action === 'label') {
                const payload = {
                    source: 'inventory.transfer-item-label',
                    title: this.$t('Transfer Items')
                };

                if ((this.model.items || []).every(item => this.$app.round(item.actualQty, 'unit') === 0)) {
                    this.$program.message(
                        'error',
                        this.$t('Actual quantity must be greater than zero for at least one item!')
                    );
                    return;
                }

                this.$refs.view.handlePrint(payload);
                // this.$refs.printLabelBtn.handlePrint();
            } else if (action === 'bill-of-lading') {
                this.$params('loading', true);

                try {
                    const {url, title} = await this.$rpc('inventory.print-bill-of-lading', this.$params('id'));

                    this.$program.dialog({
                        component: 'inventory.operations.transfers.print',
                        params: {title, url}
                    });
                } catch (error) {
                    this.$program.message('error', error.message);
                }

                this.$params('loading', false);
            } else if (action === 'check-list') {
                this.$params('loading', true);

                try {
                    const {url, title} = await this.$rpc('inventory.print-check-list', this.$params('id'));

                    this.$program.dialog({
                        component: 'inventory.operations.transfers.print',
                        params: {title, url}
                    });
                } catch (error) {
                    this.$program.message('error', error.message);
                }

                this.$params('loading', false);
            } else if (action === 'items') {
                this.$params('loading', true);

                try {
                    const {url, title} = await this.$rpc('inventory.transfers-print-items', this.$params('id'));

                    this.$program.dialog({
                        component: 'inventory.operations.transfers.print',
                        params: {title, url}
                    });
                } catch (error) {
                    this.$program.message('error', error.message);
                }

                this.$params('loading', false);
            } else if (action === 'product-locations') {
                this.$params('loading', true);

                try {
                    const {url, title} = await this.$rpc(
                        'inventory.transfers-print-product-locations',
                        this.$params('id')
                    );

                    this.$program.dialog({
                        component: 'inventory.operations.transfers.print',
                        params: {title, url}
                    });
                } catch (error) {
                    this.$program.message('error', error.message);
                }

                this.$params('loading', false);
            }
        },
        async handlePrintItemLabel() {
            const selectedItems = this.selectedItems || [];

            if (selectedItems.length < 1) return;

            const payload = {
                source: 'inventory.transfer-item-label',
                title: this.$t('Transfer Items')
            };

            payload.payloadToken = await this.$rpc('system.templates-generate-payload-token', {
                extraData: {itemIds: selectedItems.map(item => item.productId)},
                source: payload.source
            });

            this.$refs.view.handlePrint(payload);
        },
        handleSelectItems(selected) {
            this.selectedItems = selected;
        },
        async handleUnlock() {
            // if (this.model.documentType === 'production-transfers' && this.operationType.type === 'outgoing') {
            //     this.$program.message(
            //         'error',
            //         this.$t('For production transfers, the transfer lock cannot be opened!')
            //     );
            //
            //     return;
            // }

            this.$params('loading', true);

            await this.$refs.view.submitForm({isLocked: false});

            setTimeout(() => {
                this.itemsKey = _.uniqueId('itemsKey_');

                this.$nextTick(() => {
                    this.$params('loading', false);
                });
            }, 100);
        },
        async handleLock() {
            this.$params('loading', true);

            await this.$refs.view.submitForm({isLocked: true});

            setTimeout(() => {
                this.itemsKey = _.uniqueId('itemsKey_');

                this.$nextTick(() => {
                    this.$params('loading', false);
                });
            }, 100);
        },
        handleUpdateEWaybill() {
            this.$program.dialog({
                component: 'logistics.operations.logistics-detail',
                params: {
                    forcedPreview: false,
                    id: this.model.logisticId
                },
                onSubmit: () => {}
            });
        },
        async handleCancel() {
            let isConfirmed = await new Promise(resolve => {
                this.$program.alert(
                    'confirm',
                    this.$t('You are about to cancel the document. Do you want continue?'),
                    confirmed => {
                        resolve(confirmed);
                    }
                );
            });

            if (this.isIntegrationTransfer && this.integrationTransferType !== 'external-marketplace-order') {
                isConfirmed = await new Promise(resolve => {
                    this.$program.alert(
                        'confirm',
                        this.$t('Integration order will be canceled as well. Do you want continue?'),
                        confirmed => {
                            resolve(confirmed);
                        }
                    );
                });
            }

            if (!isConfirmed) return;

            if (this.model.status === 'approved') {
                if ((this.relatedDocuments || []).some(rd => rd.collection === 'accounting.journal-entries')) {
                    isConfirmed = await new Promise(resolve => {
                        this.$program.alert(
                            'confirm',
                            this.$t('Related accounting records will be created and posted. Do you want continue?'),
                            confirmed => {
                                resolve(confirmed);
                            }
                        );
                    });

                    if (!isConfirmed) return;
                }

                this.$params('loading', true);

                try {
                    await this.$rpc('inventory.cancel-transfer', this.$params('id'));

                    await this.$refs.view.refreshModel();

                    this.$program.message('success', this.$t('Transfer canceled successfully.'));
                } catch (e) {
                    this.$program.message('error', e.message);
                }

                this.$params('loading', false);
            } else {
                this.$params('loading', true);

                try {
                    await this.$rpc('inventory.cancel-transfer', this.$params('id'));

                    await this.$refs.view.refreshModel();

                    this.$program.message('success', this.$t('Transfer canceled successfully.'));
                } catch (e) {
                    this.$program.message('error', e.message);
                }

                this.$params('loading', false);
            }
        },
        async handlePreviewEWaybill() {
            if (!this.model.hasWaybill) {
                this.$params('loading', true);

                let waybillTemplateId = '';
                const templates = await this.$collection('eops.templates').find({
                    type: 'e-waybill',
                    $select: ['_id']
                });
                if (templates.length > 0) {
                    const warehouse = await this.$collection('inventory.warehouses').findOne({
                        _id: this.operationType.warehouseId,
                        $select: ['branchId'],
                        $disableSoftDelete: true,
                        $disableActiveCheck: true,
                        $disableBranchCheck: true
                    });
                    const branchId = warehouse.branchId;

                    waybillTemplateId = await new Promise(resolve => {
                        this.$program.dialog({
                            component: 'eops-components-template-select',
                            params: {
                                title: this.$t('Template'),
                                type: 'e-waybill',
                                branchId,
                                model: {
                                    templateId: !this.$setting('system.multiBranch') ? templates[0]._id : null
                                }
                            },
                            onSubmit: ({templateId}) => {
                                resolve(templateId);
                            },
                            onClose: () => {
                                resolve('');
                            }
                        });
                    });

                    if (!waybillTemplateId) {
                        return;
                    }

                    this.$params('loading', true);
                }

                try {
                    const url = await this.$rpc('inventory.preview-e-waybill', {
                        transfer: {
                            ...this.model,
                            _id: this.$params('id')
                        },
                        waybillTemplateId
                    });

                    this.$program.dialog({
                        component: 'eops.waybills.e-waybills.preview',
                        params: {
                            title: this.model.code,
                            url
                        }
                    });
                } catch (error) {
                    this.$program.message('error', error.message);
                }

                this.$params('loading', false);
            } else {
                this.$params('loading', true);

                const ew = await this.$collection('logistics.logistics').findOne({
                    transferId: this.$params('id'),
                    $select: ['_id', 'code', 'waybillTemplateId']
                });

                if (!ew) {
                    this.$program.message('error', 'E-Waybill is not found!');

                    return;
                }

                let url = null;
                try {
                    url = await this.$rpc('eops.e-waybills-preview', ew._id);
                } catch (error) {
                    this.$program.message('error', error.message);
                }

                this.$program.dialog({
                    component: 'eops.waybills.e-waybills.preview',
                    params: {
                        title: ew.code,
                        url
                    }
                });

                this.$params('loading', false);
            }
        },
        handleChangeDocumentNo() {
            this.$program.dialog({
                component: 'inventory.operations.transfers.change-document-no',
                params: {
                    transferId: this.$params('id'),
                    transferRecordDate: this.model.recordDate,
                    model: {
                        documentNo: this.model.documentNo
                    }
                }
            });
        },
        handleCustomsDeclarationNo() {
            this.$program.dialog({
                component: 'inventory.operations.transfers.change-customs-declaration-no',
                params: {
                    transferId: this.$params('id'),
                    model: {
                        customsDeclarationNo: this.model.customsDeclarationNo
                    }
                }
            });
        },
        async handleImportItems(payload) {
            this.$params('loading', true);

            try {
                const importItems = await this.$rpc('inventory.import-transfer-items', {
                    fileId: payload._id
                });
                const items = [];

                for (const importItem of importItems) {
                    const item = await this.beforeSaveItem({
                        row: {
                            productId: importItem.productId,
                            stockQuantity: 0,
                            orderedQuantity: 0,
                            assignedQuantity: 0,
                            availableQuantity: 0,
                            requestedQty: importItem.requestedQty,
                            actualQty: 0,
                            unitPrice: importItem.unitPrice,
                            discount: 0,
                            unitPriceAfterDiscount: 0,
                            freight: 0,
                            cost: 0,
                            total: 0,
                            deliveryNote: importItem.deliveryNote
                        },
                        params: {
                            colDef: {field: 'productId'}
                        }
                    });

                    items.push(item);
                }

                if (items.length > 0) {
                    this.$set(this.model, 'items', fastCopy(this.model.items).concat(items));
                    this.afterSaveItem();
                }
            } catch (error) {
                this.$program.message('error', error.message);
            }

            this.$params('loading', false);
        },
        async beforeInit(model) {
            const company = this.$store.getters['session/company'];
            const user = this.$user;

            // Default currency.
            if (!model.currencyId) model.currencyId = company.currencyId;

            // Currency format.
            if (company.currencyId !== model.currencyId) {
                const currency = await this.$collection('kernel.currencies').findOne({
                    _id: model.currencyId,
                    $select: ['name', 'symbol', 'symbolPosition']
                });

                if (_.isObject(currency)) {
                    this.currencyFormat = {
                        currency: {
                            symbol: currency.symbol,
                            format: currency.symbolPosition === 'before' ? '%s %v' : '%v %s'
                        }
                    };
                }
            }

            // Save current currency rate.
            this.currentCurrencyRate = model.currencyRate;

            if (this.$params('id') && Array.isArray(model.stages) && model.stages.length > 0) {
                this.stages = model.stages;
            }
            if (!this.$params('id') && this.stages.length > 0 && !model.stageId) {
                model.stageId = this.stages[0]._id;
            }
            if (model.stageId && this.stages.length > 0) {
                this.activeStageIndex = _.findIndex(this.stages, stage => stage._id === model.stageId);
            }

            if (!Array.isArray(model.exchangeRates) || model.exchangeRates.length < 1) {
                const currencies = await this.$collection('kernel.currencies').find({
                    $select: ['name']
                });
                const exchangeRates = [];
                const payloads = [];

                for (const currency of currencies) {
                    if (currency.name === company.currency.name) {
                        continue;
                    }

                    payloads.push({
                        from: currency.name,
                        to: company.currency.name,
                        value: 1,
                        options: {
                            date: model.issueDate
                        }
                    });
                }

                for (const payload of await this.$rpc('kernel.common.convert-currencies', payloads)) {
                    exchangeRates.push({
                        currencyName: payload.from,
                        rate: payload.rate
                    });
                }

                model.exchangeRates = exchangeRates;
            }

            // Get operation type.
            if (model.operationTypeId) {
                this.operationType = this.operationTypes.find(o => o._id === model.operationTypeId);
                if (!_.isPlainObject(this.operationType)) {
                    this.operationType = await this.$collection('inventory.operation-types').findOne({
                        _id: model.operationTypeId,
                        $disableSoftDelete: true,
                        $disableActiveCheck: true
                    });
                }

                // Keep initial values during editing
                if (this.$params('id')) {
                    this.originalOperationTypeId = model.operationTypeId;
                    this.originalCode = model.code;
                } else {
                    this.originalOperationTypeId = null;
                    this.originalCode = null;
                }

                this.itemsKey = _.uniqueId('itemsKey_');

                this.mainLocationId = (
                    await this.$collection('inventory.locations').findOne({
                        type: 'internal',
                        warehouseId: this.operationType.warehouseId,
                        $select: ['_id']
                    })
                )._id;

                if (!model.branchId) {
                    model.branchId = this.operationType.warehouse.branchId;
                }

                // Journal.
                const journal = await this.$collection('accounting.journals').findOne({
                    type: 'stock',
                    $select: ['_id']
                });
                if (!model.journalId) {
                    model.journalId = journal._id;
                }
                if (!model.journalDescription) {
                    if (this.operationType.type === 'incoming') {
                        model.journalDescription = this.$t('Inventory - Purchase goods receipts');
                    } else if (this.operationType.type === 'outgoing') {
                        model.journalDescription = this.$t('Inventory - Cost of goods sold');
                    } else {
                        model.journalDescription = '';
                    }
                }
            }

            if (model.sourceLocationId) {
                this.selectedSourceLocation = await this.$collection('inventory.locations').findOne({
                    _id: model.sourceLocationId,
                    $select: ['name', 'path', 'tree']
                });
            }
            if (model.destinationLocationId) {
                this.selectedDestinationLocation = await this.$collection('inventory.locations').findOne({
                    _id: model.destinationLocationId,
                    $select: ['name', 'path', 'tree']
                });
            }

            if (!!model.deliveryMethodId) {
                const deliveryMethod = await this.$collection('logistics.delivery-methods').findOne({
                    _id: model.deliveryMethodId,
                    $select: ['type'],
                    $disableActiveCheck: true,
                    $disableSoftDelete: true
                });

                if (!!deliveryMethod) {
                    this.deliveryMethodType = deliveryMethod.type;
                } else {
                    this.deliveryMethodType = null;
                }
            } else {
                this.deliveryMethodType = null;
            }

            const basicProductionOrderDocument = (model.relatedDocuments || []).find(
                relatedDocument => relatedDocument.collection === 'basic-production.orders'
            );
            const productionOrderDocument = (model.relatedDocuments || []).find(
                relatedDocument => relatedDocument.collection === 'production.orders'
            );

            let relatedDocumentsChanged = false;

            if (productionOrderDocument) {
                const targetOrders = await this.$app.collection('production.orders').find({
                    _id: {$in: productionOrderDocument.ids}
                });

                if (targetOrders.length === 0) {
                    model.relatedDocuments = model.relatedDocuments.filter(
                        relatedDocument => relatedDocument.collection !== 'production.orders'
                    );

                    relatedDocumentsChanged = true;
                }
            }

            if (basicProductionOrderDocument) {
                const targetOrders = await this.$app.collection('basic-production.orders').find({
                    _id: {$in: basicProductionOrderDocument.ids}
                });

                if (targetOrders.length === 0) {
                    model.relatedDocuments = model.relatedDocuments.filter(
                        relatedDocument => relatedDocument.collection !== 'basic-production.orders'
                    );

                    relatedDocumentsChanged = true;
                }
            }

            if (relatedDocumentsChanged) {
                await this.$app
                    .collection('inventory.transfers')
                    .patch({_id: model._id}, {relatedDocuments: model.relatedDocuments});
            }

            // Init related documents.
            const relatedDocuments = model.relatedDocuments || [];
            if (!!this.$params('id')) {
                const copiedDocumentRecords = await this.$collection('kernel.copied-document-records').find({
                    destinationDocumentId: this.$params('id')
                });

                for (const copiedDocumentRecord of copiedDocumentRecords) {
                    const collection = copiedDocumentRecord.sourceDocumentCollection;
                    const view = copiedDocumentRecord.sourceDocumentView;
                    const title = copiedDocumentRecord.sourceDocumentCollectionLabel;
                    const id = copiedDocumentRecord.sourceDocumentId;
                    const existingIndex = relatedDocuments.findIndex(rd => rd.collection === collection);

                    if (existingIndex !== -1) {
                        relatedDocuments[existingIndex].ids.push(id);
                        relatedDocuments[existingIndex].ids = _.uniq(relatedDocuments[existingIndex].ids);
                    } else {
                        relatedDocuments.push({collection, view, title, ids: [id]});
                    }
                }
            }
            this.relatedDocuments = relatedDocuments;

            if (_.isPlainObject(model.integrationPayload)) {
                model.integrationErrorCount = await this.$collection('kernel.logs').count({
                    'props.referenceId': model._id,
                    collection: 'inventory.integrations'
                });

                const transactions = model.integrationPayload.transactions;
                if (Array.isArray(transactions) && transactions.length > 0) {
                    model.transactions = transactions.map(transaction => ({
                        status: this.$t(transaction.label),
                        date: transaction.date
                    }));
                }
            }

            // Default organization.
            if (user.partnerId && this.$setting('inventory.inventoryOrganizations')) {
                const organization = this.organizations.find(
                    o => Array.isArray(o.team) && o.team.some(m => m.partnerId === user.partnerId)
                );

                if (_.isObject(organization)) {
                    const team = organization.team || [];
                    const currentMember = team.find(m => m.partnerId === user.partnerId);

                    this.organizationSettings = organization.settings || {};
                    this.organizationTeam = team;

                    if (!this.$params('id') && !model.organizationId) {
                        model.organizationId = organization._id;
                    }

                    if (
                        _.isObject(currentMember) &&
                        _.isObject(currentMember.settings) &&
                        !_.isEmpty(currentMember.settings)
                    ) {
                        this.organizationSettings = _.assign(this.organizationSettings, currentMember.settings);
                    }
                }
            }
            if (_.isEmpty(this.organizationSettings) && model.organizationId) {
                const organization = this.organizations.find(o => o._id === model.organizationId);

                if (_.isPlainObject(organization)) {
                    const team = organization.team || [];
                    const currentMember = team.find(m => m.partnerId === user.partnerId);

                    this.organizationSettings = organization.settings || {};
                    this.organizationTeam = team;

                    if (
                        _.isObject(currentMember) &&
                        _.isObject(currentMember.settings) &&
                        !_.isEmpty(currentMember.settings)
                    ) {
                        this.organizationSettings = _.assign(this.organizationSettings, currentMember.settings);
                    }
                }
            }

            this.modelInitialized = true;

            return model;
        },
        async beforeValidate(model) {
            // if (model.shippingDocumentType === 'waybill') {
            //     const issueDate = model.issueDate;
            //     const scheduledDate = model.issueDate;
            //
            //     if (scheduledDate.getTime() < issueDate.getTime()) {
            //         throw new this.$app.errors.Unprocessable({
            //             message: this.$t('Scheduled date can not be before issue date!'),
            //             field: 'scheduledDate'
            //         });
            //     }
            // }

            // Validate locations.
            if (this.operationType.type === 'incoming') {
                if (!model.sourceLocationId) {
                    model.sourceLocationId = this.vendorLocationId;
                }

                if (!model.destinationLocationId) {
                    throw new this.$app.errors.Unprocessable({
                        message: this.$t('{{label}} is required', {
                            label: this.$t('Destination location')
                        }),
                        field: 'destinationLocationId'
                    });
                }
            } else if (this.operationType.type === 'outgoing') {
                if (!model.destinationLocationId) {
                    model.destinationLocationId = this.customerLocationId;
                }

                if (!model.sourceLocationId) {
                    throw new this.$app.errors.Unprocessable({
                        message: this.$t('{{label}} is required', {
                            label: this.$t('Source location')
                        }),
                        field: 'sourceLocationId'
                    });
                }
            } else if (this.operationType.type === 'internal') {
                if (!model.sourceLocationId) {
                    throw new this.$app.errors.Unprocessable({
                        message: this.$t('{{label}} is required', {
                            label: this.$t('Source location')
                        }),
                        field: 'sourceLocationId'
                    });
                }

                if (!model.destinationLocationId) {
                    throw new this.$app.errors.Unprocessable({
                        message: this.$t('{{label}} is required', {
                            label: this.$t('Destination location')
                        }),
                        field: 'destinationLocationId'
                    });
                }
            }

            if (!model.stageId && this.stages.length > 0) {
                throw new this.$app.errors.Unprocessable({
                    message: this.$t('{{label}} is required', {
                        label: this.$t('Stage')
                    }),
                    field: 'stageId'
                });
            }

            // Check items.
            if (model.items.some(item => !item.unitId)) {
                throw new this.$app.errors.Unprocessable(this.$t('{{label}} is required', {label: this.$t('Unit')}));
            }

            return model;
        },
        async beforeSubmit(model) {
            // Persist code.
            if (this.$params('id')) {
                if (model.operationTypeId !== this.originalOperationTypeId) {
                    model.code = await this.generateCode(true);
                }
            } else {
                model.code = await this.generateCode(true);
            }

            if (!!this.createPartialDelivery) {
                model.createPartialDelivery = true;

                this.createPartialDelivery = false;
            }

            if (!!this.$refs && !!this.$refs.additionalInformationForm) {
                model.additionalInformation = await this.$refs.additionalInformationForm.submitForm();

                if (!!this.$refs.additionalInformationForm.additionalInformation?._id) {
                    model.additionalInformationId = this.$refs.additionalInformationForm.additionalInformation._id;
                }
            }

            return model;
        },
        async afterSubmit(result, params) {
            const model = this.model;
            const id = result._id;

            const relatedDocuments = model.relatedDocuments || [];
            const copiedDocumentRecords = await this.$collection('kernel.copied-document-records').find({
                destinationDocumentId: id
            });
            for (const copiedDocumentRecord of copiedDocumentRecords) {
                const collection = copiedDocumentRecord.sourceDocumentCollection;
                const view = copiedDocumentRecord.sourceDocumentView;
                const title = copiedDocumentRecord.sourceDocumentCollectionLabel;
                const id = copiedDocumentRecord.sourceDocumentId;
                const existingIndex = relatedDocuments.findIndex(rd => rd.collection === collection);

                if (existingIndex !== -1) {
                    relatedDocuments[existingIndex].ids.push(id);
                    relatedDocuments[existingIndex].ids = _.uniq(relatedDocuments[existingIndex].ids);
                } else {
                    relatedDocuments.push({collection, view, title, ids: [id]});
                }
            }
            this.relatedDocuments = relatedDocuments;

            if (params.type === 'create') {
                setTimeout(() => {
                    this.itemsKey = _.uniqueId('itemsKey_');
                }, 150);
            }
        },
        async handleChange(model, field) {
            const company = this.$store.getters['session/company'];

            if (field === 'operationTypeId' && model.operationTypeId) {
                this.$params('loading', true);

                // Get operation type.
                this.operationType = this.operationTypes.find(o => o._id === model.operationTypeId);
                if (!_.isObject(this.operationType)) {
                    this.operationType = await this.$collection('inventory.operation-types').findOne({
                        _id: model.operationTypeId,
                        $disableSoftDelete: true,
                        $disableActiveCheck: true
                    });
                }

                // Set operation default locations.
                if (this.operationType.type === 'incoming') {
                    if (model.documentType === 'production-transfers') {
                        this.model.sourceLocationId = this.productionLocationId;
                    } else if (model.documentType === 'project-transfers') {
                        this.model.sourceLocationId = this.projectLocationId;
                    } else {
                        this.model.sourceLocationId = this.vendorLocationId;
                    }
                    this.model.destinationLocationId = this.operationType.defaultDestinationLocationId;
                } else if (this.operationType.type === 'outgoing') {
                    this.model.sourceLocationId = this.operationType.defaultSourceLocationId;
                    if (model.documentType === 'production-transfers') {
                        this.model.destinationLocationId = this.productionLocationId;
                    } else if (model.documentType === 'project-transfers') {
                        this.model.destinationLocationId = this.projectLocationId;
                    } else {
                        this.model.destinationLocationId = this.customerLocationId;
                    }
                } else if (this.operationType.type === 'internal') {
                    this.model.sourceLocationId = this.operationType.defaultSourceLocationId;
                    this.model.destinationLocationId = this.operationType.defaultDestinationLocationId;
                    this.model.shippingDocumentType = 'none';
                }

                this.selectedSourceLocation = await this.$collection('inventory.locations').findOne({
                    _id: this.model.sourceLocationId,
                    $select: ['name', 'path', 'tree']
                });
                this.selectedDestinationLocation = await this.$collection('inventory.locations').findOne({
                    _id: this.model.destinationLocationId,
                    $select: ['name', 'path', 'tree']
                });

                this.model.branchId = this.operationType.warehouse.branchId;

                if (this.operationType.type === 'incoming') {
                    const warehouse = await this.$collection('inventory.warehouses').findOne({
                        _id: this.operationType.warehouseId,
                        $select: ['address']
                    });

                    if (warehouse.address) {
                        this.model.deliveryAddress = warehouse.address;
                    }
                } else {
                    this.model.deliveryAddress = {};
                }

                // When creating, generate code according to selected operation type.
                if (this.$params('id')) {
                    if (model.operationTypeId === this.originalOperationTypeId) {
                        this.model.code = this.originalCode;
                    } else {
                        this.model.code = await this.generateCode(false);
                    }
                } else {
                    this.model.code = await this.generateCode(false);
                }

                // Journal.
                const journal = await this.$collection('accounting.journals').findOne({
                    type: 'stock',
                    $select: ['_id']
                });
                this.model.journalId = journal._id;
                if (this.operationType.type === 'incoming') {
                    this.model.journalDescription = this.$t('Inventory - Purchase goods receipts');
                } else if (this.operationType.type === 'outgoing') {
                    this.model.journalDescription = this.$t('Inventory - Cost of goods sold');
                } else {
                    this.model.journalDescription = '';
                }

                this.$params('loading', false);
            } else if (field === 'sourceLocationId' && model.sourceLocationId) {
                this.selectedSourceLocation = await this.$collection('inventory.locations').findOne({
                    _id: model.sourceLocationId,
                    $select: ['name', 'path', 'tree']
                });
            } else if (field === 'destinationLocationId' && model.destinationLocationId) {
                this.selectedDestinationLocation = await this.$collection('inventory.locations').findOne({
                    _id: model.destinationLocationId,
                    $select: ['name', 'path', 'tree']
                });
            } else if (field === 'branchId') {
                this.model.code = '';
                this.model.operationTypeId = '';
                this.model.sourceLocationId = '';
                this.model.destinationLocationId = '';
                this.model.shippingDocumentType = 'none';
                this.model.operationType = {};
                this.model.deliveryAddress = {};
                this.model.invoiceAddress = {};
                this.itemsKey = _.uniqueId('itemsKey_');
                this.sourceLocationIdKey = _.uniqueId('sourceLocationIdKey_');
                this.destinationLocationIdKey = _.uniqueId('destinationLocationIdKey_');
            } else if (field === 'partnerId') {
                if (!!model.partnerId) {
                    const partner = await this.$collection('kernel.partners').findOne({
                        _id: model.partnerId,
                        $select: ['_id', 'type', 'currencyId', 'address', 'defaultDeliveryOptionId']
                    });

                    if (partner && partner.defaultDeliveryOptionId) {
                        this.model.carrierId = partner.defaultDeliveryOptionId;
                    }

                    if (this.operationType.type === 'incoming' || this.operationType.type === 'internal') {
                        const warehouse = await this.$collection('inventory.warehouses').findOne({
                            _id: this.operationType.warehouseId,
                            $select: ['address']
                        });

                        if (warehouse.address) {
                            this.model.deliveryAddress = warehouse.address;
                        }

                        if (!!partner && partner.address) {
                            this.model.invoiceAddress = partner.address;
                        }
                    } else if (this.operationType.type === 'outgoing') {
                        if (!!partner && partner.address) {
                            this.model.invoiceAddress = partner.address;
                            this.model.deliveryAddress = partner.address;
                        }
                    }

                    // Partner currency.
                    if (!!partner && partner.currencyId) {
                        this.model.currencyId = partner.currencyId;

                        await this.handleChange(this.model, 'currencyId');
                    }

                    this.partnerType = partner.type;
                } else {
                    this.partnerType = null;
                    this.model.priceSourceId = null;
                }
            } else if (field === 'currencyId') {
                if (model.currencyId) {
                    const currency = await this.$collection('kernel.currencies').findOne({
                        _id: model.currencyId,
                        $select: ['name', 'symbol', 'symbolPosition']
                    });

                    if (_.isObject(currency)) {
                        this.currencyFormat = {
                            currency: {
                                symbol: currency.symbol,
                                format: currency.symbolPosition === 'before' ? '%s %v' : '%v %s'
                            }
                        };

                        if (company.currencyId !== model.currencyId) {
                            this.model.currencyRate = await this.$convertCurrency({
                                from: currency.name,
                                to: company.currency.name,
                                value: 1,
                                options: {
                                    date: model.issueDate
                                }
                            });
                        } else {
                            this.model.currencyRate = 1;
                        }
                    }
                } else {
                    this.model.currencyId = company.currencyId;
                    this.model.currencyRate = 1;
                }

                this.$params('loading', true);
                const items = [];
                for (const item of model.items || []) {
                    const row = await this.beforeSaveItem({
                        row: {
                            ...item,
                            unitPrice: this.$app.round(
                                (this.currentCurrencyRate * (item.unitPrice || 0)) / this.model.currencyRate,
                                'unit-price'
                            ),
                            freight: this.$app.round(
                                (this.currentCurrencyRate * (item.freight || 0)) / this.model.currencyRate,
                                'total'
                            )
                        },
                        params: {
                            colDef: {field: 'unitPrice'}
                        }
                    });

                    items.push(row);
                }
                this.$set(this.model, 'items', items);

                // Reset totals.
                await this.calculateTotals();

                this.$nextTick(() => {
                    this.itemsKey = _.uniqueId('itemsKey_');
                    this.currentCurrencyRate = this.model.currencyRate;

                    this.$params('loading', false);
                });
            } else if (field === 'currencyRate') {
                this.currentCurrencyRate = model.currencyRate || 1;
            } else if (field === 'issueDate') {
                if (company.currencyId !== model.currencyId) {
                    const currency = await this.$collection('kernel.currencies').findOne({
                        _id: model.currencyId,
                        $select: ['name']
                    });

                    this.model.currencyRate = await this.$convertCurrency({
                        from: currency.name,
                        to: company.currency.name,
                        value: 1,
                        options: {
                            date: model.issueDate
                        }
                    });
                }

                await (async () => {
                    const currencies = await this.$collection('kernel.currencies').find({
                        $select: ['name']
                    });
                    const exchangeRates = [];
                    const payloads = [];

                    for (const currency of currencies) {
                        if (currency.name === company.currency.name) {
                            continue;
                        }

                        payloads.push({
                            from: currency.name,
                            to: company.currency.name,
                            value: 1,
                            options: {
                                date: model.issueDate
                            }
                        });
                    }

                    for (const payload of await this.$rpc('kernel.common.convert-currencies', payloads)) {
                        exchangeRates.push({
                            currencyName: payload.from,
                            rate: payload.rate
                        });
                    }

                    this.model.exchangeRates = exchangeRates;
                })();

                this.$nextTick(() => {
                    this.currentCurrencyRate = this.model.currencyRate;
                });

                this.model.scheduledDate = model.issueDate;
            }

            if (
                (field === 'operationTypeId' && model.operationTypeId) ||
                (field === 'sourceLocationId' && model.sourceLocationId) ||
                (field === 'destinationLocationId' && model.destinationLocationId)
            ) {
                // Update items.
                const items = [];
                for (const item of this.model.items || []) {
                    const row = await this.beforeSaveItem({
                        row: {
                            productId: item.productId,
                            unitId: item.unitId,
                            unitPrice: item.unitPrice,
                            requestedQty: item.requestedQty,
                            actualQty: 0,
                            subItems: []
                        },
                        params: {
                            colDef: {field: 'productId'}
                        }
                    });

                    items.push(row);
                }
                this.model.items = items;
            } else if (field === 'deliveryMethodId') {
                if (!!model.deliveryMethodId) {
                    const deliveryMethod = await this.$collection('logistics.delivery-methods').findOne({
                        _id: model.deliveryMethodId,
                        $select: ['type'],
                        $disableActiveCheck: true,
                        $disableSoftDelete: true
                    });

                    if (!!deliveryMethod) {
                        this.deliveryMethodType = deliveryMethod.type;
                    } else {
                        this.deliveryMethodType = null;
                    }
                } else {
                    this.deliveryMethodType = null;
                }
            } else if (field === 'items') {
                setTimeout(() => {
                    this.model.freight = null;
                    const items = fastCopy(model.items).map(item => {
                        item.freight = 0;
                        item.cost = item.unitPriceAfterDiscount * item.actualQty + (item.freight || 0);

                        return item;
                    });
                    this.$set(this.model, 'items', items);
                }, 500);
            } else if (field === 'stageId') {
                const currentStage = this.stages[this.activeStageIndex];
                const nextStageIndex = _.findIndex(this.stages, stage => stage._id === model.stageId);
                const nextStage = this.stages[nextStageIndex];

                if (_.isObject(currentStage)) {
                    const history = {};
                    history.date = this.$datetime.local().toJSDate();
                    history.userId = this.$user._id;
                    history.username = this.$user.name;
                    history.probability = currentStage.probability;
                    history.stageId = currentStage._id;
                    history.stageName = currentStage.name;

                    let interval = '';
                    if (this.model.stageHistory.length > 0) {
                        const prevStageHistory = this.model.stageHistory[this.model.stageHistory.length - 1];
                        const now = this.$datetime.local();
                        const prevDate = this.$datetime.fromJSDate(prevStageHistory.date);
                        const diff = now.diff(prevDate).as('milliseconds');

                        let seconds = Math.abs(Math.round(diff / 1000));
                        let minutes = Math.abs(Math.round(diff / (1000 * 60)));
                        let hours = Math.abs(Math.round(diff / (1000 * 60 * 60)));
                        let days = Math.abs(Math.round(diff / (1000 * 60 * 60 * 24)));
                        let months = Math.abs(Math.round(diff / (1000 * 60 * 60 * 24 * 30)));

                        if (months) interval += `${months} ${this.$t('month(s)')}|`;
                        if (days) interval += `${days} ${this.$t('day(s)')}|`;
                        if (hours) interval += `${hours} ${this.$t('hour(s)')}|`;
                        if (minutes) interval += `${minutes} ${this.$t('minute(s)')}|`;
                        if (seconds) interval += `${seconds} ${this.$t('second(s)')}|`;
                        interval = trim(interval, '|').split('|').join(' ');

                        history.stageInterval = interval;
                    }

                    this.model.stageHistory = (this.model.stageHistory || []).concat([history]);
                }

                this.model.probability = nextStage.probability;
                this.activeStageIndex = nextStageIndex;
            }

            if (
                (field === 'currencyId' || field === 'currencyRate' || field === 'issueDate') &&
                Array.isArray(this.model.exchangeRates) &&
                this.model.exchangeRates.length > 0 &&
                company.currencyId !== this.model.currencyId
            ) {
                const currency = await this.$collection('kernel.currencies').findOne({
                    _id: this.model.currencyId,
                    $select: ['name'],
                    $disableActiveCheck: true,
                    $disableSoftDelete: true
                });
                const index = this.model.exchangeRates.findIndex(er => er.currencyName === currency.name);

                if (index !== -1) {
                    const exchangeRates = fastCopy(this.model.exchangeRates);

                    exchangeRates[index].rate = this.model.currencyRate;

                    this.model.exchangeRates = exchangeRates;
                }
            }

            if (field === 'deliveryAddressId') {
                const contact = await this.$collection('kernel.contacts').findOne({
                    _id: model.deliveryAddressId,
                    $select: ['address']
                });

                this.model.deliveryAddress = (contact || {}).address || '';
            }
            if (field === 'invoiceAddressId') {
                const contact = await this.$collection('kernel.contacts').findOne({
                    _id: model.invoiceAddressId,
                    $select: ['address']
                });

                this.model.invoiceAddress = (contact || {}).address || '';
            }
        },
        handleAddMultipleProducts() {
            const self = this;
            const filters = {
                type: {$ne: 'service'},
                isSimple: true
            };

            if (_.isObject(this.organizationSettings) && !_.isEmpty(this.organizationSettings)) {
                filters.groupIds = {
                    $in: this.organizationSettings.productGroupIds || []
                };
            }

            this.$program.dialog({
                component: 'inventory.catalog.products.master',
                params: {
                    filters
                },
                async onSelect(products) {
                    const items = [];

                    self.$params('loading', true);

                    for (const product of products) {
                        const item = await self.beforeSaveItem({
                            row: {
                                productId: product._id,
                                stockQuantity: 0,
                                orderedQuantity: 0,
                                assignedQuantity: 0,
                                availableQuantity: 0,
                                requestedQty: 0,
                                actualQty: 0,
                                unitPrice: 0,
                                discount: 0,
                                unitPriceAfterDiscount: 0,
                                freight: 0,
                                cost: 0,
                                total: 0
                            },
                            params: {
                                colDef: {field: 'productId'}
                            }
                        });

                        items.push(item);
                    }

                    self.$set(self.model, 'items', fastCopy(self.model.items).concat(items));
                    await self.afterSaveItem();
                    self.$params('loading', false);
                }
            });
        },
        handleAddKitProducts() {
            const self = this;

            const filters = {
                type: {$ne: 'service'},
                isKit: true
            };

            if (_.isObject(this.organizationSettings) && !_.isEmpty(this.organizationSettings)) {
                filters.groupIds = {
                    $in: this.organizationSettings.productGroupIds || []
                };
            }

            this.$program.dialog({
                component: 'inventory.catalog.products.master',
                params: {
                    filters
                },
                async onSelect(selected) {
                    self.$params('loading', true);

                    const kits = await self.$collection('inventory.products').find({
                        _id: {$in: selected.map(s => s._id)},
                        $select: ['_id', 'subProducts', 'useSubProducts'],
                        $disableActiveCheck: true,
                        $disableSoftDelete: true
                    });
                    const items = [];

                    for (let kit of kits.filter(k => Array.isArray(k.subProducts))) {
                        for (const subProduct of kit.subProducts.filter(subProduct => subProduct.type !== 'service')) {
                            const item = await self.beforeSaveItem({
                                row: {
                                    productId: subProduct.productId,
                                    stockQuantity: 0,
                                    orderedQuantity: 0,
                                    assignedQuantity: 0,
                                    availableQuantity: 0,
                                    actualQty: 0,
                                    unitPrice: 0,
                                    discount: 0,
                                    unitPriceAfterDiscount: 0,
                                    freight: 0,
                                    cost: 0,
                                    total: 0
                                },
                                params: {
                                    colDef: {field: 'productId'}
                                }
                            });

                            items.push(item);
                        }
                    }

                    self.$set(self.model, 'items', fastCopy(self.model.items).concat(items));
                    await self.afterSaveItem();
                    self.$params('loading', false);
                }
            });
        },
        handleBulkEntry(itemId) {
            this.$program.dialog({
                component: 'inventory.operations.transfers.bulk-entry',
                params: {
                    title: this.$t('Bulk Entry'),
                    transferId: this.$params('id'),
                    type: this.operationType.type,
                    sourceLocationId: this.model.sourceLocationId,
                    destinationLocationId: this.model.destinationLocationId,
                    warehouseId: this.operationType.warehouseId,
                    items: (this.model.items ?? []).map(item => ({
                        id: item.id,
                        productId: item.productId,
                        productCode: item.productCode,
                        productDefinition: item.productDefinition,
                        barcode: item.barcode,
                        unitId: item.unitId,
                        requestedQty: item.requestedQty,
                        actualQty: item.actualQty ?? 0,
                        pcmConfigurationId: item.pcmConfigurationId,
                        pcmHash: item.pcmHash
                    })),
                    itemId,
                    forcedPreview: this.$params('isPreview'),
                    handlePrint: this.$refs.view.handlePrint
                },
                onSubmit: ({items: newItems}) => {
                    this.$params('loading', true);

                    const items = fastCopy(this.model.items);

                    for (const item of items) {
                        const newItem = newItems.find(newItem => newItem.id === item.id);

                        if (!!newItem) {
                            item.actualQty = newItem.actualQty;
                        }
                    }

                    this.$set(this.model, 'items', items);

                    this.calculateTotals();

                    this.$nextTick(() => {
                        this.$params('loading', false);
                    });
                }
            });
        },
        handlePack() {
            this.$params('isPreview', true);

            this.$program.dialog({
                component: 'inventory.operations.transfers.pack',
                params: {
                    title: this.$t('Pack'),
                    transferId: this.$params('id'),
                    transferCode: this.model.code,
                    partnerId: this.model.partnerId,
                    carrierId: this.model.carrierId,
                    items: this.model.items,
                    packageParams: this.model.packageParams || {}
                },
                onSubmit: model => {
                    this.model.packageParams = model;
                }
            });
        },
        handleBarcodeEntry() {
            const existingSerialNumbers = [];

            for (const item of this.model.items || []) {
                for (const subItem of item.subItems || []) {
                    if (!!subItem.serialNumber && !existingSerialNumbers.includes(subItem.serialNumber)) {
                        existingSerialNumbers.push(subItem.serialNumber);
                    }
                }
            }

            this.$program.dialog({
                component: 'inventory.components.barcode-finder',
                params: {
                    title: this.$t('Barcode Entry'),
                    checkSerialNumber: true,
                    operationType: this.operationType.type,
                    warehouseId: this.operationType.warehouseId,
                    requiredItems: this.model.items
                        .map(item => ({
                            productId: item.productId,
                            productCode: item.productCode,
                            productDefinition: item.productDefinition,
                            unitId: item.unitId,
                            requiredQuantity: item.requestedQty - item.actualQty
                        }))
                        .filter(item => item.requiredQuantity > 0),
                    existingSerialNumbers
                },
                onSubmit: async ({items}) => {
                    this.$params('loading', true);

                    const existingItems = fastCopy(this.model.items) || [];

                    if (existingItems.length > 0) {
                        for (const item of items) {
                            const index = existingItems.findIndex(ei => ei.productId === item.productId);

                            if (index === -1) {
                                continue;
                            }

                            const row = existingItems[index];

                            row.subItems = await this.$rpc(
                                'inventory.transfers-initialize-sub-items-for-barcode-entry',
                                {
                                    type: this.operationType.type,
                                    tracking: item.tracking,
                                    sourceLocationId: this.model.sourceLocationId,
                                    destinationLocationId: this.model.destinationLocationId,
                                    warehouseId: this.operationType.warehouseId,
                                    row,
                                    item
                                }
                            );

                            const ratio = item.quantity / item.baseQuantity;
                            row.actualQty = row.validatedQty = _.sumBy(row.subItems, 'quantity') * ratio;

                            existingItems[index] = row;
                        }

                        this.$set(this.model, 'items', existingItems);
                    } else if (!this.model.isLocked) {
                        const rows = [];

                        for (const item of items) {
                            const row = {};

                            row.id = Random.id(8);
                            row.productId = item.productId;
                            row.productCode = item.productCode;
                            row.productDefinition = item.productDefinition;
                            row.description = `${item.productCode} - ${item.productDefinition}`;
                            row.barcode = item.barcode;
                            row.unitId = item.unitId;
                            row.unitPrice = 0;
                            row.discount = 0;
                            row.unitPriceAfterDiscount = 0;
                            row.freight = 0;
                            row.cost = 0;
                            row.total = 0;
                            row.requestedQty = item.realQty;
                            row.actualQty = item.realQty;

                            let report = [];
                            if (this.operationType.type === 'incoming') {
                                report = await this.$rpc('inventory.get-stock-report', {
                                    ...(!!this.model.referenceId
                                        ? {
                                              externalReservationsQuery: {
                                                  documentId: {
                                                      $ne: this.model.referenceId
                                                  }
                                              }
                                          }
                                        : {}),
                                    date: this.model.scheduledDate || new Date(),
                                    productId: row.productId,
                                    query: {
                                        $or: [
                                            {
                                                'location.path': {
                                                    $regex: `^${toUpper(
                                                        escapeRegExp(this.selectedDestinationLocation.path)
                                                    )}`,
                                                    $options: 'i'
                                                }
                                            },
                                            {
                                                'location.path': {
                                                    $regex: `^${toLower(
                                                        escapeRegExp(this.selectedDestinationLocation.path)
                                                    )}`,
                                                    $options: 'i'
                                                }
                                            }
                                        ]
                                    }
                                });
                            } else if (this.operationType.type === 'internal') {
                                report = await this.$rpc('inventory.get-stock-report', {
                                    ...(!!this.model.referenceId
                                        ? {
                                              externalReservationsQuery: {
                                                  documentId: {
                                                      $ne: this.model.referenceId
                                                  }
                                              }
                                          }
                                        : {}),
                                    date: this.model.scheduledDate || new Date(),
                                    productId: row.productId,
                                    query: {
                                        $or: [
                                            {
                                                'location.path': {
                                                    $regex: `^${toUpper(
                                                        escapeRegExp(this.selectedSourceLocation.path)
                                                    )}`,
                                                    $options: 'i'
                                                }
                                            },
                                            {
                                                'location.path': {
                                                    $regex: `^${toLower(
                                                        escapeRegExp(this.selectedSourceLocation.path)
                                                    )}`,
                                                    $options: 'i'
                                                }
                                            }
                                        ]
                                    }
                                });
                            } else if (this.operationType.type === 'outgoing') {
                                report = await this.$rpc('inventory.get-stock-report', {
                                    ...(!!this.model.referenceId
                                        ? {
                                              externalReservationsQuery: {
                                                  documentId: {
                                                      $ne: this.model.referenceId
                                                  }
                                              }
                                          }
                                        : {}),
                                    date: this.model.scheduledDate || new Date(),
                                    productId: row.productId,
                                    query: {
                                        $or: [
                                            {
                                                'location.path': {
                                                    $regex: `^${toUpper(
                                                        escapeRegExp(this.selectedSourceLocation.path)
                                                    )}`,
                                                    $options: 'i'
                                                }
                                            },
                                            {
                                                'location.path': {
                                                    $regex: `^${toLower(
                                                        escapeRegExp(this.selectedSourceLocation.path)
                                                    )}`,
                                                    $options: 'i'
                                                }
                                            }
                                        ]
                                    }
                                });
                            }
                            if (report.length > 0) {
                                const r = report[0];

                                row.stockQuantity = r.stockQuantity;
                                row.orderedQuantity = r.orderedQuantity;
                                row.assignedQuantity = r.assignedQuantity;
                                row.availableQuantity = r.availableQuantity;
                            } else {
                                row.stockQuantity = 0;
                                row.orderedQuantity = 0;
                                row.assignedQuantity = 0;
                                row.availableQuantity = 0;
                            }

                            row.product = await this.$collection('inventory.products').findOne({
                                _id: row.productId,
                                $select: [
                                    'code',
                                    'name',
                                    'displayName',
                                    'definition',
                                    'barcode',
                                    'barcodes',
                                    'baseUnitId',
                                    'stockUnitId',
                                    'unitConversions',
                                    'unitRatios',
                                    'tracking'
                                ]
                            });
                            if (!!row.unitId) {
                                row.unit = await this.$collection('kernel.units').findOne({
                                    _id: row.unitId,
                                    $select: ['name'],
                                    $disableActiveCheck: true,
                                    $disableSoftDelete: true
                                });
                            }

                            row.subItems = await this.$rpc(
                                'inventory.transfers-initialize-sub-items-for-barcode-entry',
                                {
                                    type: this.operationType.type,
                                    tracking: item.tracking,
                                    sourceLocationId: this.model.sourceLocationId,
                                    destinationLocationId: this.model.destinationLocationId,
                                    warehouseId: this.operationType.warehouseId,
                                    row,
                                    item
                                }
                            );

                            const ratio = item.quantity / item.baseQuantity;
                            row.actualQty =
                                row.requestedQty =
                                row.validatedQty =
                                    _.sumBy(row.subItems, 'quantity') * ratio;

                            rows.push(row);
                        }

                        this.$set(this.model, 'items', rows);
                    }

                    this.$params('loading', false);
                }
            });
        },
        async beforeItemsInit(items) {
            if (!this.modelInitialized) return items;

            let productIds = [];
            let products = [];
            let unitIds = [];
            let units = [];
            let financialProjectIds = [];
            let financialProjects = [];
            items.forEach(item => {
                if (item.productId) productIds.push(item.productId);
                if (item.unitId) unitIds.push(item.unitId);
                if (item.financialProjectId) financialProjectIds.push(item.financialProjectId);
            });
            productIds = _.uniq(productIds);
            unitIds = _.uniq(unitIds);
            financialProjectIds = _.uniq(financialProjectIds);
            if (productIds.length > 0) {
                products = await this.$collection('inventory.products').find({
                    _id: {$in: productIds},
                    $select: [
                        'code',
                        'name',
                        'displayName',
                        'definition',
                        'barcode',
                        'stockUnitId',
                        'baseUnitId',
                        'unitConversions',
                        'unitRatios',
                        'tracking'
                    ],
                    $disableActiveCheck: true,
                    $disableSoftDelete: true
                });
            }
            if (unitIds.length > 0) {
                units = await this.$collection('kernel.units').find({
                    _id: {$in: unitIds},
                    $select: ['name'],
                    $disableActiveCheck: true,
                    $disableSoftDelete: true
                });
            }
            if (financialProjectIds.length > 0) {
                financialProjects = await this.$collection('kernel.financial-projects').find({
                    _id: {$in: financialProjectIds},
                    $select: ['code', 'name'],
                    $disableActiveCheck: true,
                    $disableSoftDelete: true
                });
            }

            return items.map(item => {
                if (item.productId && products.length > 0) {
                    item.product = products.find(product => product._id === item.productId);
                }

                if (item.unitId && units.length > 0) {
                    item.unit = units.find(unit => unit._id === item.unitId);
                }

                if (item.financialProjectId)
                    item.financialProject = financialProjects.find(
                        financialProject => financialProject._id === item.financialProjectId
                    );

                return item;
            });
        },
        async beforeSaveItem({row, params}) {
            let field = params.colDef.field;

            // Row id.
            if (!row.id) {
                row.id = Random.id(8);
            }

            // Find product with barcode.
            if (field === 'barcode' && row.barcode) {
                const product = await this.$collection('inventory.products').findOne({
                    $or: [{barcode: row.barcode}, {'barcodes.barcode': row.barcode}],
                    $select: ['_id']
                });

                if (_.isObject(product)) {
                    row.productId = product._id;

                    field = 'productId';
                } else {
                    throw new this.$app.errors.Unprocessable(this.$t('No product found with the supplied barcode!'));
                }
            }

            // Find product with code.
            if (field === 'productCode' && !!row.productCode) {
                const product = await this.$collection('inventory.products').findOne({
                    code: row.productCode,
                    $select: ['_id']
                });

                if (_.isObject(product)) {
                    row.productId = product._id;

                    field = 'productId';
                } else {
                    throw new this.$app.errors.Unprocessable(this.$t('No product found with the supplied code!'));
                }
            }

            // Find product with definition.
            if (field === 'productDefinition' && !!row.productDefinition) {
                const product = await this.$collection('inventory.products').findOne({
                    definition: row.productDefinition,
                    $select: ['_id']
                });

                if (_.isObject(product)) {
                    row.productId = product._id;

                    field = 'productId';
                } else {
                    throw new this.$app.errors.Unprocessable(this.$t('No product found with the supplied definition!'));
                }
            }

            if (_.isString(row.productId)) {
                const product = await this.$collection('inventory.products').findOne({
                    _id: row.productId,
                    $select: [
                        'code',
                        'name',
                        'displayName',
                        'definition',
                        'barcode',
                        'barcodes',
                        'baseUnitId',
                        'stockUnitId',
                        'unitConversions',
                        'unitRatios',
                        'tracking'
                    ]
                });
                row.product = product;

                if (field === 'productId' && _.isPlainObject(product)) {
                    // Get product code.
                    row.productCode = product.code;

                    // Get product definition.
                    row.productDefinition = product.definition;

                    // Get description.
                    row.description = product.displayName;

                    // Get product barcode.
                    row.barcode = product.barcode;

                    if (!row.unitId) {
                        row.unitId = product.stockUnitId;
                    }

                    // Barcode entered.
                    if (!!row.barcode) {
                        const item = (product.barcodes || []).find(item => item.barcode === row.barcode);

                        if (!!item) {
                            row.unitId = item.unitId;
                        }
                    }

                    // PCM Model.
                    if (this.$app.hasModule('pcm')) {
                        const model = await this.$collection('pcm.models').findOne({
                            productId: row.productId,
                            $select: ['_id']
                        });

                        if (!!model) {
                            row.pcmModelId = model._id;
                        }
                    }

                    let report = [];
                    if (this.operationType.type === 'incoming') {
                        report = await this.$rpc('inventory.get-stock-report', {
                            ...(!!this.model.referenceId
                                ? {
                                      externalReservationsQuery: {
                                          documentId: {
                                              $ne: this.model.referenceId
                                          }
                                      }
                                  }
                                : {}),
                            date: this.model.scheduledDate || new Date(),
                            productId: row.productId,
                            query: {
                                $or: [
                                    {
                                        'location.path': {
                                            $regex: `^${toUpper(escapeRegExp(this.selectedDestinationLocation.path))}`,
                                            $options: 'i'
                                        }
                                    },
                                    {
                                        'location.path': {
                                            $regex: `^${toLower(escapeRegExp(this.selectedDestinationLocation.path))}`,
                                            $options: 'i'
                                        }
                                    }
                                ]
                            },
                            ...(this.$app.hasModule('pcm') && !!row.pcmHash ? {pcmHash: row.pcmHash} : {})
                        });
                    } else if (this.operationType.type === 'internal') {
                        report = await this.$rpc('inventory.get-stock-report', {
                            ...(!!this.model.referenceId
                                ? {
                                      externalReservationsQuery: {
                                          documentId: {
                                              $ne: this.model.referenceId
                                          }
                                      }
                                  }
                                : {}),
                            date: this.model.scheduledDate || new Date(),
                            productId: row.productId,
                            query: {
                                $or: [
                                    {
                                        'location.path': {
                                            $regex: `^${toUpper(escapeRegExp(this.selectedSourceLocation.path))}`,
                                            $options: 'i'
                                        }
                                    },
                                    {
                                        'location.path': {
                                            $regex: `^${toLower(escapeRegExp(this.selectedSourceLocation.path))}`,
                                            $options: 'i'
                                        }
                                    }
                                ]
                            },
                            ...(this.$app.hasModule('pcm') && !!row.pcmHash ? {pcmHash: row.pcmHash} : {})
                        });
                    } else if (this.operationType.type === 'outgoing') {
                        report = await this.$rpc('inventory.get-stock-report', {
                            ...(!!this.model.referenceId
                                ? {
                                      externalReservationsQuery: {
                                          documentId: {
                                              $ne: this.model.referenceId
                                          }
                                      }
                                  }
                                : {}),
                            date: this.model.scheduledDate || new Date(),
                            productId: row.productId,
                            query: {
                                $or: [
                                    {
                                        'location.path': {
                                            $regex: `^${toUpper(escapeRegExp(this.selectedSourceLocation.path))}`,
                                            $options: 'i'
                                        }
                                    },
                                    {
                                        'location.path': {
                                            $regex: `^${toLower(escapeRegExp(this.selectedSourceLocation.path))}`,
                                            $options: 'i'
                                        }
                                    }
                                ]
                            },
                            ...(this.$app.hasModule('pcm') && !!row.pcmHash ? {pcmHash: row.pcmHash} : {})
                        });
                    }
                    if (report.length > 0) {
                        const r = report[0];

                        row.stockQuantity = r.stockQuantity;
                        row.orderedQuantity = r.orderedQuantity;
                        row.assignedQuantity = r.assignedQuantity;
                        row.availableQuantity = r.availableQuantity;
                    } else {
                        row.stockQuantity = 0;
                        row.orderedQuantity = 0;
                        row.assignedQuantity = 0;
                        row.availableQuantity = 0;
                    }
                }

                if (!!row.unitId) {
                    row.unit = await this.$collection('kernel.units').findOne({
                        _id: row.unitId,
                        $select: ['name'],
                        $disableActiveCheck: true,
                        $disableSoftDelete: true
                    });
                }
                if (!!row.financialProjectId) {
                    row.financialProject = await this.$collection('kernel.financial-projects').findOne({
                        _id: row.financialProjectId,
                        $select: ['code', 'name'],
                        $disableActiveCheck: true,
                        $disableSoftDelete: true
                    });
                }

                if (
                    field === 'requestedQty' &&
                    !!this.$setting('inventory.useBulkEntryInTransfers') &&
                    !this.hasSubItems
                ) {
                    row.actualQty = 0;
                }

                row.unitPriceAfterDiscount = (row.unitPrice || 0) - ((row.unitPrice || 0) * (row.discount || 0)) / 100;
                row.cost = row.unitPriceAfterDiscount * row.actualQty + (row.freight || 0);
                row.total = row.unitPriceAfterDiscount * row.actualQty;

                if (
                    field === 'requestedQty' &&
                    !!this.$params('id') &&
                    !!this.$setting('inventory.useBulkEntryInTransfers') &&
                    !this.hasSubItems
                ) {
                    await this.$collection('inventory.transfer-sub-items').bulkWrite([
                        {
                            deleteMany: {
                                filter: {transferId: this.$params('id'), itemId: row.id}
                            }
                        }
                    ]);
                }

                if (
                    field === 'actualQty' &&
                    !!row.unitId &&
                    row.actualQty > 0 &&
                    row.product.tracking !== 'serial' &&
                    row.product.tracking !== 'lot' &&
                    !this.$setting('inventory.storageLocations')
                ) {
                    if (!this.$setting('inventory.useBulkEntryInTransfers') || this.hasSubItems) {
                        if (this.operationType.type === 'outgoing') {
                            row.subItems = [
                                {
                                    unitId: row.unitId,
                                    sourceLocationId: this.mainLocationId,
                                    destinationLocationId: this.customerLocationId,
                                    quantity: row.actualQty
                                }
                            ];
                        } else {
                            row.subItems = [
                                {
                                    unitId: row.unitId,
                                    sourceLocationId: this.vendorLocationId,
                                    destinationLocationId: this.mainLocationId,
                                    quantity: row.actualQty
                                }
                            ];
                        }
                    }
                }
            }

            return row;
        },
        async afterSaveItem() {
            this.$nextTick(() => {
                this.calculateTotals();
            });
        },
        async afterRemoveItem({row}) {
            if (this.model.items.length === 0) {
                this.model.discount = 0;
                this.model.discountAmount = 0;
            }

            this.calculateTotals();

            if (!!this.$params('id')) {
                await this.$collection('inventory.transfer-sub-items').bulkWrite([
                    {
                        deleteMany: {
                            filter: {transferId: this.$params('id'), itemId: row.id}
                        }
                    }
                ]);
            }
        },
        async handleCheckItemsAvailability() {
            this.$params('loading', true);
            this.model.items = await this.$rpc('inventory.transfers-check-items-availability', {
                type: this.operationType.type,
                model: this.model,
                selectedSourceLocation: this.selectedSourceLocation,
                selectedDestinationLocation: this.selectedDestinationLocation
            });
            this.$params('loading', false);
        },
        handleApplyFinancialProject() {
            if (this.model.items.length > 0) {
                this.$program.alert('confirm', this.$t('Do you want to apply this to all rows?'), confirmed => {
                    if (confirmed) {
                        this.model.items = fastCopy(this.model.items).map(item => {
                            item.financialProjectId = this.model.financialProjectId;

                            return item;
                        });
                    }
                });
            }
        },
        openItemDetail(row, gridParams) {
            if (!(row.requestedQty > 0)) {
                this.$program.message('error', this.$t('Requested quantity must be greater than zero!'));
                return;
            }

            if (!this.$params('id')) {
                this.$program.message('error', this.$t('First you have to save the transfer!'));
                return;
            }

            const self = this;
            // const originalItems = fastCopy(this.model.items);

            const params = {};
            params.rowId = row.id;
            params.transferId = this.$params('id');
            params.transfer = fastCopy(this.model);
            params.operationTypeId = this.operationType._id;
            params.productId = row.productId;
            params.unitId = row.unitId;
            params.sourceLocationId = this.model.sourceLocationId;
            params.destinationLocationId = this.model.destinationLocationId;
            params.requestedQty = row.requestedQty;
            params.items = row.subItems || [];
            params.forcedPreview = this.$params('id') && this.$params('isPreview');

            this.$program.dialog({
                component: 'inventory.operations.transfers.item',
                params,
                onSubmit: async () => {
                    this.$params('loading', true);

                    await this.$refs.view.refreshModel();

                    this.$nextTick(async () => {
                        const items = [];
                        for (const item of this.model.items || []) {
                            const row = await this.beforeSaveItem({
                                row: {
                                    ...item
                                },
                                params: {
                                    colDef: {field: 'unitPrice'}
                                }
                            });

                            items.push(row);
                        }
                        this.$set(this.model, 'items', items);

                        this.$nextTick(() => {
                            this.calculateTotals();

                            this.$params('loading', false);
                        });
                    });
                }
                // async onClose() {
                //     await self.$collection('inventory.transfers').patch({_id: self.$params('id')}, {items: originalItems});
                //
                //     self.$refs.view.refreshModel();
                // }
            });
        },
        async generateCode(save = false) {
            if (this.operationType.numberingId) {
                return await this.$rpc('kernel.common.request-number', {
                    numberingId: this.operationType.numberingId,
                    save
                });
            }

            return '';
        },
        updateContactIdParams(params, type) {
            if (type === 'create' || type === 'detail') {
                params.model = {type: 'contact'};
                params.partnerId = this.model.partnerId;
            } else if (type === 'list') {
                params.filters = {
                    partnerId: this.model.partnerId,
                    type: 'contact'
                };
            }

            return params;
        },
        updateDeliveryAddressSelectParams(params, type) {
            if (type === 'create' || type === 'detail') {
                params.model = {type: 'delivery-address'};
                params.partnerId = this.model.partnerId;
            } else if (type === 'list') {
                params.filters = {
                    partnerId: this.model.partnerId,
                    type: 'delivery-address'
                };
            }

            return params;
        },
        updateInvoiceAddressSelectParams(params, type) {
            if (type === 'create' || type === 'detail') {
                params.model = {type: 'invoice-address'};
                params.partnerId = this.model.partnerId;
            } else if (type === 'list') {
                params.filters = {
                    partnerId: this.model.partnerId,
                    type: 'invoice-address'
                };
            }

            return params;
        },
        async calculateTotals() {
            const model = fastCopy(this.model);
            let discount = 0;
            let discountAmount = 0;
            let subTotal = 0;
            let subTotalAfterDiscount = 0;
            let grandTotal = 0;

            if (Array.isArray(model.items)) {
                for (const row of model.items) {
                    subTotal += this.$app.round((row.unitPrice || 0) * row.actualQty, 'total');
                    discountAmount += this.$app.round(
                        (((row.unitPrice || 0) * (row.discount || 0)) / 100) * row.actualQty,
                        'total'
                    );
                }
            }
            discount = subTotal > 0 ? (discountAmount / subTotal) * 100 : 0;
            subTotalAfterDiscount = this.$app.round(subTotal - discountAmount, 'total');
            grandTotal = this.$app.round(subTotalAfterDiscount, 'total');

            this.model.subTotal = this.$app.round(subTotal, 'total');
            this.model.discountAmount = this.$app.round(discountAmount, 'total');
            this.model.subTotalAfterDiscount = subTotalAfterDiscount;
            this.model.grandTotal = this.$app.round(grandTotal, 'total');
        },
        async applyFreight() {
            const freightItems = await new Promise(resolve => {
                this.$program.dialog({
                    component: 'system.components.freight-calculator',
                    params: {
                        title: this.$t('Freight'),
                        ...(!!this.model.freight && Array.isArray(this.model.freight.items)
                            ? {
                                  model: {
                                      items: this.model.freight.items
                                  }
                              }
                            : {}),
                        forcedPreview: this.$params('isPreview') || (this.model.items ?? []).length < 1
                    },
                    onSubmit: ({items}) => resolve(items),
                    onClose: () => resolve(null)
                });
            });
            if (freightItems === null) {
                return;
            }

            this.$params('loading', true);

            const company = this.$store.getters['session/company'];
            const currencies = await this.$collection('kernel.currencies').find({
                $select: ['_id', 'name'],
                $disableSoftDelete: true,
                $disableActiveCheck: true
            });

            let exchangeRates = [];
            if (Array.isArray(this.model.exchangeRates) && this.model.exchangeRates.length > 0) {
                exchangeRates = this.model.exchangeRates;
            } else {
                const exchangeRatesPayloads = [];

                for (const currency of currencies) {
                    if (currency.name === company.currency.name) {
                        continue;
                    }

                    exchangeRatesPayloads.push({
                        from: currency.name,
                        to: company.currency.name,
                        value: 1,
                        options: {
                            date: new Date()
                        }
                    });
                }
                for (const payload of await this.$rpc('kernel.common.convert-currencies', exchangeRatesPayloads)) {
                    exchangeRates.push({
                        currencyName: payload.from,
                        rate: payload.rate
                    });
                }
            }

            const productsMap = {};
            const products = await this.$collection('inventory.products').find({
                _id: {$in: _.uniq((this.model.items || []).map(item => item.productId))},
                $select: ['_id', 'unitMeasurements'],
                $disableSoftDelete: true,
                $disableActiveCheck: true
            });
            for (const product of products) {
                productsMap[product._id] = product;
            }
            const units = await this.$collection('kernel.units').find();
            const unitsMap = {};
            for (const unit of units) {
                unitsMap[unit._id] = unit;
            }

            let modelItems = fastCopy(this.model.items).map(row => {
                row.freight = 0;
                row.cost = row.unitPriceAfterDiscount * row.actualQty + (row.freight || 0);

                const product = productsMap[row.productId];
                const um = (product.unitMeasurements || []).find(m => m.unitId === row.unitId);
                const quantity = row.actualQty || 1;
                if (!!um) {
                    const lengthUnit = units.find(u => u.category === 'length' && u.symbol === 'm');
                    const weightUnit = units.find(u => u.category === 'weight' && u.symbol === 'kg');

                    if (!!lengthUnit && !!weightUnit) {
                        row.height = 0;
                        row.heightUnitId = lengthUnit._id;
                        row.width = 0;
                        row.widthUnitId = lengthUnit._id;
                        row.depth = 0;
                        row.depthUnitId = lengthUnit._id;
                        row.netWeight = 0;
                        row.netWeightUnitId = weightUnit._id;
                        row.grossWeight = 0;
                        row.grossWeightUnitId = weightUnit._id;

                        if (um.height && um.heightUnitId) {
                            row.height = um.height * quantity;
                            row.heightUnitId = um.heightUnitId;
                        }
                        if (um.width && um.widthUnitId) {
                            row.width = um.width * quantity;
                            row.widthUnitId = um.widthUnitId;
                        }
                        if (um.depth && um.depthUnitId) {
                            row.depth = um.depth * quantity;
                            row.depthUnitId = um.depthUnitId;
                        }
                        if (um.netWeight && um.netWeightUnitId) {
                            row.netWeight = um.netWeight * quantity;
                            row.netWeightUnitId = um.netWeightUnitId;
                        }
                        if (um.grossWeight && um.grossWeightUnitId) {
                            row.grossWeight = um.grossWeight * quantity;
                            row.grossWeightUnitId = um.grossWeightUnitId;
                        }

                        const heightUnit = unitsMap[row.heightUnitId];
                        if (!!heightUnit) {
                            if (heightUnit.type === 'smaller') row.height = row.height / heightUnit.ratio;
                            else if (heightUnit.type === 'bigger') row.height = row.height * heightUnit.ratio;
                        }
                        const widthUnit = unitsMap[row.widthUnitId];
                        if (!!widthUnit) {
                            if (widthUnit.type === 'smaller') row.width = row.width / widthUnit.ratio;
                            else if (widthUnit.type === 'bigger') row.width = row.width * widthUnit.ratio;
                        }
                        const depthUnit = unitsMap[row.depthUnitId];
                        if (!!depthUnit) {
                            if (depthUnit.type === 'smaller') row.depth = row.depth / depthUnit.ratio;
                            else if (depthUnit.type === 'bigger') row.depth = row.depth * depthUnit.ratio;
                        }
                        const netWeightUnit = unitsMap[row.netWeightUnitId];
                        if (!!netWeightUnit) {
                            if (netWeightUnit.type === 'smaller') row.netWeight = row.netWeight / netWeightUnit.ratio;
                            else if (netWeightUnit.type === 'bigger')
                                row.netWeight = row.netWeight * netWeightUnit.ratio;
                        }
                        const grossWeightUnit = unitsMap[row.grossWeightUnitId];
                        if (!!grossWeightUnit) {
                            if (grossWeightUnit.type === 'smaller')
                                row.grossWeight = row.grossWeight / grossWeightUnit.ratio;
                            else if (grossWeightUnit.type === 'bigger')
                                row.grossWeight = row.grossWeight * grossWeightUnit.ratio;
                        }

                        row.heightUnitId = lengthUnit._id;
                        row.widthUnitId = lengthUnit._id;
                        row.depthUnitId = lengthUnit._id;
                        row.netWeightUnitId = weightUnit._id;
                        row.grossWeightUnitId = weightUnit._id;
                    }
                }

                return row;
            });
            const freight = {
                items: freightItems,
                amount: 0,
                taxTotal: 0,
                grossAmount: 0,
                appliedTaxes: []
            };

            for (const freightItem of freightItems) {
                const freightItemCurrency = currencies.find(currency => currency._id === freightItem.currencyId);
                const freightItemExchangeRate = exchangeRates.find(er => er.currencyName === freightItemCurrency.name);
                const freightItemRate = (freightItemExchangeRate ?? {}).rate ?? 1;
                const modelRate = this.model.currencyRate ?? 1;
                const rate = freightItemRate / modelRate;
                let amount = this.$app.round((freightItem.amount ?? 0) * rate, 'currency');

                freight.amount += amount;
                freight.taxTotal += this.$app.round((freightItem.taxTotal ?? 0) * rate, 'currency');
                freight.grossAmount += this.$app.round((freightItem.grossAmount ?? 0) * rate, 'currency');

                freight.taxTotal += freightItem.taxTotal ?? 0;
                freight.grossAmount += freightItem.grossAmount ?? 0;

                for (const appliedTax of freightItem.appliedTaxes) {
                    const appliedTaxIndex = freight.appliedTaxes.findIndex(at => at._id === appliedTax._id);

                    if (appliedTaxIndex === -1) {
                        appliedTax.appliedAmount = this.$app.round((appliedTax.appliedAmount ?? 0) * rate, 'currency');
                        appliedTax.unAppliedAmount = this.$app.round(
                            (appliedTax.unAppliedAmount ?? 0) * rate,
                            'currency'
                        );

                        freight.appliedTaxes.push(appliedTax);
                    } else {
                        freight.appliedTaxes[appliedTaxIndex].appliedAmount += this.$app.round(
                            (appliedTax.appliedAmount ?? 0) * rate,
                            'currency'
                        );
                        freight.appliedTaxes[appliedTaxIndex].unAppliedAmount += this.$app.round(
                            (appliedTax.unAppliedAmount ?? 0) * rate,
                            'currency'
                        );
                    }
                }

                if (freightItem.distributionMethod === 'equal') {
                    modelItems = modelItems.map(item => {
                        item.freight += this.$app.round(amount / modelItems.length, 'currency');

                        return item;
                    });
                } else if (freightItem.distributionMethod === 'quantity') {
                    const total = _.sumBy(modelItems, 'actualQty');

                    if (total > 0) {
                        modelItems = modelItems.map(item => {
                            item.freight += this.$app.round((amount / total) * item.quantity, 'currency');

                            return item;
                        });
                    }
                } else if (freightItem.distributionMethod === 'net-weight') {
                    let total = 0;
                    for (const modelItem of modelItems) {
                        if (_.isFinite(modelItem.netWeight)) {
                            total += modelItem.netWeight ?? 0;
                        }
                    }

                    if (total > 0) {
                        modelItems = modelItems.map(item => {
                            item.freight += this.$app.round((amount / total) * (item.netWeight ?? 1), 'currency');

                            return item;
                        });
                    }
                } else if (freightItem.distributionMethod === 'gross-weight') {
                    let total = 0;
                    for (const modelItem of modelItems) {
                        if (_.isFinite(modelItem.grossWeight)) {
                            total += modelItem.grossWeight ?? 0;
                        }
                    }

                    if (total > 0) {
                        modelItems = modelItems.map(item => {
                            item.freight += this.$app.round((amount / total) * (item.grossWeight ?? 1), 'currency');

                            return item;
                        });
                    }
                } else if (freightItem.distributionMethod === 'net-volume') {
                    let total = 0;
                    for (const modelItem of modelItems) {
                        if (_.isFinite(modelItem.netVolume)) {
                            total += modelItem.netVolume ?? 0;
                        }
                    }

                    if (total > 0) {
                        modelItems = modelItems.map(item => {
                            item.freight += this.$app.round((amount / total) * (item.netVolume ?? 1), 'currency');

                            return item;
                        });
                    }
                } else if (freightItem.distributionMethod === 'gross-volume') {
                    let total = 0;
                    for (const modelItem of modelItems) {
                        if (_.isFinite(modelItem.grossVolume)) {
                            total += modelItem.grossVolume ?? 0;
                        }
                    }

                    if (total > 0) {
                        modelItems = modelItems.map(item => {
                            item.freight += this.$app.round((amount / total) * (item.grossVolume ?? 1), 'currency');

                            return item;
                        });
                    }
                } else if (freightItem.distributionMethod === 'amount') {
                    const total = _.sumBy(modelItems, 'total');

                    if (total > 0) {
                        modelItems = modelItems.map(item => {
                            item.freight += this.$app.round((amount / total) * (item.total ?? 1), 'currency');

                            return item;
                        });
                    }
                }
            }

            freight.amount = this.$app.round(freight.amount, 'currency');
            freight.taxTotal = this.$app.round(freight.taxTotal, 'currency');
            freight.grossAmount = this.$app.round(freight.grossAmount, 'currency');
            freight.appliedTaxes = freight.appliedTaxes.map(appliedTax => {
                appliedTax.appliedAmount = this.$app.round(appliedTax.appliedAmount, 'currency');
                appliedTax.unAppliedAmount = this.$app.round(appliedTax.unAppliedAmount, 'currency');

                return appliedTax;
            });
            this.model.freight = freight;

            modelItems = modelItems.map(item => {
                item.freight = this.$app.round(item.freight, 'currency');
                item.cost = this.$app.round(
                    item.unitPriceAfterDiscount * item.actualQty + (item.freight || 0),
                    'currency'
                );

                return item;
            });
            this.$set(this.model, 'items', modelItems);

            this.$params('loading', false);
        },
        async handleDiscoveryObservation() {
            this.$params('loading', true);

            const doTypes = await this.$collection('crm.discovery-observation-types').find({
                $select: ['_id']
            });
            let doType = null;

            if (doTypes.length > 1) {
                doType = await new Promise(resolve => {
                    this.$program.dialog({
                        component: 'crm.marketing.discoveries-observations.create',
                        onSubmit: data => {
                            resolve(doTypes.find(dt => dt._id === data.discoveryObservationTypeId));
                        }
                    });
                });
            } else if (doTypes.length === 1) {
                doType = doTypes[0];
            }

            if (_.isPlainObject(doType)) {
                const paramsModel = {};

                const partner = await this.$collection('kernel.partners').findOne({
                    _id: this.model.partnerId,
                    $select: ['phoneNumbers']
                });

                paramsModel.discoveryObservationTypeId = doType._id;
                paramsModel.linkedDocumentCollection = 'inventory.transfers';
                paramsModel.linkedDocumentId = this.$params('id');
                paramsModel.linkedDocumentCode = this.model.code;
                paramsModel.communicationChannelId = '';
                paramsModel.sourceId = '';
                paramsModel.branchId = '';
                paramsModel.organizationId = '';
                paramsModel.salesManagerId = '';
                paramsModel.salespersonId = '';
                paramsModel.phoneNumbers = (partner || {}).phoneNumbers || [];
                paramsModel.address = this.model.deliveryAddress;

                this.$program.dialog({
                    component: 'crm.marketing.discoveries-observations.detail',
                    params: {
                        discoveryObservationTypeId: doType._id,
                        model: paramsModel,
                        isPreview: false
                    }
                });
            } else {
                this.$program.message(
                    'error',
                    this.$t('Could not find any discovery & observation type for this document type!')
                );
            }

            this.$params('loading', false);
        },
        handleOpenLogs() {
            this.$program.dialog({
                component: 'system.reports.logs',
                params: {
                    filters: {
                        'props.referenceId': this.$params('id')
                    }
                }
            });
        },
        async handleResendIntegrationOrder() {
            const isConfirmed = await new Promise(resolve => {
                this.$program.alert(
                    'confirm',
                    this.$t('You are about to resend the integration order. Do you want continue?'),
                    confirmed => {
                        resolve(confirmed);
                    }
                );
            });

            if (!isConfirmed) return;

            this.$params('loading', true);

            const model = this.model;

            try {
                await this.$rpc('inventory.resend-integration-order', {
                    documentId: this.$params('id'),
                    integrationId: (model.integrationPayload || {}).integrationId
                });
            } catch (error) {
                this.$program.message('error', error.message);
            }

            this.$params('loading', false);
        },
        handleStageClick(index) {
            if (this.activeStageIndex !== index && !this.$params('isPreview')) {
                const nextStage = this.stages[index];

                this.model.stageId = nextStage._id;
                this.handleChange(this.model, 'stageId');
            }
        },
        stageIdOptionsTemplate(item) {
            if (_.isNumber(item.probability)) {
                return `<span style="float: left">${
                    item.label
                }</span><span style="float: right; color: #8492a6;">%${parseInt(item.probability)}</span>`;
            }

            return item.name;
        }
    }
};
