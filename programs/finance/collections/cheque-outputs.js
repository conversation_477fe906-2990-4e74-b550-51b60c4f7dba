export default {
    name: 'cheque-outputs',
    softDelete: false,
    uid: false,
    timestamps: false,
    schema: {
        journalId: {
            type: 'string',
            label: 'Account',
            index: true
        },
        userId: {
            type: 'string',
            label: 'User',
            index: true
        },
        userCode: {
            type: 'string',
            label: 'User code',
            index: true
        },
        userName: {
            type: 'string',
            label: 'User name',
            index: true
        },
        partnerId: {
            type: 'string',
            label: 'Partner',
            index: true
        },
        endorsedPartnerId: {
            type: 'string',
            label: 'Endorsed partner',
            required: false,
            index: true
        },
        paymentSlipNo: {
            type: 'string',
            label: 'Payment slip no',
            index: true
        },
        fileId: {
            type: 'string',
            label: 'File'
        },
        date: {
            type: 'datetime',
            label: 'Date',
            index: true
        },
        paymentAccountId: {
            type: 'string',
            label: 'Payment account',
            index: true
        },
        numberOfCheques: {
            type: 'decimal',
            label: 'Number of cheques',
            default: 0
        },
        totalChequeAmount: {
            type: 'decimal',
            label: 'Total cheque amount',
            default: 0
        }
    },
    attributes: {
        paymentAccount: {
            collection: 'accounting.journals',
            parentField: 'paymentAccountId',
            childField: '_id'
        },
        partner: {
            collection: 'kernel.partners',
            parentField: 'partnerId',
            childField: '_id'
        },
        endorsedPartner: {
            collection: 'kernel.partners',
            parentField: 'endorsedPartnerId',
            childField: '_id'
        }
    }
};
