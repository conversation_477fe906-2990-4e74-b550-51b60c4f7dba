import _ from 'lodash';
import {ObjectId} from 'mongodb';
import microtime from 'microtime';

export default {
    name: 'entries',
    title: 'Financial Entries',
    branch: true,
    assignable: true,
    view: 'finance.entries',
    labelParams: {
        from: 'code'
    },
    schema: {
        status: {
            type: 'string',
            label: 'Status',
            index: true
        },
        code: {
            type: 'string',
            label: 'Code',
            required: false,
            index: true
        },
        voucherNo: {
            type: 'string',
            label: 'Voucher No',
            required: false,
            index: true
        },
        no: {
            type: 'integer',
            label: 'Number',
            default: 1,
            index: true
        },
        type: {
            type: 'string',
            label: 'Type',
            allowed: ['receipt', 'payment'],
            index: true
        },
        documentType: {
            type: 'string',
            label: 'Document type',
            default: 'cash',
            index: true
        },
        journalId: {
            type: 'string',
            label: 'Account',
            required: false,
            index: true
        },
        posId: {
            type: 'string',
            label: 'POS account',
            required: false,
            index: true
        },
        selectedCardBrand: {
            type: 'string',
            label: 'Card brand',
            required: false
        },
        creditCardId: {
            type: 'string',
            label: 'Credit card account',
            required: false,
            index: true
        },
        partnerType: {
            type: 'string',
            label: 'Partner type',
            allowed: ['customer', 'vendor', 'employee'],
            index: true
        },
        partnerId: {
            type: 'string',
            label: 'Partner',
            index: true
        },
        contactPersonId: {
            type: 'string',
            label: 'Contact person',
            required: false
        },
        amount: {
            type: 'decimal',
            label: 'Amount',
            default: 0,
            min: 0
        },
        dueDifference: {
            type: 'decimal',
            label: 'Due difference',
            default: 0
        },
        total: {
            type: 'decimal',
            label: 'Total',
            default: 0,
            min: 0,
            index: true
        },
        installmentCount: {
            type: 'integer',
            label: 'Installment count',
            default: 1,
            min: 1,
            max: 120
        },
        plusInstallmentCount: {
            type: 'integer',
            label: '(+) Installment',
            required: false,
            min: 0,
            max: 120
        },
        installmentAmount: {
            type: 'decimal',
            label: 'Installment amount',
            default: 0,
            min: 0
        },
        documentNo: {
            type: 'string',
            label: 'Document no',
            required: false,
            index: true
        },
        guaranteeId: {
            type: 'string',
            label: 'Guarantee',
            required: false,
            index: true
        },
        paymentAccountId: {
            type: 'string',
            label: 'Payment account',
            required: false,
            index: true
        },
        endorsable: {
            type: 'boolean',
            label: 'Endorsable',
            default: false
        },
        // It is used to determine firstWrittenBy in entries detail
        original: {
            type: 'boolean',
            label: 'Original',
            default: false
        },
        endorse: {
            type: 'boolean',
            label: 'Endorse',
            default: false
        },
        chequeIdToEndorse: {
            type: 'string',
            label: 'Cheque Number To Endorse',
            required: false
        },
        promissoryNoteIdToEndorse: {
            type: 'string',
            label: 'Promissory Note Number To Endorse',
            required: false
        },
        cashFlowItemId: {
            type: 'string',
            label: 'Cash flow item',
            required: false,
            index: true
        },
        financialProjectId: {
            type: 'string',
            label: 'Project',
            required: false,
            index: true
        },
        currencyId: {
            type: 'string',
            label: 'Currency',
            index: true
        },
        currencyRate: {
            type: 'number',
            label: 'Currency rate',
            default: 1
        },
        globalCurrencyRate: {
            type: 'decimal',
            default: 1
        },
        recordDate: {
            type: 'date',
            label: 'Record date',
            index: true
        },
        issueDate: {
            type: 'date',
            label: 'Issue date',
            index: true
        },
        dueDate: {
            type: 'date',
            label: 'Due date',
            index: true
        },
        reference: {
            type: 'string',
            label: 'Reference',
            required: false,
            index: true
        },
        description: {
            type: 'string',
            label: 'Description',
            required: false,
            index: true
        },
        transactionType: {
            type: 'string',
            label: 'Transaction type',
            required: false
        },
        countryId: {
            type: 'string',
            label: 'Country',
            required: false
        },
        firstWrittenBy: {
            type: 'string',
            label: 'First written by',
            required: false
        },
        financialIdentifier: {
            type: 'string',
            label: 'Financial identifier',
            required: false
        },
        partnerBankAccountId: {
            type: 'string',
            label: 'Partner Bank',
            required: false
        },
        guarantor1: {
            type: 'string',
            label: 'Guarantor 1',
            required: false
        },
        guarantor2: {
            type: 'string',
            label: 'Guarantor 2',
            required: false
        },
        collectorId: {
            type: 'string',
            label: 'Collector',
            required: false,
            index: true
        },
        issuedBy: {
            type: 'string',
            label: 'Issued by',
            required: false
        },
        salespersonId: {
            type: 'string',
            label: 'Salesperson',
            required: false,
            index: true
        },
        scope: {
            type: 'string',
            label: 'Scope',
            default: '1',
            index: true
        },
        calculateDueDifference: {
            type: 'boolean',
            label: 'Calculate due difference',
            default: true
        },

        partnerCreditCardId: {
            type: 'string',
            label: 'Partner credit card',
            required: false
        },
        cardBrand: {
            type: 'string',
            label: 'Card brand',
            required: false
        },
        cardHolder: {
            type: 'string',
            label: 'Card holder',
            required: false
        },
        cardNumber: {
            type: 'string',
            label: 'Card number',
            required: false
        },
        expireMonth: {
            type: 'integer',
            label: 'Expire month',
            required: false
        },
        expireYear: {
            type: 'integer',
            label: 'Expire year',
            required: false
        },
        cvv: {
            type: 'string',
            label: 'Cvv',
            required: false
        },

        guarantorIds: {
            type: ['string'],
            label: 'Guarantors',
            default: []
        },

        tagIds: {
            type: ['string'],
            label: 'Tags',
            default: [],
            index: true
        },

        attachments: {
            type: ['string'],
            default: []
        },

        // Internal
        workflowApprovalStatus: {
            type: 'string',
            label: 'Workflow approval status',
            required: false,
            index: true
        },
        relatedDocuments: {
            type: [
                {
                    collection: 'string',
                    view: 'string',
                    title: 'string',
                    ids: {
                        type: ['string'],
                        default: []
                    }
                }
            ],
            default: []
        },
        accountingJournalEntryId: {
            type: 'string',
            required: false,
            index: true
        },
        accountingTransactionIds: {
            type: ['string'],
            default: [],
            index: true
        },
        systemCurrencyTotal: {
            type: 'decimal',
            default: 0,
            min: 0,
            index: true
        },
        isFixed: {
            type: 'boolean',
            default: false
        },
        id: {
            type: 'string',
            required: false
        },
        baseTotal: {
            type: 'decimal',
            default: 0,
            min: 0
        },
        rounding: {
            type: 'decimal',
            default: 0
        },
        chequeNumber: {
            type: 'string',
            required: false
        },
        promissoryNoteNumber: {
            type: 'string',
            required: false
        },
        dueDayDifference: {
            type: 'decimal',
            default: 0
        },
        posTotal: {
            type: 'decimal',
            default: 0
        },
        creditCardTotal: {
            type: 'decimal',
            default: 0
        },
        receiptId: {
            type: 'string',
            label: 'Receipt',
            required: false,
            index: true
        },
        entryListId: {
            type: 'string',
            label: 'Entry list',
            required: false,
            index: true
        },
        overriddenPartnerAccountId: {
            type: 'string',
            required: false
        },
        purpose: {
            type: 'string',
            required: false,
            index: true
        },
        hasBankReconciliation: {
            type: 'boolean',
            default: false,
            index: true
        },
        // For cheque and promissoryNote document types
        bankId: {
            type: 'string',
            label: 'Bank',
            required: false,
            index: true
        },
        bankBranchName: {
            type: 'string',
            label: 'Bank branch',
            required: false
        },
        accountNumber: {
            type: 'string',
            label: 'Account number',
            required: false
        },
        iban: {
            type: 'string',
            label: 'IBAN',
            required: false
        },
        city: {
            type: 'string',
            label: 'City',
            required: false
        },
        district: {
            type: 'string',
            label: 'District',
            required: false
        },
        chequeTrackingCode: {
            type: 'string',
            label: 'Cheque tracking code',
            required: false
        },
        evaluateAccountCurrency: {
            type: 'boolean',
            label: 'Evaluate account currency',
            default: false,
            required: false
        }
    },
    attributes: {
        journal: {
            collection: 'accounting.journals',
            parentField: 'journalId',
            childField: '_id'
        },
        currency: {
            collection: 'kernel.currencies',
            parentField: 'currencyId',
            childField: '_id'
        },
        branch: {
            collection: 'kernel.branches',
            parentField: 'branchId',
            childField: '_id'
        },
        partner: {
            collection: 'kernel.partners',
            parentField: 'partnerId',
            childField: '_id'
        },
        contactPerson: {
            collection: 'kernel.contacts',
            parentField: 'contactPersonId',
            childField: '_id'
        },
        paymentAccount: {
            collection: 'accounting.journals',
            parentField: 'paymentAccountId',
            childField: '_id'
        },
        country: {
            collection: 'kernel.countries',
            parentField: 'countryId',
            childField: '_id'
        },
        collector: {
            collection: 'kernel.partners',
            parentField: 'collectorId',
            childField: '_id'
        },
        financialProject: {
            collection: 'kernel.financial-projects',
            parentField: 'financialProjectId',
            childField: '_id'
        },
        cashFlowItem: {
            collection: 'finance.cash-flow-items',
            parentField: 'cashFlowItemId',
            childField: '_id'
        },
        guarantee: {
            collection: 'finance.guarantees',
            parentField: 'guaranteeId',
            childField: '_id'
        },
        issuedByInfo: {
            collection: 'kernel.partners',
            parentField: 'issuedBy',
            childField: '_id'
        },
        salesperson: {
            collection: 'kernel.partners',
            parentField: 'salespersonId',
            childField: '_id'
        },
        tags: {
            collection: 'finance.tags',
            parentField: 'tagIds',
            childField: '_id'
        }
    },
    hooks: {
        after: {
            create: [adjustFields, syncCashFlowRecords],
            update: [
                adjustFields,
                syncAccountingRecords,
                syncReceipts,
                syncApproveForRelatedDocuments,
                syncCancelForRelatedDocuments,
                syncCashFlowRecords
            ],
            patch: [
                adjustFields,
                syncAccountingRecords,
                syncReceipts,
                syncApproveForRelatedDocuments,
                syncCancelForRelatedDocuments,
                syncCashFlowRecords
            ],
            remove: [syncRemoveForRelatedDocuments, syncCashFlowRecords]
        }
    },
    async searchTerms(document) {
        const app = this.app;
        const values = Object.values(_.pick(document, ['code', 'voucherNo', 'reference', 'description', 'documentNo']));

        if (_.isPlainObject(document.partner)) {
            values.push(document.partner.code);
            values.push(document.partner.name);
        }
        if (_.isPlainObject(document.financialProject)) {
            values.push(document.financialProject.code);
            values.push(document.financialProject.name);
        }

        return values;
    },
    async copy(document) {
        const app = this.app;
        const dueDaysDiff = app.datetime
            .fromJSDate(document.dueDate)
            .diff(app.datetime.fromJSDate(document.issueDate))
            .as('days');

        document.status = 'draft';
        delete document.code;
        delete document.voucherNo;
        delete document.id;
        delete document.chequeNumber;
        delete document.promissoryNoteNumber;
        delete document.receiptId;
        delete document.entryListId;
        delete document.accountingJournalEntryId;
        document.recordDate = app.datetime.local().toJSDate();
        document.issueDate = app.datetime.local().toJSDate();
        document.dueDate = app.datetime.local().plus({days: dueDaysDiff}).toJSDate();
        document.accountingTransactionIds = [];
        document.relatedDocuments = [];

        return document;
    }
};

async function adjustFields(context) {
    const app = context.app;
    const result = Array.isArray(context.result) ? context.result : [context.result];

    for (const document of result) {
        const payload = {};

        payload.systemCurrencyTotal = app.roundNumber(
            document.total * document.currencyRate,
            app.setting('system.currencyPrecision')
        );

        if (!document.code) {
            payload.code = 'FE' + microtime.now();
        }

        await app.db.collection('finance_entries').updateOne(
            {_id: new ObjectId(document._id)},
            {
                $set: payload
            },
            {
                collation: {locale: app.config('app.locale')}
            }
        );

        document.systemCurrencyTotal = payload.systemCurrencyTotal;
        if (!!payload.code) {
            document.code = payload.code;
        }
    }

    return context;
}

function syncAccountingRecords(context) {
    const app = context.app;
    const now = app.datetime.local().toJSDate();

    (async () => {
        const result = (Array.isArray(context.result) ? context.result : [context.result]).filter(
            document => document.status === 'approved' && !!document.accountingJournalEntryId
        );

        for (const document of result) {
            // Fix dates.
            document.recordDate = app.datetime
                .fromJSDate(document.recordDate)
                .set({
                    hour: now.getHours(),
                    minute: now.getMinutes(),
                    second: now.getSeconds()
                })
                .toJSDate();
            document.issueDate = app.datetime
                .fromJSDate(document.issueDate)
                .set({
                    hour: now.getHours(),
                    minute: now.getMinutes(),
                    second: now.getSeconds()
                })
                .toJSDate();
            document.dueDate = app.datetime
                .fromJSDate(document.dueDate)
                .set({
                    hour: now.getHours(),
                    minute: now.getMinutes(),
                    second: now.getSeconds()
                })
                .toJSDate();

            if (document.documentType === 'cash' || document.documentType === 'moneyTransfer') {
                const entry = await app.collection('accounting.journal-entries').findOne({
                    _id: document.accountingJournalEntryId,
                    $select: ['items']
                });
                const items = entry.items.map(item => {
                    item.recordDate = document.recordDate;
                    item.issueDate = document.issueDate;
                    item.reference = document.reference;
                    item.description = document.description;

                    return item;
                });
                await app.collection('accounting.journal-entries').patch(
                    {_id: document.accountingJournalEntryId},
                    {
                        documentNo: document.documentNo,
                        recordDate: document.recordDate,
                        issueDate: document.issueDate,
                        reference: document.reference,
                        description: document.description,
                        items
                    }
                );
                await app.collection('accounting.transactions').patch(
                    {entryId: document.accountingJournalEntryId},
                    {
                        documentNo: document.documentNo,
                        recordDate: document.recordDate,
                        issueDate: document.issueDate,
                        reference: document.reference,
                        description: document.description
                    }
                );
            }
        }
    })();

    return context;
}

function syncReceipts(context) {
    const app = context.app;
    const round = app.roundNumber;
    const currencyPrecision = app.setting('system.currencyPrecision');

    (async () => {
        const result = (Array.isArray(context.result) ? context.result : [context.result]).filter(
            document => document.status === 'approved' && !!document.receiptId
        );
        const receiptOperation = [];

        for (const document of result) {
            const receipt = await app.collection('finance.receipts').findOne({
                _id: document.receiptId,
                $select: ['status', 'amount', 'currencyRate', 'entryIds']
            });
            const entries = await app.collection('finance.entries').find({
                _id: {$in: receipt.entryIds},
                status: {$ne: 'canceled'},
                $select: ['status', 'systemCurrencyTotal']
            });

            let planned = 0;
            let difference = 0;
            let paid = 0;
            let remaining = 0;

            for (const entry of entries) {
                planned += round(entry.systemCurrencyTotal / receipt.currencyRate, currencyPrecision);

                if (entry.status === 'approved') {
                    paid += round(entry.systemCurrencyTotal / receipt.currencyRate, currencyPrecision);
                }
            }

            difference = round(Math.max(receipt.amount - planned, 0), currencyPrecision);
            remaining = planned - paid;

            receiptOperation.push({
                updateOne: {
                    filter: {_id: receipt._id},
                    update: {
                        $set: {
                            status: 'processing',
                            planned,
                            difference,
                            paid,
                            remaining
                        }
                    }
                }
            });
        }

        if (receiptOperation.length > 0) {
            await app.collection('finance.receipts').bulkWrite(receiptOperation);
        }
    })();

    return context;
}

function syncApproveForRelatedDocuments(context) {
    const app = context.app;
    const round = app.roundNumber;
    const currencyPrecision = app.setting('system.currencyPrecision');

    (async () => {
        const company = await app.collection('kernel.company').findOne({});
        const result = (Array.isArray(context.result) ? context.result : [context.result]).filter(
            document => document.status === 'approved'
        );
        const payload = [];

        for (const document of result) {
            if (document.currencyId !== company.currencyId) {
                continue;
            }

            // Sync customer invoices.
            const customerInvoices = await app.collection('accounting.customer-invoices').find({
                financialEntryIds: document._id,
                currencyId: company.currencyId,
                $select: ['_id', 'paidTotal', 'grandTotal', 'journalEntryId']
            });
            for (const invoice of customerInvoices) {
                payload.push({
                    type: 'customer-invoice',
                    _id: invoice._id,
                    grandTotal: invoice.grandTotal,
                    paidTotal: round((invoice.paidTotal || 0) + document.total, currencyPrecision),
                    journalEntryId: invoice.journalEntryId,
                    entryJournalEntryId: document.accountingJournalEntryId
                });
            }

            // Sync vendor invoices.
            const vendorInvoices = await app.collection('accounting.vendor-invoices').find({
                financialEntryIds: document._id,
                currencyId: company.currencyId,
                $select: ['_id', 'paidTotal', 'grandTotal', 'journalEntryId']
            });
            for (const invoice of vendorInvoices) {
                payload.push({
                    type: 'vendor-invoice',
                    _id: invoice._id,
                    grandTotal: invoice.grandTotal,
                    paidTotal: round((invoice.paidTotal || 0) + document.total, currencyPrecision),
                    journalEntryId: invoice.journalEntryId,
                    entryJournalEntryId: document.accountingJournalEntryId
                });
            }

            // Sync sale orders.
            const saleOrders = await app.collection('sale.orders').find({
                financialEntryIds: document._id,
                currencyId: company.currencyId,
                $select: ['_id', 'paidTotal', 'grandTotal']
            });
            for (const order of saleOrders) {
                payload.push({
                    type: 'sale-order',
                    _id: order._id,
                    grandTotal: order.grandTotal,
                    paidTotal: round((order.paidTotal || 0) + document.total, currencyPrecision)
                });
            }

            // Sync purchase orders.
            const purchaseOrders = await app.collection('purchase.orders').find({
                financialEntryIds: document._id,
                currencyId: company.currencyId,
                $select: ['_id', 'paidTotal', 'grandTotal']
            });
            for (const order of purchaseOrders) {
                payload.push({
                    type: 'purchase-order',
                    _id: order._id,
                    grandTotal: order.grandTotal,
                    paidTotal: round((order.paidTotal || 0) + document.total, currencyPrecision)
                });
            }
        }

        const customerInvoiceOperations = [];
        const vendorInvoiceOperations = [];
        const saleOrderOperations = [];
        const purchaseOrderOperations = [];
        const transactionOperations = [];

        for (const item of payload) {
            if (item.type === 'customer-invoice') {
                let paymentStatus = 'not-paid';
                if (item.paidTotal > 0) {
                    paymentStatus =
                        _.sumBy(
                            payload.filter(p => p.type === 'customer-invoice'),
                            'paidTotal'
                        ) >= item.grandTotal
                            ? 'paid'
                            : 'partially-paid';
                }

                let extra = {};
                if (paymentStatus !== 'not-paid') {
                    const reconciliationTransactions = await app.collection('accounting.transactions').find({
                        entryId: {$in: [payload.journalEntryId, payload.entryJournalEntryId]},
                        reconciled: false,
                        'account.reconciliation': true,
                        partnerId: {$exists: true},
                        $select: ['_id']
                    });
                    const reconciliationNumbering = await app.collection('kernel.numbering').findOne(
                        {
                            code: 'reconciliationNumbering',
                            $select: ['_id']
                        },
                        {
                            disableInUseCheck: true,
                            disableActiveCheck: true,
                            disableSoftDelete: true
                        }
                    );
                    const reconciliationCode = await app.rpc('kernel.common.request-number', {
                        numberingId: reconciliationNumbering._id,
                        save: true
                    });
                    let i = 1;

                    for (const transaction of reconciliationTransactions) {
                        transactionOperations.push({
                            updateOne: {
                                filter: {_id: transaction._id},
                                update: {
                                    $set: {
                                        reconciled: paymentStatus === 'paid',
                                        reconciliationCode,
                                        reconciliationData: {order: i++}
                                    }
                                }
                            }
                        });
                    }

                    extra.reconciliationCode = reconciliationCode;
                }

                customerInvoiceOperations.push({
                    updateOne: {
                        filter: {_id: item._id},
                        update: {
                            $set: {
                                paymentStatus,
                                paidTotal: item.paidTotal,
                                ...extra
                            }
                        }
                    }
                });
            } else if (item.type === 'vendor-invoice') {
                let paymentStatus = 'not-paid';

                if (item.paidTotal > 0) {
                    paymentStatus =
                        _.sumBy(
                            payload.filter(p => p.type === 'vendor-invoice'),
                            'paidTotal'
                        ) >= item.grandTotal
                            ? 'paid'
                            : 'partially-paid';
                }

                let extra = {};
                if (paymentStatus !== 'not-paid') {
                    const reconciliationTransactions = await app.collection('accounting.transactions').find({
                        entryId: {$in: [payload.journalEntryId, payload.entryJournalEntryId]},
                        reconciled: false,
                        'account.reconciliation': true,
                        partnerId: {$exists: true},
                        $select: ['_id']
                    });
                    const reconciliationNumbering = await app.collection('kernel.numbering').findOne(
                        {
                            code: 'reconciliationNumbering',
                            $select: ['_id']
                        },
                        {
                            disableInUseCheck: true,
                            disableActiveCheck: true,
                            disableSoftDelete: true
                        }
                    );
                    const reconciliationCode = await app.rpc('kernel.common.request-number', {
                        numberingId: reconciliationNumbering._id,
                        save: true
                    });
                    let i = 1;

                    for (const transaction of reconciliationTransactions) {
                        transactionOperations.push({
                            updateOne: {
                                filter: {_id: transaction._id},
                                update: {
                                    $set: {
                                        reconciled: paymentStatus === 'paid',
                                        reconciliationCode,
                                        reconciliationData: {order: i++}
                                    }
                                }
                            }
                        });
                    }

                    extra.reconciliationCode = reconciliationCode;
                }

                vendorInvoiceOperations.push({
                    updateOne: {
                        filter: {_id: item._id},
                        update: {
                            $set: {
                                paymentStatus,
                                paidTotal: item.paidTotal,
                                ...extra
                            }
                        }
                    }
                });
            } else if (item.type === 'sale-order') {
                let paymentStatus = 'not-paid';

                if (item.paidTotal > 0) {
                    paymentStatus =
                        _.sumBy(
                            payload.filter(p => p.type === 'sale-order'),
                            'paidTotal'
                        ) >= item.grandTotal
                            ? 'paid'
                            : 'partially-paid';
                }

                saleOrderOperations.push({
                    updateOne: {
                        filter: {_id: item._id},
                        update: {
                            $set: {
                                paymentStatus,
                                paidTotal: item.paidTotal
                            }
                        }
                    }
                });
            } else if (item.type === 'purchase-order') {
                let paymentStatus = 'not-paid';

                if (item.paidTotal > 0) {
                    paymentStatus =
                        _.sumBy(
                            payload.filter(p => p.type === 'purchase-order'),
                            'paidTotal'
                        ) >= item.grandTotal
                            ? 'paid'
                            : 'partially-paid';
                }

                purchaseOrderOperations.push({
                    updateOne: {
                        filter: {_id: item._id},
                        update: {
                            $set: {
                                paymentStatus,
                                paidTotal: item.paidTotal
                            }
                        }
                    }
                });
            }
        }

        if (customerInvoiceOperations.length > 0) {
            await app.collection('accounting.customer-invoices').bulkWrite(customerInvoiceOperations);
        }

        if (vendorInvoiceOperations.length > 0) {
            await app.collection('accounting.vendor-invoices').bulkWrite(vendorInvoiceOperations);
        }

        if (saleOrderOperations.length > 0) {
            await app.collection('sale.orders').bulkWrite(saleOrderOperations);
        }

        if (purchaseOrderOperations.length > 0) {
            await app.collection('purchase.orders').bulkWrite(purchaseOrderOperations);
        }

        if (transactionOperations.length > 0) {
            await app.collection('accounting.transactions').bulkWrite(transactionOperations);
        }

        for (const item of payload) {
            if (item.type === 'customer-invoice') {
                await app.rpc('finance.cash-flow-sync-records', {
                    collection: 'accounting.customer-invoices',
                    documentId: item._id
                });
            } else if (item.type === 'vendor-invoice') {
                await app.rpc('finance.cash-flow-sync-records', {
                    collection: 'accounting.vendor-invoices',
                    documentId: item._id
                });
            } else if (item.type === 'sale-order') {
                await app.rpc('finance.cash-flow-sync-records', {
                    collection: 'sale.orders',
                    documentId: item._id
                });
            } else if (item.type === 'purchase-order') {
                await app.rpc('finance.cash-flow-sync-records', {
                    collection: 'purchase.orders',
                    documentId: item._id
                });
            }
        }
    })();

    return context;
}

async function syncCancelForRelatedDocuments(context) {
    const app = context.app;
    const round = app.roundNumber;
    const currencyPrecision = app.setting('system.currencyPrecision');
    const result = Array.isArray(context.result) ? context.result : [context.result];
    const customerInvoiceOperations = [];
    const vendorInvoiceOperations = [];
    const saleOrderOperations = [];
    const purchaseOrderOperations = [];

    for (const document of result) {
        if (document.status !== 'canceled') {
            continue;
        }

        // Sync customer invoices.
        const customerInvoices = await app.collection('accounting.customer-invoices').find({
            financialEntryIds: document._id,
            $select: ['_id', 'grandTotal', 'financialEntryIds']
        });
        for (const invoice of customerInvoices) {
            const entryIds = invoice.financialEntryIds.filter(id => id !== document._id);
            let plannedTotal = 0;
            let paidTotal = 0;
            let paymentStatus = 'not-paid';

            if (entryIds.length > 0) {
                const entries = await app.collection('finance.entries').find({
                    _id: {$in: entryIds},
                    status: {$ne: 'canceled'},
                    $select: ['status', 'total']
                });

                if (entries.length > 0) {
                    plannedTotal = round(_.sumBy(entries, 'total'), currencyPrecision);
                    paidTotal = round(
                        _.sumBy(
                            entries.filter(entry => entry.status === 'approved'),
                            'total'
                        ),
                        currencyPrecision
                    );
                }
            }

            if (paidTotal > 0) {
                paymentStatus = paidTotal >= invoice.grandTotal;
            }

            customerInvoiceOperations.push({
                updateOne: {
                    filter: {_id: invoice._id},
                    update: {
                        $set: {
                            plannedTotal,
                            paidTotal,
                            paymentStatus
                        }
                    }
                }
            });
        }

        // Sync vendor invoices.
        const vendorInvoices = await app.collection('accounting.vendor-invoices').find({
            financialEntryIds: document._id,
            $select: ['_id', 'grandTotal', 'financialEntryIds']
        });
        for (const invoice of vendorInvoices) {
            const entryIds = invoice.financialEntryIds.filter(id => id !== document._id);
            let plannedTotal = 0;
            let paidTotal = 0;
            let paymentStatus = 'not-paid';

            if (entryIds.length > 0) {
                const entries = await app.collection('finance.entries').find({
                    _id: {$in: entryIds},
                    status: {$ne: 'canceled'},
                    $select: ['status', 'total']
                });

                if (entries.length > 0) {
                    plannedTotal = round(_.sumBy(entries, 'total'), currencyPrecision);
                    paidTotal = round(
                        _.sumBy(
                            entries.filter(entry => entry.status === 'approved'),
                            'total'
                        ),
                        currencyPrecision
                    );
                }
            }

            if (paidTotal > 0) {
                paymentStatus = paidTotal >= invoice.grandTotal ? 'paid' : 'partially-paid';
            }

            vendorInvoiceOperations.push({
                updateOne: {
                    filter: {_id: invoice._id},
                    update: {
                        $set: {
                            plannedTotal,
                            paidTotal,
                            paymentStatus
                        }
                    }
                }
            });
        }

        // Sync sale orders.
        const saleOrders = await app.collection('sale.orders').find({
            financialEntryIds: document._id,
            $select: ['_id', 'grandTotal', 'financialEntryIds']
        });
        for (const order of saleOrders) {
            const entryIds = order.financialEntryIds.filter(id => id !== document._id);
            let plannedTotal = 0;
            let paidTotal = 0;
            let paymentStatus = 'not-paid';

            if (entryIds.length > 0) {
                const entries = await app.collection('finance.entries').find({
                    _id: {$in: entryIds},
                    status: {$ne: 'canceled'},
                    $select: ['status', 'total']
                });

                if (entries.length > 0) {
                    plannedTotal = round(_.sumBy(entries, 'total'), currencyPrecision);
                    paidTotal = round(
                        _.sumBy(
                            entries.filter(entry => entry.status === 'approved'),
                            'total'
                        ),
                        currencyPrecision
                    );
                }
            }

            if (paidTotal > 0) {
                paymentStatus = paidTotal >= order.grandTotal ? 'paid' : 'partially-paid';
            }

            saleOrderOperations.push({
                updateOne: {
                    filter: {_id: order._id},
                    update: {
                        $set: {
                            plannedTotal,
                            paidTotal,
                            paymentStatus
                        }
                    }
                }
            });
        }

        // Sync sale orders.
        const purchaseOrders = await app.collection('purchase.orders').find({
            financialEntryIds: document._id,
            $select: ['_id', 'grandTotal', 'financialEntryIds', 'relatedDocuments']
        });
        for (const order of purchaseOrders) {
            const entryIds = order.financialEntryIds.filter(id => id !== document._id);
            let plannedTotal = 0;
            let paidTotal = 0;
            let paymentStatus = 'not-paid';

            if (entryIds.length > 0) {
                const entries = await app.collection('finance.entries').find({
                    _id: {$in: entryIds},
                    status: {$ne: 'canceled'},
                    $select: ['status', 'total']
                });

                if (entries.length > 0) {
                    plannedTotal = round(_.sumBy(entries, 'total'), currencyPrecision);
                    paidTotal = round(
                        _.sumBy(
                            entries.filter(entry => entry.status === 'approved'),
                            'total'
                        ),
                        currencyPrecision
                    );
                }
            }

            if (paidTotal > 0) {
                paymentStatus = paidTotal >= order.grandTotal ? 'paid' : 'partially-paid';
            }

            purchaseOrderOperations.push({
                updateOne: {
                    filter: {_id: order._id},
                    update: {
                        $set: {
                            plannedTotal,
                            paidTotal,
                            paymentStatus
                        }
                    }
                }
            });
        }
    }

    if (customerInvoiceOperations.length > 0) {
        await app.collection('accounting.customer-invoices').bulkWrite(customerInvoiceOperations);
    }

    if (vendorInvoiceOperations.length > 0) {
        await app.collection('accounting.vendor-invoices').bulkWrite(vendorInvoiceOperations);
    }

    if (saleOrderOperations.length > 0) {
        await app.collection('sale.orders').bulkWrite(saleOrderOperations);
    }

    if (purchaseOrderOperations.length > 0) {
        await app.collection('purchase.orders').bulkWrite(purchaseOrderOperations);
    }

    return context;
}

async function syncRemoveForRelatedDocuments(context) {
    const app = context.app;
    const round = app.roundNumber;
    const currencyPrecision = app.setting('system.currencyPrecision');
    const result = Array.isArray(context.result) ? context.result : [context.result];
    const customerInvoiceOperations = [];
    const vendorInvoiceOperations = [];
    const saleOrderOperations = [];
    const purchaseOrderOperations = [];

    for (const document of result) {
        if (context.method !== 'remove') {
            continue;
        }

        // Sync customer invoices.
        const customerInvoices = await app.collection('accounting.customer-invoices').find({
            financialEntryIds: document._id,
            $select: ['_id', 'grandTotal', 'entryIds', 'relatedDocuments']
        });
        for (const invoice of customerInvoices) {
            const relatedDocuments = invoice.relatedDocuments
                .map(rd => {
                    if (rd.collection === 'finance.entries') {
                        rd.ids = rd.ids.filter(rid => rid !== document._id);
                    }

                    return rd;
                })
                .filter(rd => rd.ids.length > 0);
            const entryIds = invoice.financialEntryIds.filter(id => id !== document._id);
            let plannedTotal = 0;
            let paidTotal = 0;
            let paymentStatus = 'not-paid';

            if (entryIds.length > 0) {
                const entries = await app.collection('finance.entries').find({
                    _id: {$in: entryIds},
                    status: {$ne: 'canceled'},
                    $select: ['status', 'total']
                });

                if (entries.length > 0) {
                    plannedTotal = round(_.sumBy(entries, 'total'), currencyPrecision);
                    paidTotal = round(
                        _.sumBy(
                            entries.filter(entry => entry.status === 'approved'),
                            'total'
                        ),
                        currencyPrecision
                    );
                }
            }

            if (paidTotal > 0) {
                paymentStatus = paidTotal >= invoice.grandTotal ? 'paid' : 'partially-paid';
            }

            customerInvoiceOperations.push({
                updateOne: {
                    filter: {_id: invoice._id},
                    update: {
                        $set: {
                            plannedTotal,
                            paidTotal,
                            paymentStatus,
                            financialEntryIds: entryIds,
                            relatedDocuments
                        }
                    }
                }
            });
        }

        // Sync vendor invoices.
        const vendorInvoices = await app.collection('accounting.vendor-invoices').find({
            financialEntryIds: document._id,
            $select: ['_id', 'grandTotal', 'entryIds', 'relatedDocuments']
        });
        for (const invoice of vendorInvoices) {
            const relatedDocuments = invoice.relatedDocuments
                .map(rd => {
                    if (rd.collection === 'finance.entries') {
                        rd.ids = rd.ids.filter(rid => rid !== document._id);
                    }

                    return rd;
                })
                .filter(rd => rd.ids.length > 0);
            const entryIds = invoice.financialEntryIds.filter(id => id !== document._id);
            let plannedTotal = 0;
            let paidTotal = 0;
            let paymentStatus = 'not-paid';

            if (entryIds.length > 0) {
                const entries = await app.collection('finance.entries').find({
                    _id: {$in: entryIds},
                    status: {$ne: 'canceled'},
                    $select: ['status', 'total']
                });

                if (entries.length > 0) {
                    plannedTotal = round(_.sumBy(entries, 'total'), currencyPrecision);
                    paidTotal = round(
                        _.sumBy(
                            entries.filter(entry => entry.status === 'approved'),
                            'total'
                        ),
                        currencyPrecision
                    );
                }
            }

            if (paidTotal > 0) {
                paymentStatus = paidTotal >= invoice.grandTotal ? 'paid' : 'partially-paid';
            }

            vendorInvoiceOperations.push({
                updateOne: {
                    filter: {_id: invoice._id},
                    update: {
                        $set: {
                            plannedTotal,
                            paidTotal,
                            paymentStatus,
                            financialEntryIds: entryIds,
                            relatedDocuments
                        }
                    }
                }
            });
        }

        // Sync sale orders.
        const saleOrders = await app.collection('sale.orders').find({
            financialEntryIds: document._id,
            $select: ['_id', 'grandTotal', 'entryIds', 'relatedDocuments']
        });
        for (const order of saleOrders) {
            const relatedDocuments = order.relatedDocuments
                .map(rd => {
                    if (rd.collection === 'finance.entries') {
                        rd.ids = rd.ids.filter(rid => rid !== document._id);
                    }

                    return rd;
                })
                .filter(rd => rd.ids.length > 0);
            const entryIds = order.financialEntryIds.filter(id => id !== document._id);
            let plannedTotal = 0;
            let paidTotal = 0;
            let paymentStatus = 'not-paid';

            if (entryIds.length > 0) {
                const entries = await app.collection('finance.entries').find({
                    _id: {$in: entryIds},
                    status: {$ne: 'canceled'},
                    $select: ['status', 'total']
                });

                if (entries.length > 0) {
                    plannedTotal = round(_.sumBy(entries, 'total'), currencyPrecision);
                    paidTotal = round(
                        _.sumBy(
                            entries.filter(entry => entry.status === 'approved'),
                            'total'
                        ),
                        currencyPrecision
                    );
                }
            }

            if (paidTotal > 0) {
                paymentStatus = paidTotal >= order.grandTotal ? 'paid' : 'partially-paid';
            }

            saleOrderOperations.push({
                updateOne: {
                    filter: {_id: order._id},
                    update: {
                        $set: {
                            plannedTotal,
                            paidTotal,
                            paymentStatus,
                            financialEntryIds: entryIds,
                            relatedDocuments
                        }
                    }
                }
            });
        }

        // Sync sale orders.
        const purchaseOrders = await app.collection('purchase.orders').find({
            financialEntryIds: document._id,
            $select: ['_id', 'grandTotal', 'entryIds', 'relatedDocuments']
        });
        for (const order of purchaseOrders) {
            const relatedDocuments = order.relatedDocuments
                .map(rd => {
                    if (rd.collection === 'finance.entries') {
                        rd.ids = rd.ids.filter(rid => rid !== document._id);
                    }

                    return rd;
                })
                .filter(rd => rd.ids.length > 0);
            const entryIds = order.financialEntryIds.filter(id => id !== document._id);
            let plannedTotal = 0;
            let paidTotal = 0;
            let paymentStatus = 'not-paid';

            if (entryIds.length > 0) {
                const entries = await app.collection('finance.entries').find({
                    _id: {$in: entryIds},
                    status: {$ne: 'canceled'},
                    $select: ['status', 'total']
                });

                if (entries.length > 0) {
                    plannedTotal = round(_.sumBy(entries, 'total'), currencyPrecision);
                    paidTotal = round(
                        _.sumBy(
                            entries.filter(entry => entry.status === 'approved'),
                            'total'
                        ),
                        currencyPrecision
                    );
                }
            }

            if (paidTotal > 0) {
                paymentStatus = paidTotal >= order.grandTotal ? 'paid' : 'partially-paid';
            }

            purchaseOrderOperations.push({
                updateOne: {
                    filter: {_id: order._id},
                    update: {
                        $set: {
                            plannedTotal,
                            paidTotal,
                            paymentStatus,
                            financialEntryIds: entryIds,
                            relatedDocuments
                        }
                    }
                }
            });
        }
    }

    if (customerInvoiceOperations.length > 0) {
        await app.collection('accounting.customer-invoices').bulkWrite(customerInvoiceOperations);
    }

    if (vendorInvoiceOperations.length > 0) {
        await app.collection('accounting.vendor-invoices').bulkWrite(vendorInvoiceOperations);
    }

    if (saleOrderOperations.length > 0) {
        await app.collection('sale.orders').bulkWrite(saleOrderOperations);
    }

    if (purchaseOrderOperations.length > 0) {
        await app.collection('purchase.orders').bulkWrite(purchaseOrderOperations);
    }

    return context;
}

async function syncCashFlowRecords(context) {
    // noinspection ES6MissingAwait
    (async () => {
        for (const result of Array.isArray(context.result) ? context.result : [context.result]) {
            await context.app.rpc('finance.cash-flow-sync-records', {
                collection: 'finance.entries',
                documentId: result._id
            });
        }
    })();

    return context;
}
