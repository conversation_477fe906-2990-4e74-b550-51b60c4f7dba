export default {
    name: 'promissory-note-outputs',
    softDelete: false,
    uid: false,
    timestamps: false,
    schema: {
        journalId: {
            type: 'string',
            label: 'Account',
            index: true
        },
        userId: {
            type: 'string',
            label: 'User',
            index: true
        },
        userCode: {
            type: 'string',
            label: 'User code',
            index: true
        },
        userName: {
            type: 'string',
            label: 'User name',
            index: true
        },
        partnerId: {
            type: 'string',
            label: 'Partner',
            index: true
        },
        endorsedPartnerId: {
            type: 'string',
            label: 'Endorsed partner',
            required: false,
            index: true
        },
        paymentSlipNo: {
            type: 'string',
            label: 'Payment slip no',
            index: true
        },
        fileId: {
            type: 'string',
            label: 'File'
        },
        date: {
            type: 'datetime',
            label: 'Date',
            index: true
        },
        totalPromissoryAmount: {
            type: 'decimal',
            label: 'Total promissory amount',
            default: 0
        }
    },
     attributes: {
        partner: {
            collection: 'kernel.partners',
            parentField: 'partnerId',
            childField: '_id'
        },
        endorsedPartner: {
            collection: 'kernel.partners',
            parentField: 'endorsedPartnerId',
            childField: '_id'
        }
    }
};
