import _ from 'lodash';

export default {
    name: 'approve-entry',
    async action(payload, params) {
        const app = this.app;

        // Check active entry approve.
        if (await app.cache.has(`finance.approve-entry-${JSON.stringify(payload)}`)) {
            return;
        } else {
            await app.cache.set(`finance.approve-entry-${JSON.stringify(payload)}`, true, 10);
        }

        const currencyPrecision = app.setting('system.currencyPrecision');
        const round = app.roundNumber;
        const company = await app.collection('kernel.company').findOne({
            $select: ['currencyId']
        });
        const collection = app.collection('finance.entries');

        // Normalize data.
        if (_.isString(payload)) {
            payload = await collection.get(payload);
        }

        const partner = await app.collection('kernel.partners').findOne({
            _id: payload.partnerId,
            $select: [
                'type',
                'accountingAccountId',
                'employeeExpenseAccountId',
                'employeeAccountGroupId',
                'additionalAllowanceAccount',
                'currencyId',
                'enableLimitChecks',
                'salespersonId'
            ]
        });
        const journal = await app.collection('accounting.journals').findOne({
            _id: payload.journalId,
            $select: [
                'debitAccountId',
                'creditAccountId',
                'chequeNumberingId',
                'chequeBankAccountId',
                'promissoryNoteNumberingId',
                'chequeTrackingNumberingId',
                'currencyId'
            ]
        });
        let partnerAccountId = !!payload.overriddenPartnerAccountId
            ? payload.overriddenPartnerAccountId
            : partner.accountingAccountId;

        if (!payload.overriddenPartnerAccountId && !!payload.transactionType && payload.partnerType === 'employee') {
            if (payload.transactionType === 'outgo') {
                const companyExpenseReconciliationAccount = app.defaultAccountingAccount(
                    'companyExpenseReconciliationAccount',
                    'expense'
                );

                if (!companyExpenseReconciliationAccount) {
                    throw new app.errors.Unprocessable(
                        this.translate('Company expense reconciliation account is not found!')
                    );
                }

                partnerAccountId = companyExpenseReconciliationAccount;
            } else if (payload.transactionType === 'advancePayment') {
                partnerAccountId = partner.employeeExpenseAccountId;
            } else if (payload.transactionType === 'salaryAdvancePayment') {
                const employeeAccountGroup = await app.collection('hr.employee-account-groups').findOne({
                    _id: partner.employeeAccountGroupId,
                    $disableActiveCheck: true,
                    $disableSoftDelete: true
                });
                const account = employeeAccountGroup.accounts.find(
                    account => account.accountType === 'advance-payments'
                );

                partnerAccountId = (account || {}).accountId;
            } else if (partner.transactionType === 'fee') {
                partnerAccountId = partner.accountingAccountId;
            }
        }

        if (
            !payload.overriddenPartnerAccountId &&
            !!payload.transactionType &&
            payload.transactionType === 'advancePayment' &&
            (payload.partnerType === 'customer' || payload.partnerType === 'vendor')
        ) {
            partnerAccountId = partner.additionalAllowanceAccount;
        }

        if (!partnerAccountId) {
            throw new app.errors.Unprocessable(this.translate('Partner accounting account is not found!'));
        }

        if (!!partner && partner.type === 'customer' && !!partner.salespersonId && !payload.salespersonId) {
            payload.salespersonId = partner.salespersonId;
        }

        if (!_.isPlainObject(journal)) {
            throw new app.errors.Unprocessable(
                this.translate('{{label}} is required', {
                    label: this.translate('Account')
                })
            );
        }

        // Document no.
        if (
            !payload.documentNo &&
            (payload.documentType === 'cheque' || payload.documentType === 'promissoryNote') &&
            ((payload.documentType === 'cheque' && !!journal.chequeNumberingId) ||
                (payload.documentType === 'promissoryNote' && !!journal.promissoryNoteNumberingId))
        ) {
            const numberingId =
                payload.documentType === 'cheque' ? journal.chequeNumberingId : journal.promissoryNoteNumberingId;

            payload.documentNo = await this.app.rpc('kernel.common.request-number', {
                numberingId,
                save: true
            });
        }

        if (!payload.documentNo && !(payload.documentType === 'cash' || payload.documentType === 'moneyTransfer')) {
            throw new app.errors.Unprocessable(
                this.translate('{{label}} is required', {
                    label: this.translate('Document no')
                })
            );
        }

        // Get currencies.
        const currencies = await app.collection('kernel.currencies').find({
            $select: ['name']
        });

        if (payload.endorse) {
            const entry = {items: []};

            if (!_.isString(partnerAccountId) || !partnerAccountId) {
                throw new app.errors.Unprocessable(this.translate('Partner accounting account is required!'));
            }

            // Cheque.
            if (payload.documentType === 'cheque') {
                if (!payload.chequeIdToEndorse) {
                    throw new app.errors.Unprocessable(this.translate('Cheque to endorse is required!'));
                }

                const cheque = await app.collection('finance.cheques').findOne({_id: payload.chequeIdToEndorse});

                // Prepare general data.
                entry.documentNo = payload.documentNo;
                entry.reference = payload.reference;
                entry.description = payload.description;
                entry.branchId = payload.branchId;
                entry.recordDate = payload.recordDate;
                entry.issueDate = payload.issueDate;
                entry.dueDate = payload.dueDate;
                entry.journalId = cheque.journalId;
                entry.relatedDocuments = [
                    {
                        collection: 'finance.entries',
                        view: 'finance.payable.payments',
                        title: 'Payments',
                        ids: [payload._id]
                    }
                ];
                if (cheque.chequeTrackingCode) entry.chequeTrackingCode = cheque.chequeTrackingCode;
                if (_.isPlainObject(params.user)) entry.createdBy = params.user._id;

                // First item.
                const firstItem = {};
                firstItem.accountId = cheque.currentAccountId;
                firstItem.partnerId = partner._id;
                firstItem.description = payload.description;
                firstItem.branchId = payload.branchId;
                firstItem.currencyId = payload.currencyId;
                firstItem.financialProjectId = payload.financialProjectId;
                firstItem.cashFlowItemId = payload.cashFlowItemId;
                firstItem.salespersonId = payload.salespersonId;
                firstItem.scope = payload.scope || '1';
                if (cheque.currencyId !== company.currencyId) {
                    firstItem.creditFC = cheque.amount;
                    firstItem.credit = cheque.amount * payload.currencyRate * payload.globalCurrencyRate;
                } else {
                    firstItem.credit = cheque.amount;
                }
                entry.items.push(firstItem);

                // Second item.
                const secondItem = {};
                secondItem.accountId = partnerAccountId;
                secondItem.partnerId = partner._id;
                secondItem.description = payload.description;
                secondItem.branchId = payload.branchId;
                secondItem.currencyId = payload.currencyId;
                secondItem.financialProjectId = payload.financialProjectId;
                secondItem.cashFlowItemId = payload.cashFlowItemId;
                secondItem.salespersonId = payload.salespersonId;
                secondItem.scope = payload.scope || '1';
                if (cheque.currencyId !== company.currencyId) {
                    secondItem.debitFC = cheque.amount;
                    secondItem.debit = cheque.amount * payload.currencyRate * payload.globalCurrencyRate;
                } else {
                    secondItem.debit = cheque.amount;
                }
                entry.items.push(secondItem);

                // Create accounting entry.
                const journalEntry = await app.rpc('accounting.save-journal-entry', {data: entry}, {user: params.user});
                const transactions = await app.rpc('accounting.post-journal-entry', journalEntry._id, {
                    user: params.user
                });

                // Get code.
                const voucherNo = (
                    await app.collection('accounting.journal-entries').findOne({
                        _id: journalEntry._id,
                        $select: ['voucherNo']
                    })
                ).voucherNo;

                // Save history.
                const lastHistory = await app.collection('finance.cheques-history').findOne({
                    chequeId: payload.chequeIdToEndorse,
                    $sort: {createdAt: -1}
                });
                await app.collection('finance.cheques-history').create(
                    {
                        chequeId: payload.chequeIdToEndorse,
                        journalId: cheque.journalId,
                        status: 'endorsed',
                        code: voucherNo,
                        amount: cheque.amount,
                        currencyId: cheque.currencyId,
                        currencyRate: payload.currencyRate * payload.globalCurrencyRate,
                        no: _.isObject(lastHistory) ? lastHistory.no + 1 : 1,
                        recordDate: payload.recordDate,
                        issueDate: payload.issueDate,
                        dueDate: payload.dueDate,
                        reference: payload.reference,
                        description: payload.description,
                        partnerId: partner._id,
                        journalEntryId: journalEntry._id,
                        paymentAccountId: cheque.paymentAccountId,
                        ...(_.isPlainObject(params.user) && {editedBy: params.user._id})
                    },
                    {user: params.user}
                );

                // Update cheque.
                await app.collection('finance.cheques').patch(
                    {_id: payload.chequeIdToEndorse},
                    {
                        status: 'endorsed',
                        code: voucherNo,
                        endorsedPartnerId: partner._id,
                        currentAccountId: partnerAccountId,
                        journalEntryId: journalEntry._id,
                        scope: payload.scope || '1',
                        financialProjectId: payload.financialProjectId,
                        cashFlowItemId: payload.cashFlowItemId
                    },
                    {user: params.user}
                );

                // Update entries.
                const relatedDocuments = (payload.relatedDocuments || []).concat([
                    {
                        collection: 'accounting.journal-entries',
                        view: 'accounting.adviser.journal-entries',
                        title: 'Journal Entries',
                        ids: [journalEntry._id]
                    }
                ]);
                return await collection.patch(
                    {_id: payload._id},
                    {
                        status: 'approved',
                        voucherNo,
                        accountingJournalEntryId: journalEntry._id,
                        accountingTransactionIds: transactions.map(t => t._id),
                        relatedDocuments
                    }
                );
            }
            // Promissory note.
            else if (payload.documentType === 'promissoryNote') {
                if (!payload.promissoryNoteIdToEndorse) {
                    throw new app.errors.Unprocessable(this.translate('Promissory note to endorse is required!'));
                }

                const promissoryNote = await app
                    .collection('finance.promissory-notes')
                    .findOne({_id: payload.promissoryNoteIdToEndorse});

                // Prepare general data.
                entry.documentNo = payload.documentNo;
                entry.reference = payload.reference;
                entry.description = payload.description;
                entry.branchId = payload.branchId;
                entry.recordDate = payload.recordDate;
                entry.issueDate = payload.issueDate;
                entry.dueDate = payload.dueDate;
                entry.journalId = promissoryNote.journalId;
                entry.relatedDocuments = [
                    {
                        collection: 'finance.entries',
                        view: 'finance.payable.payments',
                        title: 'Payments',
                        ids: [payload._id]
                    }
                ];
                if (_.isPlainObject(params.user)) entry.createdBy = params.user._id;

                // First item.
                const firstItem = {};
                firstItem.accountId = promissoryNote.currentAccountId;
                firstItem.partnerId = partner._id;
                firstItem.description = payload.description;
                firstItem.branchId = payload.branchId;
                firstItem.currencyId = payload.currencyId;
                firstItem.scope = payload.scope || '1';
                firstItem.financialProjectId = payload.financialProjectId;
                firstItem.cashFlowItemId = payload.cashFlowItemId;
                firstItem.salespersonId = payload.salespersonId;
                if (promissoryNote.currencyId !== company.currencyId) {
                    firstItem.creditFC = promissoryNote.amount;
                    firstItem.credit = promissoryNote.amount * payload.currencyRate * payload.globalCurrencyRate;
                } else {
                    firstItem.credit = promissoryNote.amount;
                }
                entry.items.push(firstItem);

                // Second item.
                const secondItem = {};
                secondItem.accountId = partnerAccountId;
                secondItem.partnerId = partner._id;
                secondItem.description = payload.description;
                secondItem.branchId = payload.branchId;
                secondItem.currencyId = payload.currencyId;
                secondItem.financialProjectId = payload.financialProjectId;
                secondItem.cashFlowItemId = payload.cashFlowItemId;
                secondItem.salespersonId = payload.salespersonId;
                secondItem.scope = payload.scope || '1';
                if (promissoryNote.currencyId !== company.currencyId) {
                    secondItem.debitFC = promissoryNote.amount;
                    secondItem.debit = promissoryNote.amount * payload.currencyRate * payload.globalCurrencyRate;
                } else {
                    secondItem.debit = promissoryNote.amount;
                }
                entry.items.push(secondItem);

                // Create accounting entry.
                const journalEntry = await app.rpc('accounting.save-journal-entry', {data: entry}, {user: params.user});
                const transactions = await app.rpc('accounting.post-journal-entry', journalEntry._id, {
                    user: params.user
                });

                // Get code.
                const voucherNo = (
                    await app.collection('accounting.journal-entries').findOne({
                        _id: journalEntry._id,
                        $select: ['voucherNo']
                    })
                ).voucherNo;

                // Save history.
                const lastHistory = await app.collection('finance.promissory-notes-history').findOne({
                    promissoryNoteId: payload.promissoryNoteIdToEndorse,
                    $sort: {createdAt: -1}
                });
                await app.collection('finance.promissory-notes-history').create(
                    {
                        promissoryNoteId: payload.promissoryNoteIdToEndorse,
                        journalId: promissoryNote.journalId,
                        status: 'endorsed',
                        code: voucherNo,
                        amount: promissoryNote.amount,
                        currencyId: promissoryNote.currencyId,
                        currencyRate: payload.currencyRate * payload.globalCurrencyRate,
                        no: _.isObject(lastHistory) ? lastHistory.no + 1 : 1,
                        recordDate: payload.recordDate,
                        issueDate: payload.issueDate,
                        dueDate: payload.dueDate,
                        reference: payload.reference,
                        description: payload.description,
                        partnerId: partner._id,
                        journalEntryId: journalEntry._id,
                        paymentAccountId: promissoryNote.paymentAccountId,
                        ...(_.isPlainObject(params.user) && {editedBy: params.user._id})
                    },
                    {user: params.user}
                );

                // Update promissory note.
                await app.collection('finance.promissory-notes').patch(
                    {_id: payload.promissoryNoteIdToEndorse},
                    {
                        status: 'endorsed',
                        code: voucherNo,
                        endorsedPartnerId: partner._id,
                        currentAccountId: partnerAccountId,
                        scope: payload.scope || '1',
                        journalEntryId: journalEntry._id,
                        financialProjectId: payload.financialProjectId,
                        cashFlowItemId: payload.cashFlowItemId
                    },
                    {user: params.user}
                );

                // Update entries.
                const relatedDocuments = (payload.relatedDocuments || []).concat([
                    {
                        collection: 'accounting.journal-entries',
                        view: 'accounting.adviser.journal-entries',
                        title: 'Journal Entries',
                        ids: [journalEntry._id]
                    }
                ]);
                await collection.patch(
                    {_id: payload._id},
                    {
                        status: 'approved',
                        voucherNo,
                        accountingJournalEntryId: journalEntry._id,
                        accountingTransactionIds: transactions.map(t => t._id),
                        relatedDocuments
                    }
                );
            }
        } else {
            const entry = {items: []};
            let slips = [];

            // Prepare general data.
            if (payload.documentNo) entry.documentNo = payload.documentNo;
            entry.reference = payload.reference;
            entry.description = payload.description;
            entry.branchId = payload.branchId;
            entry.recordDate = payload.recordDate;
            entry.issueDate = payload.issueDate;
            entry.dueDate = payload.dueDate;
            entry.journalId = payload.journalId;
            entry.relatedDocuments = [
                {
                    collection: 'finance.entries',
                    view: payload.type === 'receipt' ? 'finance.receivable.receipts' : 'finance.payable.payments',
                    title: payload.type === 'receipt' ? 'Receipts' : 'Payments',
                    ids: [payload._id]
                }
            ];
            if (_.isPlainObject(params.user)) entry.createdBy = params.user._id;

            if (payload.documentType === 'cheque' && !payload.documentNo) {
                throw new app.errors.Unprocessable(this.translate('Cheque number is required!'));
            }
            if (payload.documentType === 'promissoryNote' && !payload.documentNo) {
                throw new app.errors.Unprocessable(this.translate('Promissory note number is required!'));
            }
            if (payload.documentType === 'cheque') {
                const existingCheque = await app.collection('finance.cheques').findOne({
                    chequeNumber: payload.documentNo,
                    partnerId: payload.partnerId,
                    ...(!!payload.bankId
                        ? {
                              bankId: payload.bankId
                          }
                        : {}),
                    $select: ['_id']
                });

                if (!!existingCheque) {
                    throw new app.errors.Unprocessable(this.translate('Cheque number must be unique!'));
                }

                if (payload.dueDate.getTime() < payload.issueDate.getTime()) {
                    throw new app.errors.Unprocessable(
                        this.translate('The due date of the cheque cannot be earlier than the issue date!')
                    );
                }
            }
            if (payload.documentType === 'promissoryNote') {
                const existingCheque = await app.collection('finance.promissory-notes').findOne({
                    promissoryNoteNumber: payload.documentNo,
                    partnerId: payload.partnerId,
                    $select: ['_id']
                });

                if (!!existingCheque) {
                    throw new app.errors.Unprocessable(this.translate('Promissory note number must be unique!'));
                }
            }

            if (payload.documentType === 'pos') {
                if (!payload.documentNo) {
                    throw new app.errors.Unprocessable(
                        this.translate('{{label}} is required', {
                            label: this.translate('Document no')
                        })
                    );
                }

                if (!payload.posId) {
                    throw new app.errors.Unprocessable(
                        this.translate('{{label}} is required', {
                            label: this.translate('POS account')
                        })
                    );
                }

                if (!_.isNumber(payload.installmentCount)) {
                    throw new app.errors.Unprocessable(
                        this.translate('{{label}} is required', {
                            label: this.translate('Installment count')
                        })
                    );
                }

                if (!_.isNumber(payload.installmentAmount)) {
                    throw new app.errors.Unprocessable(
                        this.translate('{{label}} is required', {
                            label: this.translate('Installment amount')
                        })
                    );
                }
            }

            // Prepare items.
            const items = [];
            if (payload.type === 'receipt') {
                if (!journal.debitAccountId) {
                    throw new app.errors.Unprocessable(this.translate('Journal debit account is required!'));
                }

                if (!_.isString(partnerAccountId) || !partnerAccountId) {
                    throw new app.errors.Unprocessable(this.translate('Partner accounting account is required!'));
                }

                // Partner row.
                let partnerItem = {};
                partnerItem.accountId = partnerAccountId;
                partnerItem.partnerId = partner._id;
                partnerItem.description = payload.description;
                partnerItem.branchId = payload.branchId;
                partnerItem.currencyId = payload.currencyId;
                partnerItem.scope = payload.scope || '1';
                if (payload.currencyId !== company.currencyId) {
                    partnerItem.creditFC = payload.total;
                    partnerItem.credit = payload.total * payload.currencyRate * payload.globalCurrencyRate;
                } else {
                    partnerItem.credit = payload.total;
                }
                items.push(partnerItem);

                // Journal item.
                if (payload.documentType === 'pos') {
                    const pos = await app.collection('accounting.pos').get(payload.posId);

                    let currencyId = payload.currencyId;
                    if (!!payload.evaluateAccountCurrency) {
                        currencyId = pos.currencyId;
                    }

                    if (pos.splitCreditCardSlips) {
                        const startDate = app.datetime.fromJSDate(entry.issueDate).startOf('day');

                        for (let i = 1; i <= payload.installmentCount; i++) {
                            const slip = {};
                            let dueDate = null;

                            if (pos.paymentOnSpecificDate) {
                                const now = startDate.plus({months: i - 1});
                                const cutoffDate = pos.cutoffDate;

                                if (now.day > cutoffDate) {
                                    dueDate = now.plus({months: 1}).set({day: cutoffDate}).toJSDate();
                                } else {
                                    dueDate = now.set({day: cutoffDate}).toJSDate();
                                }
                            } else {
                                const installment = pos.installmentPayments.find(p => p.installment === i);

                                dueDate = startDate.plus({days: installment.refund || 0}).toJSDate();
                            }

                            let journalItem = {};
                            journalItem.accountId = journal.debitAccountId;
                            journalItem.partnerId = partner._id;
                            journalItem.description = payload.description;
                            journalItem.dueDate = dueDate;
                            journalItem.branchId = payload.branchId;
                            journalItem.currencyId = currencyId;
                            journalItem.financialProjectId = payload.financialProjectId;
                            journalItem.cashFlowItemId = payload.cashFlowItemId;
                            if (currencyId !== company.currencyId) {
                                journalItem.debitFC = payload.installmentAmount;
                                journalItem.debit =
                                    payload.installmentAmount * payload.currencyRate * payload.globalCurrencyRate;
                            } else {
                                if (payload.evaluateAccountCurrency) {
                                    journalItem.debit = payload.installmentAmount * payload.currencyRate;
                                } else {
                                    journalItem.debit = payload.installmentAmount;
                                }
                            }
                            items.push(journalItem);

                            slip.status = 'open';
                            slip.documentNo = payload.documentNo;
                            slip.partnerType = payload.partnerType;
                            slip.partnerId = payload.partnerId;
                            slip.contactPersonId = payload.contactPersonId;
                            slip.journalId = payload.journalId;
                            slip.paymentAccountId = payload.paymentAccountId;
                            slip.currencyId = currencyId;
                            slip.currencyRate = payload.currencyRate * payload.globalCurrencyRate;
                            slip.recordDate = payload.recordDate;
                            slip.issueDate = payload.issueDate;
                            slip.dueDate = dueDate;
                            slip.reference = payload.reference;
                            slip.description = payload.description;
                            slip.branchId = payload.branchId;
                            slip.amount = payload.baseTotal;
                            slip.installmentNo = i;
                            slip.installmentCount = payload.installmentCount;
                            slip.plusInstallmentCount = payload.plusInstallmentCount;
                            slip.installmentAmount = payload.installmentAmount;
                            slip.dueDifference = payload.dueDifference;
                            slip.total = payload.total;
                            slip.partnerCreditCardId = payload.partnerCreditCardId;
                            slip.cardBrand = payload.cardBrand;
                            slip.cardHolder = payload.cardHolder;
                            slip.cardNumber = payload.cardNumber;
                            slip.expireMonth = payload.expireMonth;
                            slip.expireYear = payload.expireYear;
                            slip.cvv = payload.cvv;
                            slip.issuedBy = payload.issuedBy;
                            slip.tagIds = payload.tagIds || [];
                            slip.financialProjectId = payload.financialProjectId;
                            slip.cashFlowItemId = payload.cashFlowItemId;

                            if (payload.evaluateAccountCurrency) {
                                slip.amount = payload.baseTotal * payload.currencyRate;
                                slip.installmentAmount = payload.installmentAmount * payload.currencyRate;
                                slip.dueDifference = payload.dueDifference * payload.currencyRate;
                                slip.total = payload.total * payload.currencyRate;
                            }

                            slips.push(slip);
                        }
                    } else {
                        let journalItem = {};
                        journalItem.accountId = journal.debitAccountId;
                        journalItem.partnerId = partner._id;
                        journalItem.description = payload.description;
                        journalItem.branchId = payload.branchId;
                        journalItem.currencyId = currencyId;
                        journalItem.financialProjectId = payload.financialProjectId;
                        journalItem.cashFlowItemId = payload.cashFlowItemId;
                        if (currencyId !== company.currencyId) {
                            journalItem.debitFC = payload.total;
                            journalItem.debit = payload.total * payload.currencyRate * payload.globalCurrencyRate;
                        } else {
                            if (payload.evaluateAccountCurrency) {
                                journalItem.debit = payload.total * payload.currencyRate;
                            } else {
                                journalItem.debit = payload.total;
                            }
                        }
                        items.push(journalItem);

                        const slip = {};
                        slip.status = 'open';
                        slip.documentNo = payload.documentNo;
                        slip.partnerType = payload.partnerType;
                        slip.partnerId = payload.partnerId;
                        slip.contactPersonId = payload.contactPersonId;
                        slip.journalId = payload.journalId;
                        slip.paymentAccountId = payload.paymentAccountId;
                        slip.currencyId = currencyId;
                        slip.currencyRate = payload.currencyRate * payload.globalCurrencyRate;
                        slip.recordDate = payload.recordDate;
                        slip.issueDate = payload.issueDate;
                        slip.dueDate = payload.dueDate;
                        slip.reference = payload.reference;
                        slip.description = payload.description;
                        slip.branchId = payload.branchId;
                        slip.amount = payload.baseTotal;
                        slip.installmentNo = 1;
                        slip.installmentCount = payload.installmentCount;
                        slip.plusInstallmentCount = payload.plusInstallmentCount;
                        slip.installmentAmount = payload.installmentAmount;
                        slip.dueDifference = payload.dueDifference;
                        slip.total = payload.total;
                        slip.partnerCreditCardId = payload.partnerCreditCardId;
                        slip.cardBrand = payload.cardBrand;
                        slip.cardHolder = payload.cardHolder;
                        slip.cardNumber = payload.cardNumber;
                        slip.expireMonth = payload.expireMonth;
                        slip.expireYear = payload.expireYear;
                        slip.cvv = payload.cvv;
                        slip.issuedBy = payload.issuedBy;
                        slip.tagIds = payload.tagIds || [];
                        slip.financialProjectId = payload.financialProjectId;
                        slip.cashFlowItemId = payload.cashFlowItemId;

                        if (payload.evaluateAccountCurrency) {
                            slip.amount = payload.baseTotal * payload.currencyRate;
                            slip.installmentAmount = payload.installmentAmount * payload.currencyRate;
                            slip.dueDifference = payload.dueDifference * payload.currencyRate;
                            slip.total = payload.total * payload.currencyRate;
                        }

                        slips.push(slip);
                    }
                } else {
                    let currencyId = payload.currencyId;
                    if (!!payload.evaluateAccountCurrency) {
                        currencyId = journal.currencyId ?? company.currencyId;
                    }

                    let journalItem = {};
                    journalItem.accountId = journal.debitAccountId;
                    journalItem.partnerId = partner._id;
                    journalItem.description = payload.description;
                    journalItem.branchId = payload.branchId;
                    journalItem.currencyId = currencyId;
                    journalItem.scope = payload.scope || '1';
                    journalItem.financialProjectId = payload.financialProjectId;
                    journalItem.cashFlowItemId = payload.cashFlowItemId;
                    if (currencyId !== company.currencyId) {
                        journalItem.debitFC = payload.total;
                        journalItem.debit = payload.total * payload.currencyRate * payload.globalCurrencyRate;
                    } else {
                        if (payload.evaluateAccountCurrency) {
                            journalItem.debit = payload.total * payload.currencyRate;
                        } else {
                            journalItem.debit = payload.total;
                        }
                    }
                    items.push(journalItem);
                }
            } else {
                if (!journal.creditAccountId) {
                    throw new app.errors.Unprocessable(this.translate('Journal credit account is required!'));
                }

                if (!partnerAccountId) {
                    throw new app.errors.Unprocessable(this.translate('Partner accounting account is required!'));
                }

                // Journal item.
                if (payload.documentType === 'creditCard') {
                    const startDate = app.datetime.fromJSDate(entry.issueDate).startOf('day');

                    for (let i = 1; i <= payload.installmentCount; i++) {
                        const dueDate = startDate.plus({months: i - 1}).toJSDate();

                        let journalItem = {};
                        journalItem.accountId = journal.creditAccountId;
                        journalItem.partnerId = partner._id;
                        journalItem.description = payload.description;
                        journalItem.branchId = payload.branchId;
                        journalItem.currencyId = payload.currencyId;
                        journalItem.dueDate = dueDate;
                        journalItem.financialProjectId = payload.financialProjectId;
                        journalItem.cashFlowItemId = payload.cashFlowItemId;
                        journalItem.scope = payload.scope || '1';
                        if (payload.currencyId !== company.currencyId) {
                            journalItem.creditFC = payload.installmentAmount;
                            journalItem.credit =
                                payload.installmentAmount * payload.currencyRate * payload.globalCurrencyRate;
                        } else {
                            journalItem.credit = payload.installmentAmount;
                        }
                        items.push(journalItem);

                        const slip = {};
                        slip.status = 'payment';
                        slip.documentNo = payload.documentNo;
                        slip.partnerType = payload.partnerType;
                        slip.partnerId = payload.partnerId;
                        slip.contactPersonId = payload.contactPersonId;
                        slip.journalId = payload.journalId;
                        slip.paymentAccountId = payload.paymentAccountId;
                        slip.currencyId = payload.currencyId;
                        slip.currencyRate = payload.currencyRate * payload.globalCurrencyRate;
                        slip.recordDate = payload.recordDate;
                        slip.issueDate = payload.issueDate;
                        slip.dueDate = dueDate;
                        slip.reference = payload.reference;
                        slip.description = payload.description;
                        slip.branchId = payload.branchId;
                        slip.amount = payload.baseTotal;
                        slip.installmentNo = i;
                        slip.installmentCount = payload.installmentCount;
                        slip.installmentAmount = payload.installmentAmount;
                        slip.issuedBy = payload.issuedBy;
                        slip.tagIds = payload.tagIds || [];
                        slip.financialProjectId = payload.financialProjectId;
                        slip.cashFlowItemId = payload.cashFlowItemId;
                        slip.scope = payload.scope || '1';
                        slips.push(slip);
                    }
                } else {
                    let currencyId = payload.currencyId;
                    if (!!payload.evaluateAccountCurrency) {
                        currencyId = journal.currencyId ?? company.currencyId;
                    }

                    let journalItem = {};
                    journalItem.accountId = journal.creditAccountId;
                    journalItem.partnerId = partner._id;
                    journalItem.description = payload.description;
                    journalItem.branchId = payload.branchId;
                    journalItem.currencyId = currencyId;
                    journalItem.financialProjectId = payload.financialProjectId;
                    journalItem.cashFlowItemId = payload.cashFlowItemId;
                    journalItem.scope = payload.scope || '1';
                    if (currencyId !== company.currencyId) {
                        journalItem.creditFC = payload.total;
                        journalItem.credit = payload.total * payload.currencyRate * payload.globalCurrencyRate;
                    } else {
                        if (payload.evaluateAccountCurrency) {
                            journalItem.credit = payload.total * payload.currencyRate;
                        } else {
                            journalItem.credit = payload.total;
                        }
                    }
                    items.push(journalItem);
                }

                // Partner row.
                let partnerItem = {};
                partnerItem.accountId = partnerAccountId;
                partnerItem.partnerId = partner._id;
                partnerItem.description = payload.description;
                partnerItem.branchId = payload.branchId;
                partnerItem.currencyId = payload.currencyId;
                partnerItem.scope = payload.scope || '1';
                if (payload.currencyId !== company.currencyId) {
                    partnerItem.debitFC = payload.total;
                    partnerItem.debit = payload.total * payload.currencyRate * payload.globalCurrencyRate;
                } else {
                    partnerItem.debit = payload.total;
                }
                items.push(partnerItem);
            }
            entry.items = items;

            // Fix pos journal items.
            if (payload.documentType === 'pos') {
                let debitTotal = 0;
                let creditTotal = 0;
                for (const item of items) {
                    if (_.isNumber(item.debit)) {
                        debitTotal += item.debit;
                    }

                    if (_.isNumber(item.credit)) {
                        creditTotal += item.credit;
                    }
                }
                debitTotal = round(debitTotal, currencyPrecision);
                creditTotal = round(creditTotal, currencyPrecision);
                const diff = creditTotal - debitTotal;
                if (payload.currencyId !== company.currencyId) {
                    items[items.length - 1].debitFC += diff;
                    items[items.length - 1].debit += diff * payload.currencyRate * payload.globalCurrencyRate;
                } else {
                    items[items.length - 1].debit += diff;
                }
            }
            entry.items = entry.items.map(item => {
                if (!item.financialProjectId && !!payload.financialProjectId) {
                    item.financialProjectId = payload.financialProjectId;
                }

                if (!item.salespersonId && !!payload.salespersonId) {
                    item.salespersonId = payload.salespersonId;
                }

                return item;
            });

            let chequeTrackingCode = null;
            if (payload.documentType === 'cheque' && journal.chequeTrackingNumberingId) {
                chequeTrackingCode = await app.rpc('kernel.common.request-number', {
                    numberingId: journal.chequeTrackingNumberingId,
                    save: true
                });
            }
            if (chequeTrackingCode) entry.chequeTrackingCode = chequeTrackingCode;

            // Create accounting entry.
            const journalEntry = await app.rpc('accounting.save-journal-entry', {data: entry}, {user: params.user});
            const transactions = await app.rpc('accounting.post-journal-entry', journalEntry._id, {user: params.user});
            const voucherNo = (
                await app.collection('accounting.journal-entries').findOne({
                    _id: journalEntry._id,
                    $select: ['voucherNo']
                })
            ).voucherNo;

            // Update accounting transaction.
            await app
                .collection('accounting.transactions')
                .patch({_id: {$in: transactions.map(t => t._id)}}, {transactionType: payload.transactionType});

            // Cheque payment.
            if (payload.documentType === 'cheque') {
                const cheque = {};

                // Prepare cheque
                cheque.status = payload.type === 'receipt' ? 'in-bill-case' : 'produced';
                cheque.code = voucherNo;
                cheque.journalId = payload.journalId;
                cheque.reference = payload.reference;
                cheque.description = payload.description;
                cheque.branchId = payload.branchId;
                cheque.recordDate = payload.recordDate;
                cheque.issueDate = payload.issueDate;
                cheque.dueDate = payload.dueDate;
                cheque.currencyId = payload.currencyId;
                cheque.currencyRate = payload.currencyRate * payload.globalCurrencyRate;
                cheque.amount = payload.total;
                cheque.partnerType = partner.type;
                cheque.partnerId = payload.partnerId;
                cheque.contactPersonId = payload.contactPersonId;
                cheque.isIssued = payload.type === 'payment';
                cheque.chequeNumber = payload.documentNo;
                cheque.endorsable = payload.endorsable;
                cheque.paymentAccountId = payload.paymentAccountId;
                cheque.currentAccountId = payload.type === 'receipt' ? journal.debitAccountId : journal.creditAccountId;
                cheque.countryId = payload.countryId;
                cheque.firstWrittenBy = payload.firstWrittenBy;
                cheque.financialIdentifier = payload.financialIdentifier;
                cheque.guaranteeId = payload.guaranteeId;
                cheque.issuedBy = payload.issuedBy;
                cheque.journalEntryId = journalEntry._id;
                cheque.tagIds = payload.tagIds || [];
                cheque.financialProjectId = payload.financialProjectId;
                cheque.cashFlowItemId = payload.cashFlowItemId;
                cheque.scope = payload.scope || '1';
                cheque.bankId = payload.bankId;
                cheque.bankBranchName = payload.bankBranchName;
                cheque.accountNumber = payload.accountNumber;
                cheque.iban = payload.iban;
                cheque.city = payload.city;
                cheque.district = payload.district;
                cheque.salespersonId = payload.salespersonId;
                if (chequeTrackingCode) cheque.chequeTrackingCode = chequeTrackingCode;

                if (cheque.status === 'produced' && !!journal.chequeBankAccountId && _.isEmpty(cheque.bankId)) {
                    const bankAccount = await app.collection('accounting.bank-accounts').findOne({
                        _id: journal.chequeBankAccountId,
                        $select: ['bankId'],
                        $disableActiveCheck: true,
                        $disableSoftDelete: true
                    });

                    if (!!bankAccount) {
                        cheque.bankId = bankAccount.bankId;
                    }
                }

                // Update exchange rates.
                const exchangeRates = [];
                const exchangeRatesMap = {};
                const dueExchangeRatesMap = {};
                for (const currency of currencies) {
                    if (currency.name === company.currency.name) {
                        continue;
                    }

                    exchangeRates.push({
                        currencyName: currency.name,
                        rate: 0,
                        dueRate: 0
                    });
                    exchangeRatesMap[currency.name] = 0;
                    dueExchangeRatesMap[currency.name] = 0;
                }
                await (async () => {
                    const payloads = [];

                    for (const currency of currencies) {
                        if (currency.name === company.currency.name) {
                            continue;
                        }

                        payloads.push({
                            from: currency.name,
                            to: company.currency.name,
                            value: 1,
                            options: {
                                date: payload.issueDate
                            }
                        });
                    }

                    for (const payload of await app.rpc('kernel.common.convert-currencies', payloads)) {
                        const index = exchangeRates.findIndex(er => er.currencyName === payload.from);

                        if (index !== -1) {
                            exchangeRates[index].rate = payload.rate;
                        }
                    }

                    for (const exchangeRate of exchangeRates || []) {
                        exchangeRatesMap[exchangeRate.currencyName] = exchangeRate.rate;
                    }
                })();
                await (async () => {
                    const payloads = [];

                    for (const currency of currencies) {
                        if (currency.name === company.currency.name) {
                            continue;
                        }

                        payloads.push({
                            from: currency.name,
                            to: company.currency.name,
                            value: 1,
                            options: {
                                date: payload.dueDate
                            }
                        });
                    }

                    for (const payload of await app.rpc('kernel.common.convert-currencies', payloads)) {
                        const index = exchangeRates.findIndex(er => er.currencyName === payload.from);

                        if (index !== -1) {
                            exchangeRates[index].dueRate = payload.rate;
                        }
                    }

                    for (const exchangeRate of exchangeRates || []) {
                        dueExchangeRatesMap[exchangeRate.currencyName] = exchangeRate.rate;
                    }
                })();
                cheque.exchangeRates = exchangeRates;
                cheque.exchangeRatesMap = exchangeRatesMap;
                cheque.dueExchangeRatesMap = dueExchangeRatesMap;

                // Create cheque.
                const result = await app.collection('finance.cheques').create(cheque, {user: params.user});

                // Create history.
                await app.collection('finance.cheques-history').create(
                    {
                        chequeId: result._id,
                        journalId: cheque.journalId,
                        status: cheque.status,
                        code: cheque.code,
                        amount: cheque.amount,
                        currencyId: cheque.currencyId,
                        currencyRate: cheque.currencyRate,
                        no: 1,
                        recordDate: cheque.recordDate,
                        issueDate: cheque.issueDate,
                        dueDate: cheque.dueDate,
                        reference: cheque.reference,
                        description: cheque.description,
                        partnerId: payload.partnerId,
                        journalEntryId: cheque.journalEntryId,
                        paymentAccountId: cheque.paymentAccountId,
                        ...(_.isPlainObject(params.user) && {editedBy: params.user._id})
                    },
                    {user: params.user}
                );
            }

            // Promissory note payment.
            if (payload.documentType === 'promissoryNote') {
                const promissoryNote = {};

                // Prepare cheque
                promissoryNote.status = payload.type === 'receipt' ? 'in-bill-case' : 'produced';
                promissoryNote.code = voucherNo;
                promissoryNote.journalId = payload.journalId;
                promissoryNote.reference = payload.reference;
                promissoryNote.description = payload.description;
                promissoryNote.branchId = payload.branchId;
                promissoryNote.recordDate = payload.recordDate;
                promissoryNote.issueDate = payload.issueDate;
                promissoryNote.dueDate = payload.dueDate;
                promissoryNote.currencyId = payload.currencyId;
                promissoryNote.currencyRate = payload.currencyRate * payload.globalCurrencyRate;
                promissoryNote.amount = payload.total;
                promissoryNote.partnerType = partner.type;
                promissoryNote.partnerId = payload.partnerId;
                promissoryNote.contactPersonId = payload.contactPersonId;
                promissoryNote.isIssued = payload.type === 'payment';
                promissoryNote.promissoryNoteNumber = payload.documentNo;
                promissoryNote.endorsable = payload.endorsable;
                promissoryNote.paymentAccountId = payload.paymentAccountId;
                promissoryNote.currentAccountId =
                    payload.type === 'receipt' ? journal.debitAccountId : journal.creditAccountId;
                promissoryNote.countryId = payload.countryId;
                promissoryNote.firstWrittenBy = payload.firstWrittenBy;
                promissoryNote.financialIdentifier = payload.financialIdentifier;
                promissoryNote.guaranteeId = payload.guaranteeId;
                promissoryNote.partnerBankAccountId = payload.partnerBankAccountId;
                promissoryNote.guarantor1 = payload.guarantor1;
                promissoryNote.guarantor2 = payload.guarantor2;
                promissoryNote.guarantorIds = payload.guarantorIds || [];
                promissoryNote.issuedBy = payload.issuedBy;
                promissoryNote.scope = payload.scope || '1';
                promissoryNote.journalEntryId = journalEntry._id;
                promissoryNote.tagIds = payload.tagIds || [];
                promissoryNote.financialProjectId = payload.financialProjectId;
                promissoryNote.cashFlowItemId = payload.cashFlowItemId;
                promissoryNote.bankId = payload.bankId;
                promissoryNote.bankBranchName = payload.bankBranchName;
                promissoryNote.accountNumber = payload.accountNumber;
                promissoryNote.iban = payload.iban;
                promissoryNote.city = payload.city;
                promissoryNote.district = payload.district;
                promissoryNote.salespersonId = payload.salespersonId;

                // Create promissory note.
                const result = await app
                    .collection('finance.promissory-notes')
                    .create(promissoryNote, {user: params.user});

                // Create history.
                await app.collection('finance.promissory-notes-history').create(
                    {
                        promissoryNoteId: result._id,
                        journalId: promissoryNote.journalId,
                        status: promissoryNote.status,
                        code: promissoryNote.code,
                        amount: promissoryNote.amount,
                        currencyId: promissoryNote.currencyId,
                        currencyRate: promissoryNote.currencyRate,
                        no: 1,
                        recordDate: promissoryNote.recordDate,
                        issueDate: promissoryNote.issueDate,
                        dueDate: promissoryNote.dueDate,
                        reference: promissoryNote.reference,
                        description: promissoryNote.description,
                        partnerId: payload.partnerId,
                        journalEntryId: promissoryNote.journalEntryId,
                        paymentAccountId: promissoryNote.paymentAccountId,
                        ...(_.isPlainObject(params.user) && {editedBy: params.user._id})
                    },
                    {user: params.user}
                );
            }

            if (payload.documentType === 'pos') {
                let lastTotal = 0;
                slips = slips.map(slip => {
                    slip.code = voucherNo;
                    slip.journalEntryId = journalEntry._id;

                    lastTotal += round(slip.installmentAmount, currencyPrecision);

                    return slip;
                });
                lastTotal = round(payload.total - lastTotal, currencyPrecision);
                slips[slips.length - 1].installmentAmount += lastTotal;

                await app.collection('finance.pos-transactions').create(slips, {user: params.user});
            }

            if (payload.documentType === 'creditCard') {
                slips = slips.map(slip => {
                    slip.code = voucherNo;
                    slip.journalEntryId = journalEntry._id;

                    return slip;
                });

                await app.collection('finance.credit-card-transactions').create(slips, {user: params.user});
            }

            // Create partner limit.  payload.type === 'payment'
            if (!!payload.partnerId && partner.enableLimitChecks && payload.documentType !== 'cheque') {
                const limit = {};

                limit.partnerType = partner.type;
                limit.partnerId = partner._id;
                limit.guaranteeId = payload.guaranteeId;
                limit.date = app.datetime.local().toJSDate();
                limit.documentCollection = 'finance.entries';
                limit.documentView =
                    payload.type === 'receipt' ? 'finance.receivable.receipts' : 'finance.payable.payments';
                limit.documentId = payload._id;
                limit.currencyId = partner.currencyId;
                limit.amount = payload.total;

                if (payload.currencyId !== partner.currencyId) {
                    const documentCurrency = await app.collection('kernel.currencies').findOne({
                        _id: payload.currencyId,
                        $select: ['name'],
                        $disableActiveCheck: true,
                        $disableSoftDelete: true
                    });
                    const partnerCurrency = await app.collection('kernel.currencies').findOne({
                        _id: partner.currencyId,
                        $select: ['name'],
                        $disableActiveCheck: true,
                        $disableSoftDelete: true
                    });
                    limit.amount = await app.rpc('kernel.common.convert-currency', {
                        from: documentCurrency.name,
                        to: partnerCurrency.name,
                        value: payload.total,
                        options: {
                            date: payload.issueDate
                        }
                    });
                }

                // const invoiceRDS = (payload.relatedDocuments || []).filter(
                //     rd => rd.collection === 'accounting.customer-invoices' || rd.collection === 'accounting.vendor-invoices'
                // );
                // if (invoiceRDS.length === 1) {
                //     const invoiceId = invoiceRDS[0].ids[0];
                //
                //     const existing = await app.collection('finance.partner-limit-transactions').findOne({documentId: invoiceId});
                //
                //     if (_.isPlainObject(existing) && existing.guaranteeId !== payload.guaranteeId) {
                //         const newAmount = Math.abs(existing.amount) - limit.amount;
                //
                //         if (newAmount > 0) {
                //             await app.collection('finance.partner-limit-transactions').patch({_id: existing._id}, {
                //                 amount: -newAmount
                //             });
                //         } else {
                //             await app.collection('finance.partner-limit-transactions').remove({_id: existing._id});
                //         }
                //
                //         await app.collection('finance.partner-limit-transactions').create({
                //             ..._.omit(existing, '_id'),
                //             date: app.datetime.local().toJSDate(),
                //             guaranteeId: payload.guaranteeId,
                //             amount: -limit.amount
                //         });
                //     }
                // }

                await app.collection('finance.partner-limit-transactions').create(limit);
            }

            // Update entries.
            const relatedDocuments = (payload.relatedDocuments || []).concat([
                {
                    collection: 'accounting.journal-entries',
                    view: 'accounting.adviser.journal-entries',
                    title: 'Journal Entries',
                    ids: [journalEntry._id]
                }
            ]);
            return await collection.patch(
                {_id: payload._id},
                {
                    status: 'approved',
                    documentNo: payload.documentNo,
                    voucherNo,
                    accountingJournalEntryId: journalEntry._id,
                    accountingTransactionIds: transactions.map(t => t._id),
                    relatedDocuments,
                    chequeTrackingCode
                }
            );
        }
    }
};
