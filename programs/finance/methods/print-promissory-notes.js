import _ from 'lodash';
import sharp from 'sharp';
import fs from 'fs-extra';
import {firstUpperAll, toUpper} from 'framework/helpers';
import writtenNumber from 'framework/written-number';

export default {
    name: 'print-promissory-notes',
    async action(data, params) {
        const app = this.app;
        const {date, printAttachedImages, move, partnerId, collectorId, paymentAccountId, journal} = data;
        const items = _.orderBy(data.items, 'dueDate');
        const t = (translation, params) => firstUpperAll(app.translate(translation, params));
        const format = app.format;
        const company = await app.collection('kernel.company').findOne({});
        const payload = {};

        try {
            // Check currency ids.
            if (_.uniq(items.map(item => item.currencyId)).length > 1) {
                throw new app.errors.Unprocessable(app.translate('All items must have the same currency!'));
            }

            // Get code.
            const numbering = await this.app.collection('kernel.numbering').findOne(
                {
                    code: 'financePaymentSlipNumbering',
                    $select: ['_id']
                },
                {
                    disableInUseCheck: true,
                    disableActiveCheck: true,
                    disableSoftDelete: true
                }
            );
            let code = null;

            let total = 0;
            let adat = 0;
            for (const entry of items) {
                const dueDateStart = app.datetime.fromJSDate(date);
                const dueDateEnd = app.datetime.fromJSDate(entry.dueDate);
                const dueDayDifference = Math.ceil(dueDateEnd.diff(dueDateStart, 'days').toObject().days);

                total += entry.amount;
                adat += dueDayDifference * entry.amount;
            }
            let avgDueDayDifference = app.roundNumber(adat / total);
            let avgDueDate = app.datetime.fromJSDate(date).plus({days: avgDueDayDifference}).toJSDate();

            if (!move && !!collectorId) {
                code = await this.app.rpc('kernel.common.request-number', {
                    numberingId: numbering._id,
                    save: true
                });

                const companyData = {
                    stack: [
                        {
                            text: company.legalName ? company.legalName : company.name,
                            fontSize: 9,
                            bold: true,
                            margin: [0, 0, 0, 4]
                        },
                        {
                            text: company.address.address,
                            fontSize: 8,
                            margin: [0, 0, 0, 10]
                        },
                        {
                            columns: [
                                {
                                    text: t('TD:'),
                                    fontSize: 8,
                                    bold: true,
                                    width: 'auto',
                                    margin: [0, 0, 3, 0]
                                },
                                {
                                    text: company.taxDepartment,
                                    fontSize: 8,
                                    margin: [0, 0, 10, 0]
                                },
                                {
                                    text: t('TN:'),
                                    fontSize: 8,
                                    bold: true,
                                    width: 'auto',
                                    margin: [0, 0, 3, 0]
                                },
                                {
                                    text: company.tin,
                                    fontSize: 8
                                },
                                {}
                            ]
                        }
                    ],
                    width: 210
                };

                const partner = await app.collection('kernel.partners').findOne({
                    _id: collectorId,
                    $select: ['code', 'name', 'identity']
                });
                const partnerData = {
                    stack: [
                        {
                            text: partner.name,
                            fontSize: 9,
                            bold: true,
                            margin: [0, 0, 0, 4]
                        },
                        {
                            text: partner.code,
                            margin: [0, 0, 0, 4],
                            fontSize: 8
                        }
                    ],
                    width: 210
                };

                const generalTable = {
                    table: {
                        headerRows: 0,
                        widths: ['auto', 'auto'],
                        body: []
                    },
                    layout: {},
                    width: 210
                };
                generalTable.table.body.push([
                    {
                        text: `${t('Document no')}:`,
                        style: 'definitionLabel',
                        border: [false, false, false, false],
                        noWrap: true
                    },
                    {
                        text: code,
                        style: 'definitionValue',
                        border: [false, false, false, false],
                        noWrap: false
                    }
                ]);
                generalTable.table.body.push([
                    {
                        text: `${t('Date of issue')}:`,
                        style: 'definitionLabel',
                        border: [false, false, false, false],
                        noWrap: true
                    },
                    {
                        text: format(date, 'date'),
                        style: 'definitionValue',
                        border: [false, false, false, false],
                        noWrap: false
                    }
                ]);

                const detailsTable = {
                    table: {
                        headerRows: 0,
                        widths: ['auto', 'auto'],
                        body: []
                    },
                    layout: {},
                    width: 210
                };
                detailsTable.table.body.push([
                    {
                        text: `${t('Due day difference')}:`,
                        style: 'definitionLabel',
                        border: [false, false, false, false],
                        noWrap: true
                    },
                    {
                        text: avgDueDayDifference,
                        style: 'definitionValue',
                        border: [false, false, false, false],
                        noWrap: false
                    }
                ]);
                detailsTable.table.body.push([
                    {
                        text: `${t('Avg. due date')}:`,
                        style: 'definitionLabel',
                        border: [false, false, false, false],
                        noWrap: true
                    },
                    {
                        text: format(avgDueDate, 'date'),
                        style: 'definitionValue',
                        border: [false, false, false, false],
                        noWrap: false
                    }
                ]);

                const itemsTable = {
                    table: {
                        headerRows: 1,
                        dontBreakRows: true,
                        widths: ['auto', 'auto', '*', '*', 'auto', 'auto', 'auto', 'auto', 'auto', 'auto', 'auto'],
                        body: []
                    },
                    layout: {
                        hLineWidth(i, node) {
                            return i === 1 ? 1 : 0.5;
                        },
                        vLineWidth(i, node) {
                            return 0.5;
                        },
                        hLineColor(i, node) {
                            return '#bdc3c7';
                        },
                        vLineColor(i, node) {
                            return '#bdc3c7';
                        },
                        fillColor(rowIndex, node, columnIndex) {
                            return rowIndex % 2 === 1 ? '#fcfcfc' : null;
                        }
                    },
                    width: 782,
                    marginBottom: 30
                };
                itemsTable.table.body.push([
                    {
                        text: t('PN no'),
                        style: 'itemsHeader',
                        border: [false, false, false, true],
                        noWrap: false
                    },
                    {
                        text: t('Due date'),
                        style: 'itemsHeader',
                        border: [false, false, false, true],
                        noWrap: false
                    },
                    {
                        text: t('Partner'),
                        style: 'itemsHeader',
                        border: [false, false, false, true],
                        noWrap: false
                    },
                    {
                        text: t('First written by'),
                        style: 'itemsHeader',
                        border: [false, false, false, true],
                        noWrap: false
                    },
                    {
                        text: t('PN bank'),
                        style: 'itemsHeader',
                        border: [false, false, false, true],
                        noWrap: false
                    },
                    {
                        text: t('Bank branch'),
                        style: 'itemsHeader',
                        border: [false, false, false, true],
                        noWrap: false
                    },
                    {
                        text: t('Serial no'),
                        style: 'itemsHeader',
                        border: [false, false, false, true],
                        noWrap: true
                    },
                    {
                        text: t('CRN'),
                        style: 'itemsHeader',
                        border: [false, false, false, true],
                        noWrap: true
                    },
                    {
                        text: t('PN amount'),
                        style: 'itemsHeader',
                        border: [false, false, false, true],
                        noWrap: true
                    },
                    {
                        text: t('Currency amount'),
                        style: 'itemsHeader',
                        border: [false, false, false, true],
                        noWrap: true
                    },
                    {
                        text: t('Exchange rate'),
                        style: 'itemsHeader',
                        border: [false, false, false, true],
                        noWrap: true
                    }
                ]);
                for (let item of items || []) {
                    const currency = await app.collection('kernel.currencies').findOne({
                        _id: item.currencyId,
                        $disableActiveCheck: true,
                        $disableSoftDelete: true
                    });

                    const promissoryNote = await app.collection('finance.promissory-notes').findOne({
                        _id: item._id,
                        $select: ['bankId', 'bankBranchName', 'firstWrittenBy'],
                        $disableActiveCheck: true,
                        $disableSoftDelete: true
                    });
                    item = {
                        ...item,
                        ...promissoryNote
                    };

                    let partnerName = '';
                    if (!!item.partnerId) {
                        const partner = await app.collection('kernel.partners').findOne({
                            _id: item.partnerId,
                            $select: ['name'],
                            $disableActiveCheck: true,
                            $disableSoftDelete: true
                        });
                        partnerName = partner.name;
                    }
                    const bankBranchName = item.bankBranchName || '';
                    let bankName = null;
                    if (!!item.bankId) {
                        const bank = await app.collection('kernel.banks').findOne({
                            _id: item.bankId,
                            $select: ['name'],
                            $disableActiveCheck: true,
                            $disableSoftDelete: true
                        });
                        bankName = bank.name;
                    }

                    itemsTable.table.body.push([
                        {text: item.code, style: 'itemText', noWrap: true},
                        {text: format(item.dueDate, 'date'), style: 'itemText', noWrap: true},
                        {text: partnerName, style: 'itemText'},
                        {text: item.firstWrittenBy || '', style: 'itemText'},
                        {text: bankName, style: 'itemText'},
                        {text: bankBranchName, style: 'itemText'},
                        {text: item.promissoryNoteNumber, style: 'itemText', noWrap: true},
                        {text: currency.name, style: 'itemText', noWrap: true},
                        {text: format(item.amount || 0, 'decimal'), style: 'itemNumber', noWrap: true},
                        {text: format(item.systemCurrencyAmount || 0, 'decimal'), style: 'itemNumber', noWrap: true},
                        {text: format(item.currencyRate || 1, 'decimal'), style: 'itemNumber', noWrap: true}
                    ]);
                }
                itemsTable.table.body.push([
                    {
                        text: '',
                        style: 'itemText',
                        border: [false, false, false, false],
                        noWrap: false
                    },
                    {
                        text: '',
                        style: 'itemText',
                        border: [false, false, false, false],
                        noWrap: false
                    },
                    {
                        text: '',
                        style: 'itemText',
                        border: [false, false, false, false],
                        noWrap: false
                    },
                    {
                        text: '',
                        style: 'itemText',
                        border: [false, false, false, false],
                        noWrap: false
                    },
                    {
                        text: '',
                        style: 'itemText',
                        border: [false, false, false, false],
                        noWrap: true
                    },
                    {
                        text: '',
                        style: 'itemText',
                        border: [false, false, false, false],
                        noWrap: true
                    },
                    {
                        text: t('Total'),
                        style: 'itemTotal',
                        border: [false, false, false, false],
                        noWrap: false
                    },
                    {
                        text: '',
                        style: 'itemText',
                        border: [false, false, false, false],
                        noWrap: true
                    },
                    {
                        text: format(_.sumBy(items, 'amount'), 'decimal'),
                        style: 'itemTotal',
                        border: [false, false, false, false],
                        noWrap: true
                    },
                    {
                        text: format(_.sumBy(items, 'systemCurrencyAmount'), 'decimal'),
                        style: 'itemTotal',
                        border: [false, false, false, false],
                        noWrap: true
                    },
                    {
                        text: '',
                        style: 'itemText',
                        border: [false, false, false, false],
                        noWrap: true
                    }
                ]);

                const currency = await app.collection('kernel.currencies').findOne({
                    _id: items[0].currencyId,
                    $disableActiveCheck: true,
                    $disableSoftDelete: true
                });
                const total = _.sumBy(items, 'amount');
                let totalWritten = '';
                if (app.config('app.locale') === 'tr' && currency.name === 'TL') {
                    if (Math.round(total) !== total) {
                        const parts = total.toFixed(2).split('.');

                        totalWritten = `YALNIZ: ${toUpper(writtenNumber(parseInt(parts[0]), {lang: 'tr'})).replace(
                            /\s/g,
                            ''
                        )} TL ${toUpper(writtenNumber(parseInt(parts[1]), {lang: 'tr'})).replace(/\s/g, '')} Kr.`;
                    } else {
                        totalWritten = `YALNIZ: ${toUpper(writtenNumber(total, {lang: 'tr'})).replace(/\s/g, '')} TL`;
                    }
                } else {
                    totalWritten = toUpper(writtenNumber(total, {lang: app.config('app.locale')}));
                }
                const noteData = {
                    stack: [
                        {
                            text: t('Note'),
                            fontSize: 9,
                            bold: true,
                            color: '#0052cc',
                            margin: [0, 0, 0, 4]
                        },
                        {
                            text: totalWritten,
                            noWrap: true,
                            fontSize: 8
                        },
                        {
                            text: '',
                            fontSize: 6
                        }
                    ],
                    margin: [0, 0, 20, 0]
                };

                const delivererData = {
                    stack: [
                        {
                            text: t('Deliverer'),
                            fontSize: 9,
                            bold: true,
                            color: '#0052cc',
                            margin: [0, 0, 0, 4]
                        },
                        {
                            text: '',
                            fontSize: 8
                        }
                    ],
                    margin: [0, 0, 20, 0]
                };
                const recipientData = {
                    stack: [
                        {
                            text: t('Recipient'),
                            fontSize: 9,
                            bold: true,
                            color: '#0052cc',
                            margin: [0, 0, 0, 4]
                        },
                        {
                            text: '',
                            fontSize: 8
                        }
                    ],
                    margin: [0, 0, 20, 0]
                };

                let attachmentData = {};
                if (!!printAttachedImages) {
                    const documents = await app.collection('finance.cheques').find({
                        _id: {$in: items.map(item => item._id)},
                        $select: ['attachments']
                    });
                    let fileIds = [];
                    for (const item of documents) {
                        fileIds.push(...(item.attachments || []));
                    }
                    fileIds = _.uniq(fileIds);

                    if (fileIds.length > 0) {
                        const files = await app.files.find({_id: {$in: fileIds}});
                        const images = [];

                        for (const file of files) {
                            const imageType = file.contentType;

                            if (
                                !(imageType === 'image/png' || imageType === 'image/jpg' || imageType === 'image/jpeg')
                            ) {
                                continue;
                            }

                            try {
                                let image = await fs.readFile(file.path);
                                image = (await sharp(image).toBuffer()).toString('base64');
                                image = `data:${imageType};base64,${image}`;

                                images.push({
                                    image,
                                    width: 375
                                });
                            } catch (e) {}
                        }

                        attachmentData = {
                            stack: []
                        };

                        for (const chunk of _.chunk(images, 2)) {
                            if (chunk.length > 1) {
                                attachmentData.stack.push({
                                    columns: [chunk[0], {}, chunk[1]],
                                    marginBottom: 30
                                });
                            } else {
                                attachmentData.stack.push({
                                    columns: [chunk[0], {}],
                                    marginBottom: 30
                                });
                            }
                        }

                        if (images.length < 1) {
                            attachmentData = null;
                        }
                    }
                }

                payload.title = t('Promissory Note Issued Receipt');
                payload.saveOutput = true;
                payload.landscape = true;
                payload.definition = {
                    content: [
                        {columns: [companyData, {}, partnerData], marginBottom: 30},
                        {columns: [generalTable, {}, detailsTable], marginBottom: 30},
                        itemsTable,
                        {
                            columns: [noteData, {}, delivererData, {}, recipientData],
                            marginBottom: !!attachmentData ? 30 : 0
                        },
                        ...(!!attachmentData ? [attachmentData] : [])
                    ],
                    styles: {
                        // Items Header
                        itemsHeader: {
                            margin: 1,
                            fontSize: 7,
                            bold: true
                        },

                        // Item.
                        itemNumber: {
                            fontSize: 6,
                            margin: 1,
                            alignment: 'right'
                        },
                        itemText: {
                            fontSize: 6,
                            margin: 1
                        },
                        itemTotal: {
                            fontSize: 7,
                            margin: 1,
                            bold: true,
                            alignment: 'right'
                        },

                        definitionLabel: {
                            margin: [0, 0, 0, 2],
                            fontSize: 9,
                            color: '#0052cc',
                            bold: true
                        },
                        definitionValue: {
                            margin: [6, 0, 0, 2],
                            fontSize: 9
                        },

                        left: {
                            alignment: 'left'
                        }
                    }
                };
            } else if (move === 'endorsed' || move === 'deposited' || move === 'given-guarantee' || move === 'refunded' || move === 'in-bill-case') {
                if (!paymentAccountId) {
                    throw new app.errors.Unprocessable(app.translate('The payment account must be selected!'));
                }
                if (_.uniq(items.map(item => item.status)).length > 1) {
                    throw new app.errors.Unprocessable(
                        app.translate('All items must have the same destination status!')
                    );
                }

                code = await this.app.rpc('kernel.common.request-number', {
                    numberingId: numbering._id,
                    save: true
                });

                const companyData = {
                    stack: [
                        {
                            text: company.legalName ? company.legalName : company.name,
                            fontSize: 9,
                            bold: true,
                            margin: [0, 0, 0, 4]
                        },
                        {
                            text: company.address.address,
                            fontSize: 8,
                            margin: [0, 0, 0, 10]
                        },
                        {
                            columns: [
                                {
                                    text: t('TD:'),
                                    fontSize: 8,
                                    bold: true,
                                    width: 'auto',
                                    margin: [0, 0, 3, 0]
                                },
                                {
                                    text: company.taxDepartment,
                                    fontSize: 8,
                                    margin: [0, 0, 10, 0]
                                },
                                {
                                    text: t('TN:'),
                                    fontSize: 8,
                                    bold: true,
                                    width: 'auto',
                                    margin: [0, 0, 3, 0]
                                },
                                {
                                    text: company.tin,
                                    fontSize: 8
                                },
                                {}
                            ]
                        }
                    ],
                    width: 210
                };

                let partnerData = null;
                const bankAccount = await app.collection('accounting.bank-accounts').findOne({
                    journalId: paymentAccountId,
                    $select: ['bankId', 'bankBranchId'],
                    $disableActiveCheck: true,
                    $disableSoftDelete: true
                });
                if (!!bankAccount) {
                    const bank = await app.collection('kernel.banks').findOne({
                        _id: bankAccount.bankId,
                        $select: ['name'],
                        $disableActiveCheck: true,
                        $disableSoftDelete: true
                    });
                    const bankBranch = await app.collection('kernel.bank-branches').findOne({
                        _id: bankAccount.bankBranchId,
                        $select: ['code', 'name', 'address'],
                        $disableActiveCheck: true,
                        $disableSoftDelete: true
                    });

                    partnerData = {
                        stack: [
                            {
                                text: bank.name,
                                fontSize: 9,
                                bold: true,
                                margin: [0, 0, 0, 4]
                            },
                            {
                                text: `${bankBranch.code}/${bankBranch.name}`,
                                fontSize: 8,
                                margin: [0, 0, 0, 4]
                            },
                            {
                                text: bankBranch.address.address,
                                fontSize: 8,
                                margin: [0, 0, 0, 10]
                            }
                        ],
                        width: 210
                    };
                } else {
                    const paymentJournal = await app.collection('accounting.journals').findOne({
                        _id: paymentAccountId,
                        $select: ['name'],
                        $disableActiveCheck: true,
                        $disableSoftDelete: true
                    });

                    partnerData = {
                        stack: [
                            {
                                text: paymentJournal.name,
                                fontSize: 9,
                                bold: true,
                                margin: [0, 0, 0, 10]
                            }
                        ],
                        width: 210
                    };
                }

                const partner = await app.collection('kernel.partners').findOne({
                    _id: collectorId,
                    $select: ['code', 'name', 'identity'],
                    $disableActiveCheck: true,
                    $disableSoftDelete: true
                });
                if (!partner) {
                    throw new app.errors.Unprocessable(app.translate('Collector not found!'));
                }
                partnerData.stack.push(
                    ...[
                        {
                            text: partner.name,
                            fontSize: 9,
                            bold: true,
                            margin: [0, 10, 0, 4]
                        },
                        {
                            text: partner.code,
                            margin: [0, 0, 0, 4],
                            fontSize: 8
                        }
                    ]
                );

                const generalTable = {
                    table: {
                        headerRows: 0,
                        widths: ['auto', 'auto'],
                        body: []
                    },
                    layout: {},
                    width: 210
                };
                generalTable.table.body.push([
                    {
                        text: `${t('Document no')}:`,
                        style: 'definitionLabel',
                        border: [false, false, false, false],
                        noWrap: true
                    },
                    {
                        text: code,
                        style: 'definitionValue',
                        border: [false, false, false, false],
                        noWrap: false
                    }
                ]);
                generalTable.table.body.push([
                    {
                        text: `${t('Date of issue')}:`,
                        style: 'definitionLabel',
                        border: [false, false, false, false],
                        noWrap: true
                    },
                    {
                        text: format(date, 'date'),
                        style: 'definitionValue',
                        border: [false, false, false, false],
                        noWrap: false
                    }
                ]);

                const detailsTable = {
                    table: {
                        headerRows: 0,
                        widths: ['auto', 'auto'],
                        body: []
                    },
                    layout: {},
                    width: 210
                };
                detailsTable.table.body.push([
                    {
                        text: `${t('Due day difference')}:`,
                        style: 'definitionLabel',
                        border: [false, false, false, false],
                        noWrap: true
                    },
                    {
                        text: avgDueDayDifference,
                        style: 'definitionValue',
                        border: [false, false, false, false],
                        noWrap: false
                    }
                ]);
                detailsTable.table.body.push([
                    {
                        text: `${t('Avg. due date')}:`,
                        style: 'definitionLabel',
                        border: [false, false, false, false],
                        noWrap: true
                    },
                    {
                        text: format(avgDueDate, 'date'),
                        style: 'definitionValue',
                        border: [false, false, false, false],
                        noWrap: false
                    }
                ]);

                const itemsTable = {
                    table: {
                        headerRows: 1,
                        dontBreakRows: true,
                        widths: ['auto', 'auto', '*', '*', 'auto', 'auto', 'auto', 'auto', 'auto', 'auto', 'auto'],
                        body: []
                    },
                    layout: {
                        hLineWidth(i, node) {
                            return i === 1 ? 1 : 0.5;
                        },
                        vLineWidth(i, node) {
                            return 0.5;
                        },
                        hLineColor(i, node) {
                            return '#bdc3c7';
                        },
                        vLineColor(i, node) {
                            return '#bdc3c7';
                        },
                        fillColor(rowIndex, node, columnIndex) {
                            return rowIndex % 2 === 1 ? '#fcfcfc' : null;
                        }
                    },
                    width: 782,
                    marginBottom: 30
                };
                itemsTable.table.body.push([
                    {
                        text: t('PN no'),
                        style: 'itemsHeader',
                        border: [false, false, false, true],
                        noWrap: false
                    },
                    {
                        text: t('Due date'),
                        style: 'itemsHeader',
                        border: [false, false, false, true],
                        noWrap: false
                    },
                    {
                        text: t('Partner'),
                        style: 'itemsHeader',
                        border: [false, false, false, true],
                        noWrap: false
                    },
                    {
                        text: t('First written by'),
                        style: 'itemsHeader',
                        border: [false, false, false, true],
                        noWrap: false
                    },
                    {
                        text: t('PN bank'),
                        style: 'itemsHeader',
                        border: [false, false, false, true],
                        noWrap: false
                    },
                    {
                        text: t('Bank branch'),
                        style: 'itemsHeader',
                        border: [false, false, false, true],
                        noWrap: false
                    },
                    {
                        text: t('Serial no'),
                        style: 'itemsHeader',
                        border: [false, false, false, true],
                        noWrap: true
                    },
                    {
                        text: t('CRN'),
                        style: 'itemsHeader',
                        border: [false, false, false, true],
                        noWrap: true
                    },
                    {
                        text: t('PN amount'),
                        style: 'itemsHeader',
                        border: [false, false, false, true],
                        noWrap: true
                    },
                    {
                        text: t('Currency amount'),
                        style: 'itemsHeader',
                        border: [false, false, false, true],
                        noWrap: true
                    },
                    {
                        text: t('Exchange rate'),
                        style: 'itemsHeader',
                        border: [false, false, false, true],
                        noWrap: true
                    }
                ]);
                for (let item of items || []) {
                    const currency = await app.collection('kernel.currencies').findOne({
                        _id: item.currencyId,
                        $disableActiveCheck: true,
                        $disableSoftDelete: true
                    });

                    const promissoryNote = await app.collection('finance.promissory-notes').findOne({
                        _id: item._id,
                        $select: ['bankId', 'bankBranchName', 'firstWrittenBy'],
                        $disableActiveCheck: true,
                        $disableSoftDelete: true
                    });
                    item = {
                        ...item,
                        ...promissoryNote
                    };

                    let partnerName = '';
                    if (!!item.partnerId) {
                        const partner = await app.collection('kernel.partners').findOne({
                            _id: item.partnerId,
                            $select: ['name'],
                            $disableActiveCheck: true,
                            $disableSoftDelete: true
                        });
                        partnerName = partner.name;
                    }
                    const bankBranchName = item.bankBranchName || '';
                    let bankName = null;
                    if (!!item.bankId) {
                        const bank = await app.collection('kernel.banks').findOne({
                            _id: item.bankId,
                            $select: ['name'],
                            $disableActiveCheck: true,
                            $disableSoftDelete: true
                        });
                        bankName = bank.name;
                    }

                    itemsTable.table.body.push([
                        {text: item.code, style: 'itemText', noWrap: true},
                        {text: format(item.dueDate, 'date'), style: 'itemText', noWrap: true},
                        {text: partnerName, style: 'itemText'},
                        {text: item.firstWrittenBy || '', style: 'itemText'},
                        {text: bankName, style: 'itemText'},
                        {text: bankBranchName, style: 'itemText'},
                        {text: item.promissoryNoteNumber, style: 'itemText', noWrap: true},
                        {text: currency.name, style: 'itemText', noWrap: true},
                        {text: format(item.amount || 0, 'decimal'), style: 'itemNumber', noWrap: true},
                        {text: format(item.systemCurrencyAmount || 0, 'decimal'), style: 'itemNumber', noWrap: true},
                        {text: format(item.currencyRate || 1, 'decimal'), style: 'itemNumber', noWrap: true}
                    ]);
                }
                itemsTable.table.body.push([
                    {
                        text: '',
                        style: 'itemText',
                        border: [false, false, false, false],
                        noWrap: false
                    },
                    {
                        text: '',
                        style: 'itemText',
                        border: [false, false, false, false],
                        noWrap: false
                    },
                    {
                        text: '',
                        style: 'itemText',
                        border: [false, false, false, false],
                        noWrap: false
                    },
                    {
                        text: '',
                        style: 'itemText',
                        border: [false, false, false, false],
                        noWrap: false
                    },
                    {
                        text: '',
                        style: 'itemText',
                        border: [false, false, false, false],
                        noWrap: true
                    },
                    {
                        text: '',
                        style: 'itemText',
                        border: [false, false, false, false],
                        noWrap: true
                    },
                    {
                        text: t('Total'),
                        style: 'itemTotal',
                        border: [false, false, false, false],
                        noWrap: false
                    },
                    {
                        text: '',
                        style: 'itemText',
                        border: [false, false, false, false],
                        noWrap: true
                    },
                    {
                        text: format(_.sumBy(items, 'amount'), 'decimal'),
                        style: 'itemTotal',
                        border: [false, false, false, false],
                        noWrap: true
                    },
                    {
                        text: format(_.sumBy(items, 'systemCurrencyAmount'), 'decimal'),
                        style: 'itemTotal',
                        border: [false, false, false, false],
                        noWrap: true
                    },
                    {
                        text: '',
                        style: 'itemText',
                        border: [false, false, false, false],
                        noWrap: true
                    }
                ]);

                const currency = await app.collection('kernel.currencies').findOne({
                    _id: items[0].currencyId,
                    $disableActiveCheck: true,
                    $disableSoftDelete: true
                });
                const total = _.sumBy(items, 'amount');
                let totalWritten = '';
                if (app.config('app.locale') === 'tr' && currency.name === 'TL') {
                    if (Math.round(total) !== total) {
                        const parts = total.toFixed(2).split('.');

                        totalWritten = `YALNIZ: ${toUpper(writtenNumber(parseInt(parts[0]), {lang: 'tr'})).replace(
                            /\s/g,
                            ''
                        )} TL ${toUpper(writtenNumber(parseInt(parts[1]), {lang: 'tr'})).replace(/\s/g, '')} Kr.`;
                    } else {
                        totalWritten = `YALNIZ: ${toUpper(writtenNumber(total, {lang: 'tr'})).replace(/\s/g, '')} TL`;
                    }
                } else {
                    totalWritten = toUpper(writtenNumber(total, {lang: app.config('app.locale')}));
                }
                const noteData = {
                    stack: [
                        {
                            text: t('Note'),
                            fontSize: 9,
                            bold: true,
                            color: '#0052cc',
                            margin: [0, 0, 0, 4]
                        },
                        {
                            text: totalWritten,
                            noWrap: true,
                            fontSize: 8
                        },
                        {
                            text: '',
                            fontSize: 6
                        }
                    ],
                    margin: [0, 0, 20, 0]
                };

                const delivererData = {
                    stack: [
                        {
                            text: t('Deliverer'),
                            fontSize: 9,
                            bold: true,
                            color: '#0052cc',
                            margin: [0, 0, 0, 4]
                        },
                        {
                            text: '',
                            fontSize: 8
                        }
                    ],
                    margin: [0, 0, 20, 0]
                };
                const recipientData = {
                    stack: [
                        {
                            text: t('Recipient'),
                            fontSize: 9,
                            bold: true,
                            color: '#0052cc',
                            margin: [0, 0, 0, 4]
                        },
                        {
                            text: '',
                            fontSize: 8
                        }
                    ],
                    margin: [0, 0, 20, 0]
                };

                let attachmentData = {};
                if (!!printAttachedImages) {
                    const documents = await app.collection('finance.promissory-notes').find({
                        _id: {$in: items.map(item => item._id)},
                        $select: ['attachments']
                    });
                    let fileIds = [];
                    for (const item of documents) {
                        fileIds.push(...(item.attachments || []));
                    }
                    fileIds = _.uniq(fileIds);

                    if (fileIds.length > 0) {
                        const files = await app.files.find({_id: {$in: fileIds}});
                        const images = [];

                        for (const file of files) {
                            const imageType = file.contentType;

                            if (
                                !(imageType === 'image/png' || imageType === 'image/jpg' || imageType === 'image/jpeg')
                            ) {
                                continue;
                            }

                            try {
                                let image = await fs.readFile(file.path);
                                image = (await sharp(image).toBuffer()).toString('base64');
                                image = `data:${imageType};base64,${image}`;

                                images.push({
                                    image,
                                    width: 375
                                });
                            } catch (e) {}
                        }

                        attachmentData = {
                            stack: []
                        };

                        for (const chunk of _.chunk(images, 2)) {
                            if (chunk.length > 1) {
                                attachmentData.stack.push({
                                    columns: [chunk[0], {}, chunk[1]],
                                    marginBottom: 30
                                });
                            } else {
                                attachmentData.stack.push({
                                    columns: [chunk[0], {}],
                                    marginBottom: 30
                                });
                            }
                        }

                        if (images.length < 1) {
                            attachmentData = null;
                        }
                    }
                }

                payload.title = t('Promissory Note Issued Receipt');
                payload.saveOutput = true;
                payload.landscape = true;
                payload.definition = {
                    content: [
                        {columns: [companyData, {}, partnerData], marginBottom: 30},
                        {columns: [generalTable, {}, detailsTable], marginBottom: 30},
                        itemsTable,
                        {
                            columns: [noteData, {}, delivererData, {}, recipientData],
                            marginBottom: !!attachmentData ? 30 : 0
                        },
                        ...(!!attachmentData ? [attachmentData] : [])
                    ],
                    styles: {
                        // Items Header
                        itemsHeader: {
                            margin: 1,
                            fontSize: 7,
                            bold: true
                        },

                        // Item.
                        itemNumber: {
                            fontSize: 6,
                            margin: 1,
                            alignment: 'right'
                        },
                        itemText: {
                            fontSize: 6,
                            margin: 1
                        },
                        itemTotal: {
                            fontSize: 7,
                            margin: 1,
                            bold: true,
                            alignment: 'right'
                        },

                        definitionLabel: {
                            margin: [0, 0, 0, 2],
                            fontSize: 9,
                            color: '#0052cc',
                            bold: true
                        },
                        definitionValue: {
                            margin: [6, 0, 0, 2],
                            fontSize: 9
                        },

                        left: {
                            alignment: 'left'
                        }
                    }
                };
            } else {
                throw new app.errors.Unprocessable(app.translate('No print format found for selected target status!'));
            }

            const result = await this.app.print({...payload, saveOutput: true});

            await app
                .collection('finance.promissory-notes')
                .patch(
                    {_id: {$in: items.map(item => item._id)}},
                    {paymentSlipNo: code, paymentSlipDate: app.datetime.local().toJSDate()},
                    {user: params.user, skipEvents: true}
                );

            const totalPromissoryAmount = items.reduce((acc, item) => acc + item.systemCurrencyAmount, 0);

             const getPartnerInfo = (items, partnerType = 'partner') => {
                const item = items.find(item => item[partnerType] && item[partnerType]._id);
                const partner = item?.[partnerType];

                return {
                    id: partner?._id || '',
                    name: partner?.name || '',
                    code: partner?.code || ''
                };
            };

            const partner = getPartnerInfo(items, 'partner');

            let endorsedPartner = { id: '', name: '', code: '' };

            if (move === 'endorsed' && partnerId) {
                const endorsedPartnerData = await app.collection('kernel.partners').findOne({
                    _id: partnerId,
                    $select: ['_id', 'name', 'code'],
                    $disableActiveCheck: true,
                    $disableSoftDelete: true
                });

                if (endorsedPartnerData) {
                    endorsedPartner = {
                        id: endorsedPartnerData._id,
                        name: endorsedPartnerData.name,
                        code: endorsedPartnerData.code || ''
                    };
                }
            }

            await app.collection('finance.promissory-note-outputs').create({
                journalId: journal._id,
                userId: params.user._id,
                userCode: params.user.code,
                userName: params.user.name,
                partnerId: partner.id,
                partnerName: partner.name,
                partnerCode: partner.code,
                endorsedPartnerId: endorsedPartner.id,
                endorsedPartnerCode: endorsedPartner.code,
                endorsedPartnerName: endorsedPartner.name,
                paymentSlipNo: code,
                fileId: result.fileId,
                date: app.datetime.local().toJSDate(),
                totalPromissoryAmount: app.round(totalPromissoryAmount, 'total')
            });

            return result;
        } catch (error) {
            console.error(error);

            throw error;
        }
    }
};
