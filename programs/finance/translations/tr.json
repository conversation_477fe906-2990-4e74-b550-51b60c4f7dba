{"Aged Payable": "Yaşlandırılmış Borç", "Aged Receivable": "Yaşlandırılmış Alacak", "Allowed Payment Types": "İzin Verilen Ödeme Türleri", "Amount (SC)": "Tutar (SPB)", "Analytic Accounts": "<PERSON><PERSON><PERSON>", "Analytic Records": "<PERSON><PERSON><PERSON>", "Analytic Tags": "Analitik Etiketleri", "Applied total": "Uygulanan toplam", "Approve Payment": "<PERSON><PERSON><PERSON><PERSON>", "Auto plan": "Otomatik plan", "Average due difference": "Ortalama vade farkı", "Avg. due date": "Ort. vade tarihi", "Avg. due day difference": "Ort. vade gün farkı", "Banking": "Bankacılık", "Base Total": "<PERSON><PERSON>", "Base total": "<PERSON>mel toplam", "Budget": "Bütçe", "Budget Statuses": "Bütçe Durumları", "Budgets": "Bütçeler", "CASH": "NAKİT", "CHEQUE": "ÇEK", "CREDIT CARD": "KREDİ KARTI", "Cash Flow": "Nakit Akışı", "Cash discount": "<PERSON><PERSON><PERSON> indirimi", "Changing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Cheque Account": "Çek Hesabı", "Cheque No": "Çek Sayısı", "Cheque Number": "Çek Numarası", "Cheque Number To Endorse": "Ciro Edilecek Çek Numarası", "Cheque account": "Çek hesabı", "Commission Rates": "Komisyon Oranları", "Commission distribution": "Komisyon dağılımı", "Credit Card Payment": "Kredi Kartı Ödemesi", "Credit Leasing": "<PERSON><PERSON><PERSON>", "Customer Invoices": "Müşteri Faturaları", "Customer Invoice": "Müşteri Faturası", "Distribution Rates": "Dağılım Oranları", "Do you want save this credit card to the partner?": "Bu kredi kartını iş ortağına kaydetmek istiyormusunuz?", "Document Type": "Belge Türü", "End (Day)": "Bitiş (Gün)", "Endorsable": "Ciro edilebilir", "Endorse": "Ciro <PERSON>", "Executive Summary": "Yönetici Özeti", "Finance": "Finans", "Financial Identifier": "Mali Tanıtıcı", "First Written By": "İlk Düzenleyen", "First installment": "İlk taksit", "Guarantee": "<PERSON><PERSON><PERSON><PERSON>", "Cancel Guarantee": "Teminâtı İptal Et", "New Guarantee": "<PERSON><PERSON>", "Guarantees": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Cancel Transfer": "Transferi İptal Et", "Guarantor 1": "Kefil 1", "Guarantor 2": "Kefil 2", "Installment": "Taks<PERSON>", "Installment Amount": "<PERSON><PERSON><PERSON>", "Installment Count": "<PERSON><PERSON><PERSON>", "Installment Information": "<PERSON>ks<PERSON>", "Installment Limits": "<PERSON>ks<PERSON>", "Installment No": "Taksit No", "Installment amount": "Taksit tutarı", "Installment count": "<PERSON>ks<PERSON>", "Installment no": "Taksit no", "Installment type": "Taksitlendirme türü", "Installments": "<PERSON><PERSON><PERSON><PERSON>", "Interest Information": "<PERSON><PERSON><PERSON>", "Interest Rates": "Faiz <PERSON>ları", "Interest calculation": "<PERSON><PERSON>z <PERSON>", "Interest period": "<PERSON>aiz periyodu", "Interest rate": "Faiz oranı", "Interval month + day": "Taksit aralığı ay + gün", "Is POS enabled": "<PERSON><PERSON>", "Is cash enabled": "<PERSON><PERSON><PERSON> etkin", "Is cheque enabled": "<PERSON><PERSON>", "Is credit card enabled": "Kredi kartı etkin", "Is money transfer enabled": "<PERSON><PERSON>", "Is promissory note enabled": "<PERSON><PERSON>", "Journal credit account is required!": "Yevmiye alacak hesabı zorunludur!", "Journal debit account is required!": "Yev<PERSON>ye borç hesabı zorunludur!", "Last installment": "<PERSON> taksit", "Lower Limit": "Alt Limit", "Lower limit": "Alt limit", "MONEY TRANSFER": "HAVALE", "Due Difference": "Vade Farkı", "Due date based on": "<PERSON><PERSON><PERSON> temel alan vade tarihi", "Due day difference": "Vade gün farkı", "Due difference": "Vade farkı", "Due type": "<PERSON><PERSON> tü<PERSON>", "Money Transfer": "Havale", "Money transfer": "Havale", "New Payment Term": "<PERSON>ni Ödeme Ko<PERSON>", "Overdue Days": "<PERSON><PERSON><PERSON>", "POS Payment": "POS Ödemesi", "PROMISSORY NOTE": "SENET", "Partner Bank": "İş Ortagı Bankası", "Partner Type": "İş Ortağı Türü", "Partner accounting account is required!": "İş ortağı muhasebe hesabı zorunludur!", "Partner credit card": "İş ortağı kredi kartı", "Partner type": "İş ortağı türü", "Payable": "<PERSON><PERSON><PERSON>", "Payment": "Ödeme", "Payment Account": "<PERSON><PERSON><PERSON>", "Payment Amount": "Ödenecek Tutar", "Payment Follow-up": "<PERSON><PERSON><PERSON>", "Payment Plan": "Ödeme Planı", "Payment Terms": "Ödeme <PERSON>ı", "Payment amount": "Ödenecek tutar", "Payment is already approved!": "Onaylanan ödemeler yeniden onaylanamaz!", "Payment term": "Ödeme koşulu", "Payments": "Ö<PERSON>mel<PERSON>", "Product Margins": "<PERSON><PERSON><PERSON><PERSON>", "Promissory Note Account": "<PERSON><PERSON>", "Promissory Note Number": "<PERSON><PERSON>", "Promissory Note Number To Endorse": "Ciro Edilecek Senet Numarası", "Promissory note account": "<PERSON><PERSON>", "Provision": "Provizyon", "Rate (%)": "Oran (%)", "Receipt Follow-up": "Alacak <PERSON>", "Receipts": "Tahsilatlar", "Receivable": "Alacak", "Start (Day)": "Başlangıç (Gün)", "Tax distribution": "<PERSON><PERSON>gi <PERSON>ı<PERSON>ı", "The maximum number of installments allowed for {{section}} was exceeded!": "{{section}} i<PERSON><PERSON> izin verilen maksimum taksit sayısı aşıldı.", "The open amount must be equal to zero!": "Açık tutar sıfıra eşit olmalıdır!", "The {{section}} total is not within the specified installment limit range!": "{{section}} toplamı belirlenen taksit limiti aralığında değil.", "Tolerance days": "Tolerans günleri", "Tolerance month + day": "Tolerans ay + gün", "Total (SC)": "Toplam (SPB)", "Total Paid": "Toplam Ödenen", "Total Remaining": "<PERSON>lam Kalan", "Transaction Date": "İşlem Tarihi", "Transaction date": "İşlem tarihi", "Update Plan": "Planı Güncelle", "Upper Limit": "Üst Limit", "Upper limit": "Üst limit", "Vendor Invoices": "Tedarikçi Faturaları", "You cannot plan more than the specified payment amount!": "Belirtilen ödeme tutarından daha fazlasını planlayamazsınız!", "Output account": "Çıkış hesabı", "Output Account": "Çıkış Hesabı", "Input account": "<PERSON><PERSON><PERSON>", "Input Account": "<PERSON><PERSON><PERSON>", "EFT": "EFT", "Withdrawal": "Para çekme", "Cash deposit": "Para yatırma", "Fee": "Ücret", "Outgo": "<PERSON><PERSON><PERSON><PERSON>", "Transaction type": "İşlem türü", "Transaction Type": "İşlem Türü", "Transfer is already approved!": "Onaylanan transferler yeniden onaylanamaz!", "Output account and input account must be different!": "G<PERSON>ş hesabı ve çıkış hesabı farklı olmalıdır!", "Bank Statement": "<PERSON><PERSON><PERSON>", "Cash Statement": "<PERSON><PERSON><PERSON>", "Last Statement": "<PERSON>", "Balance (SC)": "Bakiye (SPB)", "BANK STATEMENTS": "HESAP EKSTRELERİ", "CASH STATEMENTS": "NAKİT EKSTRELERİ", "ACCOUNT ACTIVITIES": "HESAP HAREKETLERİ", "Opening Balance": "Açılış Bakiyesi", "Closing Balance": "Kapanış Bakiyesi", "Opening balance": "Açılış bakiyesi", "Closing balance": "Kapanış bakiyesi", "Total (FC)": "Toplam (YPB)", "Document type": "Belge türü", "Overdue days": "<PERSON><PERSON><PERSON> ge<PERSON><PERSON> gün", "Bank statement": "<PERSON><PERSON><PERSON>", "Cash statement": "<PERSON><PERSON><PERSON>", "Filter matches..": "Eşleşenleri filtrele..", "Choose a counterpart or create a write-off.": "Bir karşılık seçin veya yevmiye kaydı oluşturun.", "Unable to find records that can be match!": "Eşleştirilebilecek kayıt bulunamadı!", "Reconciliation model": "Uzlaştırma modeli", "You cannot delete reconciled bank statement rows!": "Uzlaştırılmış hesap ekstresi satırlarını silemezsiniz!", "You cannot delete reconciled cash statement rows!": "Uzlaştırılmış nakit ekstresi satırlarını silemezsiniz!", "New Bank Statement": "<PERSON><PERSON>", "New Cash Statement": "<PERSON><PERSON> Na<PERSON>", "LAST STATEMENT": "SON EKSTRE", "TO RECONCILE": "UZLAŞTIRILACAK", "Reconcile": "Uzlaştır", "You cannot delete approved statements!": "Onaylanan ekstreleri silemezsiniz!", "IN BILL CASE": "PORTFÖYDE", "DEPOSITED": "TAHSİLE VERİLDİ", "CHARGED": "TAHSİL EDİLDİ", "ENDORSED": "CİRO EDİLDİ", "REFUNDED": "İADE EDİLDİ", "SENT IN THE BAILIFFS": "İCRAYA VERİLDİ", "GIVEN GUARANTEE": "TEMİNATA VERİLDİ", "In bill case": "Portföyde", "Deposited": "Tahsile verildi", "Endorsed": "<PERSON><PERSON> edildi", "Charged": "Tahsil edildi", "Refunded": "<PERSON>ade edildi", "Sent in the bailiffs": "<PERSON><PERSON><PERSON><PERSON> ve<PERSON>", "Given guarantee": "<PERSON><PERSON><PERSON> verildi", "Cheque number": "Çek numarası", "Payment account": "<PERSON><PERSON><PERSON>", "First written by": "İlk düzenleyen", "Financial identifier": "Mali tanıtıcı", "Destination Status": "<PERSON><PERSON><PERSON>", "New Cheque": "<PERSON><PERSON>", "Related accounting records will be created and posted. Do you want continue?": "İlgili muhasebe kayıtları oluşturulacak ve yayınlanacaktır. <PERSON><PERSON> etmek istiyor musunuz?", "Send Cheques": "Çekleri Gönder", "The destination status must be defined on all cheques that will be moved!": "Taşınacak tüm çeklerde hedef durum tanımlanmalıdır!", "Unknown destination status!": "Bilinmeyen hedef durumu!", "Payment account is required!": "Ödeme hesabı zorunludur!", "Payment journal cheque collection account is required!": "Ödeme yevmiyesinde çek tahsil hesabı zorunludur!", "Partner is required!": "İş ortağı zorunludur!", "Payment journal debit account is required!": "Ödeme yevmiyesinde borç hesabı zorunludur!", "Journal denied cheque account is required!": "Yev<PERSON>yede reddedilen çek hesabı zorunludur!", "Journal cheque execution account is required!": "Yevmiyede çek icra hesabı zorunludur!", "Journal cheque guarantee account is required!": "Yevmiyede çek teminat hesabı zorunludur!", "Produced": "Üretildi", "PRODUCED": "ÜRETİLDİ", "Paid": "Ödendi", "PAID": "ÖDENDİ", "Payment journal credit account is required!": "Ödeme yevmiyesinde alacak hesabı zorunludur!", "Cheques deposited": "Çekler tahsile verildi", "Cheques endorsed": "Ç<PERSON><PERSON> ciro edildi", "Cheques charged": "Çekler tahsil edildi", "Cheques denied": "<PERSON><PERSON><PERSON>  reddedildi", "Cheques refunded": "<PERSON><PERSON><PERSON>  iade edildi", "Cheques sent in the bailiffs": "<PERSON><PERSON><PERSON> ic<PERSON>a verildi", "Cheques given guarantee": "Çekler teminata verildi", "Cheques paid": "Çekler ödendi", "The destination account must be defined on all cheques that will be transferred!": "Transfer edilecek tüm çeklerde hedef hesap tanı<PERSON>lanmalıdır!", "Destination journal debit account is required!": "<PERSON><PERSON><PERSON> ye<PERSON><PERSON><PERSON><PERSON> borç hesabı zorunludur!", "Promissory note number": "<PERSON><PERSON>", "New Promissory Note": "<PERSON><PERSON>", "Send Promissory Notes": "Senetleri Gönder", "Payment journal promissory note collection account is required!": "Ödeme yevmiyesinde senet tahsil hesabı zorunludur!", "Journal protested promissory note account is required!": "<PERSON><PERSON><PERSON><PERSON><PERSON> protesto edilen senet hesabı zorunludur!", "Journal promissory note execution account is required!": "<PERSON><PERSON><PERSON><PERSON><PERSON> senet icra hesabı zorunludur!", "Journal promissory note guarantee account is required!": "<PERSON><PERSON><PERSON><PERSON><PERSON> senet teminat hesabı zorunludur!", "Promissory notes deposited": "Senetler tahsile verildi", "Promissory notes endorsed": "<PERSON><PERSON><PERSON> ciro edildi", "Promissory notes charged": "Senetler tahsil edildi", "Promissory notes protested": "Senetler  protesto edildi", "Promissory notes refunded": "<PERSON><PERSON><PERSON>  iade edildi", "Promissory notes sent in the bailiffs": "<PERSON><PERSON><PERSON> ic<PERSON>a verildi", "Promissory notes given guarantee": "<PERSON><PERSON><PERSON> teminata verildi", "Promissory notes paid": "<PERSON><PERSON><PERSON> ödendi", "The destination account must be defined on all promissory notes that will be transferred!": "Transfer edilecek tüm senetlerde hedef hesap tanı<PERSON>ıdır!", "Partner bank": "İş ortağı bankası", "PROTESTED": "PROTESTO EDİLDİ", "Protested": "Protesto edildi", "This partner accepts endorsed cheques": "Bu iş ortağı ciro edilen çekleri kabul eder", "This partner cheques can be endorsed": "Bu iş ortağının çekleri ciro edilebilir", "This partner accepts endorsed promissory notes": "Bu iş ortağı ciro edilen senetleri kabul eder", "This partner promissory notes can be endorsed": "Bu iş ortağının senetleri ciro edilebilir", "This partner cheques cannot be endorsed! Please update the details of relevant partner.": "Bu iş ortağının çekleri ciro edilemez! Lütfen ilgili iş ortağının bilgilerini güncelleyiniz.", "This partner promissory notes cannot be endorsed! Please update the details of relevant partner.": "Bu iş ortağının senetleri ciro edilemez! Lütfen ilgili iş ortağının bilgilerini güncelleyiniz.", "This partner does not accept endorsed cheques! Please update the details of relevant partner.": "Bu iş ortağı ciro edilen çekleri kabul etmiyor! Lütfen ilgili iş ortağının bilgilerini güncelleyiniz.", "This partner does not accept endorsed promissory notes! Please update the details of relevant partner.": "Bu iş ortağı ciro edilen senetleri kabul etmiyor! Lütfen ilgili iş ortağının bilgilerini güncelleyiniz.", "TO APPROVE": "ONAYLANACAK", "Cheque to endorse is required!": "Ciro edilecek çek zorunludur!", "Promissory note to endorse is required!": "Ciro edilecek senet z<PERSON>ludu<PERSON>!", "Max installment count": "Maximum taksit sayısı", "Total after due": "<PERSON><PERSON><PERSON> son<PERSON>i <PERSON>", "Lock installment due date": "Taksitlerde vade tarihini kilitle", "Discount cannot be lower than zero!": "İndirim sıfırdan küçük olamaz!", "Promissory note number is required!": "Senet numarası zorunludur!", "Cheque number is required!": "Çek numarası zorunludur!", "Total Balance": "Toplam Bakiye", "Canceled Balance": "İptal Bakiyesi", "Period Balance": "Dönem Bakiyesi", "PERIOD TRANSACTIONS": "DÖNEM İŞLEMLERİ", "COMMISSION PAYMENTS": "KOMİSYON ÖDEMELERİ", "CANCELED TRANSACTIONS": "İPTAL EDİLENLER", "Charge": "Tahsil Et", "POS account collection": "POS hesabı tahsilatı", "POS canceled transactions": "POS iptal işlemleri", "Commission model": "Komisyon modeli", "Commission amount": "Komisyon tutarı", "Slips cannot be collected for different branch offices!": "Farklı şubeler içiren sliplerin tahsilatı yapılamaz!", "Slips cannot be canceled for different branch offices!": "Farklı şubeler içiren sliplerin iptali yapılamaz!", "Slips cannot be canceled for different partners!": "Farklı iş ortakları içiren sliplerin iptali yapılamaz!", "Slips cannot be canceled for different transactions!": "Farklı işlemlerde işlenen sliplerin iptali yapılamaz!", "All the slips that are belong to current transaction must have open status!": "İşleme ait tüm sliplerin durumunun açık olması gerekir!", "PERIOD BALANCE": "DÖNEM BAKİYESİ", "CANCELED BALANCE": "İPTAL BAKİYESİ", "Available Limit": "Kullanılabilir Limit", "Available limit": "Kullanılabilir limit", "Total Debt": "Toplam Borç", "Total debt": "<PERSON>lam borç", "Total debt (SC)": "<PERSON>lam borç (SPB)", "Period Debt": "<PERSON><PERSON>ne<PERSON> Bo<PERSON>u", "Debt Payment": "<PERSON><PERSON><PERSON>", "New Credit Card Payment": "Yeni Kredi Kartı Ödemesi", "New POS Statement": "Yeni POS Tahsilatı", "Make Payment": "<PERSON><PERSON><PERSON>", "Credit card debt payment": "Kredi kartı borç ödemesi", "Pay total amount": "Toplam tutarı öde", "Pay minimum amount": "Asgari tutarı öde", "Period debt": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>", "Add Expense": "<PERSON><PERSON>", "Expense model": "Gider modeli", "Credit card expenses": "Kredi kartı giderleri", "Expense model counterpart account is required!": "Gider modeli karşıt hesabı zorunludur!", "Credit card canceled transactions": "Kredi kartı iptal işlemleri", "You cannot cancel the previously canceled slips again!": "Daha önce iptal edilmiş slipleri tekrar iptal edemezsiniz!", "TOTAL DEBT": "TOPLAM BORÇ", "PERIOD DEBT": "DÖNEM BORCU", "AVAILABLE LIMIT": "KULLANILABİLİR LİMİT", "Collector": "Tahsil sorumlusu", "Clear Payment Plan": "Ödeme Planını Temizle", "Average Due Date": "<PERSON><PERSON><PERSON>", "Total After Due": "<PERSON><PERSON><PERSON>", "Installment interval": "Taksit <PERSON>", "CALCULATE INSTALLMENTS": "TAKSİTLERİ HESAPLA", "Manuel Payment Plan": "<PERSON>", "Cash amount": "Nakit tutarı", "Cash installment count": "Nakit taksit say<PERSON>ı", "Money transfer amount": "Havale tutarı", "Money transfer installment count": "Havale taksit sayı<PERSON>ı", "Cheque amount": "Çek tutarı", "Cheque installment count": "Çek taksit sayı<PERSON>ı", "Promissory note amount": "<PERSON><PERSON> tutarı", "Promissory note installment count": "<PERSON>et taksit sayı<PERSON>ı", "Pos amount": "POS tutarı", "Pos installment count": "POS taksit sayısı", "Total before due": "<PERSON><PERSON><PERSON>", "Cash installments": "Na<PERSON>t ta<PERSON>i", "Money transfer installments": "Havale taksitleri", "Cheque installments": "Çek taksitleri", "Promissory note installments": "<PERSON><PERSON>", "Installment(s)": "Taks<PERSON>", "Rounding difference application": "Yuvarlama farkı uygulaması", "This payment term cannot be disabled!": "Bu ödeme koşulu devre dışı bırakılamaz!", "Automatic Payment Plan": "Otomatik Ödeme Planı", "Calculate Scopes": "Kapsamları Hesapla", "Automatic scope calculation is applied.": "Otomatik kapsam hesaplaması uygulandı.", "Finance employee": "Finans çalışanı", "New Payment To Account": "<PERSON><PERSON>", "Payments To Account": "<PERSON><PERSON><PERSON>", "Payment to account is already approved!": "Onaylanan hesaba ödemeler yeniden onaylanamaz!", "Receipt from account is already approved!": "Onaylanan hesaptan tahsilatlar yeniden onaylanamaz!", "Receipts From Account": "Hesaptan Tahsilatlar", "New Receipt From Account": "Yeni Hesaptan Tahsilat", "Promissory Note Payments": "<PERSON><PERSON>", "PAYMENTS": "ÖDEMELER", "Receive Payment": "Ödeme Al", "Amount cannot be greater than promissory note amount!": "<PERSON>tar senet tutarından fazla olamaz!", "Remaining amount": "<PERSON><PERSON> tutar", "Paid amount": "<PERSON>denen tutar", "New Tag": "Yeni Etiket", "MIXED": "KARIŞIK", "Receipt": "Tahsilat", "Type symbol": "<PERSON><PERSON><PERSON>", "Fixed installment": "Sabit taksit", "Distribute Difference": "Farkı Dağıt", "Planning Result": "<PERSON><PERSON><PERSON>", "Received Amount": "Alınan Tutar", "Received amount": "Alınan tutar", "Amount To Receive": "Alınacak Tutar", "Amount to receive": "Alınacak tutar", "Installment period (Month)": "Taks<PERSON> (Ay)", "Installment period (Day)": "Taks<PERSON> (Gün)", "Type report is not available!": "Tür raporu mevcut değil!", "Due date start": "Vade tarihi ba<PERSON>ı", "The difference must be equal to zero!": "Fark sıfıra eşit olmalıdır!", "Received amount update is successfully applied.": "Alınan miktar güncellemesi başarıyla uygulandı.", "Cannot find any unfixed item to apply received amount update!": "Alınan tutar güncellemesini uygulamak için herhangi bir sabitlenmemiş satır bulunamadı!", "Cannot find any unfixed item to apply difference!": "Farkı uygulamak için herhangi bir sabitlenmemiş satır bulunamadı!", "Difference is successfully distributed.": "Fark başarıyla dağıtıldı.", "Item total amount must be greater than zero!": "Sa<PERSON>ır toplam tutarı sıfırdan büyük olmalıdır!", "Clear Plan": "Planı Temizle", "Payment plan successfully cleared.": "Ödeme planı başarıyla temizlendi.", "Due difference model": "Vade farkı modeli", "New Payment": "<PERSON><PERSON>", "Receive due difference": "Vade farkını tahsil et", "Discount model": "İndirim modeli", "Transfer Promissory Notes": "Senetleri Transfer Et", "TRANSFERS": "TRANSFERLER", "Apply to all": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Amount must be greater than zero!": "<PERSON>tar sı<PERSON>ı<PERSON>n büyük olmalıdır!", "New Receipt": "Yeni Tahsilat", "Planned": "<PERSON><PERSON><PERSON>", "POS account": "POS hesabı", "Receipts approved successfully.": "Tahsilatlar başarıyla onaylandı.", "Receipts Approved": "Tahsilatlar Onaylandı", "Payments approved successfully.": "Ödemeler başarıyla onaylandı.", "Payments Approved": "Ödemeler On<PERSON>landı", "Related financial entries could not found!": "İlgili finansal kayıtlar bulunamadı!", "Mortgage": "İpotek", "Guarantee method": "Teminât aracı", "Guarantee type": "<PERSON><PERSON><PERSON><PERSON>", "Definite": "<PERSON><PERSON>", "Temporary": "Geçici", "Continuous": "<PERSON><PERSON><PERSON><PERSON>", "Extratime": "<PERSON><PERSON>", "Extratime date": "<PERSON><PERSON> süre tarihi", "Extratime description": "Ek süre açıklaması", "Requested by department": "<PERSON><PERSON> eden departman", "Tender no": "İhale no", "Tender subject": "<PERSON><PERSON> kon<PERSON>", "Connected contract": "Bağlantılı sözleşme", "Connected quotation": "Bağlantılı teklif", "BANK ISSUED": "BANKA VERİLEN", "PARTNER ISSUED": "İŞ ORTAĞI VERİLEN", "BANK RECEIVED": "BANKA ALINAN", "PARTNER RECEIVED": "İŞ ORTAĞI ALINAN", "Guarantee account": "<PERSON><PERSON><PERSON><PERSON> he<PERSON>", "Bank issued": "Banka verilen", "Bank received": "Banka alınan", "Partner issued": "İş ortağı verilen", "Partner received": "İş ortağı alınan", "Term guarantee": "<PERSON><PERSON><PERSON><PERSON>", "Letter of guarantee": "<PERSON><PERSON><PERSON><PERSON> mektubu", "DBS": "DBS", "Cheques": "<PERSON><PERSON><PERSON>", "Promissory Notes": "<PERSON><PERSON><PERSON>", "COUNTERPARTS": "KARŞILIKLAR", "Banking tax": "Banka vergisi", "Stamp tax": "<PERSON><PERSON> vergisi", "Principal": "<PERSON> para", "Payment date": "<PERSON><PERSON><PERSON>", "Pay": "Ö<PERSON>", "Commission expense category": "Komisyon gider kategorisi", "Banking tax expense category": "Banka vergisi gider kategorisi", "Outgo expense category": "<PERSON><PERSON><PERSON><PERSON> gider kate<PERSON>", "Stamp tax expense category": "Damga vergsi gider kategorisi", "Support fund": "Destek fonu", "Support fund expense category": "Destek fonu gider kategorisi", "Cash flow item": "Nakit akış kalemi", "Cash Flow Items": "Nakit Akış Kalemleri", "New Cash Flow Item": "Yeni Nakit Akış Kalemi", "Guarantee amount": "Teminât tutarı", "External": "<PERSON><PERSON>", "Payment type": "<PERSON><PERSON><PERSON>", "Payment(s) are saved successfully.": "Ödeme(ler) başarıyla kaydedildi.", "Receipt(s) are saved successfully.": "Tahsilat(lar) başarıyla kaydedildi.", "Commission must be grater than zero!": "Komisyon sıfırdan büyük olmalıdır!", "Expense category accounting account is required!": "Gider kategorisi muhasebe hesabı zorunludur!", "CONNECTED GUARANTEES": "BAĞLANTILI TEMİNATLAR", "Loan": "<PERSON><PERSON><PERSON>", "Loan Models": "<PERSON><PERSON><PERSON>", "Loan model": "Kredi model", "New Loan Model": "<PERSON><PERSON>", "Loan Types": "<PERSON><PERSON><PERSON>", "Loan type": "<PERSON><PERSON><PERSON>", "New Loan Type": "<PERSON><PERSON>", "Loan account": "<PERSON><PERSON><PERSON>", "Long term account": "Uzun vade hesabı", "Fund expense category": "Fon gider kategorisi", "Overdue expense category": "<PERSON><PERSON><PERSON><PERSON> gider kategorisi", "Tax expense category": "<PERSON><PERSON><PERSON> gider kategorisi", "Loans": "<PERSON><PERSON><PERSON>", "New Loan": "<PERSON><PERSON>", "Cancel Loan": "Krediyi İptal Et", "Remaining principal": "<PERSON><PERSON> ana para", "Interest": "Faiz", "First installment date": "İlk taksit tarihi", "Last installment date": "Son taksit tarihi", "Loan amount": "<PERSON><PERSON><PERSON> tutarı", "Tax rate": "V<PERSON>gi or<PERSON>ı", "Support fund rate": "Destek fonu oranı", "Overdue": "<PERSON><PERSON><PERSON><PERSON>", "Total installment amount": "Toplam taksit tutarı", "Usage date": "<PERSON><PERSON><PERSON><PERSON> tarihi", "Remaining installment count": "Kalan taksit sayı<PERSON>ı", "Last transaction date": "Son <PERSON><PERSON><PERSON> tarihi", "Remaining total debt": "<PERSON><PERSON> borç", "The loan could not be canceled!": "<PERSON>redi iptal edilemedi!", "Loan Information": "<PERSON><PERSON><PERSON>", "Interest expense category": "<PERSON><PERSON>z gider kategorisi", "Short term amount": "<PERSON><PERSON>sa dönem tutarı", "Long term amount": "Uzun dönem tutarı", "RECEIPTS": "TAHSİLATLAR", "Contract no": "Sözleşme no", "Guarantee amount must be greater than zero!": "Teminat tutarı sıfırdan büyük olmalıdır!", "Transaction amount": "İşlem tutarı", "Payment Receipts": "Ödeme Ma<PERSON>bu<PERSON>ları", "New Payment Receipt": "<PERSON><PERSON>", "Receivable Receipts": "Tahsilat Makbuzları", "New Receivable Receipt": "Yeni Tahsilat Makbuzu", "You are about to close the document. Do you want continue?": "Belgeyi kapatmak üzeresiniz. Devam etmek istiyor musun?", "Difference must be equal to zero!": "Fark sıfıra eşit olmalıdır!", "Amount must be greater than to zero!": "<PERSON>tar sı<PERSON>ı<PERSON>n büyük olmalıdır!", "Entries are approved successfully.": "G<PERSON><PERSON><PERSON> başarıyla onaylandı.", "Financial Receipts": "Finansal Makbuzlar", "Financial Entries": "<PERSON><PERSON><PERSON>", "Financial Entry Lists": "Finansal G<PERSON>", "Receivable Receipt": "Tahsilat Makbuzu", "Payment Receipt": "<PERSON><PERSON><PERSON>", "Calculate due difference": "Vade farkını hesapla", "The operation cannot be performed because the partner is in the blacklist!": "İş ortağı kara listede olduğu için işlem gerçekleştirilemiyor!", "You have exceeded the maximum limit defined for the partner. The transaction is blocked. Limit excess amount is {{amount}}.": "İş ortağı için tanımlanan maksimum limit sınırını aştınız. İşlem engellendi. Limit aşım tutarı {{amount}}.", "You have exceeded the maximum limit defined for the partner. Limit excess amount is {{amount}}.": "İş ortağı için tanımlanan maksimum limit sınırını aştınız. Limit aşım tutarı {{amount}}.", "You have exceeded the maximum limit defined for the partner. Do you want to continue? Limit excess amount is {{amount}}.": "İş ortağı için tanımlanan maksimum limit sınırını aştınız. İşleme devam etmek istiyor musunuz? Limit aşım tutarı {{amount}}.", "Partner Limit Definitions": "İş Ortağı Limit Tanımları", "New Partner Limit Definition": "Yeni İş Ortağı Limit Tanımı", "Partner Limit Analysis": "İş Ortağı Limit Analizi", "Customer Limit Analysis": "Müşteri Limit Ana<PERSON>zi", "Vendor Limit Analysis": "Tedarikçi Limit <PERSON>zi", "Usable limit": "Kullanılabilir limit", "Limit is available, but close to exceeding limit.": "<PERSON><PERSON>, fakat aşım sınırına yakın.", "Limit is available, but very close to exceeding limit.": "<PERSON><PERSON>, fakat aşım sınırına çok yakın.", "Limit is available for the transaction.": "İşlem için limit müsait.", "Work advance payment": "İş avansı", "GUARANTEES": "TEMİNATLAR", "Limit status": "Limit durumu", "Receipt Lists": "Tahsilat Listeleri", "New Receipt List": "Yeni Tahsilat Listesi", "Payment Lists": "<PERSON><PERSON><PERSON>", "New Payment List": "<PERSON><PERSON>", "Entries": "<PERSON><PERSON><PERSON><PERSON>", "Bank code": "Banka kodu", "Bank name": "Banka adı", "Bank branch code": "Banka şube kodu", "Bank branch name": "Banka şube adı", "Payment List": "<PERSON><PERSON><PERSON>", "Tahsilat List": "Tahsilat Listesi", "Receipt account": "Tahsilat hesabı", "Cash Safe Analysis": "<PERSON><PERSON>", "Bank Analysis": "Banka Analizi", "POS Analysis": "POS Analizi", "Credit Card Analysis": "Kredi Kartı Analizi", "Credit Cheques": "Alacak Çekleri", "Credit Cheques Analysis": "Alacak Çekleri Analizi", "Debit Cheques": "<PERSON><PERSON><PERSON>", "Debit Cheques Analysis": "<PERSON><PERSON><PERSON>ekleri Analizi", "Credit Promissory Notes": "Alacak Senetleri", "Credit Promissory Notes Analysis": "Alacak Senetleri Analizi", "Debit Promissory Notes": "<PERSON><PERSON><PERSON>", "Debit Promissory Notes Analysis": "<PERSON><PERSON><PERSON> Analizi", "Ending balance": "<PERSON><PERSON><PERSON>", "Ending balance (FC)": "<PERSON><PERSON><PERSON> (YPB)", "Transaction balance": "İşlem bakiyesi", "Transaction balance (FC)": "İşlem bakiyesi (YPB)", "Balance (FC)": "Bakiye (YPB)", "Online Banking Transactions": "Online Bankacılık Kayıtları", "Online Banking": "Online Bankacılık", "Create Receipt": "Tahsilat Oluştur", "Transfer Cheques": "Çekleri Transfer Et", "Receivable Receipt - {{code}}": "Tahsilat Makbuzu - {{code}}", "Payment Receipt - {{code}}": "<PERSON><PERSON><PERSON> - {{code}}", "Due days": "<PERSON><PERSON>", "Avg. due days": "Or<PERSON>. v<PERSON> gü<PERSON>", "The related payment record will be removed and the necessary inverse accounting records will be created. Do you want continue?": "İlgili ödeme kaydı kaldırılacak ve gerekli ters muhasebe kayıtları oluşturulacaktır. <PERSON>am etmek istiyor musunuz?", "Since this payment has approved POS slips, the operation cannot be performed!": "Bu ödemenin onaylanmış POS slipleri mevcut olduğundan dolayı, işlem gerçekleştirilemiyor!", "Record will be canceled and necessary accounting records will be created. Do you want to continue?": "<PERSON><PERSON>t iptal edilecek ve gerekli muhasebe kayıtları oluşturulacaktır. <PERSON><PERSON> etmek istiyor musunuz?", "Cheque with number {{no}} is not found!": "{{no}} nolu çek bulunamadı!", "Cheque with number {{no}} must be refunded!": "{{no}} nolu çek iade edilmelid<PERSON>!", "Promissory note with number {{no}} is not found!": "{{no}} nolu senet bulunamadı!", "Promissory note with number {{no}} must be refunded!": "{{no}} nolu senet iade edilmel<PERSON>ir!", "Incoming payment": "<PERSON><PERSON><PERSON>", "Outgoing payment": "Giden ödeme", "Cheque bank": "Çek bankası", "Partially paid": "<PERSON><PERSON><PERSON><PERSON>", "Not paid": "Ödenmedi", "Between accounts": "Hesaplar arası", "Between partners": "İş ortakları arası", "Transfer between business partners.": "İş ortakları arası transfer.", "You are about to cancel the entries. Do you want continue?": "Girişleri iptal etmek üzeresiniz. Devam etmek istiyor musunuz?", "Entries are canceled successfully.": "<PERSON><PERSON><PERSON><PERSON> başarıyla iptal edildi.", "Entry is canceled successfully.": "<PERSON><PERSON><PERSON> başarıyla iptal edildi.", "Partner output account": "İş ortağı çıkış hesabı", "Partner input account": "İş ortağı giriş hesabı", "Payment types": "<PERSON><PERSON><PERSON>", "Transactions other than cash and money transfer cannot be canceled from this section!": "Nakit ve havale dışındaki işlemler bu bölümden iptal edilemez!", "Add Promissory Note": "<PERSON><PERSON>", "Add Cheque": "<PERSON><PERSON>", "Cheque Issued Receipt": "Çek Verildi <PERSON>", "All items must have the same currency!": "Tüm kalemlerin para birimi aynı olmalıdır!", "All items must have the same partner!": "Tüm kalemlerin iş ortağı aynı olmalıdır!", "All items must have the same destination status!": "<PERSON>üm kalemlerin hedef durumu aynı olmalıdır!", "All items must have a partner!": "Tüm kalemlerde iş ortağı olmalıdır!", "Journal cheque bank account is not found!": "Yevmiyede çek banka hesabı bulunamadı!", "Cheque no": "Çek no", "Serial no": "Seri no", "Currency amount": "Döviz tutarı", "Cheque bank branch": "Çek banka şubesi", "The partner to endorse must be selected!": "Ciro edilecek iş ortağı seçilmelidir!", "Cheque issuer": "<PERSON><PERSON><PERSON> veren", "The payment account must be selected!": "Ödeme hesabı seçilmelidir!", "No print format found for selected target status!": "No print format found for selected target status!", "Promissory note bank": "Senet bankası", "Promissory note bank branch": "Senet banka şubesi", "Payment slip no": "Bordro no", "Promissory Note Issued Receipt": "<PERSON><PERSON>", "PN no": "Senet no", "PN bank": "Senet bankası", "PN amount": "<PERSON><PERSON> tutarı", "ACCOUNT LEDGER": "HESAP EKSTRESİ", "This guarantee is used by the relevant partner. First, remove this guarantee from the relevant partner!": "Bu teminat ilgili iş ortağı tarafından kullanılmaktadır. İlk önce ilgili iş ortağının içerisinden bu teminatı kaldırınız!", "Cash Safe Report": "<PERSON><PERSON>", "Report interval": "<PERSON><PERSON>ı<PERSON>", "Ending debit total": "<PERSON><PERSON><PERSON> bor<PERSON>", "Ending credit total": "<PERSON><PERSON><PERSON> al<PERSON>", "Prev. ending debit total": "<PERSON><PERSON><PERSON> de<PERSON>den borç <PERSON>", "Prev. ending credit total": "Önceki devreden alacak toplam", "Prev. ending balance": "<PERSON><PERSON><PERSON> de<PERSON> b<PERSON>", "Report total": "<PERSON><PERSON>", "The operation cannot be performed because the documents are not printed!": "Belgeler yazdırılmadığı için işlem gerçekleştirilemiyor!", "Do you want to continue without printing?": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> devam etmek istiyor musunuz?", "Collector not found!": "Tahsilat sorumlusu bulunamadı!", "Use model calculation": "Model <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kullan", "No tax found in the model!": "Model de vergi bulunamadı!", "Detailed Promissory Notes Analysis": "Detaylı Senet Analizi", "Detailed Cheque Analysis": "Detaylı Çek Analizi", "Past-due": "Vadesi geçmiş", "{{count}} instalment(s)": "{{count}} taksit", "The operation cannot be performed because there are draft payment items!": "Taslak ödeme kalemleri olduğu için işlem gerçekleştirilemiyor!", "Salary advance payment": "Maaş avansı", "No bank account found for the selected account!": "Seçilen hesap için banka hesabı bulunamadı!", "Approved entries cannot be re-approved!": "Onaylanmış girişler yeniden onaylanamaz!", "Canceled entries cannot be approved!": "İptal edilmiş girişler onaylanamaz!", "Commission day multiplier": "Komisyon gün çarpanı", "Card Brands": "<PERSON><PERSON>", "New Card Brand": "<PERSON>ni <PERSON>", "RECEIPT": "TAHSİLAT", "INSTALLMENT RATES": "TAKSİT ORANLARI", "Ins. No": "Tak. No", "Ins. Amount": "Tak. <PERSON>", "Online POS Receipt": "Online POS Tahsilatı", "Limit Status": "<PERSON><PERSON>", "Used limit": "Kullanılan limit", "Required limit": "Gerekli limit", "Remaining limit": "Kalan limit", "Not enough limit.": "<PERSON>it <PERSON><PERSON><PERSON>.", "Credit Card Information": "Kredi Kartı Bilgileri", "Installment Options": "<PERSON><PERSON><PERSON>", "Single installment": "<PERSON><PERSON>", "{{installmentCount}} installments": "{{installmentCount}} taksit", "Charge full amount.": "Tüm tutarı tahsil et.", "{{installmentCount}} months, {{installmentAmount}} per month.": "{{installmentCount}} ay, ayda {{installmentAmount}}.", "Collect the Payment": "Ödemeyi Tahsil Et", "Awaiting approval for the transaction. This process may take a few minutes. Please wait..": "İşlem için onay bekleniyor. Bu işlem birkaç dakika sürebilir. Lütfen bekleyiniz..", "Congratulations. Online collection process completed successfully.": "Tebrikler. Online tahsilat süreci başarıyla tamamlanmıştır.", "Continue With The Approval": "<PERSON><PERSON>", "Invalid card number": "Geçersiz kart numarası", "Free financial collection": "Serbest finansal tahsilat", "Online POS Receipts": "Online POS Tahsilatları", "Payment transaction not found!": "Ödeme işlemi bulunamadı!", "Payment transaction is canceled!": "Ödeme işlemi iptal edildi!", "Endorsed partner": "Ciro edilen iş ortağı", "Cash Flow Records": "Nakit Akışı Kayıtları", "Cash Flow Templates": "Nakit Akışı Şablonları", "New Cash Flow Template": "Yeni Nakit Akışı Şablonu", "System + Custom": "Sistem + Özel", "Forecast": "<PERSON><PERSON><PERSON>", "Is graph shown": "<PERSON><PERSON><PERSON><PERSON>", "Include overdue records": "Vadesi geçmiş kayıtları değerlendir", "Include draft records": "Taslak kayıtları değerlendir", "Is overdue active": "Vadesi geçmiş aktif", "FORECASTS": "TAHMİNLER", "Invalid forecast definition!": "Geçersiz tahmin tanı<PERSON>!", "Exchange Rate Simulation": "Kur Farkı Simulasyonu", "Exchange rate difference amount (SC)": "Kur farkı tutarı (SPB)", "Due amount (SC)": "Vade tutarı (SPB)", "Due exchange rate": "Vade kuru", "Create Exchange Rate Invoice": "Kur Farkı Faturası Oluştur", "Exchange rate document code": "<PERSON>r farkı belge kodu", "Exchange rate status": "<PERSON>r farkı durumu", "Invalid 3D signature or verification": "Geçersiz 3D imza veya doğrulama", "The cardholder or bank is not registered.": "Kart sahibi veya banka kayıtlı değil.", "The card's bank is not registered.": "Kartın ait olduğu banka kayıtlı değil.", "Verification attempt, the cardholder chose to register later.": "<PERSON><PERSON><PERSON><PERSON><PERSON>, kart sahibi daha sonra kaydolmayı se<PERSON>.", "Unable to verify": "<PERSON><PERSON><PERSON><PERSON>a <PERSON>ı<PERSON>ıyor", "3D Secure failure": "3D Secure hatası", "System failure": "Sistem hatası", "Unknown card number": "Bilinmeyen kart numarası", "Unknown POS error!": "Bilinmeyen POS hatası!", "Bank Reconciliations": "Banka Uzlaştırmaları", "Transaction description": "İşlem açıklaması", "Bank Transaction": "Banka Hareketi", "Record Information": "Kayıt Bilgileri", "Cheque receipt": "Çek tahsilatı", "Cheque payment": "Çek ödemesi", "Promissory note receipt": "Senet tahsilatı", "Promissory note payment": "<PERSON><PERSON>", "Credit card payment": "Kredi kartı ödemesi", "Loan payment": "<PERSON><PERSON><PERSON>", "Transaction payment type": "İşlem ödeme türü", "Transaction bank": "İşlem bankası", "Transaction bank account": "İşlem banka hesabı", "Transaction currency": "İşlem para birimi", "Transaction Information": "İşlem Bilgileri", "Reconcile with approved document": "Onaylı belge ile uzlaştır", "Reconcile by approving document": "Belgeyi onaylayarak uzlaştır", "Reconcile by creating document": "Belgeyi oluşturarak uzlaştır", "Reconcile with unequal amounts": "<PERSON><PERSON>it olmayan tutarlarla uzlaştır", "Not reconcilable": "Uzlaştırılamıyor", "Bank Reconciliation Rules": "Banka Uzlaştırması Kuralları", "New Bank Reconciliation Rule": "Yeni Banka Uzlaştırma Kuralı", "No records found to be reconciled!": "Uzlaştırılacak kayıt bulunamadı!", "Transaction reconciled successfully.": "İşlem başarıyla uzlaştırıldı.", "Transactions reconciled successfully.": "İşlemler başarıyla uzlaştırıldı.", "Reconciled by": "Uzlaştıran", "Reconciled at": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ta<PERSON>", "Reconciliation status": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> du<PERSON>", "Not Reconciled": "Uzlaştırılmadı", "Reconciled": "Uzlaştırıldı", "You are about to reconcile the transaction. Do you want to continue?": "İşlemi uzlaştırmak üzeresiniz. Devam etmek istiyor musunuz?", "You are about to reconcile filtered and reconcilable transactions. Do you want to continue?": "Filtrelenen ve uzlaştırılabilr işlemleri uzlaştırmak üzeresiniz. Devam etmek istiyor musunuz?", "To be reconciled": "Uzlaştırılacak", "Reconcilable": "Uzlaştırılabilir", "Not reconciled": "Uzlaştırılmadı", "Reconciliation type": "Uzlaş<PERSON>ırma türü", "The inquiry period can be maximum 3 days!": "Sorgulama süresi en fazla 3 gün olabilir!", "The interval between two queries with the same criteria must be at least 10 minutes!": "Aynı kriterlere sahip iki sorgu arasındaki aralık en az 10 dakika olmalıdır!", "Bulk Reconcile": "Toplu Uzlaştır", "Promissory note number must be unique!": "Senet numarası eş<PERSON>z olmalıdır!", "Cheque number must be unique!": "Çek numarası eşsiz olmalıdır!", "Cheque serial no": "<PERSON>ek seri no", "Original": "<PERSON><PERSON><PERSON>", "Payment to account": "<PERSON><PERSON><PERSON>", "The due date of the cheque cannot be earlier than the issue date!": "<PERSON><PERSON><PERSON> vade tarihi belge tarihinden önce olamaz!", "Integration order number": "Entegrasyon sipariş numarası", "No matching POS found!": "Eşleşen bir POS bulunamadı!", "Hash verification failed!": "Hash doğrulaması başarısız!", "Sequence no": "Sıra no", "Number of cheques": "<PERSON><PERSON>", "Edited by": "D<PERSON><PERSON>leyen", "Cheque tracking code": "Çek takip kodu", "Payment slip date": "<PERSON><PERSON><PERSON> tarihi", "Total cheque amount": "Toplam çek tutarı", "Cheques Given Guarantee Receipt": "Teminata Verildi <PERSON>", "Cheque Refunded Receipt": "Çek İade Bordrosu", "Charge selected": "Seçilenleri tahsil et", "Charge filtered": "Filtrelenenleri tahsil et", "Charge all": "Hepsini tahsil et", "Number of collected records": "Tahsil edilen kayıt sayısı", "You cannot cancel records of the POS type from entries!": "POS türündeki kayıtları tahsilatlardan iptal edemezsiniz!", "FINANCIAL ENTRIES": "FİNANSAL GİRİŞLER", "Canceled date": "<PERSON>ptal tarihi", "Refunds cannot be made for installment payments!": "Taksitli ödemelerde iade işlemi yapılamaz!", "Refund": "İade", "{{partnerName}} - POS account refund": "{{partner<PERSON><PERSON>}} - POS hesabı iadesi", "Partially refunded": "<PERSON><PERSON><PERSON> i<PERSON> edildi", "Refund amount": "İade tutarı", "Can cancel the approved entries": "Onaylanmış finansal girişleri iptal edebilir", "You are not allowed to cancel approved financial entries!": "Onaylanmış finansal girişleri iptal etme yetkiniz bulunmamaktadır!", "Lines that have other scope cannot be deposited!": "<PERSON><PERSON><PERSON> ka<PERSON>daki satırlar taksile verilemez!", "Create E-Commerce Order": "E-T<PERSON><PERSON>ş<PERSON>", "Cash Safes": "<PERSON><PERSON><PERSON>", "Cheque Histories": "Çek Geçmişleri", "Evaluate account currency": "Hesap para birimini değerlendir", "Total promissory amount": "Toplam senet tutarı"}