<template>
    <div class="finance-banking-cheque-tab">
        <div class="finance-banking-cheque-tab-top">
            <ui-scope
                id="finance.banking.cheque.tab-outputs"
                :filters="applicableScopeFilters"
                @changed="handleScopeChange"
            />

            <div class="items-search">
                <el-input-search class="relation-search" v-model="search" />
            </div>
        </div>

        <div class="finance-banking-cheque-tab-content">
            <ui-table
                ref="table"
                id="finance.banking.cheque.tab-outputs"
                collection="finance.cheque-outputs"
                :columns="columns"
                :filters="filters"
                :search="search"
                :extra-fields="['fileId', 'partnerType', 'partnerId', 'partner', 'partnerCode', 'partnerName', 'inBillPartner', 'inBillPartnerId']"
                :enable-selection="false"
                @double-clicked="handlePrint"
            />
        </div>
    </div>
</template>

<script>
import fastCopy from 'fast-copy';

export default {
    props: {
        journal: Object
    },

    data: () => ({
        search: '',
        scopeQuery: {}
    }),

    computed: {
        columns() {
            return [
                {field: 'date', label: 'Date', sort: 'desc', format: 'datetime', width: 150},
                {field: 'paymentSlipNo', label: 'Payment slip no', width: 180},
                {field: 'userCode', label: 'User code', width: 180},
                {field: 'userName', label: 'User name'},
               {
            field: 'partner',
            label: 'Partner',
            subSelect: ['code', 'name'],
            visible: false,
            width: 180,
            format(row) {
                if (_.isObject(row.partner) && row.partner.code && row.partner.name) {
                    return `${row.partner.code} - ${row.partner.name}`;
                } else if (_.isString(row.partner) && row.partnerCode && row.partnerName) {
                    return `${row.partnerCode} - ${row.partnerName}`;
                } else {
                    return '';
                }
            },
             relationParams(params) {
                const data = params.data;
                const relation = {};

                relation.isVisible =
                    _.isObject(data.partner);

                if (relation.isVisible) {
                    relation.view = 'partners.partners-detail';
                    relation.id = data.partner;
                    relation.template = '{{code}} - {{name}}';
                }
                return relation;
            },
            width: 180
        },
        {
            field: 'endorsedPartner',
            label: 'Endorsed partner',
            subSelect: ['code', 'name'],
            visible: false,
            width: 180,
            format(row) {
                if (_.isObject(row.endorsedPartner) && row.endorsedPartner.code && row.endorsedPartner.name) {
                    return `${row.endorsedPartner.code} - ${row.endorsedPartner.name}`;
                } else if (_.isString(row.endorsedPartner) && row.endorsedPartnerCode && row.endorsedPartnerName) {
                    return `${row.endorsedPartnerCode} - ${row.endorsedPartnerName}`;
                } else {
                    return '';
                }
            },
             relationParams(params) {
                const data = params.data;
                const relation = {};

                relation.isVisible =
                    _.isObject(data.endorsedPartner);

                if (relation.isVisible) {
                    relation.view = 'partners.partners-detail';
                    relation.id = data.endorsedPartner;
                    relation.template = '{{code}} - {{name}}';
                }

                return relation;
            },
            width: 180
        },
                {
                    field: 'paymentAccount',
                    label: 'Payment account',
                    subSelect: ['name'],
                    width: 180,
                    relationParams(params) {
                        const data = params.data;
                        const relation = {};

                        relation.isVisible = _.isObject(data.paymentAccount) && _.isString(data.paymentAccount.name);

                        if (relation.isVisible) {
                            relation.view = 'accounting.configuration.journals';
                            relation.id = data.paymentAccountId;
                            relation.template = '{{name}}';
                        }

                        return relation;
                    }
                },
                {field: 'numberOfCheques', label: 'Number of cheques', format: 'integer'},
                {field: 'totalChequeAmount', label: 'Total cheque amount', format: 'currency'}
            ];
        },
        applicableScopeFilters() {
            return [
                {field: 'date', label: 'Date', type: 'datetime'},
                {field: 'paymentSlipNo', label: 'Payment slip no'},
                {field: 'userCode', label: 'User code'},
                {field: 'userName', label: 'User name'},
                {field: 'paymentAccountId', label: 'Payment account', collection: 'accounting.journals'},
                {field: 'numberOfCheques', label: 'Number of cheques', type: 'integer'}
            ];
        },
        filters() {
            const query = fastCopy(this.scopeQuery);

            query.journalId = this.journal._id;

            return query;
        }
    },

    methods: {
        handleScopeChange(model) {
            this.scopeQuery = model.query;
        },
        handlePrint(row) {
            this.$program.dialog({
                component: 'finance.banking.cheque.print',
                params: {
                    title: this.$t('Cheque Issued Receipt'),
                    url: this.$app.absoluteUrl(`files/${row.fileId}`)
                }
            });
        }
    }
};
</script>
