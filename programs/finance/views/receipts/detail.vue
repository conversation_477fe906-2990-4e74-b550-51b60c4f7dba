<template>
    <ui-view
        type="form"
        ref="view"
        collection="finance.receipts"
        method="finance.save-receipt"
        class="finance-receipts-detail"
        :model="model"
        :title="title"
        :extra-fields="extraFields"
        :before-init="beforeInit"
        :before-validate="beforeValidate"
        :before-submit="beforeSubmit"
        :actions="actions"
        :extra-actions="extraActions"
        :assignation="assignation"
        :activity-payload="activityPayload"
        :progress-id="`finance.entries.${type}`"
        print-method="finance.print-receipt"
        @changed="handleChange"
        v-if="initialized"
    >
        <template slot="form-top">
            <ui-status :statuses="statuses" :value="status" />

            <div class="scope-rate" v-show="$setting('system.scopes')">
                <i class="fal fa-balance-scale" /> {{ $t('Scope Rate') }}: {{ scopeRate }}
            </div>

            <ui-related-documents :documents="model.relatedDocuments" />

            <el-button
                :loading="$params('loading')"
                type="primary"
                icon="far fa-thumbs-up"
                :disabled="
                    !$params('isPreview') ||
                    (!$params('id') && !model.status) ||
                    status !== 'processing' ||
                    $params('loading')
                "
                @click="handleClose"
            >
                {{ 'Close' | t }}
            </el-button>
        </template>

        <div class="columns">
            <div class="column is-half">
                <ui-field name="code" disabled />
                <ui-field
                    name="type"
                    :options="typeOptions"
                    translate-labels
                    v-show="!$params('type')"
                    :disabled="status !== 'draft'"
                />
                <ui-field
                    name="partnerType"
                    :options="partnerTypeOptions"
                    translate-labels
                    :disabled="status !== 'draft'"
                />
                <ui-field
                    name="partnerGroupId"
                    collection="kernel.partner-groups"
                    view="system.management.configuration.partner-groups"
                    disable-create
                    disable-detail
                    :filters="{type: model.partnerType}"
                    :disabled="status !== 'draft'"
                    :label="partnerGroupLabel"
                    v-show="model.partnerType === 'customer' || model.partnerType === 'vendor'"
                />
                <ui-field
                    :key="partnerIdKey"
                    name="partnerId"
                    collection="kernel.partners"
                    view="partners.partners"
                    :label="partnerLabel"
                    :extra-fields="['code']"
                    :template="'{{code}} - {{name}}'"
                    :filters="partnerIdFilters"
                    :update-params="updatePartnerSelectParams"
                    :disabled="status !== 'draft' || !model.partnerType"
                />
                <ui-field
                    name="contactPersonId"
                    collection="kernel.contacts"
                    view="partners.contacts"
                    :filters="{partnerId: model.partnerId, type: 'contact'}"
                    :update-params="updateContactSelectParams"
                    :disabled="!model.partnerId"
                />
                <ui-field
                    name="paymentTermId"
                    collection="finance.payment-terms"
                    view="finance.configuration.payment-terms"
                    disable-create
                    disable-detail
                    :filters="paymentTermIdFilters"
                    :disabled="!model.partnerId"
                />
                <template v-if="$setting('system.multiCurrency')">
                    <div class="ui-inline-fields">
                        <div class="field-label">{{ 'Amount' | t }}</div>
                        <div class="field-content">
                            <ui-field
                                name="amount"
                                :precision="$setting('system.currencyPrecision')"
                                :style="{flex: '1 1 0'}"
                                label="hide"
                            />
                            <ui-field
                                name="currencyId"
                                collection="kernel.currencies"
                                :style="{flex: '1 1 0'}"
                                label="hide"
                            />
                            <ui-field
                                name="currencyRate"
                                :style="{flex: '0 0 80px'}"
                                :precision="$setting('system.exchangeRatePrecision')"
                                label="hide"
                                v-show="isForeignCurrency"
                            />
                        </div>
                    </div>
                </template>
                <template v-else>
                    <ui-field name="amount" :precision="$setting('system.currencyPrecision')" />
                    <ui-field
                        name="currencyId"
                        collection="kernel.currencies"
                        v-show="$setting('system.multiCurrency')"
                    />
                </template>
                <ui-field
                    name="discount"
                    :precision="$setting('system.percentagePrecision')"
                    :disabled="status !== 'draft'"
                    v-show="false"
                >
                    <div slot="append">%</div>
                </ui-field>
                <ui-field name="planned" :precision="$setting('system.currencyPrecision')" disabled>
                    <div :slot="currencyFormat.currency.symbolPosition === 'after' ? 'append' : 'prepend'">
                        {{ currencyFormat.currency.symbol }}
                    </div>
                </ui-field>
                <ui-field name="difference" :precision="$setting('system.currencyPrecision')" disabled>
                    <div :slot="currencyFormat.currency.symbolPosition === 'after' ? 'append' : 'prepend'">
                        {{ currencyFormat.currency.symbol }}
                    </div>
                </ui-field>
            </div>

            <div class="column is-half">
                <kernel-common-branch-select
                    :filters="{_id: {$in: partnerBranchIds}}"
                    :disabled="!model.partnerId"
                    :set-default="false"
                />
                <ui-field name="recordDate" :disabled="status !== 'draft'" />
                <ui-field name="issueDate" />
                <div class="ui-inline-fields">
                    <div class="field-label">{{ 'Due date' | t }}</div>
                    <div class="field-content">
                        <ui-field name="dueDate" label="hide" disabled />
                        <ui-field
                            name="dueDayDifference"
                            label="hide"
                            :precision="$setting('system.amountPrecision')"
                            disabled
                        >
                            <div slot="append">
                                {{ 'Day(s)' | t }}
                            </div>
                        </ui-field>
                    </div>
                </div>
                <div class="ui-inline-fields">
                    <div class="field-label">{{ 'Avg. due date' | t }}</div>
                    <div class="field-content">
                        <ui-field name="avgDueDate" label="hide" disabled />
                        <ui-field
                            name="avgDueDayDifference"
                            label="hide"
                            :precision="$setting('system.amountPrecision')"
                            disabled
                        >
                            <div slot="append">
                                {{ 'Day(s)' | t }}
                            </div>
                        </ui-field>
                    </div>
                </div>
                <ui-field name="reference" />
                <ui-field name="description" />
                <ui-field name="transactionType" :options="transactionTypeOptions" translate-labels />
            </div>
        </div>

        <el-tabs v-model="activeTab">
            <el-tab-pane name="entries" :label="'Entries' | t">
                <ui-field
                    name="entryIds"
                    ref="entriesField"
                    field-type="relation"
                    actions="add,remove,create"
                    collection="finance.entries"
                    view="finance.entries.detail"
                    :extra-fields="['accountingJournalEntryId', 'voucherNo']"
                    auto-height
                    :disabled="!$params('id')"
                    :columns="entryIdsColumns"
                    :filters="entryIdsFilters"
                    :custom-actions="entryIdsActions"
                    :update-params="updateEntryIdsParams"
                    @record-updated="handleEntriesUpdated"
                    @record-removed="handleEntriesRemoved"
                    @selected="handleEntriesSelected"
                />
                <ui-totals :totals="totalItems" no-grand-total>
                    <ui-legend title="Note" />
                    <ui-field name="note" label="hide" :rows="2" />
                </ui-totals>
            </el-tab-pane>

            <el-tab-pane name="source-documents" :label="'Source Documents' | t" disabled>
                <ui-field
                    name="sourceDocuments"
                    :columns="sourceDocumentsColumns"
                    :custom-actions="sourceDocumentsCustomActions"
                    field-type="relation"
                    auto-height
                />
            </el-tab-pane>

            <el-tab-pane name="guarantors" :label="'Guarantors' | t">
                <ui-field
                    name="guarantorIds"
                    collection="kernel.contacts"
                    view="partners.contacts.detail"
                    :columns="[
                        {field: 'name', label: 'Name'},
                        {field: 'identity', label: 'Identity no', width: 180},
                        {field: 'phone', label: 'Phone', phoneCell: true, width: 180},
                        {field: 'email', label: 'Email Address', width: 240}
                    ]"
                    :update-params="
                        params => ({...params, model: {type: 'contact'}, forGuarantor: true, hideTypes: true})
                    "
                    field-type="relation"
                    actions="create,delete"
                    :auto-height="true"
                />
            </el-tab-pane>

            <el-tab-pane name="attachments" :label="'Attachments' | t">
                <ui-field name="attachments" field-type="attachments" auto-height />
            </el-tab-pane>
        </el-tabs>
    </ui-view>
</template>

<script>
import _ from 'lodash';
import fastCopy from 'fast-copy';

export default {
    data: () => ({
        model: {},
        currencies: [],
        tagOptions: [],
        activeTab: 'entries',
        // activeTab: 'source-documents',
        currencyFormat: null,
        extraFields: [
            'status',
            'type',
            'discount',
            'paid',
            'remaining',
            'currencyRate',
            'scopeParams',
            'paymentTerm',
            'relatedDocuments',
            'purpose',
            'guaranteeId',
            'overriddenPartnerAccountId'
        ],
        typeOptions: [
            {value: 'receipt', label: 'Receivable Receipt'},
            {value: 'payment', label: 'Payment Receipt'}
        ],
        partnerTypeOptions: [
            {value: 'customer', label: 'Customer'},
            {value: 'vendor', label: 'Vendor'},
            {value: 'employee', label: 'Employee'}
        ],
        partnerBranchIds: [],
        partnerIdKey: _.uniqueId('partnerIdKey'),
        paymentTerm: null,
        transactionTypeOptions: [
            {value: 'cheque', label: 'Cheque'},
            {value: 'moneyTransfer', label: 'Money transfer'},
            {value: 'eft', label: 'EFT'},
            {value: 'withdrawal', label: 'Withdrawal'},
            {value: 'cashDeposit', label: 'Cash deposit'},
            {value: 'fee', label: 'Fee'},
            {value: 'outgo', label: 'Outgo'},
            {value: 'advancePayment', label: 'Work advance payment'},
            {value: 'other', label: 'Other'}
        ],
        selectedEntries: [],
        generatedCode: '',
        initialized: false
    }),

    computed: {
        type() {
            return this.model.type || this.$params('type') || 'receipt';
        },
        title() {
            const model = this.model;

            if (this.$params('id')) {
                return model.code ? model.code : '';
            }

            if (this.type === 'payment') {
                return this.$t('New Payment Receipt');
            }

            return this.$t('New Receivable Receipt');
        },
        statuses() {
            const statuses = [
                {value: 'draft', label: 'Draft'},
                {value: 'processing', label: 'Processing'}
            ];

            if (this.status === 'canceled') {
                statuses.push({value: 'canceled', label: 'Canceled'});
            } else {
                statuses.push({value: 'closed', label: 'Closed'});
            }

            return statuses;
        },
        status() {
            const model = this.model;

            if (!this.$params('id')) {
                return 'draft';
            }

            return model.status;
        },
        isClosed() {
            const model = this.model;

            return model.status === 'closed';
        },
        isCanceled() {
            const model = this.model;

            return model.status === 'canceled';
        },
        scopeRate() {
            let scopeRate = 100;

            if (!!this.model.scopeParams && _.isNumber(this.model.scopeParams.scope1Rate)) {
                scopeRate = this.model.scopeParams.scope1Rate;
            }

            return `${this.$app.round(scopeRate, 'percentage')}%`;
        },
        assignation() {
            return {};
        },
        activityPayload() {
            return {
                partnerType: this.model.partnerType,
                partnerId: this.model.partnerId
            };
        },
        actions() {
            return this.$params('id') && (this.isClosed || this.isCanceled) ? 'edit:disabled,cancel' : 'edit,cancel';
        },
        extraActions() {
            return [
                {
                    name: 'cancel',
                    title: 'Cancel',
                    icon: 'fal fa-ban',
                    handler: this.handleCancel,
                    disabled: () => {
                        return !this.$params('id') || this.status !== 'draft';
                    }
                }
            ];
        },
        partnerGroupLabel() {
            const model = this.model;

            if (model.partnerType === 'customer') return 'Customer group';
            else if (model.partnerType === 'vendor') return 'Vendor group';
        },
        partnerLabel() {
            const model = this.model;

            if (model.partnerType === 'customer') return 'Customer';
            else if (model.partnerType === 'vendor') return 'Vendor';
            else if (model.partnerType === 'employee') return 'Employee';
        },
        isForeignCurrency() {
            const company = this.$store.getters['session/company'];
            const model = this.model;

            return model.currencyId !== company.currencyId;
        },
        paymentTermIdFilters() {
            const filters = {};

            if (this.type === 'receipt') {
                filters.scope = 'receipt';
            } else {
                filters.scope = 'payment';
            }

            return filters;
        },
        entryIdsFilters() {
            const model = this.model;
            const filters = {
                type: this.type,
                partnerType: model.partnerType,
                $or: [{receiptId: {$exists: false}}, {receiptId: null}],
                $disableBranchCheck: true
            };

            if (!!model.partnerId) {
                filters.partnerId = model.partnerId;
            }

            if (this.$params('id')) {
                filters.$or.push({receiptId: this.$params('id')});
            }

            return filters;
        },
        partnerIdFilters() {
            const filters = {type: this.model.partnerType};

            if (this.model.partnerGroupId) {
                filters.groupId = this.model.partnerGroupId;
            }

            return filters;
        },
        entryIdsActions() {
            const self = this;

            return [
                {
                    icon: 'far fa-thumbs-up',
                    text: this.$t('Approve'),
                    get disabled() {
                        return (
                            self.status === 'closed' ||
                            self.status === 'canceled' ||
                            !self.$params('id') ||
                            self.selectedEntries.length < 1 ||
                            self.selectedEntries.some(c => c.status === 'approved') ||
                            !!self.$params('loading')
                        );
                    },
                    handler: this.handleApproveEntries
                },
                // {
                //     icon: 'far fa-ban',
                //     text: this.$t('Cancel'),
                //     get disabled() {
                //         return (
                //             self.status === 'closed' ||
                //             self.status === 'canceled' ||
                //             !self.$params('id') ||
                //             self.selectedEntries.length < 1 ||
                //             self.selectedEntries.some(c => c.status === 'canceled') ||
                //             !!self.$params('loading')
                //         );
                //     },
                //     handler: this.handleCancelEntries
                // },
                {
                    icon: 'far fa-coins',
                    text: this.$t('Payment Plan'),
                    get disabled() {
                        return (
                            self.status === 'closed' ||
                            self.status === 'canceled' ||
                            !self.model.partnerId ||
                            !(self.model.difference > 0) ||
                            !self.$params('id') ||
                            !!self.$params('isPreview') ||
                            !!self.$params('loading')
                        );
                    },
                    handler: this.handlePaymentPlan
                }
            ];
        },
        entryIdsColumns() {
            const self = this;
            const company = this.$store.getters['session/company'];

            const scopeOptions = [
                {value: '1', label: this.$setting('system.scope1Label')},
                {value: '2', label: this.$setting('system.scope2Label')}
            ];

            return [
                {
                    field: 'documentType',
                    label: 'Document Type',
                    translateLabels: true,
                    valueLabels: [
                        {value: 'cash', label: 'Cash'},
                        {value: 'moneyTransfer', label: 'Money transfer'},
                        {value: 'cheque', label: 'Cheque'},
                        {value: 'promissoryNote', label: 'Promissory note'},
                        {value: 'creditCard', label: 'Credit card'},
                        {value: 'pos', label: 'POS'}
                    ],
                    width: 90
                },
                {
                    field: 'no',
                    label: 'Number',
                    width: 60,
                    valueGetter(params) {
                        if (_.isObject(params.data)) {
                            const row = params.data;

                            if (row.documentType === 'pos') {
                                return _.isNumber(row.plusInstallmentCount) && row.plusInstallmentCount > 0
                                    ? `${row.installmentCount}+${row.plusInstallmentCount}`
                                    : row.installmentCount;
                            }

                            return params.data.no;
                        }

                        return '';
                    }
                },
                {field: 'recordDate', label: 'Record Date', format: 'date', visible: false, width: 120},
                {field: 'issueDate', label: 'Issue Date', format: 'date', visible: false, sort: 'desc', width: 120},
                {field: 'dueDate', label: 'Due Date', format: 'date', width: 90},
                {
                    field: 'overdueDays',
                    label: 'Overdue Days',
                    cellClassRules: {
                        'is-danger': params => params.value > 0
                    },
                    valueGetter(params) {
                        if (_.isObject(params.data) && _.isDate(params.data.dueDate)) {
                            const dueDateStart = self.$datetime.local().startOf('day');
                            const dueDateEnd = self.$datetime.fromJSDate(params.data.dueDate).startOf('day');

                            return -self.$app.roundNumber(dueDateEnd.diff(dueDateStart, 'days').toObject().days);
                        }

                        return 0;
                    },
                    width: 100
                },
                {
                    field: 'journal.name',
                    label: 'Account',
                    width: 180
                },
                {
                    field: 'voucherNo',
                    label: 'Voucher No',
                    relationParams(params) {
                        const data = params.data;
                        const relation = {};

                        relation.isVisible = _.isString(data.accountingJournalEntryId) && _.isString(data.voucherNo);

                        if (relation.isVisible) {
                            relation.view = 'accounting.adviser.journal-entries-detail';
                            relation.id = data.accountingJournalEntryId;
                        }

                        return relation;
                    },
                    width: 150
                },
                {field: 'documentNo', label: 'Document No', visible: false},
                {field: 'reference', label: 'Reference', visible: false},
                {field: 'description', label: 'Description'},
                {
                    field: 'transactionType',
                    label: 'Transaction Type',
                    translateLabels: true,
                    valueLabels: [
                        {value: 'moneyTransfer', label: 'Money transfer'},
                        {value: 'eft', label: 'EFT'},
                        {value: 'withdrawal', label: 'Withdrawal'},
                        {value: 'cashDeposit', label: 'Cash deposit'},
                        {value: 'fee', label: 'Fee'},
                        {value: 'outgo', label: 'Outgo'},
                        {value: 'other', label: 'Other'}
                    ],
                    visible: false,
                    width: 120
                },
                {
                    field: 'scope',
                    label: 'Scope',
                    visible: false,
                    hidden: !this.$setting('system.scopes'),
                    width: 75,
                    valueLabels: scopeOptions
                },
                {
                    field: 'tagIds',
                    label: 'Tags',
                    tagsCell: true,
                    tagLabels: this.tagOptions,
                    visible: false,
                    width: 150
                },
                {
                    field: 'currency.name',
                    label: 'Currency',
                    hidden: !this.$setting('system.multiCurrency'),
                    visible: false,
                    width: 120
                },
                {
                    field: 'total',
                    label: 'Total',
                    format: 'currency',
                    formatOptions(row) {
                        if (_.isObject(row) && row.currencyId) {
                            const currency = self.currencies.find(c => c._id === row.currencyId);
                            let options = {currency: {}};

                            options.currency.symbol = currency.symbol;
                            options.currency.format = currency.symbolPosition === 'before' ? '%s %v' : '%v %s';

                            return options;
                        }

                        return {};
                    },
                    width: 150
                },
                {
                    field: 'systemCurrencyTotal',
                    label: 'Total (SC)',
                    visible: false,
                    hidden: !this.$setting('system.multiCurrency'),
                    format: 'currency',
                    formatOptions(row) {
                        const currency = company.currency;
                        let options = {currency: {}};

                        options.currency.symbol = currency.symbol;
                        options.currency.format = currency.symbolPosition === 'before' ? '%s %v' : '%v %s';

                        return options;
                    },
                    width: 150
                },
                {
                    field: 'workflowApprovalStatus',
                    label: 'EnterFlow',
                    workflowApprovalStatusCell: true,
                    documentCollection: 'finance.entries',
                    width: 120,
                    hidden: !this.$app.hasModule('workflow')
                },
                {
                    field: 'status',
                    label: 'Status',
                    tagsCell: true,
                    translateLabels: true,
                    tagLabels: [
                        {value: 'draft', label: 'Draft', color: 'default'},
                        {value: 'approved', label: 'Approved', color: 'primary'},
                        {value: 'canceled', label: 'Canceled', color: 'danger'}
                    ],
                    width: 100
                }
            ];
        },
        totalItems() {
            const model = this.model;
            const items = [];

            items.push({label: this.$t('Total Paid'), value: model.paid});
            items.push({label: this.$t('Total Remaining'), value: model.remaining});

            return {items, format: this.currencyFormat};
        },
        sourceDocumentsColumns() {
            const self = this;

            return [
                {
                    field: 'type',
                    label: 'Type',
                    valueLabels: [
                        {value: 'sale.orders', label: 'Sale Orders'},
                        {value: 'purchase.orders', label: 'Purchase Orders'},
                        {value: 'accounting.customer-invoices', label: 'Customer Invoices'},
                        {value: 'accounting.vendor-invoices', label: 'Vendor Invoices'}
                    ],
                    translateLabels: true,
                    minWidth: 150
                },
                {
                    field: 'recordDate',
                    label: 'Record Date',
                    format: 'date',
                    width: 150
                },
                {
                    field: 'issueDate',
                    label: 'Issue Date',
                    format: 'date',
                    width: 150
                },
                {
                    field: 'dueDate',
                    label: 'Due Date',
                    format: 'date',
                    width: 150
                },
                {
                    field: 'amount',
                    label: 'Amount',
                    format: 'currency',
                    formatOptions(row) {
                        const currency = self.currencies.find(c => c._id === self.model.currencyId);
                        let options = {currency: {}};

                        options.currency.symbol = currency.symbol;
                        options.currency.format = currency.symbolPosition === 'before' ? '%s %v' : '%v %s';

                        return options;
                    },
                    width: 150
                },
                {
                    field: 'status',
                    label: 'Status',
                    tagsCell: true,
                    translateLabels: true,
                    tagLabels: [
                        {value: 'draft', label: 'Draft', color: 'default'},
                        {value: 'payment-planned', label: 'Payment Planned', color: 'success'},
                        {value: 'approved', label: 'Approved', color: 'primary'},
                        {value: 'to-invoice', label: 'To Invoice', color: 'purple'},
                        {value: 'invoiced', label: 'Invoiced', color: 'teal'},
                        {value: 'approved', label: 'Approved', color: 'primary'},
                        {value: 'canceled', label: 'Canceled', color: 'danger'}
                    ],
                    width: 180
                }
            ];
        },
        sourceDocumentsCustomActions() {
            const self = this;

            return [
                {
                    icon: 'far fa-paperclip',
                    text: this.$t('Add Invoice'),
                    get disabled() {
                        return self.$params('isPreview');
                    },
                    handler: () => this.handleAddSourceDocument('invoice')
                },
                {
                    icon: 'far fa-paperclip',
                    text: this.$t('Add Order'),
                    get disabled() {
                        return self.$params('isPreview');
                    },
                    handler: () => this.handleAddSourceDocument('order')
                },
                {
                    icon: 'far fa-times',
                    text: this.$t('Remove'),
                    get disabled() {
                        return self.$params('isPreview');
                    },
                    async handler() {}
                }
            ];
        }
    },

    methods: {
        async handleApproveEntries() {
            const ids = this.selectedEntries.map(s => s._id);

            if (ids.length > 0) {
                const isConfirmed = await new Promise(resolve => {
                    this.$program.alert(
                        'confirm',
                        this.$t('Related accounting records will be created and posted. Do you want continue?'),
                        confirmed => {
                            resolve(confirmed);
                        }
                    );
                });

                if (!isConfirmed) return;

                if (!this.$params('isPreview')) {
                    await this.$refs.view.submitForm();

                    this.$params('isPreview', true);
                }

                let workflowCreated = false;

                if (this.$app.hasModule('workflow')) {
                    this.$params('loading', true);

                    const entries = await this.$collection('finance.entries').find({
                        _id: {$in: ids},
                        $disableBranchCheck: true,
                        $disableActiveCheck: true,
                        $disableSoftDelete: true
                    });

                    for (const entry of entries) {
                        try {
                            const flowResult = await this.$rpc('workflow.run-workflow', {
                                name: entry.type === 'payment' ? 'finance.payments' : 'finance.receipts',
                                collectionName: 'finance.entries',
                                data: {
                                    ..._.omit(entry, ['_id', 'workflowApprovalStatus'])
                                },
                                id: entry._id,
                                operation: 'update',
                                actionTypes: ['approval'],
                                approvalMethod: 'finance.save-entry',
                                approvalPayload: {
                                    id: entry._id,
                                    data: {
                                        ..._.omit(entry, ['_id', 'workflowApprovalStatus']),
                                        status: 'approved'
                                    }
                                }
                            });
                            if (!!flowResult.approvalDefinitionFound) {
                                if (flowResult.approvalIsAlreadyInProgress === true) {
                                    this.$program.alert(
                                        'error',
                                        this.$t(
                                            'The document is currently in the process of approval. When the relevant person or persons complete the approval process, the document will be automatically approved.'
                                        )
                                    );

                                    continue;
                                }

                                workflowCreated = true;
                            }
                        } catch (error) {
                            this.$program.message('error', error.message);
                        }
                    }

                    if (workflowCreated) {
                        this.$program.alert(
                            'success',
                            this.$t(
                                'The document has been successfully submitted to the workflow approval process. When the relevant person or persons complete the approval process, the document will be automatically approved.'
                            )
                        );

                        this.$nextTick(async () => {
                            await this.handleChange(this.model, 'entryIds');

                            this.$params('loading', false);
                        });
                    } else {
                        this.$params('loading', false);
                    }
                }

                if (!workflowCreated) {
                    try {
                        if (ids.length === 1) {
                            this.$params('loading', true);
                        }

                        await this.$rpc('finance.approve-entries', {type: this.type, entryIds: ids});

                        this.$program.message('success', this.$t('Entries are approved successfully.'));

                        this.$nextTick(async () => {
                            await this.handleChange(this.model, 'entryIds');

                            if (ids.length === 1) {
                                this.$params('loading', false);
                            }
                        });
                    } catch (e) {
                        this.$program.message('error', e.message);

                        this.$params('loading', false);
                    }
                }

                this.selectedEntries = [];
                this.$refs.entriesField.$refs.uiField.deselectAll();
            }
        },
        async handleCancelEntries() {
            const ids = this.selectedEntries.map(s => s._id);

            if (ids.length > 0) {
                const isConfirmed = await new Promise(resolve => {
                    this.$program.alert(
                        'confirm',
                        this.$t('You are about to cancel the entries. Do you want continue?'),
                        confirmed => {
                            resolve(confirmed);
                        }
                    );
                });

                if (!isConfirmed) return;

                if (!this.$params('isPreview')) {
                    await this.$refs.view.submitForm();

                    this.$params('isPreview', true);
                }

                try {
                    this.$params('loading', true);

                    await this.$rpc('finance.cancel-entries', ids);

                    this.$program.message('success', this.$t('Entries are canceled successfully.'));

                    this.$nextTick(async () => {
                        await this.handleChange(this.model, 'entryIds');

                        this.$params('loading', false);
                    });
                } catch (e) {
                    this.$program.message('error', e.message);

                    this.$params('loading', false);
                }

                this.selectedEntries = [];
                this.$refs.entriesField.$refs.uiField.deselectAll();
            }
        },
        async handlePaymentPlan() {
            const user = this.$user;
            const params = {};

            params.amount = this.model.amount;
            params.discount = 0;
            params.partnerId = this.model.partnerId;
            params.currencyId = this.model.currencyId;
            params.currencyRate = this.model.currencyRate;
            params.currencyFormat = this.currencyFormat;
            params.checkOpenAmount = true;
            params.isReceipt = true;
            params.isPayment = false;
            // params.guaranteeId = this.model.guaranteeId;
            params.paymentTermId = this.model.paymentTermId;
            params.paymentTerm = this.model.paymentTerm;
            params.branchId = this.model.branchId;
            params.recordDate = this.model.recordDate;
            params.issueDate = this.model.issueDate;
            params.reference = this.model.code;
            params.description = this.$t('Receipts');
            params.issuedBy = user.partnerId;

            if (_.isPlainObject(this.model.scopeParams) && !_.isEmpty(this.model.scopeParams)) {
                params.scopeParams = this.model.scopeParams;
            } else {
                params.scopeParams = {
                    scope1Rate: 100,
                    scope2Rate: 0
                };
            }

            if (this.type === 'payment') {
                params.isReceipt = false;
                params.isPayment = true;
                params.description = this.$t('Payments');
            }

            const handleSubmit = async data => {
                this.$params('loading', true);

                const entryIds = [];

                for (const item of data.items) {
                    item.status = 'draft';
                    item.type = this.type;
                    item.partnerType = this.model.partnerType;
                    item.partnerId = this.model.partnerId;
                    item.responsibleId = this.model.responsibleId;
                    item.reference = this.model.code;
                    item.receiptId = this.$params('id');
                    item.relatedDocuments = [
                        {
                            collection: 'finance.receipts',
                            view:
                                this.type === 'receipt'
                                    ? 'finance.receivable.receivable-receipts'
                                    : 'finance.payable.payment-receipts',
                            title: this.type === 'receipt' ? 'Receivable Receipts' : 'Payment Receipts',
                            ids: [this.$params('id')]
                        }
                    ];

                    if (!_.isNumber(item.amount)) {
                        item.amount = (item.total || 0) - (item.dueDifference || 0);
                    }

                    if (!!item.partnerId) {
                        const partner = await this.$collection('kernel.partners').findOne({
                            _id: item.partnerId,
                            $select: ['salespersonId']
                        });

                        item.salespersonId = '';
                        if (partner && partner.salespersonId) {
                            item.salespersonId = partner.salespersonId;
                        }
                    }

                    const entry = await this.$collection('finance.entries').create(item);

                    entryIds.push(entry._id);
                }

                this.model.entryIds = [...(this.model.entryIds || []), ...entryIds];

                await this.handleChange(this.model, 'entryIds');

                this.$params('loading', false);
            };

            this.$program.dialog({
                component: 'finance.components.pp',
                params,
                onSubmit: data => {
                    this.$nextTick(async () => {
                        await handleSubmit(data);
                    });
                }
            });
        },
        async handleEntriesUpdated() {
            await this.handleChange(this.model, 'entryIds');
        },
        async handleEntriesRemoved() {
            this.selectedEntries = [];
        },
        handleEntriesSelected(selected) {
            this.selectedEntries = selected;
        },
        handleAddSourceDocument(documentType) {
            let collection = '';
            let view = '';
            if (this.type === 'payment') {
                if (documentType === 'order') {
                    view = 'purchase.purchase.orders.master';
                    collection = 'purchase.orders';
                } else {
                    view = 'accounting.purchase.vendor-invoices.master';
                    collection = 'accounting.vendor-invoices';
                }
            } else {
                if (documentType === 'order') {
                    view = 'sale.sales.orders.master';
                    collection = 'sale.orders';
                } else {
                    view = 'accounting.sales.customer-invoices.master';
                    collection = 'accounting.customer-invoices';
                }
            }

            const filters = {
                partnerId: this.model.partnerId,
                $or: [{receiptId: {$exists: false}}, {receiptId: {$eq: null}}, {receiptId: {$eq: ''}}],
                dueDate: {$exists: true},
                grandTotal: {$gt: 0},
                status: {$ne: 'canceled'}
            };

            this.$program.dialog({
                component: view,
                params: {
                    filters
                },
                onSelect: async selected => {
                    this.$params('loading', true);

                    const items = await this.$collection(collection).find({
                        _id: {$in: selected.map(s => s._id)},
                        $select: [
                            '_id',
                            'recordDate',
                            'issueDate',
                            'dueDate',
                            'orderDate',
                            'status',
                            'currencyId',
                            'currencyRate',
                            'grandTotal'
                        ]
                    });
                    const documents = fastCopy(this.model.sourceDocuments) || [];

                    for (const item of items) {
                        const document = {};

                        document.id = item._id;
                        document.type = collection;
                        document.recordDate = item.recordDate;
                        document.issueDate = item.issueDate;
                        document.dueDate = item.dueDate;
                        document.amount = item.grandTotal;
                        document.status = item.status;

                        if (!_.isDate(document.issueDate)) {
                            document.issueDate = item.orderDate;
                        }

                        documents.push(document);
                    }

                    this.model.sourceDocuments = documents;
                    await this.handleChange(this.model, 'sourceDocuments');

                    this.$params('loading', false);
                }
            });
        },
        async handleClose() {
            this.$params('loading', true);

            const entries = await this.$collection('finance.entries').find({
                _id: {$in: this.model.entryIds},
                $select: ['status']
            });
            if (entries.some(entry => entry.status === 'draft')) {
                this.$program.message(
                    'error',
                    this.$t('The operation cannot be performed because there are draft payment items!')
                );
                this.$params('loading', false);

                return;
            }

            this.$params('loading', false);

            const isConfirmed = await new Promise(resolve => {
                this.$program.alert(
                    'confirm',
                    this.$t('You are about to close the document. Do you want continue?'),
                    confirmed => {
                        resolve(confirmed);
                    }
                );
            });

            if (!isConfirmed) return;

            this.model.status = 'closed';

            this.$refs.view.submitForm({status: 'closed'});
        },
        async handleCancel() {
            if (this.model.difference !== 0) {
                this.$program.message('error', this.$t('Difference must be equal to zero!'));

                return;
            }

            const isConfirmed = await new Promise(resolve => {
                this.$program.alert(
                    'confirm',
                    this.$t('You are about to cancel the document. Do you want continue?'),
                    confirmed => {
                        resolve(confirmed);
                    }
                );
            });

            if (!isConfirmed) return;

            this.model.status = 'canceled';

            this.$refs.view.submitForm({status: 'canceled'});
        },
        async beforeInit(model) {
            const company = this.$store.getters['session/company'];

            // Default partner type.
            if (!model.partnerType) {
                if (this.type === 'receipt') model.partnerType = 'customer';
                else if (this.type === 'payment') model.partnerType = 'vendor';
            }

            // When creating generate code.
            if (!this.$params('id')) {
                model.code = await this.generateCode(false);
            }
            this.generatedCode = model.code;

            // Default currency.
            if (!model.currencyId) model.currencyId = company.currencyId;

            // Get currency format.
            if (company.currencyId !== model.currencyId) {
                const currency = await this.$collection('kernel.currencies').findOne({
                    _id: model.currencyId
                });

                this.currencyFormat = {
                    currency: {
                        symbol: currency.symbol,
                        symbolPosition: currency.symbolPosition,
                        format: currency.symbolPosition === 'before' ? '%s %v' : '%v %s'
                    }
                };
            }

            // Get partner branches.
            if (!!model.partnerId) {
                const partner = await this.$collection('kernel.partners').findOne({
                    _id: model.partnerId,
                    $select: ['branchIds']
                });
                this.partnerBranchIds = (partner || {}).branchIds || [];
            }

            // Initialize payment term.
            let paymentTerm = null;
            if (!!model.paymentTermId) {
                if (_.isObject(model.paymentTerm)) {
                    paymentTerm = model.paymentTerm;
                } else {
                    paymentTerm = await this.$collection('finance.payment-terms').findOne({_id: model.paymentTermId});
                }
            } else {
                paymentTerm = await this.$collection('finance.payment-terms').findOne({system: true});

                model.paymentTermId = paymentTerm._id;
            }
            model.paymentTerm = paymentTerm;
            this.paymentTerm = paymentTerm;

            // Default description.
            if (!model.description) {
                if (this.type === 'receipt') {
                    model.description = this.$t('Receipts');
                } else {
                    model.description = this.$t('Payments');
                }
            }

            return model;
        },
        async beforeValidate(model) {
            model.type = this.type;

            return model;
        },
        async beforeSubmit(model) {
            model.type = this.type;
            model.paymentTerm = this.paymentTerm;

            if (this.model.code === this.generatedCode && !this.$params('id')) {
                model.code = await this.generateCode(true);
            }

            return model;
        },
        async handleChange(model, field) {
            const company = this.$store.getters['session/company'];
            const round = this.$app.roundNumber;
            const currencyPrecision = this.$setting('system.currencyPrecision');

            if (field === 'partnerType') {
                this.$set(this.model, 'partnerId', '');

                this.$nextTick(() => {
                    this.partnerIdKey = _.uniqueId('partnerIdKey');
                });

                const paymentTerm = await this.$collection('finance.payment-terms').findOne({system: true});
                this.model.paymentTermId = paymentTerm._id;
                this.model.paymentTerm = paymentTerm;
                this.model.entryIds = [];
                this.paymentTerm = paymentTerm;
                this.model.partnerGroupId = '';
            } else if (field === 'partnerId') {
                if (!!model.partnerId) {
                    const partner = await this.$collection('kernel.partners').findOne({
                        _id: model.partnerId,
                        $select: ['branchIds', 'paymentTermId']
                    });

                    this.partnerBranchIds = (partner || {}).branchIds || [];
                    if (this.partnerBranchIds.length > 0) {
                        this.model.branchId = this.partnerBranchIds[0];
                    } else {
                        this.model.branchId = '';
                    }

                    if (partner.paymentTermId) {
                        const paymentTerm = await this.$collection('finance.payment-terms').findOne({
                            _id: partner.paymentTermId
                        });
                        this.model.paymentTermId = paymentTerm._id;
                        this.model.paymentTerm = paymentTerm;
                        this.paymentTerm = paymentTerm;
                    } else {
                        const paymentTerm = await this.$collection('finance.payment-terms').findOne({system: true});
                        this.model.paymentTermId = paymentTerm._id;
                        this.model.paymentTerm = paymentTerm;
                        this.paymentTerm = paymentTerm;
                    }
                }

                this.model.entryIds = [];
            } else if (field === 'paymentTermId') {
                const paymentTerm = await this.$collection('finance.payment-terms').findOne({
                    _id: model.paymentTermId
                });

                this.model.paymentTermId = paymentTerm._id;
                this.model.paymentTerm = paymentTerm;
                this.paymentTerm = paymentTerm;
            } else if (field === 'branchId') {
                if (model.paymentTermId) {
                    const paymentTerm = await this.$collection('finance.payment-terms').findOne({
                        _id: model.paymentTermId
                    });
                    this.$set(this.model, 'paymentTerm', paymentTerm);
                    this.paymentTerm = paymentTerm;
                }
            } else if (field === 'currencyId') {
                if (model.currencyId) {
                    const currency = await this.$collection('kernel.currencies').findOne({
                        _id: model.currencyId,
                        $select: ['name', 'symbol', 'symbolPosition']
                    });

                    if (_.isObject(currency)) {
                        this.currencyFormat = {
                            currency: {
                                symbol: currency.symbol,
                                symbolPosition: currency.symbolPosition,
                                format: currency.symbolPosition === 'before' ? '%s %v' : '%v %s'
                            }
                        };

                        if (company.currencyId !== model.currencyId) {
                            this.model.currencyRate = await this.$convertCurrency({
                                from: currency.name,
                                to: company.currency.name,
                                value: 1,
                                options: {
                                    date: model.issueDate
                                }
                            });
                        } else {
                            this.model.currencyRate = 1;
                        }
                    }
                } else {
                    this.model.currencyId = company.currencyId;
                    this.model.currencyRate = 1;
                }
            } else if (field === 'issueDate') {
                const currency = await this.$collection('kernel.currencies').findOne({
                    _id: model.currencyId,
                    $select: ['name']
                });

                if (company.currencyId !== model.currencyId) {
                    this.model.currencyRate = await this.$convertCurrency({
                        from: currency.name,
                        to: company.currency.name,
                        value: 1,
                        options: {
                            date: model.issueDate
                        }
                    });
                } else {
                    this.model.currencyRate = 1;
                }
            } else if (field === 'amount') {
                this.model.difference = Math.max(model.amount - model.planned, 0);
            } else if (field === 'entryIds') {
                if (model.entryIds.length > 0) {
                    const entries = await this.$collection('finance.entries').find({
                        _id: {$in: model.entryIds},
                        status: {$ne: 'canceled'},
                        $select: ['status', 'dueDate', 'systemCurrencyTotal', 'scope']
                    });

                    if (entries.length > 0) {
                        let planned = 0;
                        let paid = 0;
                        let scope1Total = 0;

                        for (const entry of entries) {
                            planned += round(entry.systemCurrencyTotal / model.currencyRate, currencyPrecision);

                            if (entry.status === 'approved') {
                                paid += round(entry.systemCurrencyTotal / model.currencyRate, currencyPrecision);
                            }

                            if (entry.scope === '1') {
                                scope1Total += round(entry.systemCurrencyTotal / model.currencyRate, currencyPrecision);
                            }
                        }

                        const scope1Rate = planned > 0 ? (scope1Total / planned) * 100 : 100;

                        this.model.scopeParams = {
                            ...this.model.scopeParams,
                            scope1Rate,
                            scope2Rate: 100 - scope1Rate
                        };
                        this.model.planned = planned;
                        this.model.difference = round(Math.max(model.amount - planned, 0), currencyPrecision);
                        this.model.paid = paid;
                        this.model.remaining = planned - paid;
                        this.model.dueDate = _.orderBy(entries, ['dueDate'], ['desc'])[0].dueDate;
                    }
                } else {
                    this.model.scopeParams = {
                        ...this.model.scopeParams,
                        scope1Rate: 100,
                        scope2Rate: 0
                    };
                    this.model.planned = 0;
                    this.model.difference = model.amount;
                    this.model.paid = 0;
                    this.model.remaining = model.amount;
                    this.model.dueDate = this.model.recordDate;
                }
            } else if (field === 'partnerGroupId') {
                this.model.partnerId = '';
            }
        },

        async generateCode(save = false) {
            let numbering = null;

            if (this.type === 'receipt') {
                numbering = await this.$collection('kernel.numbering').findOne({
                    code: 'financeReceivableReceiptNumbering',
                    $select: ['_id'],
                    $disableInUseCheck: true,
                    $disableActiveCheck: true,
                    $disableSoftDelete: true
                });
            } else {
                numbering = await this.$collection('kernel.numbering').findOne({
                    code: 'financePaymentReceiptNumbering',
                    $select: ['_id'],
                    $disableInUseCheck: true,
                    $disableActiveCheck: true,
                    $disableSoftDelete: true
                });
            }

            return await this.$rpc('kernel.common.request-number', {numberingId: numbering._id, save});
        },
        updatePartnerSelectParams(params) {
            params.model = {type: this.model.partnerType};

            return params;
        },
        updateContactSelectParams(params, type) {
            if (type === 'create' || type === 'detail') {
                params.model = {type: 'contact'};
                params.partnerId = this.model.partnerId;
            } else if (type === 'list') {
                params.filters = {partnerId: this.model.partnerId, type: 'contact'};
            }

            return params;
        },
        updateEntryIdsParams(params, action) {
            params.type = this.type;

            if (action === 'create' || action === 'detail') {
                params.currencyFormat = this.currencyFormat;
                params.paymentTerm = this.paymentTerm;
                params.paymentTermId = this.model.paymentTermId;
                params.amount = this.model.planned > 0 ? this.model.difference : this.model.amount;
                params.documentNo = this.model.documentNo;
                params.branchId = this.model.branchId;
                params.currencyId = this.model.currencyId;
                params.recordDate = this.model.recordDate;
                params.issueDate = this.model.issueDate;
                params.dueDate = this.model.dueDate;
                params.reference = this.model.reference;
                params.description = this.model.description;
                params.transactionType = this.model.transactionType;
                params.guaranteeId = this.model.guaranteeId;
                params.overriddenPartnerAccountId = this.model.overriddenPartnerAccountId;
                params.scope = this.model.scope;

                if (action === 'create') {
                    const model = {};

                    model.receiptId = this.$params('id');
                    model.partnerType = this.model.partnerType;
                    model.partnerId = this.model.partnerId;
                    model.contactPersonId = this.model.contactPersonId;
                    model.paymentTermId = this.model.paymentTermId;
                    model.amount = this.model.planned > 0 ? this.model.difference : this.model.amount;
                    model.documentNo = this.model.documentNo;
                    model.branchId = this.model.branchId;
                    model.currencyId = this.model.currencyId;
                    model.currencyRate = this.model.currencyRate;
                    model.recordDate = this.model.recordDate;
                    model.issueDate = this.model.issueDate;
                    model.dueDate = this.model.dueDate;
                    model.reference = this.model.reference;
                    model.description = this.model.description;
                    model.transactionType = this.model.transactionType;
                    model.scope = this.model.scope;
                    model.purpose = this.model.purpose;
                    model.guaranteeId = this.model.guaranteeId;
                    model.overriddenPartnerAccountId = this.model.overriddenPartnerAccountId;
                    model.relatedDocuments = this.model.relatedDocuments || [];

                    params.model = model;
                }
            }

            return params;
        }
    },

    async created() {
        this.$params('loading', true);

        const company = this.$store.getters['session/company'];
        this.currencyFormat = {
            currency: {
                symbol: company.currency.symbol,
                symbolPosition: company.currency.symbolPosition,
                format: company.currency.symbolPosition === 'before' ? '%s %v' : '%v %s'
            }
        };
        this.currencies = await this.$collection('kernel.currencies').find();
        this.tagOptions = (await this.$collection('finance.tags').find({})).map(tag => ({
            value: tag._id,
            label: tag.name
        }));
        this.initialized = true;

        this.$params('loading', false);
    }
};
</script>

<style lang="scss">
//noinspection CssUnknownTarget
@import 'core';

.finance-receipts-detail {
    .scope-rate {
        display: flex;
        flex-flow: row nowrap;
        align-items: center;
        height: 100%;
        margin-right: 5px;
        font-size: 15px;
        font-weight: $font-weight-bold;
        color: $text-color-lighter;
        user-select: text;

        i {
            margin-right: 7px;
            color: $primary;
            user-select: none;
        }
    }
}
</style>
