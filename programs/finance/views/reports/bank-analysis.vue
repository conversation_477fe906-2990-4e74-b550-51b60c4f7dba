<template>
    <ui-view type="content"
             :title="'Bank Analysis'|t"
             class="finance-reports-bank-analysis"
             :class="{'has-graph': hasGraph}"
             v-if="initialized">
        <template slot="top-panel">
            <ui-scope ref="scope"
                      id="finance.reports.bank-analysis"
                      :view-modes="['graph', 'table']"
                      default-view-mode="graph"
                      :filters="scopeApplicableFilters"
                      @changed="handleScopeChange"/>
        </template>

        <div class="report-container">
            <div class="report-graph" v-if="hasGraph">
                <ui-chartx :key="chartKey" :options="chartOptions"/>
            </div>
            <div class="report-table">
                <ui-table ref="table"
                          id="finance.reports.bank-analysis"
                          :items="items"
                          :columns="columns"
                          :summary-row="summaryRow"
                          :enable-selection="false"/>
            </div>
        </div>
    </ui-view>
</template>

<script>
import _ from 'lodash';
import {rawMongoQuery} from 'framework/helpers';

export default {
    data: () => ({
        viewMode: 'graph',
        scopeQuery: {},
        report: [],
        items: null,
        currencies: [],
        chartKey: _.uniqueId('chartKey_'),
        initialized: false
    }),

    computed: {
        hasGraph() {
            return this.viewMode === 'graph';
        },
        chartOptions() {
            const self = this;
            const report = this.report;

            return {
                chart: {
                    type: 'column',
                    events: {
                        drilldown(e) {
                            const chart = this;

                            if (e.seriesOptions) {
                                return;
                            }

                            self.$params('loading', true);

                            const date = self.$datetime.fromJSDate(e.point.payload.date);
                            self.drilldownInProgress = true;
                            self.$refs.scope.apply({
                                type: 'filter',
                                field: 'issueDate',
                                operator: 'in-range',
                                value: {
                                    start: date.startOf('month').toJSDate(),
                                    end: date.endOf('month').toJSDate()
                                }
                            });
                            setTimeout(async () => {
                                const report = await self.$rpc('finance.get-bank-analysis-report', {
                                    query: self.scopeQuery,
                                    drilldown: true
                                });
                                const items = [];

                                for (const r of report) {
                                    const date = self.$datetime.local().set({
                                        year: r.year,
                                        month: r.month,
                                        day: r.day
                                    }).startOf('day');
                                    const dayFormatted = date.toFormat('LLL dd');
                                    const dayFormattedLong = date.toFormat('LLLL dd');

                                    items.push({
                                        ...r,
                                        date: date.toJSDate(),
                                        dayFormatted,
                                        dayFormattedLong
                                    });
                                }

                                chart.addSeriesAsDrilldown(e.point, {
                                    name: self.$t('Balance'),
                                    data: items.map(item => [item.dayFormatted, item.balance])
                                });

                                self.$params('loading', false);
                            }, 50);
                        },
                        drillup() {
                            self.drilldownInProgress = true;
                            self.$refs.scope.remove('filter', 'issueDate');
                        }
                    }
                },
                title: false,
                xAxis: {
                    type: 'category'
                    // categories: report.map(r => r.monthFormattedLong)
                },
                yAxis: {
                    title: false
                },
                legend: false,
                plotOptions: {
                    series: {
                        borderWidth: 0
                        // dataLabels: {
                        //     enabled: true,
                        //     formatter() {
                        //         return self.$format(this.y, 'currency');
                        //     }
                        // }
                    }
                },
                tooltip: {
                    pointFormatter() {
                        return `<span style="color:${this.color}">●</span> ${this.series.name}: <b>${self.$format(this.y, 'currency')}</b><br/>`;
                    }
                },
                series: [{
                    name: this.$t('Balance'),
                    data: report.map(r => ({
                        name: r.monthFormattedLong,
                        y: r.balance,
                        payload: r,
                        drilldown: true
                    }))
                }],
                drilldown: {
                    activeAxisLabelStyle: {
                        color: this.$app.colors.text,
                        fontWeight: 'normal',
                        textDecoration: 'none'
                    },
                    activeDataLabelStyle: {
                        color: this.$app.colors.text,
                        textDecoration: 'none'
                    },
                    series: []
                }
            };
        },
        filters() {
            let filters = _.cloneDeep(this.scopeQuery);

            return filters;
        },
        columns() {
            const company = this.$store.getters['session/company'];

            return [
                {
                    field: 'name',
                    label: 'Name',
                    sort: 'asc'
                },
                {
                    field: 'branchCode',
                    label: 'Branch code',
                    width: 150,
                    hidden: !this.$setting('system.multiBranch'),
                    visible: false
                },
                {
                    field: 'branchName',
                    label: 'Branch name',
                    hidden: !this.$setting('system.multiBranch')
                },
                {
                    field: 'currencyName',
                    label: 'Currency',
                    hidden: !this.$setting('system.multiCurrency'),
                    width: 120
                },
                {
                    field: 'endingBalance',
                    label: 'Ending balance',
                    format: 'currency',
                    width: 150
                },
                {
                    field: 'endingBalanceFC',
                    label: 'Ending balance (FC)',
                    format: 'currency',
                    formatOptions: data => {
                        let options = {currency: {}};

                        if (_.isPlainObject(data) && _.isString(data.currencyId)) {
                            const currency = this.currencies.find(c => c._id === data.currencyId);

                            if (!!currency) {
                                options.currency.symbol = currency.symbol;
                                options.currency.format = currency.symbolPosition === 'before' ? '%s %v' : '%v %s';
                            }
                        }

                        return options;
                    },
                    visible: false,
                    width: 150
                },
                {
                    field: 'transactionBalance',
                    label: 'Transaction balance',
                    format: 'currency',
                    width: 150
                },
                {
                    field: 'transactionBalanceFC',
                    label: 'Transaction balance (FC)',
                    format: 'currency',
                    formatOptions: data => {
                        let options = {currency: {}};

                        if (_.isPlainObject(data) && _.isString(data.currencyId)) {
                            const currency = this.currencies.find(c => c._id === data.currencyId);

                            if (!!currency) {
                                options.currency.symbol = currency.symbol;
                                options.currency.format = currency.symbolPosition === 'before' ? '%s %v' : '%v %s';
                            }
                        }

                        return options;
                    },
                    visible: false,
                    width: 150
                },
                {
                    field: 'balance',
                    label: 'Balance',
                    format: 'currency',
                    width: 150
                },
                {
                    field: 'balanceFC',
                    label: 'Balance (FC)',
                    format: 'currency',
                    formatOptions: data => {
                        let options = {currency: {}};

                        if (_.isPlainObject(data) && _.isString(data.currencyId)) {
                            const currency = this.currencies.find(c => c._id === data.currencyId);

                            if (!!currency) {
                                options.currency.symbol = currency.symbol;
                                options.currency.format = currency.symbolPosition === 'before' ? '%s %v' : '%v %s';
                            }
                        }

                        return options;
                    },
                    visible: false,
                    width: 150
                }
            ];
        },
        scopeApplicableFilters() {
            const self = this;

            const scopeOptions = [
                {value: '1', label: this.$setting('system.scope1Label')},
                {value: '2', label: this.$setting('system.scope2Label')}
            ];

            return [
                // Pre-defined
                {code: 'today', label: 'Today', query: 'issueDate|today'},
                {code: 'yesterday', label: 'Yesterday', query: 'issueDate|yesterday'},
                {code: 'thisWeek', label: 'This week', query: 'issueDate|thisWeek'},
                {code: 'lastWeek', label: 'Last week', query: 'issueDate|lastWeek'},
                {code: 'thisMonth', label: 'This month', query: 'issueDate|thisMonth'},
                {code: 'lastMonth', label: 'Last month', query: 'issueDate|lastMonth'},
                {code: 'thisQuarter', label: 'This quarter', query: 'issueDate|thisQuarter'},
                {code: 'lastQuarter', label: 'Last quarter', query: 'issueDate|lastQuarter'},

                // Filters.
                {
                    field: 'journalId',
                    label: 'Bank',
                    collection: 'accounting.journals',
                    filters: {
                        type: 'bank',
                        $sort: {name: 1}
                    }
                },
                {
                    field: 'issueDate',
                    code: 'issueDate',
                    label: 'Issue date',
                    operator: 'in-range',
                    allowedOperators: ['in-range'],
                    type: 'date'
                },
                {
                    field: 'branchId',
                    label: 'Branch office',
                    collection: 'kernel.branches',
                    filters: {
                        _id: {$in: this.$user.branchIds},
                        $sort: {name: 1}
                    },
                    condition() {
                        return self.$setting('system.multiBranch');
                    }
                },
                {
                    field: 'financialProjectId',
                    label: 'Project',
                    collection: 'kernel.financial-projects',
                    extraFields: ['code'],
                    filters: {$sort: {name: 1}},
                    template: '{{code}} - {{name}}'
                },
                {field: 'reference', label: 'Reference'},
                {field: 'description', label: 'Description'},
                {
                    field: 'scope',
                    label: 'Scope',
                    condition() {
                        return self.$setting('system.scopes');
                    },
                    valueLabels: scopeOptions
                },
                {
                    field: 'currencyId',
                    label: 'Currency',
                    collection: 'kernel.currencies',
                    filters: {$sort: {name: 1}},
                    condition() {
                        return self.$setting('system.multiCurrency');
                    }
                }
            ];
        }
    },

    methods: {
        async handleScopeChange(model) {
            this.viewMode = model.viewMode;

            if (JSON.stringify(model.query) !== JSON.stringify(this.scopeQuery)) {
                this.scopeQuery = model.query;

                if (!!this.drilldownInProgress) {
                    this.drilldownInProgress = false;

                    await this.getItems();
                } else {
                    this.$params('loading', true);

                    this.chartKey = _.uniqueId('chartKey_');

                    await this.getReport();
                    await this.getItems();

                    this.$params('loading', false);
                }
            }
        },
        async summaryRow() {
            const items = this.items || [];

            return {
                rowCount: items.length,
                endingBalance: _.sumBy(items, 'endingBalance'),
                transactionBalance: _.sumBy(items, 'transactionBalance'),
                balance: _.sumBy(items, 'balance')
            };
        },
        async getReport() {
            const report = await this.$rpc('finance.get-bank-analysis-report', {query: this.scopeQuery});
            const items = [];

            for (const r of report) {
                const date = this.$datetime.local().set({
                    year: r.year,
                    month: r.month
                }).startOf('month');
                const monthFormatted = date.toFormat('yyyy LLL');
                const monthFormattedLong = date.toFormat('yyyy LLLL');

                items.push({
                    ...r,
                    date: date.toJSDate(),
                    monthFormatted,
                    monthFormattedLong
                });
            }

            this.report = items;
        },
        async getItems() {
            this.items = await this.$rpc('finance.get-bank-analysis-items', this.scopeQuery);
        }
    },

    async created() {
        this.getReportDebounced = _.debounce(this.getReport, 300, {leading: false, trailing: true});
        this.getItemsDebounced = _.debounce(this.getItems, 300, {leading: false, trailing: true});

        this.$collection('accounting.transactions').on('all', this.getReportDebounced);
        this.$collection('accounting.transactions').on('all', this.getItemsDebounced);

        this.$params('loading', true);

        this.currencies = await this.$collection('kernel.currencies').find();
        await this.getReport();
        await this.getItems();
        this.initialized = true;

        this.$params('loading', false);
    },

    beforeDestroy() {
        this.$collection('accounting.transactions').removeListener('all', this.getReportDebounced);
        this.$collection('accounting.transactions').removeListener('all', this.getItemsDebounced);
    }
};
</script>

<style lang="scss">
//noinspection CssUnknownTarget
@import "core";

.finance-reports-bank-analysis {
    .report-container {
        position: relative;
        width: 100%;
        height: 100%;
        overflow: hidden;
    }

    .report-graph {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 220px;
        overflow: hidden;
    }

    .report-table {
        width: 100%;
        height: 100%;
        overflow: hidden;
    }

    &.has-graph {
        .report-container {
            padding-top: 220px;
        }
    }
}
</style>
