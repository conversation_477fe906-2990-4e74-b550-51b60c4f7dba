import _ from 'lodash';

export default {
    name: 'cheque-histories',
    title: 'Cheque Histories',
    collection: 'finance.cheques-history',
    async query(app) {
        return {};
    },
    fields: [
        // General.
        {type: 'string', name: 'status', label: 'Status'},
        {type: 'string', name: 'statusValue', label: 'Status value'},
        {type: 'string', name: 'code', label: 'Code'},
        {type: 'string', name: 'no', label: 'No'},
        {type: 'string', name: 'chequeNumber', label: 'Cheque Number'},
        {type: 'string', name: 'chequeTrackingCode', label: 'Cheque Tracking Code'},
        {type: 'string', name: 'financialIdentifier', label: 'Financial Identifier'},
        {type: 'string', name: 'firstWrittenBy', label: 'First Written By'},
        {type: 'string', name: 'city', label: 'City'},
        {type: 'string', name: 'district', label: 'District'},
        {type: 'string', name: 'reference', label: 'Reference'},
        {type: 'string', name: 'description', label: 'Description'},
        {type: 'string', name: 'editedBy', label: 'Edited by'},

        {type: 'decimal', name: 'amount', label: 'Amount'},
        {type: 'decimal', name: 'currencyRate', label: 'Currency rate'},

        {type: 'date', name: 'dueDate', label: 'Due Date'},
        {type: 'date', name: 'recordDate', label: 'Record Date'},
        {type: 'date', name: 'issueDate', label: 'Issue Date'},

        // Relations.
        {type: 'string', name: 'journalName', label: 'Journal Name'},
        {type: 'string', name: 'currencyName', label: 'Currency Name'},
        {type: 'string', name: 'partnerCode', label: 'Partner code'},
        {type: 'string', name: 'partnerName', label: 'Partner name'},
        {type: 'string', name: 'paymentAccountName', label: 'Payment Account Name'},

        // IDs.
        {type: 'string', name: 'chequeId', label: 'Cheque ID'},
        {type: 'string', name: 'journalId', label: 'Journal ID'},
        {type: 'string', name: 'currencyId', label: 'Currency ID'},
        {type: 'string', name: 'partnerId', label: 'Partner ID'},
        {type: 'string', name: 'journalEntryId', label: 'Journal entry ID'},
        {type: 'string', name: 'paymentAccountId', label: 'Payment Account ID'}
    ],

    async bulkDocumentExtra(app, schema, documents) {
        const journalsMap = {};
        const partnersMap = {};
        const chequesMap = {};

        let journalIds = [];
        let partnerIds = [];
        let chequeIds = [];

        for (const document of documents) {
            if (document.journalId) journalIds.push(document.journalId);
            if (document.partnerId) partnerIds.push(document.partnerId);
            if (document.paymentAccountId) journalIds.push(document.paymentAccountId);
            if (document.chequeId) chequeIds.push(document.chequeId);
        }

        journalIds = _.uniq(journalIds);
        partnerIds = _.uniq(partnerIds);
        chequeIds = _.uniq(chequeIds);

        if (chequeIds.length > 0) {
            const cheques = await app.collection('finance.cheques').find({
                _id: {$in: chequeIds},
                $select: ['_id', 'chequeNumber', 'chequeTrackingCode', 'financialIdentifier', 'firstWrittenBy', 'city', 'district'],
                $disableSoftDelete: true,
                $disableActiveCheck: true
            });

            for (const cheque of cheques) {
                chequesMap[cheque._id] = cheque;
            }
        }

        if (journalIds.length > 0) {
            const journals = await app.collection('accounting.journals').find({
                _id: {$in: journalIds},
                $select: ['_id', 'code', 'name'],
                $disableSoftDelete: true,
                $disableActiveCheck: true
            });

            for (const journal of journals) {
                journalsMap[journal._id] = journal;
            }
        }

        if (partnerIds.length > 0) {
            const partners = await app.collection('kernel.partners').find({
                _id: {$in: partnerIds},
                $select: ['_id', 'code', 'name'],
                $disableSoftDelete: true,
                $disableActiveCheck: true
            });

            for (const partner of partners) {
                partnersMap[partner._id] = partner;
            }
        }

        return {journalsMap, partnersMap, chequesMap};
    },
    async data(app, schema, document, {journalsMap, partnersMap, banksMap, chequesMap}) {
        const data = {};

        const statusOptions = [
            {value: 'in-bill-case', label: 'In bill case'},
            {value: 'deposited', label: 'Deposited'},
            {value: 'endorsed', label: 'Endorsed'},
            {value: 'charged', label: 'Charged'},
            {value: 'denied', label: 'Denied'},
            {value: 'refunded', label: 'Refunded'},
            {value: 'sent-in-the-bailiffs', label: 'Sent in the bailiffs'},
            {value: 'given-guarantee', label: 'Given guarantee'},
            {value: 'produced', label: 'Produced'},
            {value: 'paid', label: 'Paid'}
        ];
        const statusLabel = (statusOptions.find(option => option.value === document.status) || {}).label || '';
        if (!!statusLabel) {
            data.status = app.translate(statusLabel);
        } else {
            data.status = app.translate('In bill case');
        }
        data.statusValue = document.status;

        if (document.chequeId && chequesMap[document.chequeId]) {
            const cheque = chequesMap[document.chequeId];
            data.chequeNumber = cheque.chequeNumber;
            data.chequeTrackingCode = cheque.chequeTrackingCode;
            data.financialIdentifier = cheque.financialIdentifier;
            data.firstWrittenBy = cheque.firstWrittenBy;
            data.city = cheque.city;
            data.district = cheque.district;
        }

        data.chequeId = document._id;
        data.code = document.code;
        data.reference = document.reference;
        data.description = document.description;
        data.paymentSlipNo = document.paymentSlipNo;
        data.scope = document.scope;

        data.dueDate = document.dueDate;
        data.recordDate = document.recordDate;
        data.issueDate = document.issueDate;
        data.paymentSlipDate = document.paymentSlipDate;

        data.endorsable = document.endorsable;

        data.amount = document.amount ?? 0;
        data.amountSC = document.amountSC ?? 0;

        if (!!document.journalId) {
            data.journalId = document.journalId;
            const journal = journalsMap[document.journalId];

            if (!!journal) {
                data.journalName = journal.name;
                data.journalType = journal.type;
            }
        }

        if (!!document.branchId) {
            data.branchId = document.branchId;

            if (_.isObject(document.branch)) {
                data.branchName = document.branch.name;
            }
        }

        if (!!document.currencyId) {
            data.currencyId = document.currencyId;

            if (_.isObject(document.currency)) {
                data.currencyName = document.currency.name;
            }
        }

        if (!!document.partnerId) {
            data.partnerId = document.partnerId;

            if (partnersMap[document.partnerId]) {
                data.partnerCode = partnersMap[document.partnerId].code;
                data.partnerName = partnersMap[document.partnerId].name;
            }
        }

        if (!!document.endorsedPartnerId) {
            data.endorsedPartnerId = document.endorsedPartnerId;

            if (partnersMap[document.endorsedPartnerId]) {
                data.endorsedPartnerCode = partnersMap[document.endorsedPartnerId].code;
                data.endorsedPartnerName = partnersMap[document.endorsedPartnerId].name;
            }
        }

        if (!!document.bankId) {
            data.bankId = document.bankId;

            if (banksMap[document.bankId]) {
                data.bankName = banksMap[document.bankId].name;
                data.bankCode = banksMap[document.bankId].code;
            }
        }

        if (!!document.paymentAccountId) {
            data.paymentAccountId = document.paymentAccountId;

            if (journalsMap[document.paymentAccountId]) {
                data.paymentAccountName = journalsMap[document.paymentAccountId].name;
            }
        }

        return data;
    }
};
