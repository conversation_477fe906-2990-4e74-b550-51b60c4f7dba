import path from 'path';
import fs from 'fs-extra';
import _ from 'lodash';

export default {
    name: 'request',
    title: 'Requests',
    contentType: 'html',
    outputType: 'pdf',
    async getLocale(app, recordId) {
        if (!!recordId) {
            const document = await app.collection('purchase.requests').findOne({
                _id: recordId,
                $select: ['partnerId'],
                $disableSoftDelete: true
            });

            if (!!document) {
                const partner = await app.collection('kernel.partners').findOne({
                    _id: document.partnerId || document.relatedPartnerId,
                    $select: ['languageId'],
                    $disableActiveCheck: true,
                    $disableSoftDelete: true
                });

                if (!!partner) {
                    const language = await app.collection('kernel.languages').findOne({
                        _id: partner.languageId,
                        $select: ['isoCode'],
                        $disableActiveCheck: true,
                        $disableSoftDelete: true
                    });

                    if (!!language) {
                        return language.isoCode;
                    }
                }
            }
        }

        const company = await app.collection('kernel.company').findOne({});
        return company.language.isoCode;
    },
    async qr(app, recordId = '', data = {}) {
        return {
            text: `purchase.purchase.requests.detail/${recordId}`
        };
    },
    async record(app, recordId, params) {
        const round = n => app.roundNumber(n, 2);
        const d = await app.collection('purchase.requests').findOne({
            _id: recordId,
            $populate: [
                {
                    field: 'relatedPartner',
                    query: {
                        $select: [
                            'code',
                            'name',
                            'isCompany',
                            'legalName',
                            'email',
                            'languageId',
                            'currencyId',
                            'website',
                            'identity',
                            'tin',
                            'taxDepartment',
                            'phone',
                            'address'
                        ]
                    }
                }
            ],
            $disableSoftDelete: true
        });

        const {locale} = params;
        const t = (text, data = {}) => app.translate(text, locale, data);
        const document = {};

        const currency = await app.collection('kernel.currencies').findOne({
            _id: d.currencyId,
            $disableActiveCheck: true,
            $disableSoftDelete: true
        });
        const formatOptions = {
            currency: {
                symbol: currency.symbol,
                format: currency.symbolPosition === 'before' ? '%s %v' : '%v %s'
            },
            useOutputPrecision: true
        };
        const f = (amount, type) => app.format(amount, type, formatOptions);

        document.locale = locale;
        document.status = t(d.status);
        document.code = d.code;
        document.reference = d.reference;
        document.currency = (d.currency || {}).name;
        document.currencyRate = f(d.currencyRate, 'decimal');
        document.recordDate = d.recordDate ? f(d.recordDate, 'datetime') : '';
        document.expiryDate = d.expiryDate ? f(d.expiryDate, 'date') : '';
        document.requiredDate = d.requiredDate ? f(d.requiredDate, 'date') : '';
        document.issueDate = d.issueDate ? f(d.issueDate, 'date') : '';
        document.note = (d.note || '').split('\r\n').join('<br/>').split('\n').join('<br/>');

        document.subTotal = f(d.subTotal, 'total');
        document.taxTotal = f(d.taxTotal, 'total');
        document.grandTotal = f(d.grandTotal, 'total');


        document.appliedTaxes = (d.appliedTaxes || []).map(tax => ({
            name: tax.name,
            label: tax.label,
            percentage: f(tax.amount, 'percentage'),
            amount: f(tax.appliedAmount, 'total')
        }));

        if (!!d.relatedPartner) {
            const partner = {};
            partner.code = d.relatedPartner.code;
            partner.isCompany = d.relatedPartner.isCompany;
            partner.name = d.relatedPartner.name;
            partner.legalName = d.relatedPartner.legalName;
            partner.email = d.relatedPartner.email;
            partner.website = d.relatedPartner.website;
            partner.identity = d.relatedPartner.identity;
            partner.tin = d.relatedPartner.tin;
            partner.taxDepartment = d.relatedPartner.taxDepartment;
            partner.phone = d.relatedPartner.phone;

            partner.language = (
                (await app.collection('kernel.languages').findOne({_id: d.relatedPartner.languageId})) || {}
            ).name;
            partner.currency = (
                (await app.collection('kernel.currencies').findOne({_id: d.relatedPartner.currencyId})) || {}
            ).name;

            if(d.relatedPartner.address) {
                partner.address = d.relatedPartner.address.address;
                partner.country = (
                    (await app.collection('kernel.countries').findOne({_id: d.relatedPartner.address.countryId})) || {}
                ).name;
                partner.city = d.relatedPartner.address.city;
                partner.district = d.relatedPartner.address.district;
                partner.subDistrict = d.relatedPartner.address.subDistrict;
                partner.street = d.relatedPartner.address.street;
                partner.doorNumber = d.relatedPartner.address.doorNumber;
                partner.apartmentNumber = d.relatedPartner.address.apartmentNumber;
                partner.postalCode = d.relatedPartner.address.postalCode;
            }
            document.partner = partner;
            document.partnerName = partner ? (partner.isCompany ? partner.legalName : partner.name) : '';
        }

        const units = await app.collection('kernel.units').find({$disableSoftDelete: true, $disableActiveCheck: true});
        const unitsMap = _.keyBy(units, '_id');
        let totalQty = 0;
        const items = [];

        for (const i of d.items || []) {
            const item = {};

            const tax = await app.collection('kernel.taxes').findOne({_id: i.taxId, $disableActiveCheck: true, $disableSoftDelete: true});
            const unit = unitsMap[i.unitId];
            const baseUnit = unitsMap[i.baseUnitId];

            item.id = i.id;
            item.productCode = i.productCode;
            item.productDefinition = i.productDefinition;
            item.productType = i.productType === 'service' ? t('Service product') : t('Stockable product');
            item.description = (i.description || '').split('\r\n').join('<br/>').split('\n').join('<br/>');
            item.barcode = i.barcode || '';

            item.warehouse = (
                (await app.collection('inventory.warehouses').findOne({
                    _id: i.warehouseId,
                    $disableActiveCheck: true,
                    $disableSoftDelete: true
                })) || {}
            ).name;

            item.quantity = f(i.quantity, 'unit');
            item.supplierCatalogNo = i.supplierCatalogNo || '';
            item.baseQuantity = f(i.baseQuantity, 'unit');
            item.unit = (unit || {}).name;
            item.unitSymbol = (unit || {}).symbol;
            item.baseUnit = (baseUnit || {}).name;
            item.baseUnitSymbol = (baseUnit || {}).symbol;
            item.unitPrice = f(i.unitPrice, 'unit-price');
            item.realUnitPrice = f((i.realTotal || 0) / (i.quantity || 1), 'unit-price');
            item.discount = f(i.discount, 'percentage');
            item.unitPriceAfterDiscount = f(i.unitPriceAfterDiscount, 'unit-price');
            item.tax = (tax || {}).label || (tax || {}).name;
            item.taxPercentage = f((tax || {}).amount, 'percentage');
            item.taxTotal = f(i.taxTotal, 'total');
            item.grossUnitPriceAfterDiscount = f(i.grossUnitPriceAfterDiscount, 'total');
            item.realTotal = f(i.realTotal, 'total');
            item.total = f(i.total, 'total');
            item.grossTotal = f(i.grossTotal, 'total');

            item.requiredDate =  f(i.requiredDate, 'date')


            item.availableStock = i.warehouseAvailableQuantity || 0;
            item.remainingStock = item.availableStock - i.baseQuantity;
            item.stockStatusPercentage = (item.availableStock > 0) ? Math.min(Math.max(round((item.remainingStock / item.availableStock) * 100, 'percentage'), 0), 100) : 0;
            item.stockStatusColor = 'red';
            if (item.remainingStock >= 0) {
                item.stockStatusColor = 'green';
            } else if (item.availableStock > 0 && item.remainingStock < 0) {
                item.stockStatusColor = 'orange';
            }
            items.push(item);
            totalQty += i.baseQuantity;
        }
        document.items = items;
        document.totalQty = totalQty;

        return document;
    },
    async sample(app, params) {
        const {locale} = params;
        const t = (text, data = {}) => app.translate(text, locale, data);
        const f = (amount, type) => app.format(amount, type);
        const company = await app.collection('kernel.company').findOne({});
        const statusOptions = [
            {value: 'draft', label: 'Draft'},
            {value: 'payment-planned', label: 'Payment Planned', color: 'success'},
            {value: 'converted-to-order', label: 'Converted To Order'},
            {value: 'converted-to-invoice', label: 'Converted To Invoice'},
            {value: 'canceled', label: 'Canceled'}
        ];
        const document = {};

        document.locale = locale;
        document.status = t(statusOptions.find(o => o.value === 'draft').label);
        document.code = 'PQ00000001';
        document.partnerType = 'vendor';
        document.reference = 'Test ref';
        document.documentType = (await app.collection('purchase.document-types').findOne({})).name;
        document.partnerGroup = (await app.collection('kernel.partner-groups').findOne({})).name;
        document.contactPerson = 'Test contact';
        document.currency = company.currency.name;
        document.currencyRate = 1;
        document.recordDate = f(app.datetime.local().toJSDate(), 'datetime');
        document.quotationDate = f(app.datetime.local().toJSDate(), 'date');
        document.scheduledDate = f(app.datetime.local().toJSDate(), 'date');
        document.expiryDate = f(app.datetime.local().plus({days: 7}).toJSDate(), 'date');
        document.note = `
Lorem ipsum dolor sit amet, consectetur adipisicing elit. Magnam nihil placeat praesentium
sed? Accusantium aliquid architecto doloribus fuga illum maxime molestias mollitia nisi,
officiis quidem ratione, recusandae, repudiandae tenetur voluptatum? Lorem ipsum dolor sit
amet, consectetur adipisicing elit. Autem dignissimos doloribus nam omnis similique? Ab,
aperiam aut dicta facilis hic id ipsa, molestiae neque officiis omnis quam quibusdam
repudiandae suscipit!
        `
            .trim()
            .split('\r\n')
            .join('<br/>')
            .split('\n')
            .join('<br/>');

        // Totals.
        document.subTotal = f(1120, 'total');
        document.discount = f(10, 'percentage');
        document.discountAmount = f(112.0, 'total');
        document.subTotalAfterDiscount = f(1008.0, 'total');
        document.taxTotal = f(176.94, 'total');
        document.rounding = f(0.15, 'total');
        document.grandTotal = f(1185.09, 'total');
        document.appliedTaxes = [
            {
                label: 'KDV %18',
                percentage: f(18, 'percentage'),
                amount: f(173.34, 'total')
            },
            {
                label: 'KDV %8',
                percentage: f(8, 'percentage'),
                amount: f(3.6, 'total')
            }
        ];

        const fields = [
            {name: 'test1', label: 'Test 1', value: 'test 1', group: 'Group 1'},
            {name: 'test2', label: 'Test 2', value: 'test 2', group: 'Group 1'},
            {name: 'test3', label: 'Test 3', value: 'test 3', group: 'Group 1'},
            {name: 'test4', label: 'Test 4', value: 'test 4', group: 'Group 2'},
            {name: 'test5', label: 'Test 5', value: 'test 5', group: 'Group 2'},
            {name: 'test6', label: 'Test 6', value: 'test 6', group: 'Group 2'}
        ];
        document.fields = fields;

        const groupedFields = [];
        if (fields.every(f => !!f.group)) {
            const grouped = _.groupBy(fields, 'group');

            for (const group of Object.keys(grouped)) {
                groupedFields.push({
                    group,
                    fields: grouped[group]
                });
            }
        }
        document.groupedFields = groupedFields;

        // Partner.
        const partner = {};
        partner.code = 'V00000001';
        partner.isCompany = true;
        partner.name = 'EnterSoft';
        partner.legalName = 'EnterSoft Inc';
        partner.email = '<EMAIL>';
        partner.language = 'Turkish';
        partner.currency = company.currency.name;
        partner.website = 'https://entersoft.com.tr';
        partner.identity = '';
        partner.tin = '3334445556';
        partner.taxDepartment = 'KARTAL';
        partner.phone = '+90(850) 454 45 45';
        partner.address = 'Cevizli Mahallesi, Tugay Yolu Caddesi, No: 20/A/10, 34846 Maltepe/İstanbul Türkiye';
        partner.country = (await app.collection('kernel.countries').findOne({code: 'TR'})).name;
        partner.city = 'İstanbul';
        partner.district = 'Maltepe';
        partner.subDistrict = 'Cevizli Mahallesi';
        partner.street = 'Tugay Yolu Caddesi';
        partner.doorNumber = '20/A';
        partner.apartmentNumber = '10';
        partner.postalCode = '34846';
        document.partner = partner;
        document.paymentTerm = 'Test payment term';

        // Delivery address.
        const deliveryAddress = {};
        deliveryAddress.country = (await app.collection('kernel.countries').findOne({code: 'TR'})).name;
        deliveryAddress.city = 'İstanbul';
        deliveryAddress.district = 'Maltepe';
        deliveryAddress.subDistrict = 'Cevizli Mahallesi';
        deliveryAddress.street = 'Tugay Yolu Caddesi';
        deliveryAddress.doorNumber = '20/A';
        deliveryAddress.apartmentNumber = '10';
        deliveryAddress.postalCode = '34846';
        document.deliveryAddress = deliveryAddress;

        document.invoiceAddress = deliveryAddress;

        document.partnerName = document.partner.name;
        document.partnerAddress = deliveryAddress.address;

        document.organization = 'Default purchases organization';
        document.purchaseManager = 'Tarık AKTAŞ';
        document.purchaseManagerPosition = 'Sales manager';
        document.purchaseManagerOccupation = 'Test Occupation';
        document.purchaseManagerEmail = '<EMAIL>';
        document.purchaseManagerPhone = '+90 505 787 84 12';
        document.purchaseRepresentative = 'Mahmut AKTAŞ';
        document.purchaseRepresentativePhone = '+90 505 787 84 12';
        document.purchaseRepresentativePosition = 'Salesperson';
        document.purchaseRepresentativeOccupation = 'Test Occupation';
        document.purchaseRepresentativeEmail = '<EMAIL>';

        document.warehouse = 'Central warehouse';
        document.deliveryPriority = 'Normal';
        document.deliveryPolicy = 'When all products are ready';
        document.deliveryCondition = 'Sample';
        document.deliveryMethod = 'Sample';
        document.carrier = 'DHL';
        document.cargoTrackingCode = 'AAAD333334';
        document.shippingPaymentType = ' Freight prepaid';

        document.items = [
            {
                productImage: app.absoluteUrl('static/images/no-image.png'),
                productCode: 'U00000001',
                productDefinition: 'Test product 1',
                productType: t('Stockable product'),
                productBrand: 'EnterERP',
                productDescription: 'Sample product description\nwith new line'
                    .split('\r\n')
                    .join('<br/>')
                    .split('\n')
                    .join('<br/>'),
                productSalesNote: 'Sample purchase note',
                barcode: '1200124526541',
                hsCode: '1200124526FFG541',
                supplierCatalogNo: 'SUP-2024-001',
                countryOfOrigin: 'Turkey',
                description: 'U00000001 - Test product 1',
                scheduledDate: f(app.datetime.local().toJSDate(), 'date'),
                requiredDate: f(app.datetime.local().toJSDate(), 'date'),
                branch: 'Central branch',
                warehouse: 'Central warehouse',
                quantity: f(3, 'unit'),
                unit: 'EA',
                unitSymbol: 'ea',
                baseQuantity: f(3, 'unit'),
                baseUnit: 'EA',
                baseUnitSymbol: 'ea',
                unitPrice: f(100, 'unit-price'),
                realUnitPrice: f(100, 'unit-price'),
                surplusGoodDescription: '3 + 1',
                discount: f(10, 'percentage'),
                unitPriceAfterDiscount: f(90, 'unit-price'),
                tax: 'KDV 18',
                taxPercentage: 18,
                taxTotal: f(16.2, 'total'),
                grossUnitPriceAfterDiscount: f(106.2, 'unit-price'),
                realTotal: f(90 * 3, 'total'),
                total: f(90 * 3, 'total'),
                grossTotal: f(106.2 * 3, 'total'),
                height: 0,
                width: 0,
                depth: 0,
                netWeight: 0,
                grossWeight: 0,
                totalWeight: 0,
                availableStock: 0,
                remainingStock: 0,
                stockStatusPercentage: 0,
                stockStatusColor: 'red',
                additionalFields: [
                    {code: 'test1', label: 'Test 1', subLabel: 'Demo 1', value: 'Test'},
                    {code: 'test2', label: 'Test 2', subLabel: 'Demo 2', value: f(106.2, 'currency')}
                ]
            },
            {
                productImage: app.absoluteUrl('static/images/no-image.png'),
                productCode: 'U00000002',
                productDefinition: 'Test product 2',
                productType: t('Stockable product'),
                productBrand: 'EnterERP',
                productDescription: 'Sample product description\nwith new line',
                productSalesNote: 'Sample purchases note',
                barcode: '1200124526542',
                hsCode: '120012452ADF6541',
                supplierCatalogNo: 'SUP-2024-002',
                countryOfOrigin: 'Turkey',
                description: 'U00000001 - Test product 2',
                scheduledDate: f(app.datetime.local().toJSDate(), 'date'),
                requiredDate: f(app.datetime.local().toJSDate(), 'date'),
                branch: 'Central branch',
                warehouse: 'Central warehouse',
                quantity: f(1, 'unit'),
                unit: 'EA',
                unitSymbol: 'ea',
                baseQuantity: f(1, 'unit'),
                baseUnit: 'EA',
                baseUnitSymbol: 'ea',
                unitPrice: f(50, 'unit-price'),
                realUnitPrice: f(50, 'unit-price'),
                surplusGoodDescription: '',
                discount: f(0, 'percentage'),
                unitPriceAfterDiscount: f(50, 'unit-price'),
                tax: 'KDV 8',
                taxPercentage: 8,
                taxTotal: f(4, 'total'),
                grossUnitPriceAfterDiscount: f(54, 'unit-price'),
                realTotal: f(50, 'total'),
                total: f(50, 'total'),
                grossTotal: f(54, 'total'),
                height: 0,
                width: 0,
                depth: 0,
                netWeight: 0,
                grossWeight: 0,
                totalWeight: 0,
                availableStock: 0,
                remainingStock: 0,
                stockStatusPercentage: 0,
                stockStatusColor: 'red',
                additionalFields: [
                    {code: 'test1', label: 'Test 1', subLabel: 'Demo 1', value: 'Test'},
                    {code: 'test2', label: 'Test 2', subLabel: 'Demo 2', value: f(106.2, 'currency')}
                ]
            },
            {
                productImage: app.absoluteUrl('static/images/no-image.png'),
                productCode: 'U00000003',
                productDefinition: 'Test product 3',
                productBrand: 'EnterERP',
                productType: t('Stockable product'),
                productDescription: 'Sample product description\nwith new line',
                productSalesNote: 'Sample purchases note',
                barcode: '1200124526543',
                hsCode: '120012452AA6541',
                supplierCatalogNo: 'SUP-2024-003',
                countryOfOrigin: 'Turkey',
                description: 'U00000001 - Test product 3',
                scheduledDate: f(app.datetime.local().toJSDate(), 'date'),
                requiredDate: f(app.datetime.local().toJSDate(), 'date'),
                branch: 'Central branch',
                warehouse: 'Central warehouse',
                quantity: f(5, 'unit'),
                unit: 'EA',
                unitSymbol: 'ea',
                baseQuantity: f(5, 'unit'),
                baseUnit: 'EA',
                baseUnitSymbol: 'ea',
                unitPrice: f(200, 'unit-price'),
                realUnitPrice: f(200, 'unit-price'),
                discount: f(20, 'percentage'),
                surplusGoodDescription: '6 + 2',
                unitPriceAfterDiscount: f(160, 'unit-price'),
                tax: 'KDV 18',
                taxPercentage: 18,
                taxTotal: f(28.8, 'total'),
                grossUnitPriceAfterDiscount: f(228.8, 'unit-price'),
                realTotal: f(160 * 5, 'total'),
                total: f(160 * 5, 'total'),
                grossTotal: f(228.8 * 5, 'total'),
                height: 0,
                width: 0,
                depth: 0,
                netWeight: 0,
                grossWeight: 0,
                totalWeight: 0,
                availableStock: 0,
                remainingStock: 0,
                stockStatusPercentage: 0,
                stockStatusColor: 'red',
                additionalFields: [
                    {code: 'test1', label: 'Test 1', subLabel: 'Demo 1', value: 'Test'},
                    {code: 'test2', label: 'Test 2', subLabel: 'Demo 2', value: f(106.2, 'currency')}
                ]
            }
        ];
        document.additionalItemFields = [
            {code: 'test1', label: 'Test 1', subLabel: 'Demo 1'},
            {code: 'test2', label: 'Test 2', subLabel: 'Demo 2'}
        ];
        document.groupedItems = [
            {
                name: 'Test group 1',
                subTotal: f(1850, 'total'),
                items: document.items.slice(1)
            },
            {
                name: 'Test group 2',
                subTotal: f(1850, 'total'),
                items: document.items
            },
            {
                name: 'Test group 3',
                subTotal: f(1850, 'total'),
                items: document.items.slice(2)
            }
        ];
        document.unGroupedItems = document.items;
        document.totalQty = 0;
        document.totalWeight = 0;

        document.paymentPlan = [
            {no: 1, type: t('Cash'), dueDate: f(app.datetime.local().toJSDate(), 'date'), amount: f(250, 'total')},
            {
                no: 1,
                type: t('Cash'),
                dueDate: f(app.datetime.local().plus({days: 30}).toJSDate(), 'date'),
                amount: f(250, 'total')
            },
            {
                no: 1,
                type: t('Cash'),
                dueDate: f(app.datetime.local().plus({days: 60}).toJSDate(), 'date'),
                amount: f(250, 'total')
            },
            {
                no: 1,
                type: t('Cash'),
                dueDate: f(app.datetime.local().plus({days: 90}).toJSDate(), 'date'),
                amount: f(250 + 185, 'total')
            }
        ];

        document.financialProjectName = 'Test project';
        document.financialProjectCode = 'TP';

        document.images = [
            app.absoluteUrl('static/images/no-image.png'),
            app.absoluteUrl('static/images/no-image.png'),
            app.absoluteUrl('static/images/no-image.png'),
            app.absoluteUrl('static/images/no-image.png'),
            app.absoluteUrl('static/images/no-image.png')
        ];

        return document;
    },
    content: (app, recordId) =>
        fs.readFile(path.join(app.config('paths.static'), 'templates/purchase/request.hbs'), {encoding: 'utf8'})
};