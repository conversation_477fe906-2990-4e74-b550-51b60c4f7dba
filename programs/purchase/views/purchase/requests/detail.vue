<template>
    <ui-view
        ref="view"
        type="form"
        collection="purchase.requests"
        method="purchase.save-request"
        :model="model"
        :schema="schema"
        :title="title"
        :extra-fields="extraFields"
        :activity-payload="activityPayload"
        :before-init="beforeInit"
        :before-validate="beforeValidate"
        :before-submit="beforeSubmit"
        :actions="actions"
        :extra-actions="extraActions"
        :assignation="assignation"
        :mail="getMailPayload"
        print-method="purchase.print-request"
        :print-payload="printPayload"
        @changed="handleChange"
        v-if="initialized"
    >
        <template slot="form-top">
            <ui-status :statuses="statuses" :value="status" />

            <ui-related-documents :documents="model.relatedDocuments" />

            <el-button
                :loading="$params('loading')"
                plain
                icon="far fa-paper-plane"
                :disabled="!$params('isPreview') || !$params('id') || status === 'approved' || status === 'canceled'"
                @click="handleOpenRequestProcurements"
            >
                {{ 'Request Procurements' | t }}
            </el-button>

            <el-button
                :loading="$params('loading')"
                type="primary"
                icon="far fa-thumbs-up"
                :disabled="
                    !$params('isPreview') ||
                    !$params('id') ||
                    status === 'approved' ||
                    status === 'requested' ||
                    status === 'partially-approved' ||
                    status === 'canceled' ||
                    $params('loading')
                "
                @click="handleRequest"
            >
                {{ 'Request' | t }}
            </el-button>
        </template>

        <div class="columns">
            <div class="column is-half">
                <ui-field name="code" disabled />
                <ui-field name="requestTypeId" collection="purchase.request-types" />
                <ui-field name="name" />
                <ui-field name="requestedByType" :options="requestedByTypeOptions" translate-labels />
                <ui-field
                    name="requestedByEmployeeId"
                    collection="kernel.partners"
                    view="partners.partners"
                    :filters="{type: 'employee'}"
                    disable-detail
                    disable-create
                    v-show="model.requestedByType === 'employee'"
                />
                <ui-field
                    name="requestedByUserId"
                    collection="kernel.users"
                    view="system.members.users"
                    disable-detail
                    disable-create
                    v-show="model.requestedByType === 'user'"
                />
                <ui-field name="reference" />
                <ui-field
                    name="relatedPartnerId"
                    collection="kernel.partners"
                    view="partners.partners"
                    :filters="{type: {$in: ['customer', 'vendor']}}"
                    disable-detail
                    disable-create
                />
                <ui-field name="priority" :options="priorityOptions" translate-labels />
            </div>
            <div class="column is-half">
                <div style="display: flex; max-width: 450px" v-show="$setting('system.multiBranch')">
                    <kernel-common-branch-select style="flex: 1 1 0" />
                    <el-button
                        class="mb5 ml5"
                        style="flex: 0 0 25px"
                        icon="far fa-check"
                        :title="'Apply To All' | t"
                        :disabled="$params('isPreview')"
                        @click="handleApplyBranch"
                    />
                </div>
                <ui-field name="currencyId" collection="kernel.currencies" v-show="$setting('system.multiCurrency')" />
                <div style="display: flex; max-width: 450px">
                    <ui-field
                        name="financialProjectId"
                        collection="kernel.financial-projects"
                        view="system.management.configuration.financial-projects"
                        disable-detail
                        disable-create
                        :extra-fields="['code']"
                        :template="'{{code}} - {{name}}'"
                        style="flex: 1 1 0"
                    />
                    <el-button
                        class="mb5 ml5"
                        style="flex: 0 0 25px"
                        icon="far fa-check"
                        :title="'Apply To All' | t"
                        :disabled="$params('isPreview')"
                        @click="handleApplyFinancialProject"
                    />
                </div>
                <ui-field name="recordDate" />
                <ui-field name="issueDate" />
                <ui-field name="expiryDate" />
                <div style="display: flex; max-width: 450px">
                    <ui-field name="requiredDate" style="flex: 1 1 0" />
                    <el-button
                        class="mb5 ml5"
                        style="flex: 0 0 25px"
                        icon="far fa-check"
                        :title="'Apply To All' | t"
                        :disabled="$params('isPreview')"
                        @click="handleApplyRequiredDate"
                    />
                </div>
            </div>
        </div>

        <el-tabs v-model="activeTab">
            <el-tab-pane name="items" :label="'Items' | t">
                <ui-field
                    name="items"
                    class="mb0"
                    :key="itemsKey"
                    :min-empty-rows="3"
                    resizable
                    :before-init="beforeItemsInit"
                    :before-create="beforeSaveItem"
                    :before-update="beforeSaveItem"
                    :after-create="afterSaveItem"
                    :after-update="afterSaveItem"
                    :after-remove="afterRemoveItem"
                    :enable-enlarge="true"
                >
                    <template slot="actions">
                        <el-button
                            :loading="$params('loading')"
                            plain
                            icon="far fa-layer-plus"
                            :disabled="$params('isPreview')"
                            @click="handleAddMultipleProducts"
                        >
                            {{ 'Add Multiple Products' | t }}
                        </el-button>

                        <el-button
                            :loading="$params('loading')"
                            plain
                            icon="far fa-layer-plus"
                            :disabled="$params('isPreview')"
                            @click="handleAddKitProducts"
                        >
                            {{ 'Add Kit Products' | t }}
                        </el-button>

                        <el-tooltip effect="dark" :content="'Add Product With Barcode' | t" placement="bottom">
                            <el-button
                                :loading="$params('loading')"
                                icon="far fa-barcode"
                                :disabled="$params('isPreview') || !model.partnerId || status === 'payment-planned'"
                                @click="handleAddProductWithBarcode"
                            />
                        </el-tooltip>
                    </template>
                </ui-field>

                <ui-totals :totals="totalItems">
                    <el-tabs :value="'note'" style="max-width: 450px">
                        <el-tab-pane name="note" :label="'Request Note' | t">
                            <ui-field class="mt10" name="note" label="hide" :rows="2" />
                        </el-tab-pane>

                        <el-tab-pane
                            name="organization"
                            :label="'Organization Information' | t"
                            :disabled="!$setting('purchase.purchaseOrganizations')"
                        >
                            <ui-field
                                class="mt10"
                                name="organizationId"
                                collection="kernel.organizations"
                                :extra-fields="['code']"
                                :template="'{{ code }} - {{ name }}'"
                                disabled
                            />
                            <ui-field
                                name="purchaseManagerId"
                                collection="kernel.partners"
                                :html-template="organizationMemberTemplate"
                                disabled
                            />
                            <ui-field name="purchaseRepresentativeId" collection="kernel.partners" disabled />
                        </el-tab-pane>
                    </el-tabs>
                </ui-totals>
            </el-tab-pane>

            <el-tab-pane name="attachments" :label="'Attachments' | t">
                <ui-field name="attachments" field-type="attachments" auto-height />
            </el-tab-pane>
        </el-tabs>
    </ui-view>
</template>

<script>
import data from './detail/data';
import computed from './detail/computed';
import watch from './detail/watch';
import methods from './detail/methods';
import created from './detail/created';
import mounted from './detail/mounted';

export default {
    mixins: [data, computed, watch, methods, created, mounted]
};
</script>
