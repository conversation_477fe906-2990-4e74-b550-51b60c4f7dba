import _ from 'lodash';
import fastCopy from 'fast-copy';

export default {
    methods: {
        handleOpenRequestProcurements() {
            const requestId = this.$params('id');

            this.$program.dialog({
                component: 'purchase.procurement-and-source.request-procurements.master',
                params: {requestId}
            });
        },
        handleRequest() {
            this.model.status = 'requested';

            this.$refs.view.submitForm({status: 'requested'});
        },
        async handleCancel() {
            const isConfirmed = await new Promise(resolve => {
                this.$program.alert(
                    'confirm',
                    this.$t('You are about to cancel the document. Do you want continue?'),
                    confirmed => {
                        resolve(confirmed);
                    }
                );
            });

            if (!isConfirmed) return;

            this.model.status = 'canceled';

            this.$refs.view.submitForm({status: 'canceled'});
        },
        async beforeInit(model) {
            const company = this.$store.getters['session/company'];
            const user = this.$user;

            // Currency format.
            if (company.currencyId !== model.currencyId) {
                const currency = await this.$collection('kernel.currencies').findOne({
                    _id: model.currencyId,
                    $select: ['name', 'symbol', 'symbolPosition']
                });

                if (_.isObject(currency)) {
                    this.currencyFormat = {
                        currency: {
                            symbol: currency.symbol,
                            format: currency.symbolPosition === 'before' ? '%s %v' : '%v %s'
                        }
                    };
                }
            }

            // When creating generate code.
            if (!this.$params('id')) {
                model.code = await this.generateCode(false);
            }
            this.generatedCode = model.code;

            // Requested bt
            if (!this.$params('id')) {
                if (!!user.partnerId) {
                    model.requestedByType = 'employee';
                    model.requestedByEmployeeId = user.partnerId;
                } else {
                    model.requestedByType = 'user';
                    model.requestedByEmployeeId = user._id;
                }
            }

            // Default currency.
            if (!model.currencyId) model.currencyId = company.currencyId;

            // Default request type.
            if (!model.requestTypeId) {
                const generalRequestType = await this.$collection('purchase.request-types').findOne({
                    code: 'general'
                });

                if (!!generalRequestType) {
                    model.requestTypeId = generalRequestType._id;
                }
            }

            // Default organization.
            if (user.partnerId && this.$setting('purchase.purchaseOrganizations')) {
                const organization = this.organizations.find(
                    o =>
                        Array.isArray(o.team) &&
                        o.team.some(m => m.partnerId === user.partnerId) &&
                        o.scope.indexOf('purchase') !== -1
                );

                if (_.isObject(organization)) {
                    const team = organization.team || [];
                    const currentMember = team.find(m => m.partnerId === user.partnerId);

                    this.organizationSettings = organization.settings || {};
                    this.organizationTeam = team;

                    if (!this.$params('id')) {
                        if (!model.organizationId) {
                            model.organizationId = organization._id;
                        }

                        if (_.isObject(currentMember) && !model.purchaseManagerId) {
                            if (currentMember.managerId) {
                                model.purchaseManagerId = currentMember.managerId;
                            } else {
                                model.purchaseManagerId = currentMember.partnerId;
                            }
                        }

                        if (_.isObject(currentMember) && !model.purchaseRepresentativeId) {
                            model.purchaseRepresentativeId = currentMember.partnerId;
                        }
                    }

                    if (
                        _.isObject(currentMember) &&
                        _.isObject(currentMember.settings) &&
                        !_.isEmpty(currentMember.settings)
                    ) {
                        this.organizationSettings = _.assign(this.organizationSettings, currentMember.settings);
                    }
                }
            }
            if (_.isEmpty(this.organizationSettings) && model.organizationId) {
                const organization = this.organizations.find(o => o._id === model.organizationId);

                if (_.isPlainObject(organization)) {
                    const team = organization.team || [];
                    const currentMember = team.find(m => m.partnerId === user.partnerId);

                    this.organizationSettings = organization.settings || {};
                    this.organizationTeam = team;

                    if (
                        _.isObject(currentMember) &&
                        _.isObject(currentMember.settings) &&
                        !_.isEmpty(currentMember.settings)
                    ) {
                        this.organizationSettings = _.assign(this.organizationSettings, currentMember.settings);
                    }
                }
            }

            return model;
        },
        async beforeValidate(model) {
            if (model.requestedByType === 'employee' && !model.requestedByEmployeeId) {
                throw new this.$app.errors.Unprocessable({
                    message: this.$t('{{label}} is required', {
                        label: this.$t('Request by')
                    }),
                    field: 'requestedByEmployeeId'
                });
            }
            if (model.requestedByType === 'user' && !model.requestedByUserId) {
                throw new this.$app.errors.Unprocessable({
                    message: this.$t('{{label}} is required', {
                        label: this.$t('Request by')
                    }),
                    field: 'requestedByUserId'
                });
            }

            return model;
        },
        async beforeSubmit(model) {
            if (this.model.code === this.generatedCode && !this.$params('id')) {
                model.code = await this.generateCode(true);
            }

            return model;
        },
        async printPayload() {
            const payload = {
                source: 'purchase.request',
                title: this.$t('Purchase Request')
            };

            const partner = await this.$collection('kernel.partners').findOne({
                _id: this.model.relatedPartnerId,
                $select: ['code', 'name'],
                $disableActiveCheck: true,
                $disableSoftDelete: true
            });
            payload.title = `${partner ? partner.name : ''} - ${this.$t('Purchase Request')} - ${this.model.code}`;

            if (!!this.documentType && this.documentType.defaultPrintingTemplateId) {
                payload.templateId = this.documentType.defaultPrintingTemplateId;
            }

            return payload;
        },
        async handleChange(model, field) {
            const company = this.$store.getters['session/company'];
            const user = this.$user;

            if (field === 'requestedByType') {
                this.model.requestedByEmployeeId = '';
                this.model.requestedByUserId = '';
            } else if (field === 'currencyId') {
                if (model.currencyId) {
                    const currency = await this.$collection('kernel.currencies').findOne({
                        _id: model.currencyId,
                        $select: ['name', 'symbol', 'symbolPosition']
                    });

                    if (_.isObject(currency)) {
                        this.currencyFormat = {
                            currency: {
                                symbol: currency.symbol,
                                format: currency.symbolPosition === 'before' ? '%s %v' : '%v %s'
                            }
                        };

                        if (company.currencyId !== model.currencyId) {
                            this.model.currencyRate = await this.$convertCurrency({
                                from: currency.name,
                                to: company.currency.name,
                                value: 1,
                                options: {
                                    date: model.issueDate
                                }
                            });
                        } else {
                            this.model.currencyRate = 1;
                        }
                    }
                } else {
                    this.model.currencyId = company.currencyId;
                    this.model.currencyRate = 1;
                }

                this.$set(this.model, 'items', []);

                // Reset totals.
                this.model.subTotal = 0;
                this.model.taxTotal = 0;
                this.model.grandTotal = 0;
                this.model.appliedTaxes = [];

                this.$nextTick(() => {
                    this.itemsKey = _.uniqueId('requestItems_');
                });
            } else if (field === 'issueDate') {
                if (company.currencyId !== model.currencyId) {
                    const currency = await this.$collection('kernel.currencies').findOne({
                        _id: model.currencyId,
                        $select: ['name']
                    });

                    this.model.currencyRate = await this.$convertCurrency({
                        from: currency.name,
                        to: company.currency.name,
                        value: 1,
                        options: {
                            date: model.issueDate
                        }
                    });

                    this.$set(this.model, 'items', []);

                    // Reset totals.
                    this.model.subTotal = 0;
                    this.model.taxTotal = 0;
                    this.model.grandTotal = 0;
                    this.model.appliedTaxes = [];

                    this.$nextTick(() => {
                        this.itemsKey = _.uniqueId('requestItems_');
                    });
                }
            } else if (field === 'branchId') {
                // Branch default warehouse.
                const warehouse = await this.$collection('inventory.warehouses').findOne({
                    branchId: this.model.branchId,
                    $select: ['_id', 'address']
                });
                if (_.isObject(warehouse)) {
                    this.model.warehouseId = warehouse._id;
                } else {
                    this.$program.message(
                        'error',
                        this.$t('No warehouse that is belongs to the selected branch is found.')
                    );
                }

                this.$set(this.model, 'items', []);

                // Reset totals.
                this.model.subTotal = 0;
                this.model.taxTotal = 0;
                this.model.grandTotal = 0;
                this.model.appliedTaxes = [];
            }
        },
        async beforeItemsInit(items) {
            return await this.$rpc('purchase.init-request-items', {
                items,
                productFields: this.productFields,
                status: this.status,
                id: this.$params('id')
            });
        },
        async beforeSaveItem({row, originalRow, params}) {
            let field = params.colDef.field;
            let result = row;

            try {
                result = (
                    await this.$rpc('purchase.decorate-request-items', {
                        items: [row],
                        field,
                        model: _.omit(this.model, 'items'),
                        productFields: this.productFields
                    })
                )[0];
            } catch (error) {
                this.$program.message('error', error.message);

                result = originalRow;
            }

            return result;
        },
        afterSaveItem() {
            this.$nextTick(() => {
                this.calculateTotals();
            });
        },
        afterRemoveItem() {
            this.calculateTotals();
        },
        handleAddMultipleProducts() {
            const self = this;

            const filters = {
                isSimple: true,
                canBePurchased: true
            };

            if (_.isObject(this.organizationSettings) && !_.isEmpty(this.organizationSettings)) {
                filters.groupIds = {
                    $in: this.organizationSettings.productGroupIds || []
                };
            }

            this.$program.dialog({
                component: 'inventory.catalog.products.master',
                params: {filters},
                async onSelect(products) {
                    self.$params('loading', true);

                    const items = await self.$rpc('purchase.decorate-request-items', {
                        items: products.map(product => {
                            return {
                                productId: product._id,
                                requiredDate: new Date(),
                                quantity: 1,
                                baseQuantity: 1,
                                unitPrice: 0,
                                discount: 0,
                                unitPriceAfterDiscount: 0,
                                grossUnitPriceAfterDiscount: 0,
                                taxTotal: 0,
                                grossTotal: 0,
                                stockQuantity: 0,
                                orderedQuantity: 0,
                                assignedQuantity: 0,
                                availableQuantity: 0,
                                warehouseStockQuantity: 0,
                                warehouseOrderedQuantity: 0,
                                warehouseAssignedQuantity: 0,
                                warehouseAvailableQuantity: 0,
                                total: 0
                            };
                        }),
                        field: 'productId',
                        model: _.omit(self.model, 'items'),
                        productFields: self.productFields
                    });

                    self.$set(self.model, 'items', fastCopy(self.model.items).concat(items));
                    self.afterSaveItem();
                    self.$params('loading', false);
                }
            });
        },
        handleAddKitProducts() {
            const self = this;

            const filters = {
                isKit: true,
                canBePurchased: true
            };

            if (_.isObject(this.organizationSettings) && !_.isEmpty(this.organizationSettings)) {
                filters.groupIds = {
                    $in: this.organizationSettings.productGroupIds || []
                };
            }

            this.$program.dialog({
                component: 'inventory.catalog.products.master',
                params: {filters},
                async onSelect(selected) {
                    self.$params('loading', true);

                    const kits = await self.$collection('inventory.products').find({
                        _id: {$in: selected.map(s => s._id)},
                        $select: ['_id', 'subProducts', 'useSubProducts'],
                        $disableActiveCheck: true,
                        $disableSoftDelete: true
                    });
                    const rows = [];

                    for (let kit of kits.filter(k => Array.isArray(k.subProducts))) {
                        if (kit.useSubProducts) {
                            for (const subProduct of kit.subProducts.filter(sp => !!sp.canBePurchased)) {
                                rows.push({
                                    productId: subProduct.productId,
                                    scheduledDate: new Date(),
                                    quantity: subProduct.quantity || 1,
                                    baseQuantity: 1,
                                    unitPrice: 0,
                                    discount: 0,
                                    unitPriceAfterDiscount: 0,
                                    grossUnitPriceAfterDiscount: 0,
                                    taxTotal: 0,
                                    grossTotal: 0,
                                    stockQuantity: 0,
                                    orderedQuantity: 0,
                                    assignedQuantity: 0,
                                    availableQuantity: 0,
                                    warehouseStockQuantity: 0,
                                    warehouseOrderedQuantity: 0,
                                    warehouseAssignedQuantity: 0,
                                    warehouseAvailableQuantity: 0,
                                    total: 0
                                });
                            }
                        } else {
                            rows.push({
                                productId: kit._id,
                                scheduledDate: new Date(),
                                quantity: 1,
                                baseQuantity: 1,
                                unitPrice: 0,
                                discount: 0,
                                unitPriceAfterDiscount: 0,
                                grossUnitPriceAfterDiscount: 0,
                                taxTotal: 0,
                                grossTotal: 0,
                                stockQuantity: 0,
                                orderedQuantity: 0,
                                assignedQuantity: 0,
                                availableQuantity: 0,
                                warehouseStockQuantity: 0,
                                warehouseOrderedQuantity: 0,
                                warehouseAssignedQuantity: 0,
                                warehouseAvailableQuantity: 0,
                                total: 0
                            });
                        }
                    }

                    const items = await self.$rpc('purchase.decorate-request-items', {
                        items: rows,
                        field: 'productId',
                        model: _.omit(self.model, 'items'),
                        productFields: self.productFields
                    });

                    self.$set(self.model, 'items', fastCopy(self.model.items).concat(items));
                    self.afterSaveItem();
                    self.$params('loading', false);
                }
            });
        },
        handleAddProductWithBarcode() {
            this.$program.dialog({
                component: 'inventory.components.barcode-finder',
                params: {
                    title: this.$t('Add Product With Barcode')
                },
                onSubmit: async ({items}) => {
                    this.$params('loading', true);

                    const rows = await this.$rpc('purchase.decorate-request-items', {
                        items: items.map(item => {
                            return {
                                productId: item.productId,
                                scheduledDate: new Date(),
                                quantity: item.quantity,
                                baseQuantity: item.baseQuantity,
                                unitPrice: 0,
                                discount: 0,
                                unitPriceAfterDiscount: 0,
                                grossUnitPriceAfterDiscount: 0,
                                taxTotal: 0,
                                grossTotal: 0,
                                stockQuantity: 0,
                                orderedQuantity: 0,
                                assignedQuantity: 0,
                                availableQuantity: 0,
                                warehouseStockQuantity: 0,
                                warehouseOrderedQuantity: 0,
                                warehouseAssignedQuantity: 0,
                                warehouseAvailableQuantity: 0,
                                total: 0
                            };
                        }),
                        field: 'productId',
                        model: _.omit(this.model, 'items'),
                        productFields: this.productFields
                    });

                    this.$set(this.model, 'items', fastCopy(this.model.items).concat(rows));
                    this.afterSaveItem();
                    this.$params('loading', false);
                }
            });
        },
        handleApplyRequiredDate() {
            if (this.model.items.length > 0) {
                this.$program.alert('confirm', this.$t('Do you want to apply this to all rows?'), confirmed => {
                    if (confirmed) {
                        this.model.items = fastCopy(this.model.items).map(item => {
                            item.requiredDate = this.model.requiredDate;

                            return item;
                        });
                    }
                });
            }
        },
        handleApplyBranch() {
            if (this.model.items.length > 0) {
                this.$program.alert('confirm', this.$t('Do you want to apply this to all rows?'), async confirmed => {
                    if (confirmed) {
                        const items = [];

                        for (const item of fastCopy(this.model.items)) {
                            item.branchId = this.model.branchId;

                            const warehouse = await this.$collection('inventory.warehouses').findOne({
                                branchId: item.branchId,
                                $select: ['_id', 'address'],
                                $disableActiveCheck: true,
                                $disableSoftDelete: true
                            });

                            if (_.isObject(warehouse)) {
                                item.warehouseId = warehouse._id;
                            } else {
                                item.warehouseId = '';
                            }

                            items.push(item);
                        }

                        this.model.items = items;
                    }
                });
            }
        },
        handleApplyFinancialProject() {
            if (this.model.items.length > 0) {
                this.$program.alert('confirm', this.$t('Do you want to apply this to all rows?'), confirmed => {
                    if (confirmed) {
                        this.model.items = fastCopy(this.model.items).map(item => {
                            item.financialProjectId = this.model.financialProjectId;

                            return item;
                        });
                    }
                });
            }
        },

        async generateCode(save = false) {
            const numbering = await this.$collection('kernel.numbering').findOne({
                code: 'purchaseRequestNumbering',
                $select: ['_id'],
                $disableInUseCheck: true,
                $disableActiveCheck: true,
                $disableSoftDelete: true
            });

            return await this.$rpc('kernel.common.request-number', {
                numberingId: numbering._id,
                save
            });
        },
        async calculateRowTotals(row, field) {
            try {
                row = await this.$rpc('purchase.calculate-request-row-totals', {
                    row: row,
                    field,
                    model: this.model
                });
            } catch (error) {
                this.$program.message('error', error.message);
            }

            return row;
        },
        async calculateTotals(getResult = false, forcedDiscount = null) {
            const model = fastCopy(this.model);
            let subTotal = 0;
            let taxTotal = 0;
            let grandTotal = 0;
            let appliedTaxes = [];

            if (Array.isArray(model.items)) {
                model.items.forEach(row => {
                    subTotal += this.$app.round(row.total, 'total');

                    if (_.isObject(row.taxDetail)) {
                        taxTotal += this.$app.round(row.taxDetail.taxTotal, 'total');

                        row.taxDetail.applied.forEach(tax => {
                            const taxIndex = _.findIndex(appliedTaxes, t => t._id === tax._id);

                            if (taxIndex !== -1) {
                                appliedTaxes[taxIndex].unAppliedAmount += this.$app.round(
                                    tax.unAppliedAmount || 0,
                                    'total'
                                );
                                appliedTaxes[taxIndex].appliedAmount += this.$app.round(
                                    tax.appliedAmount || 0,
                                    'total'
                                );
                            } else {
                                tax.unAppliedAmount = this.$app.round(tax.unAppliedAmount || 0, 'total');
                                tax.appliedAmount = this.$app.round(tax.appliedAmount || 0, 'total');
                                appliedTaxes.push(fastCopy(tax));
                            }
                        });
                    }
                });
                grandTotal = this.$app.round(subTotal + taxTotal, 'total');

                if (getResult) {
                    return {
                        subTotal: this.$app.round(subTotal, 'total'),
                        taxTotal: this.$app.round(taxTotal, 'total'),
                        grandTotal: this.$app.round(grandTotal, 'total'),
                        appliedTaxes: appliedTaxes
                    };
                } else {
                    this.model.subTotal = this.$app.round(subTotal, 'total');
                    this.model.taxTotal = this.$app.round(taxTotal, 'total');
                    this.model.grandTotal = this.$app.round(grandTotal, 'total');
                    this.model.appliedTaxes = appliedTaxes;
                }
            }
        },
        organizationMemberTemplate(item) {
            if (item.position) {
                return `<span style="float: left">${item.label}</span><span style="float: right; color: #8492a6;">${item.position}</span>`;
            }

            return item.label;
        },
        async getMailPayload() {
            return {
                documentId: this.$params('id'),
                subject: 'Purchase Request - {{code}}',
                subjectTranslationPayload: {code: this.model.code},
                message: `
<p>${this.$t('Hello')}</p>
<br/>
<p>${this.$t(
                    'A copy of the purchase request that has {{code}} code is attached to this email. You can download the related document from the attachments of this email.',
                    {code: this.model.code}
                )}</p>
                `.trim(),
                messageTranslationPayload: {},
                printMethod: 'purchase.print-request'
            };
        }
    }
};
