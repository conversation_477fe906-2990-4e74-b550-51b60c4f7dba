import _ from 'lodash';
import {trim} from 'framework/helpers';
import {getPartnerAccounts} from '../../../accounting/methods/partner-ledger';

export default async function (app, store, request, response) {
    // Tek bir partnerId veya tüm partnerlar için bakiyeleri dönebilir
    const partnerId = trim(request.body.partnerId || '');
    const {skip = 0, limit = 50} = request.body;

    const accountIds = await getPartnerAccounts(app, {excludeAdditionalAllowances: false});

    // Sorgu filtresi: partnerId verilmişse ona göre, yoksa tüm partnerlar
    const matchFilter = {
        accountId: {$in: accountIds}
    };
    if (partnerId) {
        matchFilter.partnerId = partnerId;
    }

    // Toplam borç, alacak ve bakiye hesaplama
    const balanceReport = await app.collection('accounting.transactions').aggregate([
        { $match: matchFilter },
        { $group: {
            _id: '$partnerId',
            debit: { $sum: '$debit' },
            credit: { $sum: '$credit' }
        }},
        { $project: {
            partnerId: '$_id',
            debit: 1,
            credit: 1,
            balance: { $subtract: ['$debit', '$credit'] },
            _id: 0
        }},
        { $sort: { balance: -1 } },
        { $skip: skip },
        { $limit: limit }
    ]);

    // Partner bilgilerini ekle (isteğe bağlı)
    const partnerIds = balanceReport.map(r => r.partnerId);
    let partners = [];
    if (partnerIds.length > 0) {
        partners = await app.collection('kernel.partners').find({
            _id: { $in: partnerIds },
            $select: ['_id', 'name', 'code', 'tinIdentity']
        });
    }
    const partnerMap = _.keyBy(partners, '_id');

    // Sonuçları partner bilgileriyle birleştir
    const result = balanceReport.map(r => ({
        partnerId: r.partnerId,
        name: partnerMap[r.partnerId]?.name || '',
        code: partnerMap[r.partnerId]?.code || '',
        tinIdentity: partnerMap[r.partnerId]?.tinIdentity || '',
        debit: r.debit,
        credit: r.credit,
        balance: r.balance
    }));

    return response.send({
        status: 'ok',
        count: result.length,
        data: result
    });
}
