import _ from 'lodash';
import microtime from 'microtime';
import {generateRandomBarcode, trim} from 'framework/helpers';
import Random from 'framework/random';

export default async function (app, store, request, response) {
    try {
        const {shipmentTackingCode, returnItems, reasonId, description, orderId, isIntegrationOrder} = request.body;

        if (!Array.isArray(returnItems)) {
            throw new Error('Return items not found!');
        }

        const user = await app.collection('kernel.users').findOne({isRoot: true});
        const now = app.datetime.local().toJSDate();

        const numbering = await app.collection('kernel.numbering').findOne({
            code: 'salesReturnNumbering',
            $select: ['_id'],
            $disableInUseCheck: true,
            $disableActiveCheck: true,
            $disableSoftDelete: true
        });

        let returnWarehouse = await app.collection('inventory.warehouses').findOne({
            _id: store.returnWarehouseId,
            $select: ['_id', 'address'],
            $disableActiveCheck: true,
            $disableSoftDelete: true
        });
        if (!returnWarehouse) {
            returnWarehouse = await app.collection('inventory.warehouses').findOne({
                _id: store.warehouseId,
                $select: ['_id', 'address'],
                $disableActiveCheck: true,
                $disableSoftDelete: true
            });
        }

        if (isIntegrationOrder) {
            const order = await app.collection('sale.orders').findOne({
                _id: orderId
            });
            if (!order) {
                throw new Error('Order is not found!');
            }

            const defaultDeliveryOption = store.deliveryOptions.find(
                deliveryOption => deliveryOption.id === store.defaultDeliveryOptionId
            );

            if (!defaultDeliveryOption) {
                throw new Error('Default delivery option is not found!');
            }

            const carrier = await app.collection('logistics.carriers').findOne({
                _id: defaultDeliveryOption.carrierId,
                $disableActiveCheck: true,
                $disableSoftDelete: true
            });

            if (!carrier) {
                throw new Error('Carrier is not found!');
            }

            let returnOrder = {};
            returnOrder.module = 'ecommerce';
            returnOrder.status = 'draft';
            returnOrder.code = await app.rpc('kernel.common.request-number', {
                numberingId: numbering._id,
                save: true
            });
            returnOrder.storeId = store._id;
            returnOrder.branchId = order.branchId;
            returnOrder.returnReasonId = reasonId;
            returnOrder.partnerId = order.partnerId;
            returnOrder.currencyId = order.currencyId;
            returnOrder.currencyRate = 1;
            returnOrder.recordDate = now;
            returnOrder.issueDate = now;
            returnOrder.scheduledDate = now;
            returnOrder.requests = '';
            returnOrder.note = description || '';
            returnOrder.warehouseId = returnWarehouse._id;
            returnOrder.deliveryAddress = returnWarehouse.address;
            returnOrder.carrierId = carrier._id;
            returnOrder.transferIds = order.transferIds;
            returnOrder.orderId = order._id;
            returnOrder.relatedDocuments = [
                {
                    collection: 'sale.orders',
                    view: 'sale.sales.orders',
                    title: 'Source Sales Orders',
                    ids: [order._id]
                },
                {
                    collection: 'inventory.transfers',
                    view: 'inventory.operations.transfers',
                    title: 'Source Deliveries',
                    ids: order.transferIds
                }
            ];
            returnOrder.items = await app.rpc(
                'sale.decorate-return-items',
                {
                    items: returnItems.map(returnItem => ({
                        reference: order.code,
                        referenceId: order._id,
                        referenceCollection: 'sale.orders',
                        productId: returnItem.productId,
                        scheduledDate: now,
                        quantity: returnItem.quantity,
                        stockQuantity: 0,
                        orderedQuantity: 0,
                        assignedQuantity: 0,
                        availableQuantity: 0,
                        warehouseStockQuantity: 0,
                        warehouseOrderedQuantity: 0,
                        warehouseAssignedQuantity: 0,
                        warehouseAvailableQuantity: 0
                    })),
                    field: 'productId',
                    model: _.omit(returnOrder, 'items'),
                    productFields: [
                        'code',
                        'name',
                        'displayName',
                        'definition',
                        'type',
                        'baseUnitId',
                        'salesUnitId',
                        'barcode',
                        'unitRatios',
                        'unitConversions',
                        'salesNote',
                        'isSimple'
                    ]
                },
                {user}
            );
            returnOrder = await app.collection('sale.returns').create(returnOrder, {user});
            await app.rpc(
                'sale.returns-save',
                {
                    id: returnOrder._id,
                    data: {
                        ..._.omit(returnOrder, '_id'),
                        status: 'approved'
                    }
                },
                {user}
            );
            returnOrder = await app.collection('sale.returns').get(returnOrder._id);

            let newShippingOrder = {};
            await (async () => {
                const transfer = await app.collection('inventory.transfers').get(returnOrder.transferIds[0]);
                const packageTypes = await app.collection('logistics.package-types').find({
                    carrierId: carrier._id,
                    $disableActiveCheck: true,
                    $disableSoftDelete: true
                });
                const packageType = packageTypes.find(pkg => pkg.isDefault) ?? packageTypes[0];
                const lengthUnit = await app.collection('kernel.units').findOne({
                    category: 'length',
                    symbol: 'm',
                    $select: ['symbol']
                });
                const weightUnit = await app.collection('kernel.units').findOne({
                    category: 'weight',
                    symbol: 'kg',
                    $select: ['symbol']
                });
                const round = n => app.roundNumber(n, 4);

                const partner = await app.collection('kernel.partners').findOne({
                    _id: order.partnerId,
                    $disableActiveCheck: true,
                    $disableSoftDelete: true
                });

                newShippingOrder.type = 'return';
                newShippingOrder.transferId = transfer._id;
                newShippingOrder.orderId = order._id;
                newShippingOrder.status = 'draft';
                newShippingOrder.code = microtime.now();
                newShippingOrder.carrierId = carrier._id;
                newShippingOrder.carrierCode = carrier.code;
                newShippingOrder.carrierName = carrier.name;
                newShippingOrder.partnerId = partner._id;
                newShippingOrder.partnerIsCompany = partner.isCompany;
                newShippingOrder.partnerCode = partner.code;
                newShippingOrder.partnerTinIdentity = partner.tinIdentity;
                newShippingOrder.partnerTaxDepartment = partner.taxDepartment;
                newShippingOrder.partnerEmail = partner.email;
                newShippingOrder.partnerPhone = partner.phone;
                newShippingOrder.currencyId = order.currencyId;
                newShippingOrder.recordDate = now;
                newShippingOrder.issueDate = now;
                newShippingOrder.shippingPaymentType = 'freight-collect';
                newShippingOrder.packagingType = packageType.packagingType;
                newShippingOrder.warehouseId = returnWarehouse._id;
                newShippingOrder.deliveryAddress = returnWarehouse.address;
                newShippingOrder.warehouseAddress = returnWarehouse.address;
                newShippingOrder.integrationType = carrier.integrationType;
                newShippingOrder.integrationParams = carrier.integrationParams;
                newShippingOrder.volumetricWeightFactor = carrier.volumetricWeightFactor;
                newShippingOrder.items = [];

                const pkgNumbering = await app.collection('kernel.numbering').findOne({
                    code: 'logisticsPackageNumbering',
                    $select: ['_id'],
                    $disableInUseCheck: true,
                    $disableActiveCheck: true,
                    $disableSoftDelete: true
                });
                let pkg = {};
                pkg.code = await app.rpc('kernel.common.request-number', {numberingId: pkgNumbering._id, save: true});
                pkg.packageTypeId = packageType._id;
                pkg.carrierId = carrier._id;
                pkg.partnerId = transfer.partnerId;
                pkg.barcode = generateRandomBarcode(16);
                pkg.recordDate = newShippingOrder.recordDate;
                pkg.isUsed = false;
                pkg.height = packageType.height;
                pkg.heightUnitId = packageType.heightUnitId;
                pkg.width = packageType.width;
                pkg.widthUnitId = packageType.widthUnitId;
                pkg.depth = packageType.depth;
                pkg.depthUnitId = packageType.depthUnitId;
                pkg.netWeight = packageType.defaultWeight;
                pkg.netWeightUnitId = packageType.defaultWeightUnitId;
                pkg.grossWeight = packageType.defaultWeight;
                pkg.grossWeightUnitId = packageType.defaultWeightUnitId;
                pkg.items = [];
                for (const transferItem of transfer.items || []) {
                    if (Array.isArray(transferItem.subItems)) {
                        for (const subItem of transferItem.subItems) {
                            const item = {};

                            item.id = `${transferItem.id}${subItem.serialNumber}${subItem.lotNumber}`;
                            item.transferId = transfer._id;
                            item.transferCode = transfer.code;
                            item.productId = transferItem.productId;
                            item.productCode = transferItem.productCode;
                            item.productDefinition = transferItem.productDefinition;
                            item.barcode = transferItem.barcode;
                            item.serialNumber = subItem.serialNumber;
                            item.lotNumber = subItem.lotNumber;
                            item.height = 0;
                            item.heightUnitId = lengthUnit._id;
                            item.heightUnitName = lengthUnit.symbol;
                            item.width = 0;
                            item.widthUnitId = lengthUnit._id;
                            item.widthUnitName = lengthUnit.symbol;
                            item.depth = 0;
                            item.depthUnitId = lengthUnit._id;
                            item.depthUnitName = lengthUnit.symbol;
                            item.netWeight = 0;
                            item.netWeightUnitId = weightUnit._id;
                            item.netWeightUnitName = weightUnit.symbol;
                            item.grossWeight = 0;
                            item.grossWeightUnitId = weightUnit._id;
                            item.grossWeightUnitName = weightUnit.symbol;
                            item.unitId = transferItem.unitId;
                            item.quantity = subItem.quantity;

                            pkg.items.push(item);
                        }
                    } else {
                        const item = {};

                        item.id = transferItem.id;
                        item.transferId = transfer._id;
                        item.transferCode = transfer.code;
                        item.productId = transferItem.productId;
                        item.productCode = transferItem.productCode;
                        item.productDefinition = transferItem.productDefinition;
                        item.barcode = transferItem.barcode;
                        item.serialNumber = '';
                        item.lotNumber = '';
                        item.height = 0;
                        item.heightUnitId = lengthUnit._id;
                        item.heightUnitName = lengthUnit.symbol;
                        item.width = 0;
                        item.widthUnitId = lengthUnit._id;
                        item.widthUnitName = lengthUnit.symbol;
                        item.depth = 0;
                        item.depthUnitId = lengthUnit._id;
                        item.depthUnitName = lengthUnit.symbol;
                        item.netWeight = 0;
                        item.netWeightUnitId = weightUnit._id;
                        item.netWeightUnitName = weightUnit.symbol;
                        item.grossWeight = 0;
                        item.grossWeightUnitId = weightUnit._id;
                        item.grossWeightUnitName = weightUnit.symbol;
                        item.unitId = transferItem.unitId;
                        item.quantity = transferItem.actualQty;

                        pkg.items.push(item);
                    }
                }
                const products = await app.collection('inventory.products').find({
                    _id: {$in: _.uniq(pkg.items.map(item => item.productId))},
                    $select: ['unitMeasurements'],
                    $disableActiveCheck: true,
                    $disableSoftDelete: true
                });
                const productsMap = {};
                for (const product of products) {
                    productsMap[product._id] = product;
                }
                pkg.items = pkg.items.map(item => {
                    const product = productsMap[item.productId];
                    const um = product.unitMeasurements.find(um => um.unitId === item.unitId);

                    if (!!um) {
                        if (um.height && um.heightUnitId) {
                            item.height = um.height;
                            item.heightUnitId = um.heightUnitId;
                        }
                        if (um.width && um.widthUnitId) {
                            item.width = um.width;
                            item.widthUnitId = um.widthUnitId;
                        }
                        if (um.depth && um.depthUnitId) {
                            item.depth = um.depth;
                            item.depthUnitId = um.depthUnitId;
                        }
                        if (um.netWeight && um.netWeightUnitId) {
                            item.netWeight = um.netWeight;
                            item.netWeightUnitId = um.netWeightUnitId;
                        }
                        if (um.grossWeight && um.grossWeightUnitId) {
                            item.grossWeight = um.grossWeight;
                            item.grossWeightUnitId = um.grossWeightUnitId;
                        }
                    }

                    return item;
                });
                let unitIds = [];
                for (const item of pkg.items) {
                    unitIds.push(
                        ...[
                            pkg.grossWeightUnitId,
                            pkg.widthUnitId,
                            pkg.heightUnitId,
                            pkg.depthUnitId,
                            item.unitId,
                            item.heightUnitId,
                            item.widthUnitId,
                            item.depthUnitId,
                            item.netWeightUnitId,
                            item.grossWeightUnitId
                        ]
                    );
                }
                unitIds = _.uniq(unitIds);
                const units = await app.collection('kernel.units').find({
                    _id: {$in: unitIds},
                    $disableActiveCheck: true,
                    $disableSoftDelete: true
                });
                const unitsMap = {};
                for (const unit of units) {
                    unitsMap[unit._id] = unit;
                }
                pkg.items = pkg.items.map(item => {
                    item.unitName = unitsMap[item.unitId].name;
                    item.heightUnitName = unitsMap[item.heightUnitId].symbol;
                    item.widthUnitName = unitsMap[item.widthUnitId].symbol;
                    item.depthUnitName = unitsMap[item.depthUnitId].symbol;
                    item.netWeightUnitName = unitsMap[item.netWeightUnitId].symbol;
                    item.grossWeightUnitName = unitsMap[item.grossWeightUnitId].symbol;

                    return item;
                });
                pkg = await app.collection('logistics.packages').create(pkg, {user});

                const item = {};
                item.id = Random.id();
                item.packageId = pkg._id;
                item.barcode = pkg.barcode;
                item.description = `${item.barcode} - ${partner.name}`;
                item.quantity = 1;
                const widthUnit = unitsMap[pkg.widthUnitId];
                const heightUnit = unitsMap[pkg.heightUnitId];
                const depthUnit = unitsMap[pkg.depthUnitId];
                const grossWeightUnit = unitsMap[pkg.grossWeightUnitId];
                let width = 0;
                let height = 0;
                let depth = 0;
                let weight = packageType.defaultWeight;
                if (widthUnit) {
                    if (widthUnit.type === 'smaller') width = round(round(pkg.width) / round(widthUnit.ratio));
                    else if (widthUnit.type === 'reference') width = round(pkg.width);
                    else if (widthUnit.type === 'bigger') width = round(pkg.width) * round(widthUnit.ratio);
                }
                if (heightUnit) {
                    if (heightUnit.type === 'smaller') height = round(round(pkg.height) / round(heightUnit.ratio));
                    else if (heightUnit.type === 'reference') height = round(pkg.height);
                    else if (heightUnit.type === 'bigger') height = round(pkg.height) * round(heightUnit.ratio);
                }
                if (depthUnit) {
                    if (depthUnit.type === 'smaller') depth = round(round(pkg.depth) / round(depthUnit.ratio));
                    else if (depthUnit.type === 'reference') depth = round(pkg.depth);
                    else if (depthUnit.type === 'bigger') depth = round(pkg.depth) * round(depthUnit.ratio);
                }
                if (grossWeightUnit) {
                    if (grossWeightUnit.type === 'smaller')
                        weight = round(round(weight) / round(grossWeightUnit.ratio));
                    else if (grossWeightUnit.type === 'reference') weight = round(weight);
                    else if (grossWeightUnit.type === 'bigger') weight = round(weight) * round(grossWeightUnit.ratio);
                }
                width = round(width * 100);
                height = round(height * 100);
                depth = round(depth * 100);
                item.width = width;
                item.height = height;
                item.depth = depth;
                item.weight = weight;
                item.volumetricWeight = round((width * height * depth) / (carrier.volumetricWeightFactor || 5000));
                newShippingOrder.items.push(item);

                newShippingOrder = await app.collection('logistics.shipping-orders').create(newShippingOrder, {user});

                // noinspection ES6MissingAwait
                (async () => {
                    try {
                        await app.rpc('logistics.shipping-orders-approve', newShippingOrder._id);
                    } catch (error) {
                        console.log('Shipping order approve ->', error);
                    }
                })();
            })();

            const cart = await app.collection('store.cart').findOne({
                orderId: order._id
            });
            const returnedProductIds = returnItems.map(returnItem => returnItem.productId);
            cart.items = cart.items.map(item => {
                if (returnedProductIds.includes(item.productId)) {
                    item.returnShipmentTackingCode = newShippingOrder.code;
                    item.isReturnRequested = true;
                }

                return item;
            });
            await app.collection('store.cart').patch(
                {_id: cart._id},
                {
                    items: cart.items
                }
            );

            const company = await app.collection('kernel.company').findOne({});

            await sendReturnOrderMail(
                app,
                store,
                cart,
                newShippingOrder.code,
                carrier.name,
                company,
                carrier.storeCode
            );

            return response.status(200).json({
                returnShipmentTackingCode: newShippingOrder.code,
                carrierName: carrier.name,
                storeCode: carrier.storeCode || '',
                legalName: company.legalName || ''
            });
        } else {
            const shippingOrder = await app.collection('logistics.shipping-orders').findOne({
                code: shipmentTackingCode,
                status: 'delivered'
            });
            if (!shippingOrder) {
                throw new Error('Shipment order is not found!');
            }

            const order = await app.collection('sale.orders').findOne({
                _id: shippingOrder.orderId
            });
            if (!order) {
                throw new Error('Order is not found!');
            }

            let returnOrder = {};
            returnOrder.module = 'ecommerce';
            returnOrder.status = 'draft';
            returnOrder.code = await app.rpc('kernel.common.request-number', {
                numberingId: numbering._id,
                save: true
            });
            returnOrder.storeId = store._id;
            returnOrder.branchId = order.branchId;
            returnOrder.returnReasonId = reasonId;
            returnOrder.partnerId = shippingOrder.partnerId;
            returnOrder.currencyId = shippingOrder.currencyId;
            returnOrder.currencyRate = 1;
            returnOrder.recordDate = now;
            returnOrder.issueDate = now;
            returnOrder.scheduledDate = now;
            returnOrder.requests = '';
            returnOrder.note = description || '';
            returnOrder.warehouseId = returnWarehouse._id;
            returnOrder.deliveryAddress = returnWarehouse.address;
            returnOrder.carrierId = shippingOrder.carrierId;
            returnOrder.transferIds = [shippingOrder.transferId];
            returnOrder.orderId = order._id;
            returnOrder.relatedDocuments = [
                {
                    collection: 'sale.orders',
                    view: 'sale.sales.orders',
                    title: 'Source Sales Orders',
                    ids: [shippingOrder.orderId]
                },
                {
                    collection: 'inventory.transfers',
                    view: 'inventory.operations.transfers',
                    title: 'Source Deliveries',
                    ids: [shippingOrder.transferId]
                }
            ];
            returnOrder.items = await app.rpc(
                'sale.decorate-return-items',
                {
                    items: returnItems.map(returnItem => ({
                        reference: order.code,
                        referenceId: order._id,
                        referenceCollection: 'sale.orders',
                        productId: returnItem.productId,
                        scheduledDate: now,
                        quantity: returnItem.quantity,
                        stockQuantity: 0,
                        orderedQuantity: 0,
                        assignedQuantity: 0,
                        availableQuantity: 0,
                        warehouseStockQuantity: 0,
                        warehouseOrderedQuantity: 0,
                        warehouseAssignedQuantity: 0,
                        warehouseAvailableQuantity: 0
                    })),
                    field: 'productId',
                    model: _.omit(returnOrder, 'items'),
                    productFields: [
                        'code',
                        'name',
                        'displayName',
                        'definition',
                        'type',
                        'baseUnitId',
                        'salesUnitId',
                        'barcode',
                        'unitRatios',
                        'unitConversions',
                        'salesNote',
                        'isSimple'
                    ]
                },
                {user}
            );
            returnOrder = await app.collection('sale.returns').create(returnOrder, {user});
            await app.rpc(
                'sale.returns-save',
                {
                    id: returnOrder._id,
                    data: {
                        ..._.omit(returnOrder, '_id'),
                        status: 'approved'
                    }
                },
                {user}
            );
            returnOrder = await app.collection('sale.returns').get(returnOrder._id);

            let newShippingOrder = {};
            await (async () => {
                const transfer = await app.collection('inventory.transfers').get(returnOrder.transferIds[0]);
                const originalPackage = await app
                    .collection('logistics.packages')
                    .get(shippingOrder.items[0].packageId);
                const packageType = await app.collection('logistics.package-types').get(originalPackage.packageTypeId);
                const lengthUnit = await app.collection('kernel.units').findOne({
                    category: 'length',
                    symbol: 'm',
                    $select: ['symbol']
                });
                const weightUnit = await app.collection('kernel.units').findOne({
                    category: 'weight',
                    symbol: 'kg',
                    $select: ['symbol']
                });
                const round = n => app.roundNumber(n, 4);

                newShippingOrder.type = 'return';
                newShippingOrder.transferId = transfer._id;
                newShippingOrder.orderId = order._id;
                newShippingOrder.status = 'draft';
                newShippingOrder.code = microtime.now();
                newShippingOrder.carrierId = shippingOrder.carrierId;
                newShippingOrder.carrierCode = shippingOrder.carrierCode;
                newShippingOrder.carrierName = shippingOrder.carrierName;
                newShippingOrder.partnerId = shippingOrder.partnerId;
                newShippingOrder.partnerIsCompany = shippingOrder.partnerIsCompany;
                newShippingOrder.partnerCode = shippingOrder.partnerCode;
                newShippingOrder.partnerTinIdentity = shippingOrder.partnerTinIdentity;
                newShippingOrder.partnerTaxDepartment = shippingOrder.partnerTaxDepartment;
                newShippingOrder.partnerEmail = shippingOrder.partnerEmail;
                newShippingOrder.partnerPhone = shippingOrder.partnerPhone;
                newShippingOrder.currencyId = shippingOrder.currencyId;
                newShippingOrder.recordDate = now;
                newShippingOrder.issueDate = now;
                newShippingOrder.shippingPaymentType = shippingOrder.shippingPaymentType;
                newShippingOrder.packagingType = shippingOrder.packagingType;
                newShippingOrder.warehouseId = returnWarehouse._id;
                newShippingOrder.deliveryAddress = returnWarehouse.address;
                newShippingOrder.warehouseAddress = returnWarehouse.address;
                newShippingOrder.integrationType = shippingOrder.integrationType;
                newShippingOrder.integrationParams = shippingOrder.integrationParams;
                newShippingOrder.volumetricWeightFactor = shippingOrder.volumetricWeightFactor;
                newShippingOrder.items = [];

                const pkgNumbering = await app.collection('kernel.numbering').findOne({
                    code: 'logisticsPackageNumbering',
                    $select: ['_id'],
                    $disableInUseCheck: true,
                    $disableActiveCheck: true,
                    $disableSoftDelete: true
                });
                let pkg = {};
                pkg.code = await app.rpc('kernel.common.request-number', {numberingId: pkgNumbering._id, save: true});
                pkg.packageTypeId = originalPackage.packageTypeId;
                pkg.carrierId = shippingOrder.carrierId;
                pkg.partnerId = transfer.partnerId;
                pkg.barcode = generateRandomBarcode(16);
                pkg.recordDate = newShippingOrder.recordDate;
                pkg.isUsed = false;
                pkg.height = packageType.height;
                pkg.heightUnitId = packageType.heightUnitId;
                pkg.width = packageType.width;
                pkg.widthUnitId = packageType.widthUnitId;
                pkg.depth = packageType.depth;
                pkg.depthUnitId = packageType.depthUnitId;
                pkg.netWeight = packageType.defaultWeight;
                pkg.netWeightUnitId = packageType.defaultWeightUnitId;
                pkg.grossWeight = packageType.defaultWeight;
                pkg.grossWeightUnitId = packageType.defaultWeightUnitId;
                pkg.items = [];
                for (const transferItem of transfer.items || []) {
                    if (Array.isArray(transferItem.subItems)) {
                        for (const subItem of transferItem.subItems) {
                            const item = {};

                            item.id = `${transferItem.id}${subItem.serialNumber}${subItem.lotNumber}`;
                            item.transferId = transfer._id;
                            item.transferCode = transfer.code;
                            item.productId = transferItem.productId;
                            item.productCode = transferItem.productCode;
                            item.productDefinition = transferItem.productDefinition;
                            item.barcode = transferItem.barcode;
                            item.serialNumber = subItem.serialNumber;
                            item.lotNumber = subItem.lotNumber;
                            item.height = 0;
                            item.heightUnitId = lengthUnit._id;
                            item.heightUnitName = lengthUnit.symbol;
                            item.width = 0;
                            item.widthUnitId = lengthUnit._id;
                            item.widthUnitName = lengthUnit.symbol;
                            item.depth = 0;
                            item.depthUnitId = lengthUnit._id;
                            item.depthUnitName = lengthUnit.symbol;
                            item.netWeight = 0;
                            item.netWeightUnitId = weightUnit._id;
                            item.netWeightUnitName = weightUnit.symbol;
                            item.grossWeight = 0;
                            item.grossWeightUnitId = weightUnit._id;
                            item.grossWeightUnitName = weightUnit.symbol;
                            item.unitId = transferItem.unitId;
                            item.quantity = subItem.quantity;

                            pkg.items.push(item);
                        }
                    } else {
                        const item = {};

                        item.id = transferItem.id;
                        item.transferId = transfer._id;
                        item.transferCode = transfer.code;
                        item.productId = transferItem.productId;
                        item.productCode = transferItem.productCode;
                        item.productDefinition = transferItem.productDefinition;
                        item.barcode = transferItem.barcode;
                        item.serialNumber = '';
                        item.lotNumber = '';
                        item.height = 0;
                        item.heightUnitId = lengthUnit._id;
                        item.heightUnitName = lengthUnit.symbol;
                        item.width = 0;
                        item.widthUnitId = lengthUnit._id;
                        item.widthUnitName = lengthUnit.symbol;
                        item.depth = 0;
                        item.depthUnitId = lengthUnit._id;
                        item.depthUnitName = lengthUnit.symbol;
                        item.netWeight = 0;
                        item.netWeightUnitId = weightUnit._id;
                        item.netWeightUnitName = weightUnit.symbol;
                        item.grossWeight = 0;
                        item.grossWeightUnitId = weightUnit._id;
                        item.grossWeightUnitName = weightUnit.symbol;
                        item.unitId = transferItem.unitId;
                        item.quantity = transferItem.actualQty;

                        pkg.items.push(item);
                    }
                }
                const products = await app.collection('inventory.products').find({
                    _id: {$in: _.uniq(pkg.items.map(item => item.productId))},
                    $select: ['unitMeasurements'],
                    $disableActiveCheck: true,
                    $disableSoftDelete: true
                });
                const productsMap = {};
                for (const product of products) {
                    productsMap[product._id] = product;
                }
                pkg.items = pkg.items.map(item => {
                    const product = productsMap[item.productId];
                    const um = product.unitMeasurements.find(um => um.unitId === item.unitId);

                    if (!!um) {
                        if (um.height && um.heightUnitId) {
                            item.height = um.height;
                            item.heightUnitId = um.heightUnitId;
                        }
                        if (um.width && um.widthUnitId) {
                            item.width = um.width;
                            item.widthUnitId = um.widthUnitId;
                        }
                        if (um.depth && um.depthUnitId) {
                            item.depth = um.depth;
                            item.depthUnitId = um.depthUnitId;
                        }
                        if (um.netWeight && um.netWeightUnitId) {
                            item.netWeight = um.netWeight;
                            item.netWeightUnitId = um.netWeightUnitId;
                        }
                        if (um.grossWeight && um.grossWeightUnitId) {
                            item.grossWeight = um.grossWeight;
                            item.grossWeightUnitId = um.grossWeightUnitId;
                        }
                    }

                    return item;
                });
                let unitIds = [];
                for (const item of pkg.items) {
                    unitIds.push(
                        ...[
                            pkg.grossWeightUnitId,
                            pkg.widthUnitId,
                            pkg.heightUnitId,
                            pkg.depthUnitId,
                            item.unitId,
                            item.heightUnitId,
                            item.widthUnitId,
                            item.depthUnitId,
                            item.netWeightUnitId,
                            item.grossWeightUnitId
                        ]
                    );
                }
                unitIds = _.uniq(unitIds);
                const units = await app.collection('kernel.units').find({
                    _id: {$in: unitIds},
                    $disableActiveCheck: true,
                    $disableSoftDelete: true
                });
                const unitsMap = {};
                for (const unit of units) {
                    unitsMap[unit._id] = unit;
                }
                pkg.items = pkg.items.map(item => {
                    item.unitName = unitsMap[item.unitId].name;
                    item.heightUnitName = unitsMap[item.heightUnitId].symbol;
                    item.widthUnitName = unitsMap[item.widthUnitId].symbol;
                    item.depthUnitName = unitsMap[item.depthUnitId].symbol;
                    item.netWeightUnitName = unitsMap[item.netWeightUnitId].symbol;
                    item.grossWeightUnitName = unitsMap[item.grossWeightUnitId].symbol;

                    return item;
                });
                pkg = await app.collection('logistics.packages').create(pkg, {user});

                const item = {};
                item.id = Random.id();
                item.packageId = pkg._id;
                item.barcode = pkg.barcode;
                item.description = shippingOrder.items[0].description;
                item.quantity = 1;
                const widthUnit = unitsMap[pkg.widthUnitId];
                const heightUnit = unitsMap[pkg.heightUnitId];
                const depthUnit = unitsMap[pkg.depthUnitId];
                let width = 0;
                let height = 0;
                let depth = 0;
                if (widthUnit) {
                    if (widthUnit.type === 'smaller') width = round(round(pkg.width) / round(widthUnit.ratio));
                    else if (widthUnit.type === 'reference') width = round(pkg.width);
                    else if (widthUnit.type === 'bigger') width = round(pkg.width) * round(widthUnit.ratio);
                }
                if (heightUnit) {
                    if (heightUnit.type === 'smaller') height = round(round(pkg.height) / round(heightUnit.ratio));
                    else if (heightUnit.type === 'reference') height = round(pkg.height);
                    else if (heightUnit.type === 'bigger') height = round(pkg.height) * round(heightUnit.ratio);
                }
                if (depthUnit) {
                    if (depthUnit.type === 'smaller') depth = round(round(pkg.depth) / round(depthUnit.ratio));
                    else if (depthUnit.type === 'reference') depth = round(pkg.depth);
                    else if (depthUnit.type === 'bigger') depth = round(pkg.depth) * round(depthUnit.ratio);
                }
                width = round(width * 100);
                height = round(height * 100);
                depth = round(depth * 100);
                item.width = width;
                item.height = height;
                item.depth = depth;
                item.weight = round(shippingOrder.items[0].weight);
                item.volumetricWeight = round(shippingOrder.items[0].volumetricWeight);
                newShippingOrder.items.push(item);

                newShippingOrder = await app.collection('logistics.shipping-orders').create(newShippingOrder, {user});

                // noinspection ES6MissingAwait
                (async () => {
                    try {
                        await app.rpc('logistics.shipping-orders-approve', newShippingOrder._id);
                    } catch (error) {
                        console.log('Shipping order approve ->', error);
                    }
                })();
            })();

            const cart = await app.collection('store.cart').findOne({
                orderId: shippingOrder.orderId
            });
            const returnedProductIds = returnItems.map(returnItem => returnItem.productId);
            cart.items = cart.items.map(item => {
                if (returnedProductIds.includes(item.productId)) {
                    item.returnShipmentTackingCode = newShippingOrder.code;
                    item.isReturnRequested = true;
                }

                return item;
            });
            await app.collection('store.cart').patch(
                {_id: cart._id},
                {
                    items: cart.items
                }
            );

            const company = await app.collection('kernel.company').findOne({});
            const carrier =
                (await app.collection('logistics.carriers').findOne({
                    _id: shippingOrder.carrierId,
                    $select: ['storeCode'],
                    $disableActiveCheck: true,
                    $disableSoftDelete: true
                })) ?? {};

            await sendReturnOrderMail(
                app,
                store,
                cart,
                newShippingOrder.code,
                shippingOrder.carrierName,
                company,
                carrier.storeCode
            );

            return response.status(200).json({
                returnShipmentTackingCode: newShippingOrder.code,
                carrierName: shippingOrder.carrierName,
                storeCode: carrier.storeCode || '',
                legalName: company.legalName || ''
            });
        }
    } catch (error) {
        console.error('Error while creating e-commerce return order! Error: ', error.message);

        return response.status(400).send({
            status: 'error',
            code: 400,
            message: 'An error occurred while requesting return!'
        });
    }
}

async function sendReturnOrderMail(app, store, cart, returnShipmentTackingCode, carrierName, company, storeCode) {
    try {
        const t = text => app.translate(text);
        const siteUrl = trim(store.website, '/');

        const template = `
<mj-wrapper full-width background-color="#FECACA" padding="24px" css-class="content-wrapper">
    <mj-section padding-bottom="48px" padding-top="32px">
        <mj-column>
            <mj-image align="center" src="${app.absoluteUrl('static/images/mail/return-request.png')}" width="150px"/>
        </mj-column>
    </mj-section>
    <mj-section>
        <mj-column>
            <mj-text font-size="21px" color="#DC2626" font-weight="600" padding="0" padding-bottom="12px">${t(
                'Your return request has been received'
            )}</mj-text>
            <mj-text>${t('You can find the return code for the products you have chosen to return below.')}</mj-text>
        </mj-column>
    </mj-section>

    <mj-section padding-top="16px">
        <mj-column padding-bottom="12px">
            <mj-text font-size="13px" font-weight="600" padding="0">${t('RETURN SHIPPING CODE')}</mj-text>
            <mj-text font-size="13px" padding="0">${returnShipmentTackingCode}</mj-text>
        </mj-column>
        <mj-column>
            <mj-text font-size="13px" font-weight="600" padding="0">${t('RETURN SHIPPING CARRIER')}</mj-text>
            <mj-text font-size="13px" padding="0">${carrierName}</mj-text>
        </mj-column>
    </mj-section>

    <mj-section padding-top="12px">
        <mj-column>
            <mj-text font-size="13px" font-weight="600" padding="0">${t(
                'RECIPIENT COMPANY TITLE INFORMATION'
            )}</mj-text>
            <mj-text font-size="13px" padding="0">${company.legalName}</mj-text>
        </mj-column>
        ${
            _.isEmpty(storeCode)
                ? ''
                : `
                <mj-column>
                    <mj-text font-size="13px" font-weight="600" padding="0">${t('CARGO STORE CODE')}</mj-text>
                    <mj-text font-size="13px" padding="0">${storeCode}</mj-text>
                </mj-column>`
        }
    </mj-section>
</mj-wrapper>

<mj-section padding-top="48px" padding-bottom="16px">
    <mj-column>
        <mj-text padding="0" font-size="18px" font-weight="600">${t('Returned Products')}</mj-text>
    </mj-column>
</mj-section>

<mj-wrapper background-color="white" padding-left="24px" padding-right="24px" padding-bottom="12px" padding-top="24px" css-class="content-wrapper-with-border">
    ${cart.items
        .map(item => {
            if (!item.isReturnRequested) return;

            return `
        <mj-section>
            <mj-column width="15%">
                <mj-image padding="0" padding-right="32px" padding-bottom="12px" src="${item.productImage}.png"/>
            </mj-column>
            <mj-column width="85%">
                <mj-text font-size="15px" padding="0">
                    <a href="${
                        !!item.productLink ? `${siteUrl}${item.productLink}` : `${siteUrl}/${item.productSlug}`
                    }">
                        ${item.productName}
                    </a>
                </mj-text>
                <mj-text padding="0" padding-top="8px" font-size="14px" font-weight="500">
                    ${t('Quantity')}: ${item.quantity}
                </mj-text>
            </mj-column>
        </mj-section>
        <mj-section>
            <mj-column>
                <mj-divider padding-top="16px" padding-bottom="16px"/>
            </mj-column>
        </mj-section>
    `;
        })
        .join('\n')}
</mj-wrapper>
    `;

        await app.mail({
            from: `${store.name} <${store.email || company.email}>`,
            to: cart.email,
            subject: `${app.translate('Return Request')}🔄`,
            noContentWrapper: true,
            template
        });
    } catch (error) {
        console.error(error.message);
    }
}
