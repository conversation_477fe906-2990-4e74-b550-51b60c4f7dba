import _ from 'lodash';
import {getPartnerAccounts} from '../../../accounting/methods/partner-ledger';
import {trim} from 'framework/helpers';

export default async function (app, store, request, response) {
    // Parametreleri al
    const {
        startDate,
        endDate,
        partnerTypes,
        partnerGroupIds,
        partnerTags,
        partnerId,
        currencyIds,
        branchIds,
        financialProjectIds,
        salespersonId,
        scope,
        groupByCurrency,
        groupByBranch,
        groupByProject,
        groupBySalesperson,
        hideZeroBalances,
        query = {},
        sort = {partnerCode: -1},
        skip = 0,
        limit = 50
    } = request.body;

    // Partner hesaplarını al
    const accountIds = await getPartnerAccounts(app, {excludeAdditionalAllowances: false});

    // Ana filtreyi oluştur
    const match = {
        accountId: {$in: accountIds}
    };
    if (startDate) match.issueDate = {...(match.issueDate || {}), $gte: new Date(startDate)};
    if (endDate) match.issueDate = {...(match.issueDate || {}), $lte: new Date(endDate)};
    if (partnerId) match.partnerId = partnerId;
    if (partnerTypes && partnerTypes.length > 0) match.partnerType = {$in: partnerTypes};
    if (partnerGroupIds && partnerGroupIds.length > 0) match.partnerGroupId = {$in: partnerGroupIds};
    if (partnerTags && partnerTags.length > 0) match.partnerTags = {$in: partnerTags};
    if (currencyIds && currencyIds.length > 0) match.currencyId = {$in: currencyIds};
    if (branchIds && branchIds.length > 0) match.branchId = {$in: branchIds};
    if (financialProjectIds && financialProjectIds.length > 0) match.financialProjectId = {$in: financialProjectIds};
    if (salespersonId) match.salespersonId = salespersonId;
    if (scope) match.scope = scope;
    // Ekstra query'den gelen filtreler
    if (query && typeof query === 'object') Object.assign(match, query);

    // Pipeline: sadece filtre, sıralama, sayfalama ve projection
    const pipeline = [
        { $match: match },
        { $sort: sort },
        { $skip: skip },
        { $limit: limit },
        { $project: {
            partnerId: 1,
            partnerType: 1,
            partnerCode: 1,
            partnerName: 1,
            partnerTinIdentity: 1,
            partnerGroupId: 1,
            partnerTags: 1,
            date: 1,
            currencyId: 1,
            currencyName: 1,
            branchId: 1,
            branchCode: 1,
            branchName: 1,
            financialProjectId: 1,
            financialProjectCode: 1,
            financialProjectName: 1,
            salespersonId: 1,
            salespersonCode: 1,
            salespersonName: 1,
            scope: 1,
            debit: 1,
            credit: 1,
            debitFC: 1,
            creditFC: 1,
            balance: 1,
            balanceFC: 1,
            balanceType: 1
        }}
    ];

    // Sıfır bakiyeleri gizle
    if (hideZeroBalances) {
        pipeline.splice(1, 0, { $match: { balance: { $ne: 0 } } });
    }

    // Sonuçları çek
    const data = await app.collection('accounting_partner-balances').aggregate(pipeline);

    // summaryRow için toplamlar
    const summaryPipeline = [
        { $match: match },
        { $group: {
            _id: null,
            debit: { $sum: '$debit' },
            credit: { $sum: '$credit' },
            debitFC: { $sum: '$debitFC' },
            creditFC: { $sum: '$creditFC' },
            balance: { $sum: '$balance' },
            balanceFC: { $sum: '$balanceFC' }
        }},
        { $project: { _id: 0, debit: 1, credit: 1, debitFC: 1, creditFC: 1, balance: 1, balanceFC: 1 } }
    ];
    const summaryResult = (await app.collection('accounting_partner-balances').aggregate(summaryPipeline))[0] || {debit: 0, credit: 0, debitFC: 0, creditFC: 0, balance: 0, balanceFC: 0};

    // Toplam kayıt sayısı
    const count = await app.collection('accounting_partner-balances').countDocuments(match);

    // Tablo sonucu
    const tableResult = {
        total: count,
        data
    };

    return response.send({
        status: 'ok',
        tableResult,
        summaryResult
    });
}
