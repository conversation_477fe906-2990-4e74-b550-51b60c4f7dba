<template>
    <ui-view type="content"
             class="hr-attendance-tally"
             :extra-actions="extraActions"
             v-if="initialized">
        <div class="tally-wrapper">
            <header class="tally-header">
                <ui-scope id="hr.attendances.tally"
                          :applied-items="scopeAppliedItems"
                          :filters="scopeApplicableFilters"
                          @changed="handleScopeChange"
                          @initialized="scopeInitialized = true"/>
            </header>

            <section class="tally-content">
                <ui-table id="hr.attendances.tally"
                          ref="table"
                          :key="tableKey"
                          :columns="columns"
                          :get-rows="getRows"
                          row-model="serverSide"
                          :enable-sorting="true"
                          :enable-selection="false"
                          no-zebra
                          v-if="scopeInitialized"/>
            </section>

            <footer class="tally-footer">
                <div class="current-tally">{{ currentTally }}</div>

                <div class="tally-day-types">
                    <div class="tally-day-type">
                        <el-label is-dot color="green"/>
                        {{ $t('Full') }}
                    </div>
                    <div class="tally-day-type">
                        <el-label is-dot color="amber"/>
                        {{ $t('Not Full') }}
                    </div>
                    <div class="tally-day-type">
                        <el-label is-dot color="red"/>
                        {{ $t('Absent') }}
                    </div>
                    <div class="tally-day-type">
                        <el-label is-dot color="lightBlue"/>
                        {{ $t('Paid Leave') }}
                    </div>
                    <div class="tally-day-type">
                        <el-label is-dot color="purple"/>
                        {{ $t('Unpaid Leave') }}
                    </div>
                    <div class="tally-day-type">
                        <el-label is-dot color="blueGrey"/>
                        {{ $t('Holiday') }}
                    </div>
                    <div class="tally-day-type">
                        <el-label is-dot color="brown"/>
                        {{ $t('Global Leave') }}
                    </div>
                </div>
            </footer>
        </div>
    </ui-view>
</template>

<script>
import _ from 'lodash';
import DayCellRenderer from './_day-cell-renderer';
import ManuelRecord from './_manuel-record';

export default {
    data: () => ({
        scopeQuery: {},
        firstYear: null,
        tableKey: _.uniqueId('tally-table_'),
        refreshingForRealTime: false,
        scopeInitialized: false,
        initialized: false
    }),

    computed: {
        extraActions() {
            const self = this;

            return [
                {
                    name: 'check-all',
                    title: 'Check All',
                    icon: 'fal fa-user-check',
                    handler: this.handleCheckAll
                }
            ];
        },
        columns() {
            const self = this;
            const round = this.$app.roundNumber;
            const format = this.$app.format;
            const now = this.$datetime.local();
            const query = this.scopeQuery;
            const hasManuelRecordPermission =
                this.$checkPermission({
                    type: 'permission',
                    name: 'hr.manage-tally-records'
                }) ||
                (this.$checkPermission({
                    type: 'record',
                    name: 'hr.manuel-tally-records',
                    method: 'create'
                }) &&
                this.$checkPermission({type: 'record', name: 'hr.manuel-tally-records', method: 'update'}) &&
                this.$checkPermission({type: 'record', name: 'hr.manuel-tally-records', method: 'patch'}) &&
                this.$checkPermission({type: 'record', name: 'hr.manuel-tally-records', method: 'read'}));
            const columns = [];
            let year = now.year;
            let month = now.month;

            // Get year and month.
            if (_.isNumber(query.year)) {
                year = query.year;
            } else if (Array.isArray(query.$and)) {
                query.$and.forEach(q => {
                    if (_.isNumber(q.year)) {
                        year = q.year;
                    }
                });
            }
            if (_.isNumber(query.month)) {
                month = query.month;
            } else if (Array.isArray(query.$and)) {
                query.$and.forEach(q => {
                    if (_.isNumber(q.month)) {
                        month = q.month;
                    }
                });
            }

            // Get last month day.
            const lastMonthDay = now.set({year, month}).endOf('month').day;

            columns.push(...[
                {
                    field: 'employeeCode',
                    label: 'Employee code',
                    relationParams(params) {
                        const data = params.data;
                        const relation = {};

                        relation.isVisible = _.isString(data.partnerId) && _.isString(data.employeeCode);

                        if (relation.isVisible) {
                            relation.view = 'hr.employee-relations.employees-detail';
                            relation.id = data.partnerId;
                        }

                        return relation;
                    },
                    pinned: 'left',
                    suppressMovable: true,
                    lockPosition: true,
                    sortable: false,
                    width: 120
                },
                {
                    field: 'employeeName',
                    label: 'Employee name',
                    suppressMovable: true,
                    lockPosition: true,
                    pinned: 'left',
                    sortable: true,
                    width: 180
                },
                {
                    field: 'identity',
                    label: 'Identity no',
                    suppressMovable: true,
                    lockPosition: true,
                    visible: false,
                    sortable: false,
                    width: 150
                },
                {
                    field: 'contractCode',
                    label: 'Contract code',
                    relationParams(params) {
                        const data = params.data;
                        const relation = {};

                        relation.isVisible = _.isString(data.contractId) && _.isString(data.contractCode);

                        if (relation.isVisible) {
                            relation.view = 'hr.employee-relations.contracts-detail';
                            relation.id = data.contractId;
                        }

                        return relation;
                    },
                    suppressMovable: true,
                    lockPosition: true,
                    visible: false,
                    sortable: false,
                    width: 120
                },
                {
                    field: 'branchName',
                    label: 'Branch office',
                    suppressMovable: true,
                    lockPosition: true,
                    visible: false,
                    sortable: false,
                    width: 180
                }
            ]);

            columns.push(..._.range(1, lastMonthDay + 1).map(i => ({
                field: `d${i}`,
                label: `${i} ${now.set({year, month, day: i}).toFormat('EEE')}`,
                width: 75,
                suppressMenu: true,
                lockVisible: true,
                suppressMovable: true,
                lockPosition: true,
                suppressColumnsToolPanel: true,
                resizable: false,
                sortable: false,
                headerClass: now.day === i ? 'is-current-day' : '',
                cellClass: 'tally-day-cell-wrapper text-right',
                valueFormatter: ({value: dayRecord}) => {
                    if (_.isPlainObject(dayRecord)) {
                        const workedDuration = format(round((dayRecord.workedDuration || 0) / 60, 1), 'decimal', {number: {precision: 1}});

                        if (dayRecord.overtimeDuration > 0) {
                            const overtimeDuration = format(round((dayRecord.overtimeDuration || 0) / 60, 1), 'decimal', {number: {precision: 1}});

                            return `${workedDuration} + ${overtimeDuration}`;
                        }

                        return workedDuration;
                    }

                    return '';
                },
                cellRendererFramework: DayCellRenderer
            })));

            columns.push(...[
                {
                    field: 'workedHours',
                    label: 'Worked (Hour)',
                    sortable: false
                },
                {
                    field: 'workedDays',
                    label: 'Worked (Day)',
                    sortable: false
                },
                {
                    field: 'absenceDays',
                    label: 'Absence (Day)',
                    sortable: false
                },
                {
                    field: 'paidLeaveDays',
                    label: 'Paid Leave (Day)',
                    sortable: false
                },
                {
                    field: 'unpaidLeaveDays',
                    label: 'Unpaid Leave (Day)',
                    sortable: false
                },
                {
                    field: 'globalLeaveDays',
                    label: 'Global Leave (Day)',
                    sortable: false
                },
                {
                    field: 'holidayDays',
                    label: 'Holiday (Day)',
                    sortable: false
                },
                {
                    field: 'overtimeHours',
                    label: 'Overtime (Hour)',
                    sortable: false
                },
                {
                    field: 'workingDays',
                    label: 'Working (Day)',
                    sortable: false
                },
                {
                    field: 'totalDays',
                    label: 'Total (Day)',
                    sortable: false
                }
            ].map(c => {
                c.suppressMovable = true;
                c.lockPosition = true;
                c.cellClass = 'text-right';
                c.width = 120;

                c.cellClass = params => {
                    const data = params.data;
                    const classes = ['text-right'];

                    if (_.isPlainObject(data) && Array.isArray(data.overriddenFields) && data.overriddenFields.indexOf(c.field) !== -1) {
                        classes.push('is-overridden');
                    }

                    if (hasManuelRecordPermission) {
                        classes.push('is-overridable');
                    }

                    return classes;
                };

                c.render = params => {
                    let value = params.value;

                    if (_.isNumber(value) && !_.isInteger(value)) {
                        value = format(round(value, 2), 'amount');
                    }

                    return value;
                };

                c.onCellClicked = params => {
                    const data = params.data;

                    if (_.isPlainObject(data) && hasManuelRecordPermission) {
                        this.$program.dialog({
                            component: ManuelRecord,
                            params: {
                                title: this.$t('Manuel Record'),
                                year: data.year,
                                month: data.month,
                                contractId: data.contractId,
                                employmentCode: data.employmentCode,
                                isOverridden: Array.isArray(data.overriddenFields) && data.overriddenFields.indexOf(c.field) !== -1,
                                field: c.field,
                                label: c.label,
                                value: params.value
                            }
                        });
                    }
                };

                return c;
            }));

            return columns;
        },
        scopeAppliedItems() {
            const currentYear = this.$datetime.local().year;
            const currentMonth = this.$datetime.local().month;

            return [
                {
                    type: 'filter',
                    payload: {
                        label: 'Year',
                        field: 'year',
                        value: {
                            value: currentYear,
                            label: currentYear
                        },
                        items: _.range(this.firstYear, this.firstYear + 101).map(y => ({value: y, label: y})),
                        isRemovable: false,
                        singleSelect: true
                    }
                },
                {
                    type: 'filter',
                    payload: {
                        label: 'Month',
                        field: 'month',
                        value: {
                            value: currentMonth,
                            label: this.$datetime.local().set({month: currentMonth}).toFormat('MMMM')
                        },
                        items: _.range(1, 13).map(i => ({
                            value: i,
                            label: this.$datetime.local().set({month: i}).toFormat('MMMM')
                        })),
                        isRemovable: false,
                        singleSelect: true
                    }
                }
            ];
        },
        scopeApplicableFilters() {
            const self = this;

            return [
                {
                    field: 'partnerId',
                    label: 'Employee',
                    collection: 'kernel.partners',
                    extraFields: ['code'],
                    filters: {type: 'employee', $sort: {name: 1}},
                    template: '{{code}} - {{name}}'
                },
                {
                    field: 'branchId',
                    label: 'Branch office',
                    collection: 'kernel.branches',
                    filters: {
                        _id: {$in: this.$user.branchIds},
                        $sort: {name: 1}
                    },
                    condition() {
                        return self.$setting('system.multiBranch');
                    }
                },
                {
                    field: 'groupId',
                    label: 'Group',
                    collection: 'kernel.partner-groups',
                    filters: {type: 'employee', $sort: {name: 1}}
                },
                {
                    field: 'departmentId',
                    label: 'Department',
                    collection: 'hr.departments',
                    valueFrom: '_id',
                    labelFrom: 'path',
                    operator: 'starts-with',
                    filters: {
                        'tree.hasChild': {$ne: true}
                    }
                },
                {
                    field: 'positionId',
                    label: 'Position',
                    collection: 'kernel.positions',
                    filters: {$sort: {name: 1}}
                },
                {
                    field: 'occupationId',
                    label: 'Occupation',
                    collection: 'kernel.occupations',
                    filters: {$sort: {name: 1}},
                    extraFields: ['code'],
                    template: '{{code}} - {{name}}'
                }
            ];
        },
        currentTally() {
            const query = this.scopeQuery;
            let year = this.$datetime.local().year;
            let month = this.$datetime.local().month;

            // Get year and month.
            if (_.isNumber(query.year)) {
                year = query.year;
            } else if (Array.isArray(query.$and)) {
                query.$and.forEach(q => {
                    if (_.isNumber(q.year)) {
                        year = q.year;
                    }
                });
            }
            if (_.isNumber(query.month)) {
                month = query.month;
            } else if (Array.isArray(query.$and)) {
                query.$and.forEach(q => {
                    if (_.isNumber(q.month)) {
                        month = q.month;
                    }
                });
            }

            // Get date.
            const date = this.$datetime.local().set({year, month});

            return `1 - ${date.endOf('month').day} ${date.toFormat('LLLL yyyy')}`;
        }
    },

    methods: {
        async handleCheckAll() {
            this.$params('loading', true);

            const now = this.$datetime.local();
            const query = this.scopeQuery;
            let year = now.year;
            let month = now.month;
            const branchIds = [];
            const partnerIds = [];

            // Get year and month.
            if (_.isNumber(query.year)) {
                year = query.year;
            } else if (Array.isArray(query.$and)) {
                query.$and.forEach(q => {
                    if (_.isNumber(q.year)) {
                        year = q.year;
                    }
                });
            }
            if (_.isNumber(query.month)) {
                month = query.month;
            } else if (Array.isArray(query.$and)) {
                query.$and.forEach(q => {
                    if (_.isNumber(q.month)) {
                        month = q.month;
                    }
                });
            }

            if (!!query.branchId && Array.isArray(query.branchId.$in)) {
                branchIds.push(...query.branchId.$in);
            } else if (Array.isArray(query.$and)) {
                query.$and.forEach(q => {
                    if (!!q.branchId && Array.isArray(q.branchId.$in)) {
                        branchIds.push(...q.branchId.$in)
                    }
                });
            }

            if (!!query.partnerId && Array.isArray(query.partnerId.$in)) {
                partnerIds.push(...query.partnerId.$in);
            } else if (Array.isArray(query.$and)) {
                query.$and.forEach(q => {
                    if (!!q.partnerId && Array.isArray(q.partnerId.$in)) {
                        partnerIds.push(...q.partnerId.$in)
                    }
                });
            }

            const date = now.set({year, month});
            const dates = [];

            if (date.startOf('month') < now.startOf('month')) {
                for (let i = 1; i <= date.endOf('month').day; i++) {
                    dates.push(date.set({day: i}).startOf('day').toJSDate());
                }
            } else if (+date.startOf('month') === +now.startOf('month')) {
                for (let i = 1; i <= now.day; i++) {
                    dates.push(now.set({day: i}).startOf('day').toJSDate());
                }
            }
            if (dates.length > 0) {
                await this.$rpc('hr.tally-update-records', {
                    date: dates,
                    ...(branchIds.length > 0 ? {
                        branchIds: _.uniq(branchIds)
                    } : {}),
                    ...(partnerIds.length > 0 ? {
                        partnerIds: _.uniq(partnerIds)
                    } : {})
                });
            }

            this.$params('loading', false);
        },
        handleScopeChange(model) {
            this.scopeQuery = model.query;
            this.tableKey = _.uniqueId('tally-table_');
        },

        async getRows(query, params) {
            if (!this.refreshingForRealTime) {
                this.$params('loading', true);
            }

            const startRow = params.request.startRow;
            const endRow = params.request.endRow;

            let sortModel = params.request.sortModel || [];
            let sortInfo = {};
            
            if (sortModel.length > 0) {
                sortInfo = {
                    $sort: {}
                };
                sortModel.forEach(sort => {
                    sortInfo.$sort[sort.colId] = sort.sort === 'asc' ? 1 : -1;
                });
            }

            const result = await this.$rpc('hr.tally-get-records', {
                query: this.scopeQuery,
                limit: endRow - startRow,
                skip: startRow,
                ...sortInfo
            });

            if (this.refreshingForRealTime) {
                this.refreshingForRealTime = false;
            } else {
                this.$params('loading', false);
            }

            setTimeout(() => {
                const table = this.$refs.table;
                if (_.isObject(table) && !!table.api) {
                    table.api.redrawRows();
                }
            }, 250);

            return result;
        },
        refresh(refreshingForRealTime) {
            if (refreshingForRealTime) {
                this.refreshingForRealTime = true;
            }

            const table = this.$refs.table;
            if (_.isObject(table)) {
                table.refreshData();
            }
        }
    },

    async created() {
        this.$params('loading', true);

        this.refreshForRealTime = _.debounce(this.refresh, 300, {leading: false, trailing: true});
        this.firstYear = await this.$rpc('hr.tally-find-first-year');

        this.$collection('hr.tally-records').on('all', this.refreshForRealTime);
        this.$collection('hr.manuel-tally-records').on('all', this.refreshForRealTime);

        this.initialized = true;

        this.$params('loading', false);
    },

    beforeDestroy() {
        this.$collection('hr.tally-records').removeListener('all', this.refreshForRealTime);
        this.$collection('hr.manuel-tally-records').removeListener('all', this.refreshForRealTime);
    }
};
</script>

<style lang="scss">
//noinspection CssUnknownTarget
@import "core";

.hr-attendance-tally {
    .tally-wrapper {
        position: relative;
        display: block;
        width: 100%;
        height: 100%;
        padding: 62px 0 35px;
        background-color: #fff;
        overflow: hidden;
    }

    .tally-header {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 62px;
        padding: 8.5px;
    }

    .tally-content {
        position: relative;
        display: block;
        width: 100%;
        height: 100%;
        overflow: hidden;
    }

    .tally-footer {
        position: absolute;
        display: flex;
        flex-flow: row nowrap;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 35px;
        line-height: 35px;
        padding: 0 15px;
        border-top: 1px solid $border-color-light;

        .current-tally {
            flex: 1 1 0;
            font-size: 14px;
            font-weight: $font-weight-bold;
            color: $text-color-lighter;
        }

        .tally-day-types {
            display: flex;
            flex-flow: row nowrap;

            .tally-day-type {
                position: relative;
                margin-right: 15px;
                padding-left: 13px;
                font-size: 11px;
                color: $text-color-light;

                &:last-child {
                    margin-right: 0;
                }

                .el-label {
                    position: absolute;
                    top: 50%;
                    left: 0;
                    width: 10px;
                    height: 10px;
                    margin-top: -5px;
                }
            }
        }
    }

    .ag-theme-balham {
        .ag-header-cell.is-current-day {
            color: $primary;
        }
        
        .ag-header-cell-label {
            cursor: pointer;
        }

        .ag-cell {
            border: none;
            line-height: 21px;

            &.tally-day-cell-wrapper {
                padding: 0;
            }

            &.is-overridden:after {
                position: absolute;
                bottom: 0;
                left: 0;
                width: 100%;
                height: 2px;
                background-color: $warning;
                content: ' ';
            }

            &.is-overridable {
                cursor: pointer;

                &:hover:after {
                    position: absolute;
                    bottom: 0;
                    left: 0;
                    width: 100%;
                    height: 2px;
                    background-color: $primary;
                    content: ' ';
                }
            }
        }
    }
}
</style>
