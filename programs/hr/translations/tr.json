{"HR": "İK", "Human Resources": "<PERSON>nsan <PERSON>", "Employees": "Personeller", "External Employees": "<PERSON><PERSON>", "Employee Relations": "Personel İlişkileri", "Education Levels": "Tahsil Seviyeleri", "New Education Level": "Yeni Tahsil Seviyesi", "Foreign Languages": "Yabancı Diller", "New Foreign Language": "Yeni <PERSON>ı Dil", "Foreign Language Levels": "Yabancı Dil Seviyeleri", "New Foreign Language Level": "Yeni Yabancı Dil Seviyesi", "Driving Licence Types": "Sürücü Belgesi Türleri", "New Driving Licence Type": "<PERSON>ni Sürücü Belgesi Türü", "Fields Of Activity": "Faaliyet Alanları", "Field of activity": "Faaliyet alanı", "New Field Of Activity": "Yeni Faaliyet Alanı", "Working Types": "Çalışma Şekilleri", "Working type": "Çalışma şekli", "New Working Type": "Yeni Çalışma Şekli", "Last salary amount": "Son maaş miktarı", "Skills": "Yetenekler", "New Skill": "<PERSON><PERSON>", "Skill Levels": "Yetenek <PERSON>i", "New Skill Level": "<PERSON><PERSON>", "Kinship Degrees": "Yakınlık <PERSON>eleri", "New Kinship Degree": "Yeni Yakınlık Derecesi", "Medical Workup Types": "Tetkik Türleri", "New Medical Workup Type": "Yeni Tetkik Türü", "New Disabled Category": "<PERSON><PERSON>gor<PERSON>", "Disabled Categories": "Engellilik <PERSON>gorileri", "New Disabled Degree": "<PERSON><PERSON>", "Disabled Degrees": "<PERSON><PERSON><PERSON><PERSON>", "Employee Additional Information": "Personel Ek Bilgileri", "Employee Groups": "Personel Grupları", "Leave Types": "<PERSON><PERSON>", "New Leave Type": "<PERSON>ni İzin Türü", "Leave type": "İzin türü", "Missing Day Reasons": "Eksik Gü<PERSON>", "New Missing Day Reason": "Yeni Eksik Gün <PERSON>", "Yearly limit": "Yılık limit", "Maximum number of days allowed": "İzin verilen maksimum gün sayısı", "Limit renewal": "<PERSON><PERSON>", "Employment date": "<PERSON><PERSON><PERSON> giriş ta<PERSON>hi", "New year": "Yılbaşı", "Missing day reason": "<PERSON><PERSON><PERSON> gün <PERSON>", "Reduce the remaining leave": "İzin hakkından düşülsün", "Reduce the remaining leave as calendar day": "İzin hakkından takvim günü olarak düşülsün", "Paid leave": "Ücretli izin", "Calculate hourly": "Saatlik hesapla", "Block leave request": "<PERSON><PERSON> talebin<PERSON> en<PERSON>le", "Attendance": "Devamlılık", "Attendances": "Devamlılıklar", "Check in": "<PERSON><PERSON><PERSON>", "Check out": "Çıkış", "Check In": "<PERSON><PERSON><PERSON>", "Check Out": "Çıkış", "Import Attendances": "Devamlılıkları İçe Aktar", "Cannot find the employee with the code {{code}}.": "{{code}} kodu ile eş<PERSON>şen herhangi bir personel bulunamadı.", "Leaves": "<PERSON><PERSON><PERSON>", "New Leave": "<PERSON><PERSON>", "Leaves To Approve": "Onaylanacak İzinler", "Payroll record": "<PERSON><PERSON><PERSON> ka<PERSON>ı", "Manager Note": "Yönetici Notu", "Requested by type": "<PERSON><PERSON> eden türü", "HR employee": "IK çalışanı", "Itself": "<PERSON><PERSON><PERSON>", "Employee will work instead": "<PERSON><PERSON> b<PERSON>el", "Half day": "<PERSON><PERSON><PERSON><PERSON> gün", "Employment start date": "<PERSON><PERSON><PERSON> ba<PERSON><PERSON>a tarihi", "Leave duration": "<PERSON><PERSON> süresi", "Employee Leave": "Personel <PERSON>zni", "The working hours that is named {{name}} is invalid!": "{{name}} ad<PERSON>ı çalışma saatleri geçersiz!", "The end date cannot be earlier than the start date!": "<PERSON><PERSON><PERSON> tarihi, b<PERSON><PERSON><PERSON><PERSON><PERSON> tarihinden önce olamaz!", "The end hour cannot be earlier than the start hour!": "<PERSON><PERSON><PERSON> saati, ba<PERSON><PERSON><PERSON><PERSON> saati önce olamaz!", "Start hour": "Başlangıç <PERSON>", "End hour": "Bitiş <PERSON>ati", "Approve Leave": "<PERSON><PERSON><PERSON>", "Approve Leaves": "İzinleri Onayla", "Leaves Approved": "İzinler Onaylandı", "Leaves approved successfully.": "İzinler başarıyla onaylandı.", "Deny Leave": "<PERSON><PERSON><PERSON>", "Deny Leaves": "İzinleri Reddet", "Leaves Denied": "<PERSON>zin<PERSON> Reddedildi", "Leaves denied successfully.": "İzinler başarıyla reddedildi.", "Cancel Leave": "İzini İptal Et", "Advance Payments": "Avanslar", "New Advance Payment": "<PERSON><PERSON>", "Approve Advance Payment": "Avansı Onayla", "Approve Advance Payments": "Avansları Onayla", "Deny Advance Payment": "Avansı Reddet", "Deny Advance Payments": "Avansları Reddet", "Cancel Advance Payment": "Avansı İptal Et", "Payback type": "<PERSON><PERSON><PERSON><PERSON>", "Salary": "Maaş", "Debt": "<PERSON><PERSON><PERSON>", "Request date": "<PERSON><PERSON> tarihi", "Payback": "<PERSON><PERSON>", "Payback date": "<PERSON><PERSON><PERSON><PERSON>", "Advance Payments Approved": "Avanslar Onaylandı", "Advance payments approved successfully.": "Avanslar başarıyla onaylandı.", "Advance Payments Denied": "Avanslar Reddedildi", "Advance payments denied successfully.": "Avanslar başarıyla reddedildi.", "You must enter at least one payment item!": "En az bir adet ödeme satırı girilmelidir!", "You must enter at least one payback item!": "En az bir adet geri ödeme satırı girilmelidir!", "You must enter at least one payback installment item!": "En az bir adet geri ödeme taksit satırı girilmelidir!", "The Payment and payback scope rates should be equal!": "Ödeme ve geri ödeme kapsam oranları eşit olmalıdır!", "Payment scope rate": "<PERSON><PERSON><PERSON> ka<PERSON> oranı", "Payback scope rate": "<PERSON><PERSON><PERSON> ka<PERSON> oranı", "Advance Payments To Approve": "Onaylanacak Avanslar", "Advance payment": "Avans", "Bonus": "Prim", "Net": "Net", "Gross": "<PERSON><PERSON><PERSON><PERSON>", "Account type": "<PERSON><PERSON><PERSON>", "Calculation rule": "<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>ı", "Deduction day": "<PERSON><PERSON><PERSON>", "Work day": "İş günü", "Calendar day": "<PERSON><PERSON><PERSON><PERSON>", "Payback with installment": "Taks<PERSON><PERSON> geri <PERSON>me", "Advance payment type": "<PERSON>ns türü", "Advance Payment Types": "<PERSON><PERSON>", "New Advance Payment Type": "Yeni Avans Türü", "Limit calculation": "<PERSON>it <PERSON>", "Limit amount": "Limit tutarı", "Limit percentage": "<PERSON>it <PERSON>", "Advance payment limit": "Avans limiti", "Additional Allowances": "<PERSON><PERSON>", "Additional allowance": "<PERSON>k <PERSON>", "Earnings": "Kazanç", "Additional Allowance Types": "Ek Ödenek Türleri", "New Additional Allowance Type": "Yeni Ek Ödenek Türü", "Cancel Additional Allowance": "Ek Ödeneği İptal Et", "Approve Additional Allowance": "Ek Ödeneği On<PERSON>la", "New Additional Allowance": "Yeni Ek Ödenek", "Additional allowance type": "Ek ödenek türü", "Monthly wage percentage": "Aylık ücret yüzdesi", "Daily wage percentage": "Günlük ücret yüzdesi", "Hourly wage percentage": "Saatlik ücret yüzdesi", "Earnings percentage": "Kazanç yüzdesi", "Allowance date": "<PERSON><PERSON><PERSON> ta<PERSON>", "Allowance type": "Ödenek türü", "Allowance day": "Ödenek gü<PERSON>ü", "Multiplier": "Çarpan", "Amount / Percentage": "Tutar / Yüzde", "Deductions": "<PERSON><PERSON><PERSON><PERSON>", "New Deduction": "<PERSON><PERSON>", "Approve Deduction": "<PERSON><PERSON><PERSON><PERSON>", "Cancel Deduction": "Kesintiyi İptal Et", "Deduction type": "<PERSON><PERSON><PERSON>", "Deduction date": "<PERSON><PERSON><PERSON> ta<PERSON>", "Deduction Types": "<PERSON><PERSON><PERSON>", "New Deduction Type": "<PERSON><PERSON>", "Exempt": "Muaf", "Not exempt": "<PERSON><PERSON>", "Is advanced payment deduction type": "Avans kesinti tür<PERSON>", "Deduction type is required!": "<PERSON><PERSON><PERSON> türü z<PERSON>r!", "Overtime Types": "Fazla Mesai Türleri", "New Overtime Type": "Yeni Fazla Mesai Türü", "Quarter (15 min)": "<PERSON><PERSON><PERSON> (15 dk)", "Daily limit": "Günlük limit", "Multipliers": "Çarpanlar", "Multiplier codes must be unique!": "Çarpan kodları eşsiz olmalıdır!", "Overtimes": "<PERSON><PERSON><PERSON>", "New Overtime": "<PERSON><PERSON>", "Overtime type": "Fazla mesai türü", "Cancel Overtime": "Fazla Mesaiyi İptal Et", "Approve Overtime": "Fazla <PERSON>", "Overtime duration": "Fazla mesai süresi", "The end hour cannot be before the start hour!": "Bitiş saati başlangıç saatinden önce olamaz!", "Duration (Min)": "S<PERSON><PERSON> (Dk)", "Multiplier code": "Çarpan kodu", "Foreclosure Types": "<PERSON><PERSON>ra <PERSON>", "New Foreclosure Type": "Yeni İcra Türü", "Working hour types": "Çalışma saati türleri", "Global leave": "<PERSON><PERSON><PERSON>", "Foreclosures": "İcralar", "New Foreclosure": "<PERSON><PERSON>", "Activate Foreclosure": "İcrayı Aktif Et", "Disable Foreclosure": "İcrayı Pasifleştir", "Cancel Foreclosure": "İcrayı İptal Et", "Foreclosure type": "İcra türü", "Payment journal": "<PERSON><PERSON><PERSON>", "Foreclosure department": "<PERSON><PERSON>ra da<PERSON>i", "Effects the foreclosure after this one": "Sonraki icrayı etkiler", "Leave Durations": "<PERSON><PERSON>", "New Leave Duration": "<PERSON><PERSON>üresi", "Period type": "Periyot türü", "Day off": "İzin günü", "Age operator": "Yaş operatörü", "Start age": "<PERSON><PERSON> başlangıcı", "End age": "<PERSON><PERSON>", "Dismissal Justifications": "İşten Çıkış Gerekçeleri", "New Dismissal Justification": "Yeni İşten Çıkış Gerekçesi", "Dismissal Reasons": "İşten Çıkış Nedenleri", "New Dismissal Reason": "Yeni İşten Çıkış Nedeni", "Term contract": "<PERSON><PERSON><PERSON><PERSON>", "Trial period end date": "<PERSON><PERSON><PERSON> süresi bit<PERSON>ş tarihi", "Employment": "<PERSON><PERSON><PERSON>", "Dismissal": "İşten Çıkış", "Employments": "<PERSON><PERSON><PERSON>", "Dismissals": "İşten Çıkışlar", "Fee rule": "Ücret kuralı", "Fee period": "Ücret periyotu", "Tally record": "<PERSON><PERSON><PERSON>", "Justification": "Gerekçe", "Dismissal date": "İşten çıkış tarihi", "Activate Employment": "İşe Girişi Aktif Et", "Cancel Employment": "İşe Girişi İptal Et", "Activate Dismissal": "İşten Çıkışı Aktif Et", "Cancel Dismissal": "İşten Çıkışı İptal Et", "Cancel Rule": "Kuralı İptal Et", "First employment date": "İlk işe giriş tarihi", "{{artifact}} fee": "{{artifact}} <PERSON><PERSON><PERSON>", "Fee Information": "Ücret Bilgileri", "Deserved leave duration": "Hakedilen izin sü<PERSON>i", "Continuing leave duration": "<PERSON><PERSON><PERSON> i<PERSON>", "Used leave duration": "Kullanılan izin süresi", "Remaining leave duration": "<PERSON><PERSON> i<PERSON>ü<PERSON>", "Leave Information": "İzin Bilgileri", "EATS": "PDTS", "EATS type": "PDTS türü", "EATS no": "PDTS no", "Dismissal date cannot be before employment date!": "Çıkış tarihi giriş tarihinden önce olamaz!", "There are different rules for the same entry that are still valid!": "<PERSON><PERSON><PERSON> giriş için hala geçerli olan farklı kurallar mevcut!", "Each active employment must have at least one applicable rule!": "Her aktif işe girişin en az bir geçerli aktif kuralı olmalı!", "Cannot found the birthdate information of the selected employee!": "Seçilen personelin doğum tarihi bilgisi bulunamadı!", "Cannot found the working hours information of the selected employee!": "Seçilen personelin çalışma saati bilgisi bulunamadı!", "The leave duration that has the code {{code}} definition is invalid!": "{{code}} koduna sahip izin süresi geçersiz!", "The employee already has another active contract!": "Çalışanın zaten başka bir aktif sözleşmesi var!", "Working hour type": "Çalışma saati türü", "No active contract found for the employee!": "Çalışan için aktif bir sözleşme bulunamadı!", "No active contract found at the branch specified for the employee!": "Çalışan için belirlenen şubede aktif sözleşme bulunamadı!", "The maximum allowed time for the specified type of leave at a time is {{days}} day(s)!": "Bir seferde belirtilen izin türü i<PERSON>in, izin verilen maksimum süre {{days}} gündür!", "There cannot be multiple active employment of the same branch!": "Aynı şubenin birden fazla aktif iş girişi olamaz!", "The annual limit of this type of leave is {{limit}}!": "Bu izin türünün yıllık limiti {{limit}} adettir!", "No leave claim found for the employee!": "Personelin izin hakkı bulunamadı!", "The remaining leave duration of the employee is inadequate!": "Personelin kalan izin süresi yetersiz!", "Leave duration must be greater than zero!": "İzin süresi sıfırdan büyük olmalıdır!", "Duration (min)": "<PERSON><PERSON><PERSON> (dk)", "Employee Code": "<PERSON><PERSON>", "Branch Office Code": "<PERSON><PERSON>", "EATS No": "PDTS No", "Tally": "<PERSON><PERSON><PERSON>", "Employee code": "<PERSON><PERSON> kodu", "Employee name": "Personel adı soyadı", "Employee first name": "<PERSON><PERSON> adı", "Employee last name": "Personel soyadı", "Tally Records": "<PERSON><PERSON><PERSON>", "Manuel Tally Records": "<PERSON>", "Absent": "Devamsız", "Global Leave": "<PERSON><PERSON><PERSON>", "Paid Leave": "Ücretli <PERSON>", "Unpaid Leave": "Ücretsiz İzin", "Time to work": "Çalışılacak süre", "Working duration": "Çalışılan süre", "Absence duration": "Devamsızlık süresi", "Attendance duration": "Devamlılık süresi", "Paid leave duration": "Ücretli izin süresi", "Unpaid leave duration": "Ücretsiz izin süresi", "Holiday duration": "<PERSON><PERSON>", "Global leave duration": "<PERSON><PERSON><PERSON>", "Recess duration": "<PERSON>", "Break-time duration": "<PERSON><PERSON>", "Rest-time duration": "<PERSON><PERSON><PERSON>", "Durations": "<PERSON><PERSON><PERSON><PERSON>", "Paid Leaves": "<PERSON><PERSON><PERSON><PERSON>", "Unpaid Leaves": "Ücretsiz İzinler", "Add Leave": "<PERSON><PERSON>", "Add Overtime": "Fazla Mesai Ekle", "Add Attendance": "Devamlılık Ekle", "Worked (Hour)": "Çalışılan (Saat)", "Worked (Day)": "Çalışılan (Gün)", "Absence (Day)": "Devamsızlık (Gün)", "Paid Leave (Day)": "<PERSON><PERSON><PERSON><PERSON> (Gün)", "Unpaid Leave (Day)": "Ücretsiz İzin (Gün)", "Global Leave (Day)": "<PERSON><PERSON><PERSON> (Gün)", "Holiday (Day)": "<PERSON><PERSON> (Gün)", "Overtime (Hour)": "Fazla Mesai (Saat)", "Working (Day)": "Çalışma (Gün)", "Total (Day)": "Toplam (Gün)", "Employee Account Groups": "Personel Hesap <PERSON>", "New Employee Account Group": "<PERSON><PERSON> He<PERSON>", "Fee payable": "Ödenecek ücret", "Net fee payable": "Net ödenecek ücret", "Total earnings": "Toplam kazanç", "Additional allowances": "<PERSON><PERSON>", "Advance payments": "Avanslar", "Fees": "<PERSON><PERSON><PERSON>", "New Fee": "<PERSON><PERSON>", "Approve Fee": "<PERSON><PERSON><PERSON>", "Cancel Fee": "Ücreti İptal Et", "Manuel Record": "<PERSON>", "Fee debit account could not found!": "Ücret borç hesabı bulunamadı!", "Start hour cannot be before end hour!": "Başlangıç saati bitiş saatinden önce olamaz!", "Is {{artifact}} fee dynamic": "<PERSON>amik {{artifact}} <PERSON><PERSON><PERSON>", "Contract must have at least one applicable rule!": "Sözleşmenin en az bir aktif kuralı olmalıdır!", "End date cannot be before now!": "Bitiş tarihi bu günden önce olamaz!", "Fee Calculations": "Ücret Hesaplamaları", "New Fee Calculation": "Yeni Ücret Hesaplaması", "Working duration (Day)": "Çalışma süresi (Gün)", "Hourly fee": "Saatlik ücret", "Daily fee": "Günlük ücret", "Weekly fee": "Haftalık ücret", "Monthly fee": "Aylık ücret", "Yearly fee": "Yıllık ücret", "Additional allowance amount": "Ek ödenek tutarı", "Overtime duration (Hour)": "Fazla mesai süresi (Saat)", "Overtime amount": "Fazla mesai tutarı", "Legal deduction amount": "Yasal kesinti tutarı", "Deduction amount": "<PERSON><PERSON><PERSON> tutarı", "Add from departments": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "{{date}} Tally Records": "{{date}} <PERSON><PERSON><PERSON>", "Employment code": "<PERSON><PERSON><PERSON> kodu", "Contract code": "Sözleşme kodu", "Normal earnings": "Normal kazanç", "Leave duration in hours": "Saatlik izin süresi", "Leave duration in days": "Günlük izin süresi", "Worked day": "Çalışılan gün", "Working day": "Çalışma günü", "Payroll Records": "<PERSON><PERSON><PERSON>", "Can be used as expense": "Gider olarak kullanılabilir", "Deduction Type": "<PERSON><PERSON><PERSON>", "Employee Ledger": "<PERSON><PERSON>", "Advance payment payback": "Avans geri <PERSON>", "Salary advance deduction": "Maaş avansı kesintisi", "Salary advance deduction {{installmentNo}}. installment": "Maaş avansı kesintisi {{installmentNo}}. taksit", "Foreclosure amount": "İcra tutarı", "The document cannot be canceled because it has been recorded in the payroll record!": "Belge bordro kaydına işlendiği için iptal edilemiyor!", "The salary calculation for the personnel with the code {{code}} cannot be made because the net salary fell below zero due to the deduction with the code {{deductionCode}}! Salary: {{salary}}": "{{code}} kodlu personel için maaş hesaplaması yapılamamaktadır çünkü {{deductionCode}} kodlu kesinti nedeniyle net maaş sıfırın altına düşmektedir!  Maaş: {{salary}}", "Can be used as income": "<PERSON><PERSON><PERSON>ılabil<PERSON>", "Fees approved successfully.": "Ücretler başarıyla onaylandı.", "Is irs deduction": "BES kesintisi", "IRS deduction amount": "BES kesintisi", "IRS deductions": "BES kesintileri", "Tax Exemption Types": "Vergi İstisnası Türleri", "New Tax Exemption Type": "Yeni Vergi İstisnası Türü", "Tax exemption type": "<PERSON><PERSON><PERSON> istisnası türü", "Tax Exemptions": "Vergi İstisnaları", "New Tax Exemption": "Yeni Vergi İstisnası", "Cancel Tax Exemption": "Vergi İstisnası İptal Et", "Approve Tax Exemption": "Vergi İstisnası Onayla", "Bulk Operations": "Toplu İşlemler", "Bulk Deductions": "<PERSON><PERSON>", "New Bulk Deduction": "<PERSON><PERSON>", "Cancel Bulk Deduction": "Toplu Kesintiyi İptal Et", "Bulk Overtimes": "<PERSON><PERSON>", "New Bulk Overtime": "<PERSON><PERSON>", "Cancel Bulk Overtime": "Toplu Mesaiyi İptal Et", "Income creates accounting entry": "<PERSON><PERSON><PERSON> muh<PERSON> kaydı oluşturur", "Expense creates accounting entry": "Gider muhasebe kaydı oluşturur", "The operation cannot be performed because there are payroll rows in the calculation.": "Hesaplamada bordro satırları mevcut olduğundan dolayı işlem gerçekleştirilemiyor.", "Bulk Deduction Items": "<PERSON><PERSON>", "Employee Information": "<PERSON><PERSON>", "Number of male employees": "Erkek çalışan sayısı", "Number of female employees": "Kadın çalışan sayı<PERSON>ı", "Number of total employees": "<PERSON><PERSON> personel sayısı", "Employee Detail Analysis": "<PERSON><PERSON>", "Entry method": "<PERSON><PERSON><PERSON>", "Overtime type code": "Fazla mesai tipi kodu", "Personnel coded {{employeeCode}} has no contract at branch coded {{branchCode}}!": "{{employeeCode}} kodlu personelin {{branchCode}} kodlu şubede sözleşmesi yok!", "Leave Usages": "<PERSON><PERSON>", "Deserved cumulative": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Used cumulative": "Kümü<PERSON><PERSON>", "Leave code": "İzin kodu", "Leave description": "İzin açıklaması", "New Bulk Leave": "<PERSON><PERSON>", "Bulk Leaves": "<PERSON><PERSON>", "Cancel Bulk Leave": "Toplu İzin İptal Et", "Approve Bulk Leave": "<PERSON><PERSON>", "Bulk Leave": "Toplu İzin", "Bulk leave approved successfully.": "Toplu izin başarıyla onaylandı.", "Leave Details": "<PERSON><PERSON>ı", "You are about to cancel this bulk leave and all related leaves. Do you want to continue?": "Bu toplu izni ve ilgili tüm izinleri iptal etmek üzeresiniz. Devam etmek istiyor musunuz?", "Employee Balances": "<PERSON>el <PERSON>i", "Can manage record in tally": "<PERSON><PERSON><PERSON>nı Yönetebilir"}