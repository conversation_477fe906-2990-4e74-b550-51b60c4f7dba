var hu=(e,t,n)=>{if(!t.has(e))throw TypeError("Cannot "+n)};var $=(e,t,n)=>(hu(e,t,"read from private field"),n?n.call(e):t.get(e)),K=(e,t,n)=>{if(t.has(e))throw TypeError("Cannot add the same private member more than once");t instanceof WeakSet?t.add(e):t.set(e,n)},M=(e,t,n,r)=>(hu(e,t,"write to private field"),r?r.call(e,n):t.set(e,n),n);var ta=(e,t,n,r)=>({set _(o){M(e,t,o,n)},get _(){return $(e,t,r)}}),re=(e,t,n)=>(hu(e,t,"access private method"),n);function c1(e,t){for(var n=0;n<t.length;n++){const r=t[n];if(typeof r!="string"&&!Array.isArray(r)){for(const o in r)if(o!=="default"&&!(o in e)){const i=Object.getOwnPropertyDescriptor(r,o);i&&Object.defineProperty(e,o,i.get?i:{enumerable:!0,get:()=>r[o]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))r(o);new MutationObserver(o=>{for(const i of o)if(i.type==="childList")for(const s of i.addedNodes)s.tagName==="LINK"&&s.rel==="modulepreload"&&r(s)}).observe(document,{childList:!0,subtree:!0});function n(o){const i={};return o.integrity&&(i.integrity=o.integrity),o.referrerPolicy&&(i.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?i.credentials="include":o.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function r(o){if(o.ep)return;o.ep=!0;const i=n(o);fetch(o.href,i)}})();function Wh(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Gh={exports:{}},Vl={},Kh={exports:{}},ue={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ks=Symbol.for("react.element"),d1=Symbol.for("react.portal"),f1=Symbol.for("react.fragment"),p1=Symbol.for("react.strict_mode"),g1=Symbol.for("react.profiler"),h1=Symbol.for("react.provider"),m1=Symbol.for("react.context"),v1=Symbol.for("react.forward_ref"),y1=Symbol.for("react.suspense"),w1=Symbol.for("react.memo"),x1=Symbol.for("react.lazy"),wp=Symbol.iterator;function S1(e){return e===null||typeof e!="object"?null:(e=wp&&e[wp]||e["@@iterator"],typeof e=="function"?e:null)}var Qh={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},Yh=Object.assign,qh={};function bi(e,t,n){this.props=e,this.context=t,this.refs=qh,this.updater=n||Qh}bi.prototype.isReactComponent={};bi.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};bi.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function Xh(){}Xh.prototype=bi.prototype;function Dd(e,t,n){this.props=e,this.context=t,this.refs=qh,this.updater=n||Qh}var Ld=Dd.prototype=new Xh;Ld.constructor=Dd;Yh(Ld,bi.prototype);Ld.isPureReactComponent=!0;var xp=Array.isArray,Zh=Object.prototype.hasOwnProperty,Id={current:null},Jh={key:!0,ref:!0,__self:!0,__source:!0};function em(e,t,n){var r,o={},i=null,s=null;if(t!=null)for(r in t.ref!==void 0&&(s=t.ref),t.key!==void 0&&(i=""+t.key),t)Zh.call(t,r)&&!Jh.hasOwnProperty(r)&&(o[r]=t[r]);var a=arguments.length-2;if(a===1)o.children=n;else if(1<a){for(var l=Array(a),u=0;u<a;u++)l[u]=arguments[u+2];o.children=l}if(e&&e.defaultProps)for(r in a=e.defaultProps,a)o[r]===void 0&&(o[r]=a[r]);return{$$typeof:Ks,type:e,key:i,ref:s,props:o,_owner:Id.current}}function C1(e,t){return{$$typeof:Ks,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function Fd(e){return typeof e=="object"&&e!==null&&e.$$typeof===Ks}function b1(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var Sp=/\/+/g;function mu(e,t){return typeof e=="object"&&e!==null&&e.key!=null?b1(""+e.key):t.toString(36)}function Da(e,t,n,r,o){var i=typeof e;(i==="undefined"||i==="boolean")&&(e=null);var s=!1;if(e===null)s=!0;else switch(i){case"string":case"number":s=!0;break;case"object":switch(e.$$typeof){case Ks:case d1:s=!0}}if(s)return s=e,o=o(s),e=r===""?"."+mu(s,0):r,xp(o)?(n="",e!=null&&(n=e.replace(Sp,"$&/")+"/"),Da(o,t,n,"",function(u){return u})):o!=null&&(Fd(o)&&(o=C1(o,n+(!o.key||s&&s.key===o.key?"":(""+o.key).replace(Sp,"$&/")+"/")+e)),t.push(o)),1;if(s=0,r=r===""?".":r+":",xp(e))for(var a=0;a<e.length;a++){i=e[a];var l=r+mu(i,a);s+=Da(i,t,n,l,o)}else if(l=S1(e),typeof l=="function")for(e=l.call(e),a=0;!(i=e.next()).done;)i=i.value,l=r+mu(i,a++),s+=Da(i,t,n,l,o);else if(i==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return s}function na(e,t,n){if(e==null)return e;var r=[],o=0;return Da(e,r,"","",function(i){return t.call(n,i,o++)}),r}function $1(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var lt={current:null},La={transition:null},E1={ReactCurrentDispatcher:lt,ReactCurrentBatchConfig:La,ReactCurrentOwner:Id};ue.Children={map:na,forEach:function(e,t,n){na(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return na(e,function(){t++}),t},toArray:function(e){return na(e,function(t){return t})||[]},only:function(e){if(!Fd(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};ue.Component=bi;ue.Fragment=f1;ue.Profiler=g1;ue.PureComponent=Dd;ue.StrictMode=p1;ue.Suspense=y1;ue.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=E1;ue.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=Yh({},e.props),o=e.key,i=e.ref,s=e._owner;if(t!=null){if(t.ref!==void 0&&(i=t.ref,s=Id.current),t.key!==void 0&&(o=""+t.key),e.type&&e.type.defaultProps)var a=e.type.defaultProps;for(l in t)Zh.call(t,l)&&!Jh.hasOwnProperty(l)&&(r[l]=t[l]===void 0&&a!==void 0?a[l]:t[l])}var l=arguments.length-2;if(l===1)r.children=n;else if(1<l){a=Array(l);for(var u=0;u<l;u++)a[u]=arguments[u+2];r.children=a}return{$$typeof:Ks,type:e.type,key:o,ref:i,props:r,_owner:s}};ue.createContext=function(e){return e={$$typeof:m1,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:h1,_context:e},e.Consumer=e};ue.createElement=em;ue.createFactory=function(e){var t=em.bind(null,e);return t.type=e,t};ue.createRef=function(){return{current:null}};ue.forwardRef=function(e){return{$$typeof:v1,render:e}};ue.isValidElement=Fd;ue.lazy=function(e){return{$$typeof:x1,_payload:{_status:-1,_result:e},_init:$1}};ue.memo=function(e,t){return{$$typeof:w1,type:e,compare:t===void 0?null:t}};ue.startTransition=function(e){var t=La.transition;La.transition={};try{e()}finally{La.transition=t}};ue.unstable_act=function(){throw Error("act(...) is not supported in production builds of React.")};ue.useCallback=function(e,t){return lt.current.useCallback(e,t)};ue.useContext=function(e){return lt.current.useContext(e)};ue.useDebugValue=function(){};ue.useDeferredValue=function(e){return lt.current.useDeferredValue(e)};ue.useEffect=function(e,t){return lt.current.useEffect(e,t)};ue.useId=function(){return lt.current.useId()};ue.useImperativeHandle=function(e,t,n){return lt.current.useImperativeHandle(e,t,n)};ue.useInsertionEffect=function(e,t){return lt.current.useInsertionEffect(e,t)};ue.useLayoutEffect=function(e,t){return lt.current.useLayoutEffect(e,t)};ue.useMemo=function(e,t){return lt.current.useMemo(e,t)};ue.useReducer=function(e,t,n){return lt.current.useReducer(e,t,n)};ue.useRef=function(e){return lt.current.useRef(e)};ue.useState=function(e){return lt.current.useState(e)};ue.useSyncExternalStore=function(e,t,n){return lt.current.useSyncExternalStore(e,t,n)};ue.useTransition=function(){return lt.current.useTransition()};ue.version="18.2.0";Kh.exports=ue;var p=Kh.exports;const A=Wh(p),R1=c1({__proto__:null,default:A},[p]);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var P1=p,_1=Symbol.for("react.element"),k1=Symbol.for("react.fragment"),N1=Object.prototype.hasOwnProperty,O1=P1.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,T1={key:!0,ref:!0,__self:!0,__source:!0};function tm(e,t,n){var r,o={},i=null,s=null;n!==void 0&&(i=""+n),t.key!==void 0&&(i=""+t.key),t.ref!==void 0&&(s=t.ref);for(r in t)N1.call(t,r)&&!T1.hasOwnProperty(r)&&(o[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)o[r]===void 0&&(o[r]=t[r]);return{$$typeof:_1,type:e,key:i,ref:s,props:o,_owner:O1.current}}Vl.Fragment=k1;Vl.jsx=tm;Vl.jsxs=tm;Gh.exports=Vl;var S=Gh.exports,uc={},nm={exports:{}},_t={},rm={exports:{}},om={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(k,I){var D=k.length;k.push(I);e:for(;0<D;){var G=D-1>>>1,H=k[G];if(0<o(H,I))k[G]=I,k[D]=H,D=G;else break e}}function n(k){return k.length===0?null:k[0]}function r(k){if(k.length===0)return null;var I=k[0],D=k.pop();if(D!==I){k[0]=D;e:for(var G=0,H=k.length,de=H>>>1;G<de;){var J=2*(G+1)-1,ge=k[J],ie=J+1,X=k[ie];if(0>o(ge,D))ie<H&&0>o(X,ge)?(k[G]=X,k[ie]=D,G=ie):(k[G]=ge,k[J]=D,G=J);else if(ie<H&&0>o(X,D))k[G]=X,k[ie]=D,G=ie;else break e}}return I}function o(k,I){var D=k.sortIndex-I.sortIndex;return D!==0?D:k.id-I.id}if(typeof performance=="object"&&typeof performance.now=="function"){var i=performance;e.unstable_now=function(){return i.now()}}else{var s=Date,a=s.now();e.unstable_now=function(){return s.now()-a}}var l=[],u=[],c=1,d=null,f=3,h=!1,y=!1,v=!1,x=typeof setTimeout=="function"?setTimeout:null,m=typeof clearTimeout=="function"?clearTimeout:null,g=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function w(k){for(var I=n(u);I!==null;){if(I.callback===null)r(u);else if(I.startTime<=k)r(u),I.sortIndex=I.expirationTime,t(l,I);else break;I=n(u)}}function C(k){if(v=!1,w(k),!y)if(n(l)!==null)y=!0,U(E);else{var I=n(u);I!==null&&q(C,I.startTime-k)}}function E(k,I){y=!1,v&&(v=!1,m(R),R=-1),h=!0;var D=f;try{for(w(I),d=n(l);d!==null&&(!(d.expirationTime>I)||k&&!V());){var G=d.callback;if(typeof G=="function"){d.callback=null,f=d.priorityLevel;var H=G(d.expirationTime<=I);I=e.unstable_now(),typeof H=="function"?d.callback=H:d===n(l)&&r(l),w(I)}else r(l);d=n(l)}if(d!==null)var de=!0;else{var J=n(u);J!==null&&q(C,J.startTime-I),de=!1}return de}finally{d=null,f=D,h=!1}}var _=!1,P=null,R=-1,O=5,T=-1;function V(){return!(e.unstable_now()-T<O)}function j(){if(P!==null){var k=e.unstable_now();T=k;var I=!0;try{I=P(!0,k)}finally{I?Q():(_=!1,P=null)}}else _=!1}var Q;if(typeof g=="function")Q=function(){g(j)};else if(typeof MessageChannel<"u"){var z=new MessageChannel,Y=z.port2;z.port1.onmessage=j,Q=function(){Y.postMessage(null)}}else Q=function(){x(j,0)};function U(k){P=k,_||(_=!0,Q())}function q(k,I){R=x(function(){k(e.unstable_now())},I)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(k){k.callback=null},e.unstable_continueExecution=function(){y||h||(y=!0,U(E))},e.unstable_forceFrameRate=function(k){0>k||125<k?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):O=0<k?Math.floor(1e3/k):5},e.unstable_getCurrentPriorityLevel=function(){return f},e.unstable_getFirstCallbackNode=function(){return n(l)},e.unstable_next=function(k){switch(f){case 1:case 2:case 3:var I=3;break;default:I=f}var D=f;f=I;try{return k()}finally{f=D}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(k,I){switch(k){case 1:case 2:case 3:case 4:case 5:break;default:k=3}var D=f;f=k;try{return I()}finally{f=D}},e.unstable_scheduleCallback=function(k,I,D){var G=e.unstable_now();switch(typeof D=="object"&&D!==null?(D=D.delay,D=typeof D=="number"&&0<D?G+D:G):D=G,k){case 1:var H=-1;break;case 2:H=250;break;case 5:H=**********;break;case 4:H=1e4;break;default:H=5e3}return H=D+H,k={id:c++,callback:I,priorityLevel:k,startTime:D,expirationTime:H,sortIndex:-1},D>G?(k.sortIndex=D,t(u,k),n(l)===null&&k===n(u)&&(v?(m(R),R=-1):v=!0,q(C,D-G))):(k.sortIndex=H,t(l,k),y||h||(y=!0,U(E))),k},e.unstable_shouldYield=V,e.unstable_wrapCallback=function(k){var I=f;return function(){var D=f;f=I;try{return k.apply(this,arguments)}finally{f=D}}}})(om);rm.exports=om;var A1=rm.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var im=p,Et=A1;function N(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var sm=new Set,cs={};function po(e,t){fi(e,t),fi(e+"Capture",t)}function fi(e,t){for(cs[e]=t,e=0;e<t.length;e++)sm.add(t[e])}var Vn=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),cc=Object.prototype.hasOwnProperty,M1=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,Cp={},bp={};function D1(e){return cc.call(bp,e)?!0:cc.call(Cp,e)?!1:M1.test(e)?bp[e]=!0:(Cp[e]=!0,!1)}function L1(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function I1(e,t,n,r){if(t===null||typeof t>"u"||L1(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function ut(e,t,n,r,o,i,s){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=o,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=i,this.removeEmptyString=s}var Ze={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){Ze[e]=new ut(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];Ze[t]=new ut(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){Ze[e]=new ut(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){Ze[e]=new ut(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){Ze[e]=new ut(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){Ze[e]=new ut(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){Ze[e]=new ut(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){Ze[e]=new ut(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){Ze[e]=new ut(e,5,!1,e.toLowerCase(),null,!1,!1)});var jd=/[\-:]([a-z])/g;function zd(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(jd,zd);Ze[t]=new ut(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(jd,zd);Ze[t]=new ut(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(jd,zd);Ze[t]=new ut(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){Ze[e]=new ut(e,1,!1,e.toLowerCase(),null,!1,!1)});Ze.xlinkHref=new ut("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){Ze[e]=new ut(e,1,!1,e.toLowerCase(),null,!0,!0)});function Vd(e,t,n,r){var o=Ze.hasOwnProperty(t)?Ze[t]:null;(o!==null?o.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(I1(t,n,o,r)&&(n=null),r||o===null?D1(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):o.mustUseProperty?e[o.propertyName]=n===null?o.type===3?!1:"":n:(t=o.attributeName,r=o.attributeNamespace,n===null?e.removeAttribute(t):(o=o.type,n=o===3||o===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var Kn=im.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,ra=Symbol.for("react.element"),No=Symbol.for("react.portal"),Oo=Symbol.for("react.fragment"),Hd=Symbol.for("react.strict_mode"),dc=Symbol.for("react.profiler"),am=Symbol.for("react.provider"),lm=Symbol.for("react.context"),Ud=Symbol.for("react.forward_ref"),fc=Symbol.for("react.suspense"),pc=Symbol.for("react.suspense_list"),Bd=Symbol.for("react.memo"),or=Symbol.for("react.lazy"),um=Symbol.for("react.offscreen"),$p=Symbol.iterator;function Di(e){return e===null||typeof e!="object"?null:(e=$p&&e[$p]||e["@@iterator"],typeof e=="function"?e:null)}var Ne=Object.assign,vu;function qi(e){if(vu===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);vu=t&&t[1]||""}return`
`+vu+e}var yu=!1;function wu(e,t){if(!e||yu)return"";yu=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&typeof u.stack=="string"){for(var o=u.stack.split(`
`),i=r.stack.split(`
`),s=o.length-1,a=i.length-1;1<=s&&0<=a&&o[s]!==i[a];)a--;for(;1<=s&&0<=a;s--,a--)if(o[s]!==i[a]){if(s!==1||a!==1)do if(s--,a--,0>a||o[s]!==i[a]){var l=`
`+o[s].replace(" at new "," at ");return e.displayName&&l.includes("<anonymous>")&&(l=l.replace("<anonymous>",e.displayName)),l}while(1<=s&&0<=a);break}}}finally{yu=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?qi(e):""}function F1(e){switch(e.tag){case 5:return qi(e.type);case 16:return qi("Lazy");case 13:return qi("Suspense");case 19:return qi("SuspenseList");case 0:case 2:case 15:return e=wu(e.type,!1),e;case 11:return e=wu(e.type.render,!1),e;case 1:return e=wu(e.type,!0),e;default:return""}}function gc(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case Oo:return"Fragment";case No:return"Portal";case dc:return"Profiler";case Hd:return"StrictMode";case fc:return"Suspense";case pc:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case lm:return(e.displayName||"Context")+".Consumer";case am:return(e._context.displayName||"Context")+".Provider";case Ud:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case Bd:return t=e.displayName||null,t!==null?t:gc(e.type)||"Memo";case or:t=e._payload,e=e._init;try{return gc(e(t))}catch{}}return null}function j1(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return gc(t);case 8:return t===Hd?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function Pr(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function cm(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function z1(e){var t=cm(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var o=n.get,i=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return o.call(this)},set:function(s){r=""+s,i.call(this,s)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(s){r=""+s},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function oa(e){e._valueTracker||(e._valueTracker=z1(e))}function dm(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=cm(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function Ja(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function hc(e,t){var n=t.checked;return Ne({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function Ep(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=Pr(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function fm(e,t){t=t.checked,t!=null&&Vd(e,"checked",t,!1)}function mc(e,t){fm(e,t);var n=Pr(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?vc(e,t.type,n):t.hasOwnProperty("defaultValue")&&vc(e,t.type,Pr(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function Rp(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function vc(e,t,n){(t!=="number"||Ja(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var Xi=Array.isArray;function Ho(e,t,n,r){if(e=e.options,t){t={};for(var o=0;o<n.length;o++)t["$"+n[o]]=!0;for(n=0;n<e.length;n++)o=t.hasOwnProperty("$"+e[n].value),e[n].selected!==o&&(e[n].selected=o),o&&r&&(e[n].defaultSelected=!0)}else{for(n=""+Pr(n),t=null,o=0;o<e.length;o++){if(e[o].value===n){e[o].selected=!0,r&&(e[o].defaultSelected=!0);return}t!==null||e[o].disabled||(t=e[o])}t!==null&&(t.selected=!0)}}function yc(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(N(91));return Ne({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function Pp(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(N(92));if(Xi(n)){if(1<n.length)throw Error(N(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:Pr(n)}}function pm(e,t){var n=Pr(t.value),r=Pr(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function _p(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function gm(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function wc(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?gm(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var ia,hm=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,o){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,o)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(ia=ia||document.createElement("div"),ia.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=ia.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function ds(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var ts={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},V1=["Webkit","ms","Moz","O"];Object.keys(ts).forEach(function(e){V1.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),ts[t]=ts[e]})});function mm(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||ts.hasOwnProperty(e)&&ts[e]?(""+t).trim():t+"px"}function vm(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,o=mm(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,o):e[n]=o}}var H1=Ne({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function xc(e,t){if(t){if(H1[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(N(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(N(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(N(61))}if(t.style!=null&&typeof t.style!="object")throw Error(N(62))}}function Sc(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Cc=null;function Wd(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var bc=null,Uo=null,Bo=null;function kp(e){if(e=qs(e)){if(typeof bc!="function")throw Error(N(280));var t=e.stateNode;t&&(t=Gl(t),bc(e.stateNode,e.type,t))}}function ym(e){Uo?Bo?Bo.push(e):Bo=[e]:Uo=e}function wm(){if(Uo){var e=Uo,t=Bo;if(Bo=Uo=null,kp(e),t)for(e=0;e<t.length;e++)kp(t[e])}}function xm(e,t){return e(t)}function Sm(){}var xu=!1;function Cm(e,t,n){if(xu)return e(t,n);xu=!0;try{return xm(e,t,n)}finally{xu=!1,(Uo!==null||Bo!==null)&&(Sm(),wm())}}function fs(e,t){var n=e.stateNode;if(n===null)return null;var r=Gl(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(N(231,t,typeof n));return n}var $c=!1;if(Vn)try{var Li={};Object.defineProperty(Li,"passive",{get:function(){$c=!0}}),window.addEventListener("test",Li,Li),window.removeEventListener("test",Li,Li)}catch{$c=!1}function U1(e,t,n,r,o,i,s,a,l){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(c){this.onError(c)}}var ns=!1,el=null,tl=!1,Ec=null,B1={onError:function(e){ns=!0,el=e}};function W1(e,t,n,r,o,i,s,a,l){ns=!1,el=null,U1.apply(B1,arguments)}function G1(e,t,n,r,o,i,s,a,l){if(W1.apply(this,arguments),ns){if(ns){var u=el;ns=!1,el=null}else throw Error(N(198));tl||(tl=!0,Ec=u)}}function go(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function bm(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function Np(e){if(go(e)!==e)throw Error(N(188))}function K1(e){var t=e.alternate;if(!t){if(t=go(e),t===null)throw Error(N(188));return t!==e?null:e}for(var n=e,r=t;;){var o=n.return;if(o===null)break;var i=o.alternate;if(i===null){if(r=o.return,r!==null){n=r;continue}break}if(o.child===i.child){for(i=o.child;i;){if(i===n)return Np(o),e;if(i===r)return Np(o),t;i=i.sibling}throw Error(N(188))}if(n.return!==r.return)n=o,r=i;else{for(var s=!1,a=o.child;a;){if(a===n){s=!0,n=o,r=i;break}if(a===r){s=!0,r=o,n=i;break}a=a.sibling}if(!s){for(a=i.child;a;){if(a===n){s=!0,n=i,r=o;break}if(a===r){s=!0,r=i,n=o;break}a=a.sibling}if(!s)throw Error(N(189))}}if(n.alternate!==r)throw Error(N(190))}if(n.tag!==3)throw Error(N(188));return n.stateNode.current===n?e:t}function $m(e){return e=K1(e),e!==null?Em(e):null}function Em(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=Em(e);if(t!==null)return t;e=e.sibling}return null}var Rm=Et.unstable_scheduleCallback,Op=Et.unstable_cancelCallback,Q1=Et.unstable_shouldYield,Y1=Et.unstable_requestPaint,Le=Et.unstable_now,q1=Et.unstable_getCurrentPriorityLevel,Gd=Et.unstable_ImmediatePriority,Pm=Et.unstable_UserBlockingPriority,nl=Et.unstable_NormalPriority,X1=Et.unstable_LowPriority,_m=Et.unstable_IdlePriority,Hl=null,mn=null;function Z1(e){if(mn&&typeof mn.onCommitFiberRoot=="function")try{mn.onCommitFiberRoot(Hl,e,void 0,(e.current.flags&128)===128)}catch{}}var Xt=Math.clz32?Math.clz32:tw,J1=Math.log,ew=Math.LN2;function tw(e){return e>>>=0,e===0?32:31-(J1(e)/ew|0)|0}var sa=64,aa=4194304;function Zi(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function rl(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,o=e.suspendedLanes,i=e.pingedLanes,s=n&268435455;if(s!==0){var a=s&~o;a!==0?r=Zi(a):(i&=s,i!==0&&(r=Zi(i)))}else s=n&~o,s!==0?r=Zi(s):i!==0&&(r=Zi(i));if(r===0)return 0;if(t!==0&&t!==r&&!(t&o)&&(o=r&-r,i=t&-t,o>=i||o===16&&(i&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-Xt(t),o=1<<n,r|=e[n],t&=~o;return r}function nw(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function rw(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,o=e.expirationTimes,i=e.pendingLanes;0<i;){var s=31-Xt(i),a=1<<s,l=o[s];l===-1?(!(a&n)||a&r)&&(o[s]=nw(a,t)):l<=t&&(e.expiredLanes|=a),i&=~a}}function Rc(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function km(){var e=sa;return sa<<=1,!(sa&4194240)&&(sa=64),e}function Su(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Qs(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-Xt(t),e[t]=n}function ow(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var o=31-Xt(n),i=1<<o;t[o]=0,r[o]=-1,e[o]=-1,n&=~i}}function Kd(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-Xt(n),o=1<<r;o&t|e[r]&t&&(e[r]|=t),n&=~o}}var me=0;function Nm(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var Om,Qd,Tm,Am,Mm,Pc=!1,la=[],wr=null,xr=null,Sr=null,ps=new Map,gs=new Map,sr=[],iw="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Tp(e,t){switch(e){case"focusin":case"focusout":wr=null;break;case"dragenter":case"dragleave":xr=null;break;case"mouseover":case"mouseout":Sr=null;break;case"pointerover":case"pointerout":ps.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":gs.delete(t.pointerId)}}function Ii(e,t,n,r,o,i){return e===null||e.nativeEvent!==i?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:i,targetContainers:[o]},t!==null&&(t=qs(t),t!==null&&Qd(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,o!==null&&t.indexOf(o)===-1&&t.push(o),e)}function sw(e,t,n,r,o){switch(t){case"focusin":return wr=Ii(wr,e,t,n,r,o),!0;case"dragenter":return xr=Ii(xr,e,t,n,r,o),!0;case"mouseover":return Sr=Ii(Sr,e,t,n,r,o),!0;case"pointerover":var i=o.pointerId;return ps.set(i,Ii(ps.get(i)||null,e,t,n,r,o)),!0;case"gotpointercapture":return i=o.pointerId,gs.set(i,Ii(gs.get(i)||null,e,t,n,r,o)),!0}return!1}function Dm(e){var t=Hr(e.target);if(t!==null){var n=go(t);if(n!==null){if(t=n.tag,t===13){if(t=bm(n),t!==null){e.blockedOn=t,Mm(e.priority,function(){Tm(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Ia(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=_c(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);Cc=r,n.target.dispatchEvent(r),Cc=null}else return t=qs(n),t!==null&&Qd(t),e.blockedOn=n,!1;t.shift()}return!0}function Ap(e,t,n){Ia(e)&&n.delete(t)}function aw(){Pc=!1,wr!==null&&Ia(wr)&&(wr=null),xr!==null&&Ia(xr)&&(xr=null),Sr!==null&&Ia(Sr)&&(Sr=null),ps.forEach(Ap),gs.forEach(Ap)}function Fi(e,t){e.blockedOn===t&&(e.blockedOn=null,Pc||(Pc=!0,Et.unstable_scheduleCallback(Et.unstable_NormalPriority,aw)))}function hs(e){function t(o){return Fi(o,e)}if(0<la.length){Fi(la[0],e);for(var n=1;n<la.length;n++){var r=la[n];r.blockedOn===e&&(r.blockedOn=null)}}for(wr!==null&&Fi(wr,e),xr!==null&&Fi(xr,e),Sr!==null&&Fi(Sr,e),ps.forEach(t),gs.forEach(t),n=0;n<sr.length;n++)r=sr[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<sr.length&&(n=sr[0],n.blockedOn===null);)Dm(n),n.blockedOn===null&&sr.shift()}var Wo=Kn.ReactCurrentBatchConfig,ol=!0;function lw(e,t,n,r){var o=me,i=Wo.transition;Wo.transition=null;try{me=1,Yd(e,t,n,r)}finally{me=o,Wo.transition=i}}function uw(e,t,n,r){var o=me,i=Wo.transition;Wo.transition=null;try{me=4,Yd(e,t,n,r)}finally{me=o,Wo.transition=i}}function Yd(e,t,n,r){if(ol){var o=_c(e,t,n,r);if(o===null)Ou(e,t,r,il,n),Tp(e,r);else if(sw(o,e,t,n,r))r.stopPropagation();else if(Tp(e,r),t&4&&-1<iw.indexOf(e)){for(;o!==null;){var i=qs(o);if(i!==null&&Om(i),i=_c(e,t,n,r),i===null&&Ou(e,t,r,il,n),i===o)break;o=i}o!==null&&r.stopPropagation()}else Ou(e,t,r,null,n)}}var il=null;function _c(e,t,n,r){if(il=null,e=Wd(r),e=Hr(e),e!==null)if(t=go(e),t===null)e=null;else if(n=t.tag,n===13){if(e=bm(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return il=e,null}function Lm(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(q1()){case Gd:return 1;case Pm:return 4;case nl:case X1:return 16;case _m:return 536870912;default:return 16}default:return 16}}var mr=null,qd=null,Fa=null;function Im(){if(Fa)return Fa;var e,t=qd,n=t.length,r,o="value"in mr?mr.value:mr.textContent,i=o.length;for(e=0;e<n&&t[e]===o[e];e++);var s=n-e;for(r=1;r<=s&&t[n-r]===o[i-r];r++);return Fa=o.slice(e,1<r?1-r:void 0)}function ja(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function ua(){return!0}function Mp(){return!1}function kt(e){function t(n,r,o,i,s){this._reactName=n,this._targetInst=o,this.type=r,this.nativeEvent=i,this.target=s,this.currentTarget=null;for(var a in e)e.hasOwnProperty(a)&&(n=e[a],this[a]=n?n(i):i[a]);return this.isDefaultPrevented=(i.defaultPrevented!=null?i.defaultPrevented:i.returnValue===!1)?ua:Mp,this.isPropagationStopped=Mp,this}return Ne(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=ua)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=ua)},persist:function(){},isPersistent:ua}),t}var $i={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Xd=kt($i),Ys=Ne({},$i,{view:0,detail:0}),cw=kt(Ys),Cu,bu,ji,Ul=Ne({},Ys,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Zd,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==ji&&(ji&&e.type==="mousemove"?(Cu=e.screenX-ji.screenX,bu=e.screenY-ji.screenY):bu=Cu=0,ji=e),Cu)},movementY:function(e){return"movementY"in e?e.movementY:bu}}),Dp=kt(Ul),dw=Ne({},Ul,{dataTransfer:0}),fw=kt(dw),pw=Ne({},Ys,{relatedTarget:0}),$u=kt(pw),gw=Ne({},$i,{animationName:0,elapsedTime:0,pseudoElement:0}),hw=kt(gw),mw=Ne({},$i,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),vw=kt(mw),yw=Ne({},$i,{data:0}),Lp=kt(yw),ww={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},xw={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Sw={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Cw(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Sw[e])?!!t[e]:!1}function Zd(){return Cw}var bw=Ne({},Ys,{key:function(e){if(e.key){var t=ww[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=ja(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?xw[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Zd,charCode:function(e){return e.type==="keypress"?ja(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?ja(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),$w=kt(bw),Ew=Ne({},Ul,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Ip=kt(Ew),Rw=Ne({},Ys,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Zd}),Pw=kt(Rw),_w=Ne({},$i,{propertyName:0,elapsedTime:0,pseudoElement:0}),kw=kt(_w),Nw=Ne({},Ul,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Ow=kt(Nw),Tw=[9,13,27,32],Jd=Vn&&"CompositionEvent"in window,rs=null;Vn&&"documentMode"in document&&(rs=document.documentMode);var Aw=Vn&&"TextEvent"in window&&!rs,Fm=Vn&&(!Jd||rs&&8<rs&&11>=rs),Fp=" ",jp=!1;function jm(e,t){switch(e){case"keyup":return Tw.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function zm(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var To=!1;function Mw(e,t){switch(e){case"compositionend":return zm(t);case"keypress":return t.which!==32?null:(jp=!0,Fp);case"textInput":return e=t.data,e===Fp&&jp?null:e;default:return null}}function Dw(e,t){if(To)return e==="compositionend"||!Jd&&jm(e,t)?(e=Im(),Fa=qd=mr=null,To=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Fm&&t.locale!=="ko"?null:t.data;default:return null}}var Lw={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function zp(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!Lw[e.type]:t==="textarea"}function Vm(e,t,n,r){ym(r),t=sl(t,"onChange"),0<t.length&&(n=new Xd("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var os=null,ms=null;function Iw(e){Zm(e,0)}function Bl(e){var t=Do(e);if(dm(t))return e}function Fw(e,t){if(e==="change")return t}var Hm=!1;if(Vn){var Eu;if(Vn){var Ru="oninput"in document;if(!Ru){var Vp=document.createElement("div");Vp.setAttribute("oninput","return;"),Ru=typeof Vp.oninput=="function"}Eu=Ru}else Eu=!1;Hm=Eu&&(!document.documentMode||9<document.documentMode)}function Hp(){os&&(os.detachEvent("onpropertychange",Um),ms=os=null)}function Um(e){if(e.propertyName==="value"&&Bl(ms)){var t=[];Vm(t,ms,e,Wd(e)),Cm(Iw,t)}}function jw(e,t,n){e==="focusin"?(Hp(),os=t,ms=n,os.attachEvent("onpropertychange",Um)):e==="focusout"&&Hp()}function zw(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Bl(ms)}function Vw(e,t){if(e==="click")return Bl(t)}function Hw(e,t){if(e==="input"||e==="change")return Bl(t)}function Uw(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var en=typeof Object.is=="function"?Object.is:Uw;function vs(e,t){if(en(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var o=n[r];if(!cc.call(t,o)||!en(e[o],t[o]))return!1}return!0}function Up(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Bp(e,t){var n=Up(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Up(n)}}function Bm(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Bm(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Wm(){for(var e=window,t=Ja();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=Ja(e.document)}return t}function ef(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function Bw(e){var t=Wm(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&Bm(n.ownerDocument.documentElement,n)){if(r!==null&&ef(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var o=n.textContent.length,i=Math.min(r.start,o);r=r.end===void 0?i:Math.min(r.end,o),!e.extend&&i>r&&(o=r,r=i,i=o),o=Bp(n,i);var s=Bp(n,r);o&&s&&(e.rangeCount!==1||e.anchorNode!==o.node||e.anchorOffset!==o.offset||e.focusNode!==s.node||e.focusOffset!==s.offset)&&(t=t.createRange(),t.setStart(o.node,o.offset),e.removeAllRanges(),i>r?(e.addRange(t),e.extend(s.node,s.offset)):(t.setEnd(s.node,s.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var Ww=Vn&&"documentMode"in document&&11>=document.documentMode,Ao=null,kc=null,is=null,Nc=!1;function Wp(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Nc||Ao==null||Ao!==Ja(r)||(r=Ao,"selectionStart"in r&&ef(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),is&&vs(is,r)||(is=r,r=sl(kc,"onSelect"),0<r.length&&(t=new Xd("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=Ao)))}function ca(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Mo={animationend:ca("Animation","AnimationEnd"),animationiteration:ca("Animation","AnimationIteration"),animationstart:ca("Animation","AnimationStart"),transitionend:ca("Transition","TransitionEnd")},Pu={},Gm={};Vn&&(Gm=document.createElement("div").style,"AnimationEvent"in window||(delete Mo.animationend.animation,delete Mo.animationiteration.animation,delete Mo.animationstart.animation),"TransitionEvent"in window||delete Mo.transitionend.transition);function Wl(e){if(Pu[e])return Pu[e];if(!Mo[e])return e;var t=Mo[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in Gm)return Pu[e]=t[n];return e}var Km=Wl("animationend"),Qm=Wl("animationiteration"),Ym=Wl("animationstart"),qm=Wl("transitionend"),Xm=new Map,Gp="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Tr(e,t){Xm.set(e,t),po(t,[e])}for(var _u=0;_u<Gp.length;_u++){var ku=Gp[_u],Gw=ku.toLowerCase(),Kw=ku[0].toUpperCase()+ku.slice(1);Tr(Gw,"on"+Kw)}Tr(Km,"onAnimationEnd");Tr(Qm,"onAnimationIteration");Tr(Ym,"onAnimationStart");Tr("dblclick","onDoubleClick");Tr("focusin","onFocus");Tr("focusout","onBlur");Tr(qm,"onTransitionEnd");fi("onMouseEnter",["mouseout","mouseover"]);fi("onMouseLeave",["mouseout","mouseover"]);fi("onPointerEnter",["pointerout","pointerover"]);fi("onPointerLeave",["pointerout","pointerover"]);po("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));po("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));po("onBeforeInput",["compositionend","keypress","textInput","paste"]);po("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));po("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));po("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Ji="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Qw=new Set("cancel close invalid load scroll toggle".split(" ").concat(Ji));function Kp(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,G1(r,t,void 0,e),e.currentTarget=null}function Zm(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],o=r.event;r=r.listeners;e:{var i=void 0;if(t)for(var s=r.length-1;0<=s;s--){var a=r[s],l=a.instance,u=a.currentTarget;if(a=a.listener,l!==i&&o.isPropagationStopped())break e;Kp(o,a,u),i=l}else for(s=0;s<r.length;s++){if(a=r[s],l=a.instance,u=a.currentTarget,a=a.listener,l!==i&&o.isPropagationStopped())break e;Kp(o,a,u),i=l}}}if(tl)throw e=Ec,tl=!1,Ec=null,e}function Ce(e,t){var n=t[Dc];n===void 0&&(n=t[Dc]=new Set);var r=e+"__bubble";n.has(r)||(Jm(t,e,2,!1),n.add(r))}function Nu(e,t,n){var r=0;t&&(r|=4),Jm(n,e,r,t)}var da="_reactListening"+Math.random().toString(36).slice(2);function ys(e){if(!e[da]){e[da]=!0,sm.forEach(function(n){n!=="selectionchange"&&(Qw.has(n)||Nu(n,!1,e),Nu(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[da]||(t[da]=!0,Nu("selectionchange",!1,t))}}function Jm(e,t,n,r){switch(Lm(t)){case 1:var o=lw;break;case 4:o=uw;break;default:o=Yd}n=o.bind(null,t,n,e),o=void 0,!$c||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(o=!0),r?o!==void 0?e.addEventListener(t,n,{capture:!0,passive:o}):e.addEventListener(t,n,!0):o!==void 0?e.addEventListener(t,n,{passive:o}):e.addEventListener(t,n,!1)}function Ou(e,t,n,r,o){var i=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var s=r.tag;if(s===3||s===4){var a=r.stateNode.containerInfo;if(a===o||a.nodeType===8&&a.parentNode===o)break;if(s===4)for(s=r.return;s!==null;){var l=s.tag;if((l===3||l===4)&&(l=s.stateNode.containerInfo,l===o||l.nodeType===8&&l.parentNode===o))return;s=s.return}for(;a!==null;){if(s=Hr(a),s===null)return;if(l=s.tag,l===5||l===6){r=i=s;continue e}a=a.parentNode}}r=r.return}Cm(function(){var u=i,c=Wd(n),d=[];e:{var f=Xm.get(e);if(f!==void 0){var h=Xd,y=e;switch(e){case"keypress":if(ja(n)===0)break e;case"keydown":case"keyup":h=$w;break;case"focusin":y="focus",h=$u;break;case"focusout":y="blur",h=$u;break;case"beforeblur":case"afterblur":h=$u;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":h=Dp;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":h=fw;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":h=Pw;break;case Km:case Qm:case Ym:h=hw;break;case qm:h=kw;break;case"scroll":h=cw;break;case"wheel":h=Ow;break;case"copy":case"cut":case"paste":h=vw;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":h=Ip}var v=(t&4)!==0,x=!v&&e==="scroll",m=v?f!==null?f+"Capture":null:f;v=[];for(var g=u,w;g!==null;){w=g;var C=w.stateNode;if(w.tag===5&&C!==null&&(w=C,m!==null&&(C=fs(g,m),C!=null&&v.push(ws(g,C,w)))),x)break;g=g.return}0<v.length&&(f=new h(f,y,null,n,c),d.push({event:f,listeners:v}))}}if(!(t&7)){e:{if(f=e==="mouseover"||e==="pointerover",h=e==="mouseout"||e==="pointerout",f&&n!==Cc&&(y=n.relatedTarget||n.fromElement)&&(Hr(y)||y[Hn]))break e;if((h||f)&&(f=c.window===c?c:(f=c.ownerDocument)?f.defaultView||f.parentWindow:window,h?(y=n.relatedTarget||n.toElement,h=u,y=y?Hr(y):null,y!==null&&(x=go(y),y!==x||y.tag!==5&&y.tag!==6)&&(y=null)):(h=null,y=u),h!==y)){if(v=Dp,C="onMouseLeave",m="onMouseEnter",g="mouse",(e==="pointerout"||e==="pointerover")&&(v=Ip,C="onPointerLeave",m="onPointerEnter",g="pointer"),x=h==null?f:Do(h),w=y==null?f:Do(y),f=new v(C,g+"leave",h,n,c),f.target=x,f.relatedTarget=w,C=null,Hr(c)===u&&(v=new v(m,g+"enter",y,n,c),v.target=w,v.relatedTarget=x,C=v),x=C,h&&y)t:{for(v=h,m=y,g=0,w=v;w;w=$o(w))g++;for(w=0,C=m;C;C=$o(C))w++;for(;0<g-w;)v=$o(v),g--;for(;0<w-g;)m=$o(m),w--;for(;g--;){if(v===m||m!==null&&v===m.alternate)break t;v=$o(v),m=$o(m)}v=null}else v=null;h!==null&&Qp(d,f,h,v,!1),y!==null&&x!==null&&Qp(d,x,y,v,!0)}}e:{if(f=u?Do(u):window,h=f.nodeName&&f.nodeName.toLowerCase(),h==="select"||h==="input"&&f.type==="file")var E=Fw;else if(zp(f))if(Hm)E=Hw;else{E=zw;var _=jw}else(h=f.nodeName)&&h.toLowerCase()==="input"&&(f.type==="checkbox"||f.type==="radio")&&(E=Vw);if(E&&(E=E(e,u))){Vm(d,E,n,c);break e}_&&_(e,f,u),e==="focusout"&&(_=f._wrapperState)&&_.controlled&&f.type==="number"&&vc(f,"number",f.value)}switch(_=u?Do(u):window,e){case"focusin":(zp(_)||_.contentEditable==="true")&&(Ao=_,kc=u,is=null);break;case"focusout":is=kc=Ao=null;break;case"mousedown":Nc=!0;break;case"contextmenu":case"mouseup":case"dragend":Nc=!1,Wp(d,n,c);break;case"selectionchange":if(Ww)break;case"keydown":case"keyup":Wp(d,n,c)}var P;if(Jd)e:{switch(e){case"compositionstart":var R="onCompositionStart";break e;case"compositionend":R="onCompositionEnd";break e;case"compositionupdate":R="onCompositionUpdate";break e}R=void 0}else To?jm(e,n)&&(R="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(R="onCompositionStart");R&&(Fm&&n.locale!=="ko"&&(To||R!=="onCompositionStart"?R==="onCompositionEnd"&&To&&(P=Im()):(mr=c,qd="value"in mr?mr.value:mr.textContent,To=!0)),_=sl(u,R),0<_.length&&(R=new Lp(R,e,null,n,c),d.push({event:R,listeners:_}),P?R.data=P:(P=zm(n),P!==null&&(R.data=P)))),(P=Aw?Mw(e,n):Dw(e,n))&&(u=sl(u,"onBeforeInput"),0<u.length&&(c=new Lp("onBeforeInput","beforeinput",null,n,c),d.push({event:c,listeners:u}),c.data=P))}Zm(d,t)})}function ws(e,t,n){return{instance:e,listener:t,currentTarget:n}}function sl(e,t){for(var n=t+"Capture",r=[];e!==null;){var o=e,i=o.stateNode;o.tag===5&&i!==null&&(o=i,i=fs(e,n),i!=null&&r.unshift(ws(e,i,o)),i=fs(e,t),i!=null&&r.push(ws(e,i,o))),e=e.return}return r}function $o(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function Qp(e,t,n,r,o){for(var i=t._reactName,s=[];n!==null&&n!==r;){var a=n,l=a.alternate,u=a.stateNode;if(l!==null&&l===r)break;a.tag===5&&u!==null&&(a=u,o?(l=fs(n,i),l!=null&&s.unshift(ws(n,l,a))):o||(l=fs(n,i),l!=null&&s.push(ws(n,l,a)))),n=n.return}s.length!==0&&e.push({event:t,listeners:s})}var Yw=/\r\n?/g,qw=/\u0000|\uFFFD/g;function Yp(e){return(typeof e=="string"?e:""+e).replace(Yw,`
`).replace(qw,"")}function fa(e,t,n){if(t=Yp(t),Yp(e)!==t&&n)throw Error(N(425))}function al(){}var Oc=null,Tc=null;function Ac(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Mc=typeof setTimeout=="function"?setTimeout:void 0,Xw=typeof clearTimeout=="function"?clearTimeout:void 0,qp=typeof Promise=="function"?Promise:void 0,Zw=typeof queueMicrotask=="function"?queueMicrotask:typeof qp<"u"?function(e){return qp.resolve(null).then(e).catch(Jw)}:Mc;function Jw(e){setTimeout(function(){throw e})}function Tu(e,t){var n=t,r=0;do{var o=n.nextSibling;if(e.removeChild(n),o&&o.nodeType===8)if(n=o.data,n==="/$"){if(r===0){e.removeChild(o),hs(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=o}while(n);hs(t)}function Cr(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function Xp(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var Ei=Math.random().toString(36).slice(2),pn="__reactFiber$"+Ei,xs="__reactProps$"+Ei,Hn="__reactContainer$"+Ei,Dc="__reactEvents$"+Ei,ex="__reactListeners$"+Ei,tx="__reactHandles$"+Ei;function Hr(e){var t=e[pn];if(t)return t;for(var n=e.parentNode;n;){if(t=n[Hn]||n[pn]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=Xp(e);e!==null;){if(n=e[pn])return n;e=Xp(e)}return t}e=n,n=e.parentNode}return null}function qs(e){return e=e[pn]||e[Hn],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function Do(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(N(33))}function Gl(e){return e[xs]||null}var Lc=[],Lo=-1;function Ar(e){return{current:e}}function be(e){0>Lo||(e.current=Lc[Lo],Lc[Lo]=null,Lo--)}function we(e,t){Lo++,Lc[Lo]=e.current,e.current=t}var _r={},rt=Ar(_r),gt=Ar(!1),oo=_r;function pi(e,t){var n=e.type.contextTypes;if(!n)return _r;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var o={},i;for(i in n)o[i]=t[i];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=o),o}function ht(e){return e=e.childContextTypes,e!=null}function ll(){be(gt),be(rt)}function Zp(e,t,n){if(rt.current!==_r)throw Error(N(168));we(rt,t),we(gt,n)}function e0(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var o in r)if(!(o in t))throw Error(N(108,j1(e)||"Unknown",o));return Ne({},n,r)}function ul(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||_r,oo=rt.current,we(rt,e),we(gt,gt.current),!0}function Jp(e,t,n){var r=e.stateNode;if(!r)throw Error(N(169));n?(e=e0(e,t,oo),r.__reactInternalMemoizedMergedChildContext=e,be(gt),be(rt),we(rt,e)):be(gt),we(gt,n)}var Tn=null,Kl=!1,Au=!1;function t0(e){Tn===null?Tn=[e]:Tn.push(e)}function nx(e){Kl=!0,t0(e)}function Mr(){if(!Au&&Tn!==null){Au=!0;var e=0,t=me;try{var n=Tn;for(me=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}Tn=null,Kl=!1}catch(o){throw Tn!==null&&(Tn=Tn.slice(e+1)),Rm(Gd,Mr),o}finally{me=t,Au=!1}}return null}var Io=[],Fo=0,cl=null,dl=0,It=[],Ft=0,io=null,Dn=1,Ln="";function Ir(e,t){Io[Fo++]=dl,Io[Fo++]=cl,cl=e,dl=t}function n0(e,t,n){It[Ft++]=Dn,It[Ft++]=Ln,It[Ft++]=io,io=e;var r=Dn;e=Ln;var o=32-Xt(r)-1;r&=~(1<<o),n+=1;var i=32-Xt(t)+o;if(30<i){var s=o-o%5;i=(r&(1<<s)-1).toString(32),r>>=s,o-=s,Dn=1<<32-Xt(t)+o|n<<o|r,Ln=i+e}else Dn=1<<i|n<<o|r,Ln=e}function tf(e){e.return!==null&&(Ir(e,1),n0(e,1,0))}function nf(e){for(;e===cl;)cl=Io[--Fo],Io[Fo]=null,dl=Io[--Fo],Io[Fo]=null;for(;e===io;)io=It[--Ft],It[Ft]=null,Ln=It[--Ft],It[Ft]=null,Dn=It[--Ft],It[Ft]=null}var $t=null,Ct=null,$e=!1,qt=null;function r0(e,t){var n=jt(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function eg(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,$t=e,Ct=Cr(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,$t=e,Ct=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=io!==null?{id:Dn,overflow:Ln}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=jt(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,$t=e,Ct=null,!0):!1;default:return!1}}function Ic(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Fc(e){if($e){var t=Ct;if(t){var n=t;if(!eg(e,t)){if(Ic(e))throw Error(N(418));t=Cr(n.nextSibling);var r=$t;t&&eg(e,t)?r0(r,n):(e.flags=e.flags&-4097|2,$e=!1,$t=e)}}else{if(Ic(e))throw Error(N(418));e.flags=e.flags&-4097|2,$e=!1,$t=e}}}function tg(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;$t=e}function pa(e){if(e!==$t)return!1;if(!$e)return tg(e),$e=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Ac(e.type,e.memoizedProps)),t&&(t=Ct)){if(Ic(e))throw o0(),Error(N(418));for(;t;)r0(e,t),t=Cr(t.nextSibling)}if(tg(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(N(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){Ct=Cr(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}Ct=null}}else Ct=$t?Cr(e.stateNode.nextSibling):null;return!0}function o0(){for(var e=Ct;e;)e=Cr(e.nextSibling)}function gi(){Ct=$t=null,$e=!1}function rf(e){qt===null?qt=[e]:qt.push(e)}var rx=Kn.ReactCurrentBatchConfig;function Kt(e,t){if(e&&e.defaultProps){t=Ne({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}var fl=Ar(null),pl=null,jo=null,of=null;function sf(){of=jo=pl=null}function af(e){var t=fl.current;be(fl),e._currentValue=t}function jc(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Go(e,t){pl=e,of=jo=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(pt=!0),e.firstContext=null)}function Vt(e){var t=e._currentValue;if(of!==e)if(e={context:e,memoizedValue:t,next:null},jo===null){if(pl===null)throw Error(N(308));jo=e,pl.dependencies={lanes:0,firstContext:e}}else jo=jo.next=e;return t}var Ur=null;function lf(e){Ur===null?Ur=[e]:Ur.push(e)}function i0(e,t,n,r){var o=t.interleaved;return o===null?(n.next=n,lf(t)):(n.next=o.next,o.next=n),t.interleaved=n,Un(e,r)}function Un(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var ir=!1;function uf(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function s0(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function In(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function br(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,fe&2){var o=r.pending;return o===null?t.next=t:(t.next=o.next,o.next=t),r.pending=t,Un(e,n)}return o=r.interleaved,o===null?(t.next=t,lf(r)):(t.next=o.next,o.next=t),r.interleaved=t,Un(e,n)}function za(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Kd(e,n)}}function ng(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var o=null,i=null;if(n=n.firstBaseUpdate,n!==null){do{var s={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};i===null?o=i=s:i=i.next=s,n=n.next}while(n!==null);i===null?o=i=t:i=i.next=t}else o=i=t;n={baseState:r.baseState,firstBaseUpdate:o,lastBaseUpdate:i,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function gl(e,t,n,r){var o=e.updateQueue;ir=!1;var i=o.firstBaseUpdate,s=o.lastBaseUpdate,a=o.shared.pending;if(a!==null){o.shared.pending=null;var l=a,u=l.next;l.next=null,s===null?i=u:s.next=u,s=l;var c=e.alternate;c!==null&&(c=c.updateQueue,a=c.lastBaseUpdate,a!==s&&(a===null?c.firstBaseUpdate=u:a.next=u,c.lastBaseUpdate=l))}if(i!==null){var d=o.baseState;s=0,c=u=l=null,a=i;do{var f=a.lane,h=a.eventTime;if((r&f)===f){c!==null&&(c=c.next={eventTime:h,lane:0,tag:a.tag,payload:a.payload,callback:a.callback,next:null});e:{var y=e,v=a;switch(f=t,h=n,v.tag){case 1:if(y=v.payload,typeof y=="function"){d=y.call(h,d,f);break e}d=y;break e;case 3:y.flags=y.flags&-65537|128;case 0:if(y=v.payload,f=typeof y=="function"?y.call(h,d,f):y,f==null)break e;d=Ne({},d,f);break e;case 2:ir=!0}}a.callback!==null&&a.lane!==0&&(e.flags|=64,f=o.effects,f===null?o.effects=[a]:f.push(a))}else h={eventTime:h,lane:f,tag:a.tag,payload:a.payload,callback:a.callback,next:null},c===null?(u=c=h,l=d):c=c.next=h,s|=f;if(a=a.next,a===null){if(a=o.shared.pending,a===null)break;f=a,a=f.next,f.next=null,o.lastBaseUpdate=f,o.shared.pending=null}}while(!0);if(c===null&&(l=d),o.baseState=l,o.firstBaseUpdate=u,o.lastBaseUpdate=c,t=o.shared.interleaved,t!==null){o=t;do s|=o.lane,o=o.next;while(o!==t)}else i===null&&(o.shared.lanes=0);ao|=s,e.lanes=s,e.memoizedState=d}}function rg(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],o=r.callback;if(o!==null){if(r.callback=null,r=n,typeof o!="function")throw Error(N(191,o));o.call(r)}}}var a0=new im.Component().refs;function zc(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:Ne({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var Ql={isMounted:function(e){return(e=e._reactInternals)?go(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=st(),o=Er(e),i=In(r,o);i.payload=t,n!=null&&(i.callback=n),t=br(e,i,o),t!==null&&(Zt(t,e,o,r),za(t,e,o))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=st(),o=Er(e),i=In(r,o);i.tag=1,i.payload=t,n!=null&&(i.callback=n),t=br(e,i,o),t!==null&&(Zt(t,e,o,r),za(t,e,o))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=st(),r=Er(e),o=In(n,r);o.tag=2,t!=null&&(o.callback=t),t=br(e,o,r),t!==null&&(Zt(t,e,r,n),za(t,e,r))}};function og(e,t,n,r,o,i,s){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,i,s):t.prototype&&t.prototype.isPureReactComponent?!vs(n,r)||!vs(o,i):!0}function l0(e,t,n){var r=!1,o=_r,i=t.contextType;return typeof i=="object"&&i!==null?i=Vt(i):(o=ht(t)?oo:rt.current,r=t.contextTypes,i=(r=r!=null)?pi(e,o):_r),t=new t(n,i),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=Ql,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=o,e.__reactInternalMemoizedMaskedChildContext=i),t}function ig(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&Ql.enqueueReplaceState(t,t.state,null)}function Vc(e,t,n,r){var o=e.stateNode;o.props=n,o.state=e.memoizedState,o.refs=a0,uf(e);var i=t.contextType;typeof i=="object"&&i!==null?o.context=Vt(i):(i=ht(t)?oo:rt.current,o.context=pi(e,i)),o.state=e.memoizedState,i=t.getDerivedStateFromProps,typeof i=="function"&&(zc(e,t,i,n),o.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof o.getSnapshotBeforeUpdate=="function"||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(t=o.state,typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount(),t!==o.state&&Ql.enqueueReplaceState(o,o.state,null),gl(e,n,o,r),o.state=e.memoizedState),typeof o.componentDidMount=="function"&&(e.flags|=4194308)}function zi(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(N(309));var r=n.stateNode}if(!r)throw Error(N(147,e));var o=r,i=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===i?t.ref:(t=function(s){var a=o.refs;a===a0&&(a=o.refs={}),s===null?delete a[i]:a[i]=s},t._stringRef=i,t)}if(typeof e!="string")throw Error(N(284));if(!n._owner)throw Error(N(290,e))}return e}function ga(e,t){throw e=Object.prototype.toString.call(t),Error(N(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function sg(e){var t=e._init;return t(e._payload)}function u0(e){function t(m,g){if(e){var w=m.deletions;w===null?(m.deletions=[g],m.flags|=16):w.push(g)}}function n(m,g){if(!e)return null;for(;g!==null;)t(m,g),g=g.sibling;return null}function r(m,g){for(m=new Map;g!==null;)g.key!==null?m.set(g.key,g):m.set(g.index,g),g=g.sibling;return m}function o(m,g){return m=Rr(m,g),m.index=0,m.sibling=null,m}function i(m,g,w){return m.index=w,e?(w=m.alternate,w!==null?(w=w.index,w<g?(m.flags|=2,g):w):(m.flags|=2,g)):(m.flags|=1048576,g)}function s(m){return e&&m.alternate===null&&(m.flags|=2),m}function a(m,g,w,C){return g===null||g.tag!==6?(g=zu(w,m.mode,C),g.return=m,g):(g=o(g,w),g.return=m,g)}function l(m,g,w,C){var E=w.type;return E===Oo?c(m,g,w.props.children,C,w.key):g!==null&&(g.elementType===E||typeof E=="object"&&E!==null&&E.$$typeof===or&&sg(E)===g.type)?(C=o(g,w.props),C.ref=zi(m,g,w),C.return=m,C):(C=Ga(w.type,w.key,w.props,null,m.mode,C),C.ref=zi(m,g,w),C.return=m,C)}function u(m,g,w,C){return g===null||g.tag!==4||g.stateNode.containerInfo!==w.containerInfo||g.stateNode.implementation!==w.implementation?(g=Vu(w,m.mode,C),g.return=m,g):(g=o(g,w.children||[]),g.return=m,g)}function c(m,g,w,C,E){return g===null||g.tag!==7?(g=no(w,m.mode,C,E),g.return=m,g):(g=o(g,w),g.return=m,g)}function d(m,g,w){if(typeof g=="string"&&g!==""||typeof g=="number")return g=zu(""+g,m.mode,w),g.return=m,g;if(typeof g=="object"&&g!==null){switch(g.$$typeof){case ra:return w=Ga(g.type,g.key,g.props,null,m.mode,w),w.ref=zi(m,null,g),w.return=m,w;case No:return g=Vu(g,m.mode,w),g.return=m,g;case or:var C=g._init;return d(m,C(g._payload),w)}if(Xi(g)||Di(g))return g=no(g,m.mode,w,null),g.return=m,g;ga(m,g)}return null}function f(m,g,w,C){var E=g!==null?g.key:null;if(typeof w=="string"&&w!==""||typeof w=="number")return E!==null?null:a(m,g,""+w,C);if(typeof w=="object"&&w!==null){switch(w.$$typeof){case ra:return w.key===E?l(m,g,w,C):null;case No:return w.key===E?u(m,g,w,C):null;case or:return E=w._init,f(m,g,E(w._payload),C)}if(Xi(w)||Di(w))return E!==null?null:c(m,g,w,C,null);ga(m,w)}return null}function h(m,g,w,C,E){if(typeof C=="string"&&C!==""||typeof C=="number")return m=m.get(w)||null,a(g,m,""+C,E);if(typeof C=="object"&&C!==null){switch(C.$$typeof){case ra:return m=m.get(C.key===null?w:C.key)||null,l(g,m,C,E);case No:return m=m.get(C.key===null?w:C.key)||null,u(g,m,C,E);case or:var _=C._init;return h(m,g,w,_(C._payload),E)}if(Xi(C)||Di(C))return m=m.get(w)||null,c(g,m,C,E,null);ga(g,C)}return null}function y(m,g,w,C){for(var E=null,_=null,P=g,R=g=0,O=null;P!==null&&R<w.length;R++){P.index>R?(O=P,P=null):O=P.sibling;var T=f(m,P,w[R],C);if(T===null){P===null&&(P=O);break}e&&P&&T.alternate===null&&t(m,P),g=i(T,g,R),_===null?E=T:_.sibling=T,_=T,P=O}if(R===w.length)return n(m,P),$e&&Ir(m,R),E;if(P===null){for(;R<w.length;R++)P=d(m,w[R],C),P!==null&&(g=i(P,g,R),_===null?E=P:_.sibling=P,_=P);return $e&&Ir(m,R),E}for(P=r(m,P);R<w.length;R++)O=h(P,m,R,w[R],C),O!==null&&(e&&O.alternate!==null&&P.delete(O.key===null?R:O.key),g=i(O,g,R),_===null?E=O:_.sibling=O,_=O);return e&&P.forEach(function(V){return t(m,V)}),$e&&Ir(m,R),E}function v(m,g,w,C){var E=Di(w);if(typeof E!="function")throw Error(N(150));if(w=E.call(w),w==null)throw Error(N(151));for(var _=E=null,P=g,R=g=0,O=null,T=w.next();P!==null&&!T.done;R++,T=w.next()){P.index>R?(O=P,P=null):O=P.sibling;var V=f(m,P,T.value,C);if(V===null){P===null&&(P=O);break}e&&P&&V.alternate===null&&t(m,P),g=i(V,g,R),_===null?E=V:_.sibling=V,_=V,P=O}if(T.done)return n(m,P),$e&&Ir(m,R),E;if(P===null){for(;!T.done;R++,T=w.next())T=d(m,T.value,C),T!==null&&(g=i(T,g,R),_===null?E=T:_.sibling=T,_=T);return $e&&Ir(m,R),E}for(P=r(m,P);!T.done;R++,T=w.next())T=h(P,m,R,T.value,C),T!==null&&(e&&T.alternate!==null&&P.delete(T.key===null?R:T.key),g=i(T,g,R),_===null?E=T:_.sibling=T,_=T);return e&&P.forEach(function(j){return t(m,j)}),$e&&Ir(m,R),E}function x(m,g,w,C){if(typeof w=="object"&&w!==null&&w.type===Oo&&w.key===null&&(w=w.props.children),typeof w=="object"&&w!==null){switch(w.$$typeof){case ra:e:{for(var E=w.key,_=g;_!==null;){if(_.key===E){if(E=w.type,E===Oo){if(_.tag===7){n(m,_.sibling),g=o(_,w.props.children),g.return=m,m=g;break e}}else if(_.elementType===E||typeof E=="object"&&E!==null&&E.$$typeof===or&&sg(E)===_.type){n(m,_.sibling),g=o(_,w.props),g.ref=zi(m,_,w),g.return=m,m=g;break e}n(m,_);break}else t(m,_);_=_.sibling}w.type===Oo?(g=no(w.props.children,m.mode,C,w.key),g.return=m,m=g):(C=Ga(w.type,w.key,w.props,null,m.mode,C),C.ref=zi(m,g,w),C.return=m,m=C)}return s(m);case No:e:{for(_=w.key;g!==null;){if(g.key===_)if(g.tag===4&&g.stateNode.containerInfo===w.containerInfo&&g.stateNode.implementation===w.implementation){n(m,g.sibling),g=o(g,w.children||[]),g.return=m,m=g;break e}else{n(m,g);break}else t(m,g);g=g.sibling}g=Vu(w,m.mode,C),g.return=m,m=g}return s(m);case or:return _=w._init,x(m,g,_(w._payload),C)}if(Xi(w))return y(m,g,w,C);if(Di(w))return v(m,g,w,C);ga(m,w)}return typeof w=="string"&&w!==""||typeof w=="number"?(w=""+w,g!==null&&g.tag===6?(n(m,g.sibling),g=o(g,w),g.return=m,m=g):(n(m,g),g=zu(w,m.mode,C),g.return=m,m=g),s(m)):n(m,g)}return x}var hi=u0(!0),c0=u0(!1),Xs={},vn=Ar(Xs),Ss=Ar(Xs),Cs=Ar(Xs);function Br(e){if(e===Xs)throw Error(N(174));return e}function cf(e,t){switch(we(Cs,t),we(Ss,e),we(vn,Xs),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:wc(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=wc(t,e)}be(vn),we(vn,t)}function mi(){be(vn),be(Ss),be(Cs)}function d0(e){Br(Cs.current);var t=Br(vn.current),n=wc(t,e.type);t!==n&&(we(Ss,e),we(vn,n))}function df(e){Ss.current===e&&(be(vn),be(Ss))}var _e=Ar(0);function hl(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var Mu=[];function ff(){for(var e=0;e<Mu.length;e++)Mu[e]._workInProgressVersionPrimary=null;Mu.length=0}var Va=Kn.ReactCurrentDispatcher,Du=Kn.ReactCurrentBatchConfig,so=0,ke=null,He=null,Ge=null,ml=!1,ss=!1,bs=0,ox=0;function Je(){throw Error(N(321))}function pf(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!en(e[n],t[n]))return!1;return!0}function gf(e,t,n,r,o,i){if(so=i,ke=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,Va.current=e===null||e.memoizedState===null?lx:ux,e=n(r,o),ss){i=0;do{if(ss=!1,bs=0,25<=i)throw Error(N(301));i+=1,Ge=He=null,t.updateQueue=null,Va.current=cx,e=n(r,o)}while(ss)}if(Va.current=vl,t=He!==null&&He.next!==null,so=0,Ge=He=ke=null,ml=!1,t)throw Error(N(300));return e}function hf(){var e=bs!==0;return bs=0,e}function sn(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Ge===null?ke.memoizedState=Ge=e:Ge=Ge.next=e,Ge}function Ht(){if(He===null){var e=ke.alternate;e=e!==null?e.memoizedState:null}else e=He.next;var t=Ge===null?ke.memoizedState:Ge.next;if(t!==null)Ge=t,He=e;else{if(e===null)throw Error(N(310));He=e,e={memoizedState:He.memoizedState,baseState:He.baseState,baseQueue:He.baseQueue,queue:He.queue,next:null},Ge===null?ke.memoizedState=Ge=e:Ge=Ge.next=e}return Ge}function $s(e,t){return typeof t=="function"?t(e):t}function Lu(e){var t=Ht(),n=t.queue;if(n===null)throw Error(N(311));n.lastRenderedReducer=e;var r=He,o=r.baseQueue,i=n.pending;if(i!==null){if(o!==null){var s=o.next;o.next=i.next,i.next=s}r.baseQueue=o=i,n.pending=null}if(o!==null){i=o.next,r=r.baseState;var a=s=null,l=null,u=i;do{var c=u.lane;if((so&c)===c)l!==null&&(l=l.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var d={lane:c,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};l===null?(a=l=d,s=r):l=l.next=d,ke.lanes|=c,ao|=c}u=u.next}while(u!==null&&u!==i);l===null?s=r:l.next=a,en(r,t.memoizedState)||(pt=!0),t.memoizedState=r,t.baseState=s,t.baseQueue=l,n.lastRenderedState=r}if(e=n.interleaved,e!==null){o=e;do i=o.lane,ke.lanes|=i,ao|=i,o=o.next;while(o!==e)}else o===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function Iu(e){var t=Ht(),n=t.queue;if(n===null)throw Error(N(311));n.lastRenderedReducer=e;var r=n.dispatch,o=n.pending,i=t.memoizedState;if(o!==null){n.pending=null;var s=o=o.next;do i=e(i,s.action),s=s.next;while(s!==o);en(i,t.memoizedState)||(pt=!0),t.memoizedState=i,t.baseQueue===null&&(t.baseState=i),n.lastRenderedState=i}return[i,r]}function f0(){}function p0(e,t){var n=ke,r=Ht(),o=t(),i=!en(r.memoizedState,o);if(i&&(r.memoizedState=o,pt=!0),r=r.queue,mf(m0.bind(null,n,r,e),[e]),r.getSnapshot!==t||i||Ge!==null&&Ge.memoizedState.tag&1){if(n.flags|=2048,Es(9,h0.bind(null,n,r,o,t),void 0,null),Ke===null)throw Error(N(349));so&30||g0(n,t,o)}return o}function g0(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=ke.updateQueue,t===null?(t={lastEffect:null,stores:null},ke.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function h0(e,t,n,r){t.value=n,t.getSnapshot=r,v0(t)&&y0(e)}function m0(e,t,n){return n(function(){v0(t)&&y0(e)})}function v0(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!en(e,n)}catch{return!0}}function y0(e){var t=Un(e,1);t!==null&&Zt(t,e,1,-1)}function ag(e){var t=sn();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:$s,lastRenderedState:e},t.queue=e,e=e.dispatch=ax.bind(null,ke,e),[t.memoizedState,e]}function Es(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=ke.updateQueue,t===null?(t={lastEffect:null,stores:null},ke.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function w0(){return Ht().memoizedState}function Ha(e,t,n,r){var o=sn();ke.flags|=e,o.memoizedState=Es(1|t,n,void 0,r===void 0?null:r)}function Yl(e,t,n,r){var o=Ht();r=r===void 0?null:r;var i=void 0;if(He!==null){var s=He.memoizedState;if(i=s.destroy,r!==null&&pf(r,s.deps)){o.memoizedState=Es(t,n,i,r);return}}ke.flags|=e,o.memoizedState=Es(1|t,n,i,r)}function lg(e,t){return Ha(8390656,8,e,t)}function mf(e,t){return Yl(2048,8,e,t)}function x0(e,t){return Yl(4,2,e,t)}function S0(e,t){return Yl(4,4,e,t)}function C0(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function b0(e,t,n){return n=n!=null?n.concat([e]):null,Yl(4,4,C0.bind(null,t,e),n)}function vf(){}function $0(e,t){var n=Ht();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&pf(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function E0(e,t){var n=Ht();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&pf(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function R0(e,t,n){return so&21?(en(n,t)||(n=km(),ke.lanes|=n,ao|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,pt=!0),e.memoizedState=n)}function ix(e,t){var n=me;me=n!==0&&4>n?n:4,e(!0);var r=Du.transition;Du.transition={};try{e(!1),t()}finally{me=n,Du.transition=r}}function P0(){return Ht().memoizedState}function sx(e,t,n){var r=Er(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},_0(e))k0(t,n);else if(n=i0(e,t,n,r),n!==null){var o=st();Zt(n,e,r,o),N0(n,t,r)}}function ax(e,t,n){var r=Er(e),o={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(_0(e))k0(t,o);else{var i=e.alternate;if(e.lanes===0&&(i===null||i.lanes===0)&&(i=t.lastRenderedReducer,i!==null))try{var s=t.lastRenderedState,a=i(s,n);if(o.hasEagerState=!0,o.eagerState=a,en(a,s)){var l=t.interleaved;l===null?(o.next=o,lf(t)):(o.next=l.next,l.next=o),t.interleaved=o;return}}catch{}finally{}n=i0(e,t,o,r),n!==null&&(o=st(),Zt(n,e,r,o),N0(n,t,r))}}function _0(e){var t=e.alternate;return e===ke||t!==null&&t===ke}function k0(e,t){ss=ml=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function N0(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Kd(e,n)}}var vl={readContext:Vt,useCallback:Je,useContext:Je,useEffect:Je,useImperativeHandle:Je,useInsertionEffect:Je,useLayoutEffect:Je,useMemo:Je,useReducer:Je,useRef:Je,useState:Je,useDebugValue:Je,useDeferredValue:Je,useTransition:Je,useMutableSource:Je,useSyncExternalStore:Je,useId:Je,unstable_isNewReconciler:!1},lx={readContext:Vt,useCallback:function(e,t){return sn().memoizedState=[e,t===void 0?null:t],e},useContext:Vt,useEffect:lg,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,Ha(4194308,4,C0.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Ha(4194308,4,e,t)},useInsertionEffect:function(e,t){return Ha(4,2,e,t)},useMemo:function(e,t){var n=sn();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=sn();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=sx.bind(null,ke,e),[r.memoizedState,e]},useRef:function(e){var t=sn();return e={current:e},t.memoizedState=e},useState:ag,useDebugValue:vf,useDeferredValue:function(e){return sn().memoizedState=e},useTransition:function(){var e=ag(!1),t=e[0];return e=ix.bind(null,e[1]),sn().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=ke,o=sn();if($e){if(n===void 0)throw Error(N(407));n=n()}else{if(n=t(),Ke===null)throw Error(N(349));so&30||g0(r,t,n)}o.memoizedState=n;var i={value:n,getSnapshot:t};return o.queue=i,lg(m0.bind(null,r,i,e),[e]),r.flags|=2048,Es(9,h0.bind(null,r,i,n,t),void 0,null),n},useId:function(){var e=sn(),t=Ke.identifierPrefix;if($e){var n=Ln,r=Dn;n=(r&~(1<<32-Xt(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=bs++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=ox++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},ux={readContext:Vt,useCallback:$0,useContext:Vt,useEffect:mf,useImperativeHandle:b0,useInsertionEffect:x0,useLayoutEffect:S0,useMemo:E0,useReducer:Lu,useRef:w0,useState:function(){return Lu($s)},useDebugValue:vf,useDeferredValue:function(e){var t=Ht();return R0(t,He.memoizedState,e)},useTransition:function(){var e=Lu($s)[0],t=Ht().memoizedState;return[e,t]},useMutableSource:f0,useSyncExternalStore:p0,useId:P0,unstable_isNewReconciler:!1},cx={readContext:Vt,useCallback:$0,useContext:Vt,useEffect:mf,useImperativeHandle:b0,useInsertionEffect:x0,useLayoutEffect:S0,useMemo:E0,useReducer:Iu,useRef:w0,useState:function(){return Iu($s)},useDebugValue:vf,useDeferredValue:function(e){var t=Ht();return He===null?t.memoizedState=e:R0(t,He.memoizedState,e)},useTransition:function(){var e=Iu($s)[0],t=Ht().memoizedState;return[e,t]},useMutableSource:f0,useSyncExternalStore:p0,useId:P0,unstable_isNewReconciler:!1};function vi(e,t){try{var n="",r=t;do n+=F1(r),r=r.return;while(r);var o=n}catch(i){o=`
Error generating stack: `+i.message+`
`+i.stack}return{value:e,source:t,stack:o,digest:null}}function Fu(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function Hc(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var dx=typeof WeakMap=="function"?WeakMap:Map;function O0(e,t,n){n=In(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){wl||(wl=!0,Zc=r),Hc(e,t)},n}function T0(e,t,n){n=In(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var o=t.value;n.payload=function(){return r(o)},n.callback=function(){Hc(e,t)}}var i=e.stateNode;return i!==null&&typeof i.componentDidCatch=="function"&&(n.callback=function(){Hc(e,t),typeof r!="function"&&($r===null?$r=new Set([this]):$r.add(this));var s=t.stack;this.componentDidCatch(t.value,{componentStack:s!==null?s:""})}),n}function ug(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new dx;var o=new Set;r.set(t,o)}else o=r.get(t),o===void 0&&(o=new Set,r.set(t,o));o.has(n)||(o.add(n),e=Ex.bind(null,e,t,n),t.then(e,e))}function cg(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function dg(e,t,n,r,o){return e.mode&1?(e.flags|=65536,e.lanes=o,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=In(-1,1),t.tag=2,br(n,t,1))),n.lanes|=1),e)}var fx=Kn.ReactCurrentOwner,pt=!1;function it(e,t,n,r){t.child=e===null?c0(t,null,n,r):hi(t,e.child,n,r)}function fg(e,t,n,r,o){n=n.render;var i=t.ref;return Go(t,o),r=gf(e,t,n,r,i,o),n=hf(),e!==null&&!pt?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,Bn(e,t,o)):($e&&n&&tf(t),t.flags|=1,it(e,t,r,o),t.child)}function pg(e,t,n,r,o){if(e===null){var i=n.type;return typeof i=="function"&&!Ef(i)&&i.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=i,A0(e,t,i,r,o)):(e=Ga(n.type,null,r,t,t.mode,o),e.ref=t.ref,e.return=t,t.child=e)}if(i=e.child,!(e.lanes&o)){var s=i.memoizedProps;if(n=n.compare,n=n!==null?n:vs,n(s,r)&&e.ref===t.ref)return Bn(e,t,o)}return t.flags|=1,e=Rr(i,r),e.ref=t.ref,e.return=t,t.child=e}function A0(e,t,n,r,o){if(e!==null){var i=e.memoizedProps;if(vs(i,r)&&e.ref===t.ref)if(pt=!1,t.pendingProps=r=i,(e.lanes&o)!==0)e.flags&131072&&(pt=!0);else return t.lanes=e.lanes,Bn(e,t,o)}return Uc(e,t,n,r,o)}function M0(e,t,n){var r=t.pendingProps,o=r.children,i=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},we(Vo,St),St|=n;else{if(!(n&1073741824))return e=i!==null?i.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,we(Vo,St),St|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=i!==null?i.baseLanes:n,we(Vo,St),St|=r}else i!==null?(r=i.baseLanes|n,t.memoizedState=null):r=n,we(Vo,St),St|=r;return it(e,t,o,n),t.child}function D0(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Uc(e,t,n,r,o){var i=ht(n)?oo:rt.current;return i=pi(t,i),Go(t,o),n=gf(e,t,n,r,i,o),r=hf(),e!==null&&!pt?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,Bn(e,t,o)):($e&&r&&tf(t),t.flags|=1,it(e,t,n,o),t.child)}function gg(e,t,n,r,o){if(ht(n)){var i=!0;ul(t)}else i=!1;if(Go(t,o),t.stateNode===null)Ua(e,t),l0(t,n,r),Vc(t,n,r,o),r=!0;else if(e===null){var s=t.stateNode,a=t.memoizedProps;s.props=a;var l=s.context,u=n.contextType;typeof u=="object"&&u!==null?u=Vt(u):(u=ht(n)?oo:rt.current,u=pi(t,u));var c=n.getDerivedStateFromProps,d=typeof c=="function"||typeof s.getSnapshotBeforeUpdate=="function";d||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(a!==r||l!==u)&&ig(t,s,r,u),ir=!1;var f=t.memoizedState;s.state=f,gl(t,r,s,o),l=t.memoizedState,a!==r||f!==l||gt.current||ir?(typeof c=="function"&&(zc(t,n,c,r),l=t.memoizedState),(a=ir||og(t,n,a,r,f,l,u))?(d||typeof s.UNSAFE_componentWillMount!="function"&&typeof s.componentWillMount!="function"||(typeof s.componentWillMount=="function"&&s.componentWillMount(),typeof s.UNSAFE_componentWillMount=="function"&&s.UNSAFE_componentWillMount()),typeof s.componentDidMount=="function"&&(t.flags|=4194308)):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=l),s.props=r,s.state=l,s.context=u,r=a):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{s=t.stateNode,s0(e,t),a=t.memoizedProps,u=t.type===t.elementType?a:Kt(t.type,a),s.props=u,d=t.pendingProps,f=s.context,l=n.contextType,typeof l=="object"&&l!==null?l=Vt(l):(l=ht(n)?oo:rt.current,l=pi(t,l));var h=n.getDerivedStateFromProps;(c=typeof h=="function"||typeof s.getSnapshotBeforeUpdate=="function")||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(a!==d||f!==l)&&ig(t,s,r,l),ir=!1,f=t.memoizedState,s.state=f,gl(t,r,s,o);var y=t.memoizedState;a!==d||f!==y||gt.current||ir?(typeof h=="function"&&(zc(t,n,h,r),y=t.memoizedState),(u=ir||og(t,n,u,r,f,y,l)||!1)?(c||typeof s.UNSAFE_componentWillUpdate!="function"&&typeof s.componentWillUpdate!="function"||(typeof s.componentWillUpdate=="function"&&s.componentWillUpdate(r,y,l),typeof s.UNSAFE_componentWillUpdate=="function"&&s.UNSAFE_componentWillUpdate(r,y,l)),typeof s.componentDidUpdate=="function"&&(t.flags|=4),typeof s.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof s.componentDidUpdate!="function"||a===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=y),s.props=r,s.state=y,s.context=l,r=u):(typeof s.componentDidUpdate!="function"||a===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),r=!1)}return Bc(e,t,n,r,i,o)}function Bc(e,t,n,r,o,i){D0(e,t);var s=(t.flags&128)!==0;if(!r&&!s)return o&&Jp(t,n,!1),Bn(e,t,i);r=t.stateNode,fx.current=t;var a=s&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&s?(t.child=hi(t,e.child,null,i),t.child=hi(t,null,a,i)):it(e,t,a,i),t.memoizedState=r.state,o&&Jp(t,n,!0),t.child}function L0(e){var t=e.stateNode;t.pendingContext?Zp(e,t.pendingContext,t.pendingContext!==t.context):t.context&&Zp(e,t.context,!1),cf(e,t.containerInfo)}function hg(e,t,n,r,o){return gi(),rf(o),t.flags|=256,it(e,t,n,r),t.child}var Wc={dehydrated:null,treeContext:null,retryLane:0};function Gc(e){return{baseLanes:e,cachePool:null,transitions:null}}function I0(e,t,n){var r=t.pendingProps,o=_e.current,i=!1,s=(t.flags&128)!==0,a;if((a=s)||(a=e!==null&&e.memoizedState===null?!1:(o&2)!==0),a?(i=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(o|=1),we(_e,o&1),e===null)return Fc(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(s=r.children,e=r.fallback,i?(r=t.mode,i=t.child,s={mode:"hidden",children:s},!(r&1)&&i!==null?(i.childLanes=0,i.pendingProps=s):i=Zl(s,r,0,null),e=no(e,r,n,null),i.return=t,e.return=t,i.sibling=e,t.child=i,t.child.memoizedState=Gc(n),t.memoizedState=Wc,e):yf(t,s));if(o=e.memoizedState,o!==null&&(a=o.dehydrated,a!==null))return px(e,t,s,r,a,o,n);if(i){i=r.fallback,s=t.mode,o=e.child,a=o.sibling;var l={mode:"hidden",children:r.children};return!(s&1)&&t.child!==o?(r=t.child,r.childLanes=0,r.pendingProps=l,t.deletions=null):(r=Rr(o,l),r.subtreeFlags=o.subtreeFlags&14680064),a!==null?i=Rr(a,i):(i=no(i,s,n,null),i.flags|=2),i.return=t,r.return=t,r.sibling=i,t.child=r,r=i,i=t.child,s=e.child.memoizedState,s=s===null?Gc(n):{baseLanes:s.baseLanes|n,cachePool:null,transitions:s.transitions},i.memoizedState=s,i.childLanes=e.childLanes&~n,t.memoizedState=Wc,r}return i=e.child,e=i.sibling,r=Rr(i,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function yf(e,t){return t=Zl({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function ha(e,t,n,r){return r!==null&&rf(r),hi(t,e.child,null,n),e=yf(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function px(e,t,n,r,o,i,s){if(n)return t.flags&256?(t.flags&=-257,r=Fu(Error(N(422))),ha(e,t,s,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(i=r.fallback,o=t.mode,r=Zl({mode:"visible",children:r.children},o,0,null),i=no(i,o,s,null),i.flags|=2,r.return=t,i.return=t,r.sibling=i,t.child=r,t.mode&1&&hi(t,e.child,null,s),t.child.memoizedState=Gc(s),t.memoizedState=Wc,i);if(!(t.mode&1))return ha(e,t,s,null);if(o.data==="$!"){if(r=o.nextSibling&&o.nextSibling.dataset,r)var a=r.dgst;return r=a,i=Error(N(419)),r=Fu(i,r,void 0),ha(e,t,s,r)}if(a=(s&e.childLanes)!==0,pt||a){if(r=Ke,r!==null){switch(s&-s){case 4:o=2;break;case 16:o=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:o=32;break;case 536870912:o=268435456;break;default:o=0}o=o&(r.suspendedLanes|s)?0:o,o!==0&&o!==i.retryLane&&(i.retryLane=o,Un(e,o),Zt(r,e,o,-1))}return $f(),r=Fu(Error(N(421))),ha(e,t,s,r)}return o.data==="$?"?(t.flags|=128,t.child=e.child,t=Rx.bind(null,e),o._reactRetry=t,null):(e=i.treeContext,Ct=Cr(o.nextSibling),$t=t,$e=!0,qt=null,e!==null&&(It[Ft++]=Dn,It[Ft++]=Ln,It[Ft++]=io,Dn=e.id,Ln=e.overflow,io=t),t=yf(t,r.children),t.flags|=4096,t)}function mg(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),jc(e.return,t,n)}function ju(e,t,n,r,o){var i=e.memoizedState;i===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:o}:(i.isBackwards=t,i.rendering=null,i.renderingStartTime=0,i.last=r,i.tail=n,i.tailMode=o)}function F0(e,t,n){var r=t.pendingProps,o=r.revealOrder,i=r.tail;if(it(e,t,r.children,n),r=_e.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&mg(e,n,t);else if(e.tag===19)mg(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(we(_e,r),!(t.mode&1))t.memoizedState=null;else switch(o){case"forwards":for(n=t.child,o=null;n!==null;)e=n.alternate,e!==null&&hl(e)===null&&(o=n),n=n.sibling;n=o,n===null?(o=t.child,t.child=null):(o=n.sibling,n.sibling=null),ju(t,!1,o,n,i);break;case"backwards":for(n=null,o=t.child,t.child=null;o!==null;){if(e=o.alternate,e!==null&&hl(e)===null){t.child=o;break}e=o.sibling,o.sibling=n,n=o,o=e}ju(t,!0,n,null,i);break;case"together":ju(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Ua(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Bn(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),ao|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(N(153));if(t.child!==null){for(e=t.child,n=Rr(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=Rr(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function gx(e,t,n){switch(t.tag){case 3:L0(t),gi();break;case 5:d0(t);break;case 1:ht(t.type)&&ul(t);break;case 4:cf(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,o=t.memoizedProps.value;we(fl,r._currentValue),r._currentValue=o;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(we(_e,_e.current&1),t.flags|=128,null):n&t.child.childLanes?I0(e,t,n):(we(_e,_e.current&1),e=Bn(e,t,n),e!==null?e.sibling:null);we(_e,_e.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return F0(e,t,n);t.flags|=128}if(o=t.memoizedState,o!==null&&(o.rendering=null,o.tail=null,o.lastEffect=null),we(_e,_e.current),r)break;return null;case 22:case 23:return t.lanes=0,M0(e,t,n)}return Bn(e,t,n)}var j0,Kc,z0,V0;j0=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};Kc=function(){};z0=function(e,t,n,r){var o=e.memoizedProps;if(o!==r){e=t.stateNode,Br(vn.current);var i=null;switch(n){case"input":o=hc(e,o),r=hc(e,r),i=[];break;case"select":o=Ne({},o,{value:void 0}),r=Ne({},r,{value:void 0}),i=[];break;case"textarea":o=yc(e,o),r=yc(e,r),i=[];break;default:typeof o.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=al)}xc(n,r);var s;n=null;for(u in o)if(!r.hasOwnProperty(u)&&o.hasOwnProperty(u)&&o[u]!=null)if(u==="style"){var a=o[u];for(s in a)a.hasOwnProperty(s)&&(n||(n={}),n[s]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(cs.hasOwnProperty(u)?i||(i=[]):(i=i||[]).push(u,null));for(u in r){var l=r[u];if(a=o!=null?o[u]:void 0,r.hasOwnProperty(u)&&l!==a&&(l!=null||a!=null))if(u==="style")if(a){for(s in a)!a.hasOwnProperty(s)||l&&l.hasOwnProperty(s)||(n||(n={}),n[s]="");for(s in l)l.hasOwnProperty(s)&&a[s]!==l[s]&&(n||(n={}),n[s]=l[s])}else n||(i||(i=[]),i.push(u,n)),n=l;else u==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,a=a?a.__html:void 0,l!=null&&a!==l&&(i=i||[]).push(u,l)):u==="children"?typeof l!="string"&&typeof l!="number"||(i=i||[]).push(u,""+l):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&(cs.hasOwnProperty(u)?(l!=null&&u==="onScroll"&&Ce("scroll",e),i||a===l||(i=[])):(i=i||[]).push(u,l))}n&&(i=i||[]).push("style",n);var u=i;(t.updateQueue=u)&&(t.flags|=4)}};V0=function(e,t,n,r){n!==r&&(t.flags|=4)};function Vi(e,t){if(!$e)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function et(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags&14680064,r|=o.flags&14680064,o.return=e,o=o.sibling;else for(o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags,r|=o.flags,o.return=e,o=o.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function hx(e,t,n){var r=t.pendingProps;switch(nf(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return et(t),null;case 1:return ht(t.type)&&ll(),et(t),null;case 3:return r=t.stateNode,mi(),be(gt),be(rt),ff(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(pa(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,qt!==null&&(td(qt),qt=null))),Kc(e,t),et(t),null;case 5:df(t);var o=Br(Cs.current);if(n=t.type,e!==null&&t.stateNode!=null)z0(e,t,n,r,o),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(N(166));return et(t),null}if(e=Br(vn.current),pa(t)){r=t.stateNode,n=t.type;var i=t.memoizedProps;switch(r[pn]=t,r[xs]=i,e=(t.mode&1)!==0,n){case"dialog":Ce("cancel",r),Ce("close",r);break;case"iframe":case"object":case"embed":Ce("load",r);break;case"video":case"audio":for(o=0;o<Ji.length;o++)Ce(Ji[o],r);break;case"source":Ce("error",r);break;case"img":case"image":case"link":Ce("error",r),Ce("load",r);break;case"details":Ce("toggle",r);break;case"input":Ep(r,i),Ce("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!i.multiple},Ce("invalid",r);break;case"textarea":Pp(r,i),Ce("invalid",r)}xc(n,i),o=null;for(var s in i)if(i.hasOwnProperty(s)){var a=i[s];s==="children"?typeof a=="string"?r.textContent!==a&&(i.suppressHydrationWarning!==!0&&fa(r.textContent,a,e),o=["children",a]):typeof a=="number"&&r.textContent!==""+a&&(i.suppressHydrationWarning!==!0&&fa(r.textContent,a,e),o=["children",""+a]):cs.hasOwnProperty(s)&&a!=null&&s==="onScroll"&&Ce("scroll",r)}switch(n){case"input":oa(r),Rp(r,i,!0);break;case"textarea":oa(r),_p(r);break;case"select":case"option":break;default:typeof i.onClick=="function"&&(r.onclick=al)}r=o,t.updateQueue=r,r!==null&&(t.flags|=4)}else{s=o.nodeType===9?o:o.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=gm(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=s.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=s.createElement(n,{is:r.is}):(e=s.createElement(n),n==="select"&&(s=e,r.multiple?s.multiple=!0:r.size&&(s.size=r.size))):e=s.createElementNS(e,n),e[pn]=t,e[xs]=r,j0(e,t,!1,!1),t.stateNode=e;e:{switch(s=Sc(n,r),n){case"dialog":Ce("cancel",e),Ce("close",e),o=r;break;case"iframe":case"object":case"embed":Ce("load",e),o=r;break;case"video":case"audio":for(o=0;o<Ji.length;o++)Ce(Ji[o],e);o=r;break;case"source":Ce("error",e),o=r;break;case"img":case"image":case"link":Ce("error",e),Ce("load",e),o=r;break;case"details":Ce("toggle",e),o=r;break;case"input":Ep(e,r),o=hc(e,r),Ce("invalid",e);break;case"option":o=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},o=Ne({},r,{value:void 0}),Ce("invalid",e);break;case"textarea":Pp(e,r),o=yc(e,r),Ce("invalid",e);break;default:o=r}xc(n,o),a=o;for(i in a)if(a.hasOwnProperty(i)){var l=a[i];i==="style"?vm(e,l):i==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,l!=null&&hm(e,l)):i==="children"?typeof l=="string"?(n!=="textarea"||l!=="")&&ds(e,l):typeof l=="number"&&ds(e,""+l):i!=="suppressContentEditableWarning"&&i!=="suppressHydrationWarning"&&i!=="autoFocus"&&(cs.hasOwnProperty(i)?l!=null&&i==="onScroll"&&Ce("scroll",e):l!=null&&Vd(e,i,l,s))}switch(n){case"input":oa(e),Rp(e,r,!1);break;case"textarea":oa(e),_p(e);break;case"option":r.value!=null&&e.setAttribute("value",""+Pr(r.value));break;case"select":e.multiple=!!r.multiple,i=r.value,i!=null?Ho(e,!!r.multiple,i,!1):r.defaultValue!=null&&Ho(e,!!r.multiple,r.defaultValue,!0);break;default:typeof o.onClick=="function"&&(e.onclick=al)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return et(t),null;case 6:if(e&&t.stateNode!=null)V0(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(N(166));if(n=Br(Cs.current),Br(vn.current),pa(t)){if(r=t.stateNode,n=t.memoizedProps,r[pn]=t,(i=r.nodeValue!==n)&&(e=$t,e!==null))switch(e.tag){case 3:fa(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&fa(r.nodeValue,n,(e.mode&1)!==0)}i&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[pn]=t,t.stateNode=r}return et(t),null;case 13:if(be(_e),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if($e&&Ct!==null&&t.mode&1&&!(t.flags&128))o0(),gi(),t.flags|=98560,i=!1;else if(i=pa(t),r!==null&&r.dehydrated!==null){if(e===null){if(!i)throw Error(N(318));if(i=t.memoizedState,i=i!==null?i.dehydrated:null,!i)throw Error(N(317));i[pn]=t}else gi(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;et(t),i=!1}else qt!==null&&(td(qt),qt=null),i=!0;if(!i)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||_e.current&1?Ue===0&&(Ue=3):$f())),t.updateQueue!==null&&(t.flags|=4),et(t),null);case 4:return mi(),Kc(e,t),e===null&&ys(t.stateNode.containerInfo),et(t),null;case 10:return af(t.type._context),et(t),null;case 17:return ht(t.type)&&ll(),et(t),null;case 19:if(be(_e),i=t.memoizedState,i===null)return et(t),null;if(r=(t.flags&128)!==0,s=i.rendering,s===null)if(r)Vi(i,!1);else{if(Ue!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(s=hl(e),s!==null){for(t.flags|=128,Vi(i,!1),r=s.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)i=n,e=r,i.flags&=14680066,s=i.alternate,s===null?(i.childLanes=0,i.lanes=e,i.child=null,i.subtreeFlags=0,i.memoizedProps=null,i.memoizedState=null,i.updateQueue=null,i.dependencies=null,i.stateNode=null):(i.childLanes=s.childLanes,i.lanes=s.lanes,i.child=s.child,i.subtreeFlags=0,i.deletions=null,i.memoizedProps=s.memoizedProps,i.memoizedState=s.memoizedState,i.updateQueue=s.updateQueue,i.type=s.type,e=s.dependencies,i.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return we(_e,_e.current&1|2),t.child}e=e.sibling}i.tail!==null&&Le()>yi&&(t.flags|=128,r=!0,Vi(i,!1),t.lanes=4194304)}else{if(!r)if(e=hl(s),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),Vi(i,!0),i.tail===null&&i.tailMode==="hidden"&&!s.alternate&&!$e)return et(t),null}else 2*Le()-i.renderingStartTime>yi&&n!==1073741824&&(t.flags|=128,r=!0,Vi(i,!1),t.lanes=4194304);i.isBackwards?(s.sibling=t.child,t.child=s):(n=i.last,n!==null?n.sibling=s:t.child=s,i.last=s)}return i.tail!==null?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=Le(),t.sibling=null,n=_e.current,we(_e,r?n&1|2:n&1),t):(et(t),null);case 22:case 23:return bf(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?St&1073741824&&(et(t),t.subtreeFlags&6&&(t.flags|=8192)):et(t),null;case 24:return null;case 25:return null}throw Error(N(156,t.tag))}function mx(e,t){switch(nf(t),t.tag){case 1:return ht(t.type)&&ll(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return mi(),be(gt),be(rt),ff(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return df(t),null;case 13:if(be(_e),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(N(340));gi()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return be(_e),null;case 4:return mi(),null;case 10:return af(t.type._context),null;case 22:case 23:return bf(),null;case 24:return null;default:return null}}var ma=!1,nt=!1,vx=typeof WeakSet=="function"?WeakSet:Set,W=null;function zo(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){Oe(e,t,r)}else n.current=null}function Qc(e,t,n){try{n()}catch(r){Oe(e,t,r)}}var vg=!1;function yx(e,t){if(Oc=ol,e=Wm(),ef(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var o=r.anchorOffset,i=r.focusNode;r=r.focusOffset;try{n.nodeType,i.nodeType}catch{n=null;break e}var s=0,a=-1,l=-1,u=0,c=0,d=e,f=null;t:for(;;){for(var h;d!==n||o!==0&&d.nodeType!==3||(a=s+o),d!==i||r!==0&&d.nodeType!==3||(l=s+r),d.nodeType===3&&(s+=d.nodeValue.length),(h=d.firstChild)!==null;)f=d,d=h;for(;;){if(d===e)break t;if(f===n&&++u===o&&(a=s),f===i&&++c===r&&(l=s),(h=d.nextSibling)!==null)break;d=f,f=d.parentNode}d=h}n=a===-1||l===-1?null:{start:a,end:l}}else n=null}n=n||{start:0,end:0}}else n=null;for(Tc={focusedElem:e,selectionRange:n},ol=!1,W=t;W!==null;)if(t=W,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,W=e;else for(;W!==null;){t=W;try{var y=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(y!==null){var v=y.memoizedProps,x=y.memoizedState,m=t.stateNode,g=m.getSnapshotBeforeUpdate(t.elementType===t.type?v:Kt(t.type,v),x);m.__reactInternalSnapshotBeforeUpdate=g}break;case 3:var w=t.stateNode.containerInfo;w.nodeType===1?w.textContent="":w.nodeType===9&&w.documentElement&&w.removeChild(w.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(N(163))}}catch(C){Oe(t,t.return,C)}if(e=t.sibling,e!==null){e.return=t.return,W=e;break}W=t.return}return y=vg,vg=!1,y}function as(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var o=r=r.next;do{if((o.tag&e)===e){var i=o.destroy;o.destroy=void 0,i!==void 0&&Qc(t,n,i)}o=o.next}while(o!==r)}}function ql(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function Yc(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function H0(e){var t=e.alternate;t!==null&&(e.alternate=null,H0(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[pn],delete t[xs],delete t[Dc],delete t[ex],delete t[tx])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function U0(e){return e.tag===5||e.tag===3||e.tag===4}function yg(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||U0(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function qc(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=al));else if(r!==4&&(e=e.child,e!==null))for(qc(e,t,n),e=e.sibling;e!==null;)qc(e,t,n),e=e.sibling}function Xc(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(Xc(e,t,n),e=e.sibling;e!==null;)Xc(e,t,n),e=e.sibling}var qe=null,Yt=!1;function Jn(e,t,n){for(n=n.child;n!==null;)B0(e,t,n),n=n.sibling}function B0(e,t,n){if(mn&&typeof mn.onCommitFiberUnmount=="function")try{mn.onCommitFiberUnmount(Hl,n)}catch{}switch(n.tag){case 5:nt||zo(n,t);case 6:var r=qe,o=Yt;qe=null,Jn(e,t,n),qe=r,Yt=o,qe!==null&&(Yt?(e=qe,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):qe.removeChild(n.stateNode));break;case 18:qe!==null&&(Yt?(e=qe,n=n.stateNode,e.nodeType===8?Tu(e.parentNode,n):e.nodeType===1&&Tu(e,n),hs(e)):Tu(qe,n.stateNode));break;case 4:r=qe,o=Yt,qe=n.stateNode.containerInfo,Yt=!0,Jn(e,t,n),qe=r,Yt=o;break;case 0:case 11:case 14:case 15:if(!nt&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){o=r=r.next;do{var i=o,s=i.destroy;i=i.tag,s!==void 0&&(i&2||i&4)&&Qc(n,t,s),o=o.next}while(o!==r)}Jn(e,t,n);break;case 1:if(!nt&&(zo(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(a){Oe(n,t,a)}Jn(e,t,n);break;case 21:Jn(e,t,n);break;case 22:n.mode&1?(nt=(r=nt)||n.memoizedState!==null,Jn(e,t,n),nt=r):Jn(e,t,n);break;default:Jn(e,t,n)}}function wg(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new vx),t.forEach(function(r){var o=Px.bind(null,e,r);n.has(r)||(n.add(r),r.then(o,o))})}}function Bt(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var o=n[r];try{var i=e,s=t,a=s;e:for(;a!==null;){switch(a.tag){case 5:qe=a.stateNode,Yt=!1;break e;case 3:qe=a.stateNode.containerInfo,Yt=!0;break e;case 4:qe=a.stateNode.containerInfo,Yt=!0;break e}a=a.return}if(qe===null)throw Error(N(160));B0(i,s,o),qe=null,Yt=!1;var l=o.alternate;l!==null&&(l.return=null),o.return=null}catch(u){Oe(o,t,u)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)W0(t,e),t=t.sibling}function W0(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(Bt(t,e),on(e),r&4){try{as(3,e,e.return),ql(3,e)}catch(v){Oe(e,e.return,v)}try{as(5,e,e.return)}catch(v){Oe(e,e.return,v)}}break;case 1:Bt(t,e),on(e),r&512&&n!==null&&zo(n,n.return);break;case 5:if(Bt(t,e),on(e),r&512&&n!==null&&zo(n,n.return),e.flags&32){var o=e.stateNode;try{ds(o,"")}catch(v){Oe(e,e.return,v)}}if(r&4&&(o=e.stateNode,o!=null)){var i=e.memoizedProps,s=n!==null?n.memoizedProps:i,a=e.type,l=e.updateQueue;if(e.updateQueue=null,l!==null)try{a==="input"&&i.type==="radio"&&i.name!=null&&fm(o,i),Sc(a,s);var u=Sc(a,i);for(s=0;s<l.length;s+=2){var c=l[s],d=l[s+1];c==="style"?vm(o,d):c==="dangerouslySetInnerHTML"?hm(o,d):c==="children"?ds(o,d):Vd(o,c,d,u)}switch(a){case"input":mc(o,i);break;case"textarea":pm(o,i);break;case"select":var f=o._wrapperState.wasMultiple;o._wrapperState.wasMultiple=!!i.multiple;var h=i.value;h!=null?Ho(o,!!i.multiple,h,!1):f!==!!i.multiple&&(i.defaultValue!=null?Ho(o,!!i.multiple,i.defaultValue,!0):Ho(o,!!i.multiple,i.multiple?[]:"",!1))}o[xs]=i}catch(v){Oe(e,e.return,v)}}break;case 6:if(Bt(t,e),on(e),r&4){if(e.stateNode===null)throw Error(N(162));o=e.stateNode,i=e.memoizedProps;try{o.nodeValue=i}catch(v){Oe(e,e.return,v)}}break;case 3:if(Bt(t,e),on(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{hs(t.containerInfo)}catch(v){Oe(e,e.return,v)}break;case 4:Bt(t,e),on(e);break;case 13:Bt(t,e),on(e),o=e.child,o.flags&8192&&(i=o.memoizedState!==null,o.stateNode.isHidden=i,!i||o.alternate!==null&&o.alternate.memoizedState!==null||(Sf=Le())),r&4&&wg(e);break;case 22:if(c=n!==null&&n.memoizedState!==null,e.mode&1?(nt=(u=nt)||c,Bt(t,e),nt=u):Bt(t,e),on(e),r&8192){if(u=e.memoizedState!==null,(e.stateNode.isHidden=u)&&!c&&e.mode&1)for(W=e,c=e.child;c!==null;){for(d=W=c;W!==null;){switch(f=W,h=f.child,f.tag){case 0:case 11:case 14:case 15:as(4,f,f.return);break;case 1:zo(f,f.return);var y=f.stateNode;if(typeof y.componentWillUnmount=="function"){r=f,n=f.return;try{t=r,y.props=t.memoizedProps,y.state=t.memoizedState,y.componentWillUnmount()}catch(v){Oe(r,n,v)}}break;case 5:zo(f,f.return);break;case 22:if(f.memoizedState!==null){Sg(d);continue}}h!==null?(h.return=f,W=h):Sg(d)}c=c.sibling}e:for(c=null,d=e;;){if(d.tag===5){if(c===null){c=d;try{o=d.stateNode,u?(i=o.style,typeof i.setProperty=="function"?i.setProperty("display","none","important"):i.display="none"):(a=d.stateNode,l=d.memoizedProps.style,s=l!=null&&l.hasOwnProperty("display")?l.display:null,a.style.display=mm("display",s))}catch(v){Oe(e,e.return,v)}}}else if(d.tag===6){if(c===null)try{d.stateNode.nodeValue=u?"":d.memoizedProps}catch(v){Oe(e,e.return,v)}}else if((d.tag!==22&&d.tag!==23||d.memoizedState===null||d===e)&&d.child!==null){d.child.return=d,d=d.child;continue}if(d===e)break e;for(;d.sibling===null;){if(d.return===null||d.return===e)break e;c===d&&(c=null),d=d.return}c===d&&(c=null),d.sibling.return=d.return,d=d.sibling}}break;case 19:Bt(t,e),on(e),r&4&&wg(e);break;case 21:break;default:Bt(t,e),on(e)}}function on(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(U0(n)){var r=n;break e}n=n.return}throw Error(N(160))}switch(r.tag){case 5:var o=r.stateNode;r.flags&32&&(ds(o,""),r.flags&=-33);var i=yg(e);Xc(e,i,o);break;case 3:case 4:var s=r.stateNode.containerInfo,a=yg(e);qc(e,a,s);break;default:throw Error(N(161))}}catch(l){Oe(e,e.return,l)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function wx(e,t,n){W=e,G0(e)}function G0(e,t,n){for(var r=(e.mode&1)!==0;W!==null;){var o=W,i=o.child;if(o.tag===22&&r){var s=o.memoizedState!==null||ma;if(!s){var a=o.alternate,l=a!==null&&a.memoizedState!==null||nt;a=ma;var u=nt;if(ma=s,(nt=l)&&!u)for(W=o;W!==null;)s=W,l=s.child,s.tag===22&&s.memoizedState!==null?Cg(o):l!==null?(l.return=s,W=l):Cg(o);for(;i!==null;)W=i,G0(i),i=i.sibling;W=o,ma=a,nt=u}xg(e)}else o.subtreeFlags&8772&&i!==null?(i.return=o,W=i):xg(e)}}function xg(e){for(;W!==null;){var t=W;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:nt||ql(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!nt)if(n===null)r.componentDidMount();else{var o=t.elementType===t.type?n.memoizedProps:Kt(t.type,n.memoizedProps);r.componentDidUpdate(o,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var i=t.updateQueue;i!==null&&rg(t,i,r);break;case 3:var s=t.updateQueue;if(s!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}rg(t,s,n)}break;case 5:var a=t.stateNode;if(n===null&&t.flags&4){n=a;var l=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":l.autoFocus&&n.focus();break;case"img":l.src&&(n.src=l.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var u=t.alternate;if(u!==null){var c=u.memoizedState;if(c!==null){var d=c.dehydrated;d!==null&&hs(d)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(N(163))}nt||t.flags&512&&Yc(t)}catch(f){Oe(t,t.return,f)}}if(t===e){W=null;break}if(n=t.sibling,n!==null){n.return=t.return,W=n;break}W=t.return}}function Sg(e){for(;W!==null;){var t=W;if(t===e){W=null;break}var n=t.sibling;if(n!==null){n.return=t.return,W=n;break}W=t.return}}function Cg(e){for(;W!==null;){var t=W;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{ql(4,t)}catch(l){Oe(t,n,l)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var o=t.return;try{r.componentDidMount()}catch(l){Oe(t,o,l)}}var i=t.return;try{Yc(t)}catch(l){Oe(t,i,l)}break;case 5:var s=t.return;try{Yc(t)}catch(l){Oe(t,s,l)}}}catch(l){Oe(t,t.return,l)}if(t===e){W=null;break}var a=t.sibling;if(a!==null){a.return=t.return,W=a;break}W=t.return}}var xx=Math.ceil,yl=Kn.ReactCurrentDispatcher,wf=Kn.ReactCurrentOwner,zt=Kn.ReactCurrentBatchConfig,fe=0,Ke=null,je=null,Xe=0,St=0,Vo=Ar(0),Ue=0,Rs=null,ao=0,Xl=0,xf=0,ls=null,ft=null,Sf=0,yi=1/0,Nn=null,wl=!1,Zc=null,$r=null,va=!1,vr=null,xl=0,us=0,Jc=null,Ba=-1,Wa=0;function st(){return fe&6?Le():Ba!==-1?Ba:Ba=Le()}function Er(e){return e.mode&1?fe&2&&Xe!==0?Xe&-Xe:rx.transition!==null?(Wa===0&&(Wa=km()),Wa):(e=me,e!==0||(e=window.event,e=e===void 0?16:Lm(e.type)),e):1}function Zt(e,t,n,r){if(50<us)throw us=0,Jc=null,Error(N(185));Qs(e,n,r),(!(fe&2)||e!==Ke)&&(e===Ke&&(!(fe&2)&&(Xl|=n),Ue===4&&ar(e,Xe)),mt(e,r),n===1&&fe===0&&!(t.mode&1)&&(yi=Le()+500,Kl&&Mr()))}function mt(e,t){var n=e.callbackNode;rw(e,t);var r=rl(e,e===Ke?Xe:0);if(r===0)n!==null&&Op(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&Op(n),t===1)e.tag===0?nx(bg.bind(null,e)):t0(bg.bind(null,e)),Zw(function(){!(fe&6)&&Mr()}),n=null;else{switch(Nm(r)){case 1:n=Gd;break;case 4:n=Pm;break;case 16:n=nl;break;case 536870912:n=_m;break;default:n=nl}n=ev(n,K0.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function K0(e,t){if(Ba=-1,Wa=0,fe&6)throw Error(N(327));var n=e.callbackNode;if(Ko()&&e.callbackNode!==n)return null;var r=rl(e,e===Ke?Xe:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=Sl(e,r);else{t=r;var o=fe;fe|=2;var i=Y0();(Ke!==e||Xe!==t)&&(Nn=null,yi=Le()+500,to(e,t));do try{bx();break}catch(a){Q0(e,a)}while(!0);sf(),yl.current=i,fe=o,je!==null?t=0:(Ke=null,Xe=0,t=Ue)}if(t!==0){if(t===2&&(o=Rc(e),o!==0&&(r=o,t=ed(e,o))),t===1)throw n=Rs,to(e,0),ar(e,r),mt(e,Le()),n;if(t===6)ar(e,r);else{if(o=e.current.alternate,!(r&30)&&!Sx(o)&&(t=Sl(e,r),t===2&&(i=Rc(e),i!==0&&(r=i,t=ed(e,i))),t===1))throw n=Rs,to(e,0),ar(e,r),mt(e,Le()),n;switch(e.finishedWork=o,e.finishedLanes=r,t){case 0:case 1:throw Error(N(345));case 2:Fr(e,ft,Nn);break;case 3:if(ar(e,r),(r&130023424)===r&&(t=Sf+500-Le(),10<t)){if(rl(e,0)!==0)break;if(o=e.suspendedLanes,(o&r)!==r){st(),e.pingedLanes|=e.suspendedLanes&o;break}e.timeoutHandle=Mc(Fr.bind(null,e,ft,Nn),t);break}Fr(e,ft,Nn);break;case 4:if(ar(e,r),(r&4194240)===r)break;for(t=e.eventTimes,o=-1;0<r;){var s=31-Xt(r);i=1<<s,s=t[s],s>o&&(o=s),r&=~i}if(r=o,r=Le()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*xx(r/1960))-r,10<r){e.timeoutHandle=Mc(Fr.bind(null,e,ft,Nn),r);break}Fr(e,ft,Nn);break;case 5:Fr(e,ft,Nn);break;default:throw Error(N(329))}}}return mt(e,Le()),e.callbackNode===n?K0.bind(null,e):null}function ed(e,t){var n=ls;return e.current.memoizedState.isDehydrated&&(to(e,t).flags|=256),e=Sl(e,t),e!==2&&(t=ft,ft=n,t!==null&&td(t)),e}function td(e){ft===null?ft=e:ft.push.apply(ft,e)}function Sx(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var o=n[r],i=o.getSnapshot;o=o.value;try{if(!en(i(),o))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function ar(e,t){for(t&=~xf,t&=~Xl,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-Xt(t),r=1<<n;e[n]=-1,t&=~r}}function bg(e){if(fe&6)throw Error(N(327));Ko();var t=rl(e,0);if(!(t&1))return mt(e,Le()),null;var n=Sl(e,t);if(e.tag!==0&&n===2){var r=Rc(e);r!==0&&(t=r,n=ed(e,r))}if(n===1)throw n=Rs,to(e,0),ar(e,t),mt(e,Le()),n;if(n===6)throw Error(N(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,Fr(e,ft,Nn),mt(e,Le()),null}function Cf(e,t){var n=fe;fe|=1;try{return e(t)}finally{fe=n,fe===0&&(yi=Le()+500,Kl&&Mr())}}function lo(e){vr!==null&&vr.tag===0&&!(fe&6)&&Ko();var t=fe;fe|=1;var n=zt.transition,r=me;try{if(zt.transition=null,me=1,e)return e()}finally{me=r,zt.transition=n,fe=t,!(fe&6)&&Mr()}}function bf(){St=Vo.current,be(Vo)}function to(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,Xw(n)),je!==null)for(n=je.return;n!==null;){var r=n;switch(nf(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&ll();break;case 3:mi(),be(gt),be(rt),ff();break;case 5:df(r);break;case 4:mi();break;case 13:be(_e);break;case 19:be(_e);break;case 10:af(r.type._context);break;case 22:case 23:bf()}n=n.return}if(Ke=e,je=e=Rr(e.current,null),Xe=St=t,Ue=0,Rs=null,xf=Xl=ao=0,ft=ls=null,Ur!==null){for(t=0;t<Ur.length;t++)if(n=Ur[t],r=n.interleaved,r!==null){n.interleaved=null;var o=r.next,i=n.pending;if(i!==null){var s=i.next;i.next=o,r.next=s}n.pending=r}Ur=null}return e}function Q0(e,t){do{var n=je;try{if(sf(),Va.current=vl,ml){for(var r=ke.memoizedState;r!==null;){var o=r.queue;o!==null&&(o.pending=null),r=r.next}ml=!1}if(so=0,Ge=He=ke=null,ss=!1,bs=0,wf.current=null,n===null||n.return===null){Ue=1,Rs=t,je=null;break}e:{var i=e,s=n.return,a=n,l=t;if(t=Xe,a.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){var u=l,c=a,d=c.tag;if(!(c.mode&1)&&(d===0||d===11||d===15)){var f=c.alternate;f?(c.updateQueue=f.updateQueue,c.memoizedState=f.memoizedState,c.lanes=f.lanes):(c.updateQueue=null,c.memoizedState=null)}var h=cg(s);if(h!==null){h.flags&=-257,dg(h,s,a,i,t),h.mode&1&&ug(i,u,t),t=h,l=u;var y=t.updateQueue;if(y===null){var v=new Set;v.add(l),t.updateQueue=v}else y.add(l);break e}else{if(!(t&1)){ug(i,u,t),$f();break e}l=Error(N(426))}}else if($e&&a.mode&1){var x=cg(s);if(x!==null){!(x.flags&65536)&&(x.flags|=256),dg(x,s,a,i,t),rf(vi(l,a));break e}}i=l=vi(l,a),Ue!==4&&(Ue=2),ls===null?ls=[i]:ls.push(i),i=s;do{switch(i.tag){case 3:i.flags|=65536,t&=-t,i.lanes|=t;var m=O0(i,l,t);ng(i,m);break e;case 1:a=l;var g=i.type,w=i.stateNode;if(!(i.flags&128)&&(typeof g.getDerivedStateFromError=="function"||w!==null&&typeof w.componentDidCatch=="function"&&($r===null||!$r.has(w)))){i.flags|=65536,t&=-t,i.lanes|=t;var C=T0(i,a,t);ng(i,C);break e}}i=i.return}while(i!==null)}X0(n)}catch(E){t=E,je===n&&n!==null&&(je=n=n.return);continue}break}while(!0)}function Y0(){var e=yl.current;return yl.current=vl,e===null?vl:e}function $f(){(Ue===0||Ue===3||Ue===2)&&(Ue=4),Ke===null||!(ao&268435455)&&!(Xl&268435455)||ar(Ke,Xe)}function Sl(e,t){var n=fe;fe|=2;var r=Y0();(Ke!==e||Xe!==t)&&(Nn=null,to(e,t));do try{Cx();break}catch(o){Q0(e,o)}while(!0);if(sf(),fe=n,yl.current=r,je!==null)throw Error(N(261));return Ke=null,Xe=0,Ue}function Cx(){for(;je!==null;)q0(je)}function bx(){for(;je!==null&&!Q1();)q0(je)}function q0(e){var t=J0(e.alternate,e,St);e.memoizedProps=e.pendingProps,t===null?X0(e):je=t,wf.current=null}function X0(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=mx(n,t),n!==null){n.flags&=32767,je=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{Ue=6,je=null;return}}else if(n=hx(n,t,St),n!==null){je=n;return}if(t=t.sibling,t!==null){je=t;return}je=t=e}while(t!==null);Ue===0&&(Ue=5)}function Fr(e,t,n){var r=me,o=zt.transition;try{zt.transition=null,me=1,$x(e,t,n,r)}finally{zt.transition=o,me=r}return null}function $x(e,t,n,r){do Ko();while(vr!==null);if(fe&6)throw Error(N(327));n=e.finishedWork;var o=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(N(177));e.callbackNode=null,e.callbackPriority=0;var i=n.lanes|n.childLanes;if(ow(e,i),e===Ke&&(je=Ke=null,Xe=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||va||(va=!0,ev(nl,function(){return Ko(),null})),i=(n.flags&15990)!==0,n.subtreeFlags&15990||i){i=zt.transition,zt.transition=null;var s=me;me=1;var a=fe;fe|=4,wf.current=null,yx(e,n),W0(n,e),Bw(Tc),ol=!!Oc,Tc=Oc=null,e.current=n,wx(n),Y1(),fe=a,me=s,zt.transition=i}else e.current=n;if(va&&(va=!1,vr=e,xl=o),i=e.pendingLanes,i===0&&($r=null),Z1(n.stateNode),mt(e,Le()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)o=t[n],r(o.value,{componentStack:o.stack,digest:o.digest});if(wl)throw wl=!1,e=Zc,Zc=null,e;return xl&1&&e.tag!==0&&Ko(),i=e.pendingLanes,i&1?e===Jc?us++:(us=0,Jc=e):us=0,Mr(),null}function Ko(){if(vr!==null){var e=Nm(xl),t=zt.transition,n=me;try{if(zt.transition=null,me=16>e?16:e,vr===null)var r=!1;else{if(e=vr,vr=null,xl=0,fe&6)throw Error(N(331));var o=fe;for(fe|=4,W=e.current;W!==null;){var i=W,s=i.child;if(W.flags&16){var a=i.deletions;if(a!==null){for(var l=0;l<a.length;l++){var u=a[l];for(W=u;W!==null;){var c=W;switch(c.tag){case 0:case 11:case 15:as(8,c,i)}var d=c.child;if(d!==null)d.return=c,W=d;else for(;W!==null;){c=W;var f=c.sibling,h=c.return;if(H0(c),c===u){W=null;break}if(f!==null){f.return=h,W=f;break}W=h}}}var y=i.alternate;if(y!==null){var v=y.child;if(v!==null){y.child=null;do{var x=v.sibling;v.sibling=null,v=x}while(v!==null)}}W=i}}if(i.subtreeFlags&2064&&s!==null)s.return=i,W=s;else e:for(;W!==null;){if(i=W,i.flags&2048)switch(i.tag){case 0:case 11:case 15:as(9,i,i.return)}var m=i.sibling;if(m!==null){m.return=i.return,W=m;break e}W=i.return}}var g=e.current;for(W=g;W!==null;){s=W;var w=s.child;if(s.subtreeFlags&2064&&w!==null)w.return=s,W=w;else e:for(s=g;W!==null;){if(a=W,a.flags&2048)try{switch(a.tag){case 0:case 11:case 15:ql(9,a)}}catch(E){Oe(a,a.return,E)}if(a===s){W=null;break e}var C=a.sibling;if(C!==null){C.return=a.return,W=C;break e}W=a.return}}if(fe=o,Mr(),mn&&typeof mn.onPostCommitFiberRoot=="function")try{mn.onPostCommitFiberRoot(Hl,e)}catch{}r=!0}return r}finally{me=n,zt.transition=t}}return!1}function $g(e,t,n){t=vi(n,t),t=O0(e,t,1),e=br(e,t,1),t=st(),e!==null&&(Qs(e,1,t),mt(e,t))}function Oe(e,t,n){if(e.tag===3)$g(e,e,n);else for(;t!==null;){if(t.tag===3){$g(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&($r===null||!$r.has(r))){e=vi(n,e),e=T0(t,e,1),t=br(t,e,1),e=st(),t!==null&&(Qs(t,1,e),mt(t,e));break}}t=t.return}}function Ex(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=st(),e.pingedLanes|=e.suspendedLanes&n,Ke===e&&(Xe&n)===n&&(Ue===4||Ue===3&&(Xe&130023424)===Xe&&500>Le()-Sf?to(e,0):xf|=n),mt(e,t)}function Z0(e,t){t===0&&(e.mode&1?(t=aa,aa<<=1,!(aa&130023424)&&(aa=4194304)):t=1);var n=st();e=Un(e,t),e!==null&&(Qs(e,t,n),mt(e,n))}function Rx(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),Z0(e,n)}function Px(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,o=e.memoizedState;o!==null&&(n=o.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(N(314))}r!==null&&r.delete(t),Z0(e,n)}var J0;J0=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||gt.current)pt=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return pt=!1,gx(e,t,n);pt=!!(e.flags&131072)}else pt=!1,$e&&t.flags&1048576&&n0(t,dl,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Ua(e,t),e=t.pendingProps;var o=pi(t,rt.current);Go(t,n),o=gf(null,t,r,e,o,n);var i=hf();return t.flags|=1,typeof o=="object"&&o!==null&&typeof o.render=="function"&&o.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,ht(r)?(i=!0,ul(t)):i=!1,t.memoizedState=o.state!==null&&o.state!==void 0?o.state:null,uf(t),o.updater=Ql,t.stateNode=o,o._reactInternals=t,Vc(t,r,e,n),t=Bc(null,t,r,!0,i,n)):(t.tag=0,$e&&i&&tf(t),it(null,t,o,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Ua(e,t),e=t.pendingProps,o=r._init,r=o(r._payload),t.type=r,o=t.tag=kx(r),e=Kt(r,e),o){case 0:t=Uc(null,t,r,e,n);break e;case 1:t=gg(null,t,r,e,n);break e;case 11:t=fg(null,t,r,e,n);break e;case 14:t=pg(null,t,r,Kt(r.type,e),n);break e}throw Error(N(306,r,""))}return t;case 0:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:Kt(r,o),Uc(e,t,r,o,n);case 1:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:Kt(r,o),gg(e,t,r,o,n);case 3:e:{if(L0(t),e===null)throw Error(N(387));r=t.pendingProps,i=t.memoizedState,o=i.element,s0(e,t),gl(t,r,null,n);var s=t.memoizedState;if(r=s.element,i.isDehydrated)if(i={element:r,isDehydrated:!1,cache:s.cache,pendingSuspenseBoundaries:s.pendingSuspenseBoundaries,transitions:s.transitions},t.updateQueue.baseState=i,t.memoizedState=i,t.flags&256){o=vi(Error(N(423)),t),t=hg(e,t,r,n,o);break e}else if(r!==o){o=vi(Error(N(424)),t),t=hg(e,t,r,n,o);break e}else for(Ct=Cr(t.stateNode.containerInfo.firstChild),$t=t,$e=!0,qt=null,n=c0(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(gi(),r===o){t=Bn(e,t,n);break e}it(e,t,r,n)}t=t.child}return t;case 5:return d0(t),e===null&&Fc(t),r=t.type,o=t.pendingProps,i=e!==null?e.memoizedProps:null,s=o.children,Ac(r,o)?s=null:i!==null&&Ac(r,i)&&(t.flags|=32),D0(e,t),it(e,t,s,n),t.child;case 6:return e===null&&Fc(t),null;case 13:return I0(e,t,n);case 4:return cf(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=hi(t,null,r,n):it(e,t,r,n),t.child;case 11:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:Kt(r,o),fg(e,t,r,o,n);case 7:return it(e,t,t.pendingProps,n),t.child;case 8:return it(e,t,t.pendingProps.children,n),t.child;case 12:return it(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,o=t.pendingProps,i=t.memoizedProps,s=o.value,we(fl,r._currentValue),r._currentValue=s,i!==null)if(en(i.value,s)){if(i.children===o.children&&!gt.current){t=Bn(e,t,n);break e}}else for(i=t.child,i!==null&&(i.return=t);i!==null;){var a=i.dependencies;if(a!==null){s=i.child;for(var l=a.firstContext;l!==null;){if(l.context===r){if(i.tag===1){l=In(-1,n&-n),l.tag=2;var u=i.updateQueue;if(u!==null){u=u.shared;var c=u.pending;c===null?l.next=l:(l.next=c.next,c.next=l),u.pending=l}}i.lanes|=n,l=i.alternate,l!==null&&(l.lanes|=n),jc(i.return,n,t),a.lanes|=n;break}l=l.next}}else if(i.tag===10)s=i.type===t.type?null:i.child;else if(i.tag===18){if(s=i.return,s===null)throw Error(N(341));s.lanes|=n,a=s.alternate,a!==null&&(a.lanes|=n),jc(s,n,t),s=i.sibling}else s=i.child;if(s!==null)s.return=i;else for(s=i;s!==null;){if(s===t){s=null;break}if(i=s.sibling,i!==null){i.return=s.return,s=i;break}s=s.return}i=s}it(e,t,o.children,n),t=t.child}return t;case 9:return o=t.type,r=t.pendingProps.children,Go(t,n),o=Vt(o),r=r(o),t.flags|=1,it(e,t,r,n),t.child;case 14:return r=t.type,o=Kt(r,t.pendingProps),o=Kt(r.type,o),pg(e,t,r,o,n);case 15:return A0(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:Kt(r,o),Ua(e,t),t.tag=1,ht(r)?(e=!0,ul(t)):e=!1,Go(t,n),l0(t,r,o),Vc(t,r,o,n),Bc(null,t,r,!0,e,n);case 19:return F0(e,t,n);case 22:return M0(e,t,n)}throw Error(N(156,t.tag))};function ev(e,t){return Rm(e,t)}function _x(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function jt(e,t,n,r){return new _x(e,t,n,r)}function Ef(e){return e=e.prototype,!(!e||!e.isReactComponent)}function kx(e){if(typeof e=="function")return Ef(e)?1:0;if(e!=null){if(e=e.$$typeof,e===Ud)return 11;if(e===Bd)return 14}return 2}function Rr(e,t){var n=e.alternate;return n===null?(n=jt(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Ga(e,t,n,r,o,i){var s=2;if(r=e,typeof e=="function")Ef(e)&&(s=1);else if(typeof e=="string")s=5;else e:switch(e){case Oo:return no(n.children,o,i,t);case Hd:s=8,o|=8;break;case dc:return e=jt(12,n,t,o|2),e.elementType=dc,e.lanes=i,e;case fc:return e=jt(13,n,t,o),e.elementType=fc,e.lanes=i,e;case pc:return e=jt(19,n,t,o),e.elementType=pc,e.lanes=i,e;case um:return Zl(n,o,i,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case am:s=10;break e;case lm:s=9;break e;case Ud:s=11;break e;case Bd:s=14;break e;case or:s=16,r=null;break e}throw Error(N(130,e==null?e:typeof e,""))}return t=jt(s,n,t,o),t.elementType=e,t.type=r,t.lanes=i,t}function no(e,t,n,r){return e=jt(7,e,r,t),e.lanes=n,e}function Zl(e,t,n,r){return e=jt(22,e,r,t),e.elementType=um,e.lanes=n,e.stateNode={isHidden:!1},e}function zu(e,t,n){return e=jt(6,e,null,t),e.lanes=n,e}function Vu(e,t,n){return t=jt(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Nx(e,t,n,r,o){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Su(0),this.expirationTimes=Su(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Su(0),this.identifierPrefix=r,this.onRecoverableError=o,this.mutableSourceEagerHydrationData=null}function Rf(e,t,n,r,o,i,s,a,l){return e=new Nx(e,t,n,a,l),t===1?(t=1,i===!0&&(t|=8)):t=0,i=jt(3,null,null,t),e.current=i,i.stateNode=e,i.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},uf(i),e}function Ox(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:No,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function tv(e){if(!e)return _r;e=e._reactInternals;e:{if(go(e)!==e||e.tag!==1)throw Error(N(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(ht(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(N(171))}if(e.tag===1){var n=e.type;if(ht(n))return e0(e,n,t)}return t}function nv(e,t,n,r,o,i,s,a,l){return e=Rf(n,r,!0,e,o,i,s,a,l),e.context=tv(null),n=e.current,r=st(),o=Er(n),i=In(r,o),i.callback=t??null,br(n,i,o),e.current.lanes=o,Qs(e,o,r),mt(e,r),e}function Jl(e,t,n,r){var o=t.current,i=st(),s=Er(o);return n=tv(n),t.context===null?t.context=n:t.pendingContext=n,t=In(i,s),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=br(o,t,s),e!==null&&(Zt(e,o,s,i),za(e,o,s)),s}function Cl(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function Eg(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function Pf(e,t){Eg(e,t),(e=e.alternate)&&Eg(e,t)}function Tx(){return null}var rv=typeof reportError=="function"?reportError:function(e){console.error(e)};function _f(e){this._internalRoot=e}eu.prototype.render=_f.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(N(409));Jl(e,t,null,null)};eu.prototype.unmount=_f.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;lo(function(){Jl(null,e,null,null)}),t[Hn]=null}};function eu(e){this._internalRoot=e}eu.prototype.unstable_scheduleHydration=function(e){if(e){var t=Am();e={blockedOn:null,target:e,priority:t};for(var n=0;n<sr.length&&t!==0&&t<sr[n].priority;n++);sr.splice(n,0,e),n===0&&Dm(e)}};function kf(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function tu(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function Rg(){}function Ax(e,t,n,r,o){if(o){if(typeof r=="function"){var i=r;r=function(){var u=Cl(s);i.call(u)}}var s=nv(t,r,e,0,null,!1,!1,"",Rg);return e._reactRootContainer=s,e[Hn]=s.current,ys(e.nodeType===8?e.parentNode:e),lo(),s}for(;o=e.lastChild;)e.removeChild(o);if(typeof r=="function"){var a=r;r=function(){var u=Cl(l);a.call(u)}}var l=Rf(e,0,!1,null,null,!1,!1,"",Rg);return e._reactRootContainer=l,e[Hn]=l.current,ys(e.nodeType===8?e.parentNode:e),lo(function(){Jl(t,l,n,r)}),l}function nu(e,t,n,r,o){var i=n._reactRootContainer;if(i){var s=i;if(typeof o=="function"){var a=o;o=function(){var l=Cl(s);a.call(l)}}Jl(t,s,e,o)}else s=Ax(n,t,e,o,r);return Cl(s)}Om=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=Zi(t.pendingLanes);n!==0&&(Kd(t,n|1),mt(t,Le()),!(fe&6)&&(yi=Le()+500,Mr()))}break;case 13:lo(function(){var r=Un(e,1);if(r!==null){var o=st();Zt(r,e,1,o)}}),Pf(e,1)}};Qd=function(e){if(e.tag===13){var t=Un(e,134217728);if(t!==null){var n=st();Zt(t,e,134217728,n)}Pf(e,134217728)}};Tm=function(e){if(e.tag===13){var t=Er(e),n=Un(e,t);if(n!==null){var r=st();Zt(n,e,t,r)}Pf(e,t)}};Am=function(){return me};Mm=function(e,t){var n=me;try{return me=e,t()}finally{me=n}};bc=function(e,t,n){switch(t){case"input":if(mc(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var o=Gl(r);if(!o)throw Error(N(90));dm(r),mc(r,o)}}}break;case"textarea":pm(e,n);break;case"select":t=n.value,t!=null&&Ho(e,!!n.multiple,t,!1)}};xm=Cf;Sm=lo;var Mx={usingClientEntryPoint:!1,Events:[qs,Do,Gl,ym,wm,Cf]},Hi={findFiberByHostInstance:Hr,bundleType:0,version:"18.2.0",rendererPackageName:"react-dom"},Dx={bundleType:Hi.bundleType,version:Hi.version,rendererPackageName:Hi.rendererPackageName,rendererConfig:Hi.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:Kn.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=$m(e),e===null?null:e.stateNode},findFiberByHostInstance:Hi.findFiberByHostInstance||Tx,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.2.0-next-9e3b772b8-20220608"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var ya=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!ya.isDisabled&&ya.supportsFiber)try{Hl=ya.inject(Dx),mn=ya}catch{}}_t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Mx;_t.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!kf(t))throw Error(N(200));return Ox(e,t,null,n)};_t.createRoot=function(e,t){if(!kf(e))throw Error(N(299));var n=!1,r="",o=rv;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(o=t.onRecoverableError)),t=Rf(e,1,!1,null,null,n,!1,r,o),e[Hn]=t.current,ys(e.nodeType===8?e.parentNode:e),new _f(t)};_t.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(N(188)):(e=Object.keys(e).join(","),Error(N(268,e)));return e=$m(t),e=e===null?null:e.stateNode,e};_t.flushSync=function(e){return lo(e)};_t.hydrate=function(e,t,n){if(!tu(t))throw Error(N(200));return nu(null,e,t,!0,n)};_t.hydrateRoot=function(e,t,n){if(!kf(e))throw Error(N(405));var r=n!=null&&n.hydratedSources||null,o=!1,i="",s=rv;if(n!=null&&(n.unstable_strictMode===!0&&(o=!0),n.identifierPrefix!==void 0&&(i=n.identifierPrefix),n.onRecoverableError!==void 0&&(s=n.onRecoverableError)),t=nv(t,null,e,1,n??null,o,!1,i,s),e[Hn]=t.current,ys(e),r)for(e=0;e<r.length;e++)n=r[e],o=n._getVersion,o=o(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,o]:t.mutableSourceEagerHydrationData.push(n,o);return new eu(t)};_t.render=function(e,t,n){if(!tu(t))throw Error(N(200));return nu(null,e,t,!1,n)};_t.unmountComponentAtNode=function(e){if(!tu(e))throw Error(N(40));return e._reactRootContainer?(lo(function(){nu(null,null,e,!1,function(){e._reactRootContainer=null,e[Hn]=null})}),!0):!1};_t.unstable_batchedUpdates=Cf;_t.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!tu(n))throw Error(N(200));if(e==null||e._reactInternals===void 0)throw Error(N(38));return nu(e,t,n,!1,r)};_t.version="18.2.0-next-9e3b772b8-20220608";function ov(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(ov)}catch(e){console.error(e)}}ov(),nm.exports=_t;var ho=nm.exports;const iv=Wh(ho);var Pg=ho;uc.createRoot=Pg.createRoot,uc.hydrateRoot=Pg.hydrateRoot;var Ri=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(e){return this.listeners.add(e),this.onSubscribe(),()=>{this.listeners.delete(e),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},wi=typeof window>"u"||"Deno"in window;function Lt(){}function Lx(e,t){return typeof e=="function"?e(t):e}function nd(e){return typeof e=="number"&&e>=0&&e!==1/0}function sv(e,t){return Math.max(e+(t||0)-Date.now(),0)}function _g(e,t){const{type:n="all",exact:r,fetchStatus:o,predicate:i,queryKey:s,stale:a}=e;if(s){if(r){if(t.queryHash!==Nf(s,t.options))return!1}else if(!Ps(t.queryKey,s))return!1}if(n!=="all"){const l=t.isActive();if(n==="active"&&!l||n==="inactive"&&l)return!1}return!(typeof a=="boolean"&&t.isStale()!==a||typeof o<"u"&&o!==t.state.fetchStatus||i&&!i(t))}function kg(e,t){const{exact:n,status:r,predicate:o,mutationKey:i}=e;if(i){if(!t.options.mutationKey)return!1;if(n){if(uo(t.options.mutationKey)!==uo(i))return!1}else if(!Ps(t.options.mutationKey,i))return!1}return!(r&&t.state.status!==r||o&&!o(t))}function Nf(e,t){return((t==null?void 0:t.queryKeyHashFn)||uo)(e)}function uo(e){return JSON.stringify(e,(t,n)=>rd(n)?Object.keys(n).sort().reduce((r,o)=>(r[o]=n[o],r),{}):n)}function Ps(e,t){return e===t?!0:typeof e!=typeof t?!1:e&&t&&typeof e=="object"&&typeof t=="object"?!Object.keys(t).some(n=>!Ps(e[n],t[n])):!1}function Of(e,t){if(e===t)return e;const n=Ng(e)&&Ng(t);if(n||rd(e)&&rd(t)){const r=n?e.length:Object.keys(e).length,o=n?t:Object.keys(t),i=o.length,s=n?[]:{};let a=0;for(let l=0;l<i;l++){const u=n?l:o[l];s[u]=Of(e[u],t[u]),s[u]===e[u]&&a++}return r===i&&a===r?e:s}return t}function bl(e,t){if(e&&!t||t&&!e)return!1;for(const n in e)if(e[n]!==t[n])return!1;return!0}function Ng(e){return Array.isArray(e)&&e.length===Object.keys(e).length}function rd(e){if(!Og(e))return!1;const t=e.constructor;if(typeof t>"u")return!0;const n=t.prototype;return!(!Og(n)||!n.hasOwnProperty("isPrototypeOf"))}function Og(e){return Object.prototype.toString.call(e)==="[object Object]"}function av(e){return new Promise(t=>{setTimeout(t,e)})}function Tg(e){av(0).then(e)}function od(e,t,n){return typeof n.structuralSharing=="function"?n.structuralSharing(e,t):n.structuralSharing!==!1?Of(e,t):t}function Ix(e,t,n=0){const r=[...e,t];return n&&r.length>n?r.slice(1):r}function Fx(e,t,n=0){const r=[t,...e];return n&&r.length>n?r.slice(0,-1):r}var Gr,lr,qo,Dh,jx=(Dh=class extends Ri{constructor(){super();K(this,Gr,void 0);K(this,lr,void 0);K(this,qo,void 0);M(this,qo,t=>{if(!wi&&window.addEventListener){const n=()=>t();return window.addEventListener("visibilitychange",n,!1),()=>{window.removeEventListener("visibilitychange",n)}}})}onSubscribe(){$(this,lr)||this.setEventListener($(this,qo))}onUnsubscribe(){var t;this.hasListeners()||((t=$(this,lr))==null||t.call(this),M(this,lr,void 0))}setEventListener(t){var n;M(this,qo,t),(n=$(this,lr))==null||n.call(this),M(this,lr,t(r=>{typeof r=="boolean"?this.setFocused(r):this.onFocus()}))}setFocused(t){$(this,Gr)!==t&&(M(this,Gr,t),this.onFocus())}onFocus(){this.listeners.forEach(t=>{t()})}isFocused(){var t;return typeof $(this,Gr)=="boolean"?$(this,Gr):((t=globalThis.document)==null?void 0:t.visibilityState)!=="hidden"}},Gr=new WeakMap,lr=new WeakMap,qo=new WeakMap,Dh),$l=new jx,Xo,ur,Zo,Lh,zx=(Lh=class extends Ri{constructor(){super();K(this,Xo,!0);K(this,ur,void 0);K(this,Zo,void 0);M(this,Zo,t=>{if(!wi&&window.addEventListener){const n=()=>t(!0),r=()=>t(!1);return window.addEventListener("online",n,!1),window.addEventListener("offline",r,!1),()=>{window.removeEventListener("online",n),window.removeEventListener("offline",r)}}})}onSubscribe(){$(this,ur)||this.setEventListener($(this,Zo))}onUnsubscribe(){var t;this.hasListeners()||((t=$(this,ur))==null||t.call(this),M(this,ur,void 0))}setEventListener(t){var n;M(this,Zo,t),(n=$(this,ur))==null||n.call(this),M(this,ur,t(this.setOnline.bind(this)))}setOnline(t){$(this,Xo)!==t&&(M(this,Xo,t),this.listeners.forEach(r=>{r(t)}))}isOnline(){return $(this,Xo)}},Xo=new WeakMap,ur=new WeakMap,Zo=new WeakMap,Lh),El=new zx;function Vx(e){return Math.min(1e3*2**e,3e4)}function ru(e){return(e??"online")==="online"?El.isOnline():!0}var lv=class{constructor(e){this.revert=e==null?void 0:e.revert,this.silent=e==null?void 0:e.silent}};function Hu(e){return e instanceof lv}function uv(e){let t=!1,n=0,r=!1,o,i,s;const a=new Promise((x,m)=>{i=x,s=m}),l=x=>{var m;r||(h(new lv(x)),(m=e.abort)==null||m.call(e))},u=()=>{t=!0},c=()=>{t=!1},d=()=>!$l.isFocused()||e.networkMode!=="always"&&!El.isOnline(),f=x=>{var m;r||(r=!0,(m=e.onSuccess)==null||m.call(e,x),o==null||o(),i(x))},h=x=>{var m;r||(r=!0,(m=e.onError)==null||m.call(e,x),o==null||o(),s(x))},y=()=>new Promise(x=>{var m;o=g=>{const w=r||!d();return w&&x(g),w},(m=e.onPause)==null||m.call(e)}).then(()=>{var x;o=void 0,r||(x=e.onContinue)==null||x.call(e)}),v=()=>{if(r)return;let x;try{x=e.fn()}catch(m){x=Promise.reject(m)}Promise.resolve(x).then(f).catch(m=>{var _;if(r)return;const g=e.retry??(wi?0:3),w=e.retryDelay??Vx,C=typeof w=="function"?w(n,m):w,E=g===!0||typeof g=="number"&&n<g||typeof g=="function"&&g(n,m);if(t||!E){h(m);return}n++,(_=e.onFail)==null||_.call(e,n,m),av(C).then(()=>{if(d())return y()}).then(()=>{t?h(m):v()})})};return ru(e.networkMode)?v():y().then(v),{promise:a,cancel:l,continue:()=>(o==null?void 0:o())?a:Promise.resolve(),cancelRetry:u,continueRetry:c}}function Hx(){let e=[],t=0,n=c=>{c()},r=c=>{c()};const o=c=>{let d;t++;try{d=c()}finally{t--,t||a()}return d},i=c=>{t?e.push(c):Tg(()=>{n(c)})},s=c=>(...d)=>{i(()=>{c(...d)})},a=()=>{const c=e;e=[],c.length&&Tg(()=>{r(()=>{c.forEach(d=>{n(d)})})})};return{batch:o,batchCalls:s,schedule:i,setNotifyFunction:c=>{n=c},setBatchNotifyFunction:c=>{r=c}}}var Fe=Hx(),Kr,Ih,cv=(Ih=class{constructor(){K(this,Kr,void 0)}destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),nd(this.gcTime)&&M(this,Kr,setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(e){this.gcTime=Math.max(this.gcTime||0,e??(wi?1/0:5*60*1e3))}clearGcTimeout(){$(this,Kr)&&(clearTimeout($(this,Kr)),M(this,Kr,void 0))}},Kr=new WeakMap,Ih),Jo,ei,At,cr,Mt,We,Ds,Qr,ti,Ka,Qt,kn,Fh,Ux=(Fh=class extends cv{constructor(t){super();K(this,ti);K(this,Qt);K(this,Jo,void 0);K(this,ei,void 0);K(this,At,void 0);K(this,cr,void 0);K(this,Mt,void 0);K(this,We,void 0);K(this,Ds,void 0);K(this,Qr,void 0);M(this,Qr,!1),M(this,Ds,t.defaultOptions),re(this,ti,Ka).call(this,t.options),M(this,We,[]),M(this,At,t.cache),this.queryKey=t.queryKey,this.queryHash=t.queryHash,M(this,Jo,t.state||Bx(this.options)),this.state=$(this,Jo),this.scheduleGc()}get meta(){return this.options.meta}optionalRemove(){!$(this,We).length&&this.state.fetchStatus==="idle"&&$(this,At).remove(this)}setData(t,n){const r=od(this.state.data,t,this.options);return re(this,Qt,kn).call(this,{data:r,type:"success",dataUpdatedAt:n==null?void 0:n.updatedAt,manual:n==null?void 0:n.manual}),r}setState(t,n){re(this,Qt,kn).call(this,{type:"setState",state:t,setStateOptions:n})}cancel(t){var r;const n=$(this,cr);return(r=$(this,Mt))==null||r.cancel(t),n?n.then(Lt).catch(Lt):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState($(this,Jo))}isActive(){return $(this,We).some(t=>t.options.enabled!==!1)}isDisabled(){return this.getObserversCount()>0&&!this.isActive()}isStale(){return this.state.isInvalidated||!this.state.dataUpdatedAt||$(this,We).some(t=>t.getCurrentResult().isStale)}isStaleByTime(t=0){return this.state.isInvalidated||!this.state.dataUpdatedAt||!sv(this.state.dataUpdatedAt,t)}onFocus(){var n;const t=$(this,We).find(r=>r.shouldFetchOnWindowFocus());t==null||t.refetch({cancelRefetch:!1}),(n=$(this,Mt))==null||n.continue()}onOnline(){var n;const t=$(this,We).find(r=>r.shouldFetchOnReconnect());t==null||t.refetch({cancelRefetch:!1}),(n=$(this,Mt))==null||n.continue()}addObserver(t){$(this,We).includes(t)||($(this,We).push(t),this.clearGcTimeout(),$(this,At).notify({type:"observerAdded",query:this,observer:t}))}removeObserver(t){$(this,We).includes(t)&&(M(this,We,$(this,We).filter(n=>n!==t)),$(this,We).length||($(this,Mt)&&($(this,Qr)?$(this,Mt).cancel({revert:!0}):$(this,Mt).cancelRetry()),this.scheduleGc()),$(this,At).notify({type:"observerRemoved",query:this,observer:t}))}getObserversCount(){return $(this,We).length}invalidate(){this.state.isInvalidated||re(this,Qt,kn).call(this,{type:"invalidate"})}fetch(t,n){var u,c,d,f;if(this.state.fetchStatus!=="idle"){if(this.state.dataUpdatedAt&&(n!=null&&n.cancelRefetch))this.cancel({silent:!0});else if($(this,cr))return(u=$(this,Mt))==null||u.continueRetry(),$(this,cr)}if(t&&re(this,ti,Ka).call(this,t),!this.options.queryFn){const h=$(this,We).find(y=>y.options.queryFn);h&&re(this,ti,Ka).call(this,h.options)}const r=new AbortController,o={queryKey:this.queryKey,meta:this.meta},i=h=>{Object.defineProperty(h,"signal",{enumerable:!0,get:()=>(M(this,Qr,!0),r.signal)})};i(o);const s=()=>this.options.queryFn?(M(this,Qr,!1),this.options.persister?this.options.persister(this.options.queryFn,o,this):this.options.queryFn(o)):Promise.reject(new Error(`Missing queryFn: '${this.options.queryHash}'`)),a={fetchOptions:n,options:this.options,queryKey:this.queryKey,state:this.state,fetchFn:s};i(a),(c=this.options.behavior)==null||c.onFetch(a,this),M(this,ei,this.state),(this.state.fetchStatus==="idle"||this.state.fetchMeta!==((d=a.fetchOptions)==null?void 0:d.meta))&&re(this,Qt,kn).call(this,{type:"fetch",meta:(f=a.fetchOptions)==null?void 0:f.meta});const l=h=>{var y,v,x,m;Hu(h)&&h.silent||re(this,Qt,kn).call(this,{type:"error",error:h}),Hu(h)||((v=(y=$(this,At).config).onError)==null||v.call(y,h,this),(m=(x=$(this,At).config).onSettled)==null||m.call(x,this.state.data,h,this)),this.isFetchingOptimistic||this.scheduleGc(),this.isFetchingOptimistic=!1};return M(this,Mt,uv({fn:a.fetchFn,abort:r.abort.bind(r),onSuccess:h=>{var y,v,x,m;if(typeof h>"u"){l(new Error(`${this.queryHash} data is undefined`));return}this.setData(h),(v=(y=$(this,At).config).onSuccess)==null||v.call(y,h,this),(m=(x=$(this,At).config).onSettled)==null||m.call(x,h,this.state.error,this),this.isFetchingOptimistic||this.scheduleGc(),this.isFetchingOptimistic=!1},onError:l,onFail:(h,y)=>{re(this,Qt,kn).call(this,{type:"failed",failureCount:h,error:y})},onPause:()=>{re(this,Qt,kn).call(this,{type:"pause"})},onContinue:()=>{re(this,Qt,kn).call(this,{type:"continue"})},retry:a.options.retry,retryDelay:a.options.retryDelay,networkMode:a.options.networkMode})),M(this,cr,$(this,Mt).promise),$(this,cr)}},Jo=new WeakMap,ei=new WeakMap,At=new WeakMap,cr=new WeakMap,Mt=new WeakMap,We=new WeakMap,Ds=new WeakMap,Qr=new WeakMap,ti=new WeakSet,Ka=function(t){this.options={...$(this,Ds),...t},this.updateGcTime(this.options.gcTime)},Qt=new WeakSet,kn=function(t){const n=r=>{switch(t.type){case"failed":return{...r,fetchFailureCount:t.failureCount,fetchFailureReason:t.error};case"pause":return{...r,fetchStatus:"paused"};case"continue":return{...r,fetchStatus:"fetching"};case"fetch":return{...r,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:t.meta??null,fetchStatus:ru(this.options.networkMode)?"fetching":"paused",...!r.dataUpdatedAt&&{error:null,status:"pending"}};case"success":return{...r,data:t.data,dataUpdateCount:r.dataUpdateCount+1,dataUpdatedAt:t.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!t.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":const o=t.error;return Hu(o)&&o.revert&&$(this,ei)?{...$(this,ei),fetchStatus:"idle"}:{...r,error:o,errorUpdateCount:r.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:r.fetchFailureCount+1,fetchFailureReason:o,fetchStatus:"idle",status:"error"};case"invalidate":return{...r,isInvalidated:!0};case"setState":return{...r,...t.state}}};this.state=n(this.state),Fe.batch(()=>{$(this,We).forEach(r=>{r.onQueryUpdate()}),$(this,At).notify({query:this,type:"updated",action:t})})},Fh);function Bx(e){const t=typeof e.initialData=="function"?e.initialData():e.initialData,n=typeof t<"u",r=n?typeof e.initialDataUpdatedAt=="function"?e.initialDataUpdatedAt():e.initialDataUpdatedAt:0;return{data:t,dataUpdateCount:0,dataUpdatedAt:n?r??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:n?"success":"pending",fetchStatus:"idle"}}var un,jh,Wx=(jh=class extends Ri{constructor(t={}){super();K(this,un,void 0);this.config=t,M(this,un,new Map)}build(t,n,r){const o=n.queryKey,i=n.queryHash??Nf(o,n);let s=this.get(i);return s||(s=new Ux({cache:this,queryKey:o,queryHash:i,options:t.defaultQueryOptions(n),state:r,defaultOptions:t.getQueryDefaults(o)}),this.add(s)),s}add(t){$(this,un).has(t.queryHash)||($(this,un).set(t.queryHash,t),this.notify({type:"added",query:t}))}remove(t){const n=$(this,un).get(t.queryHash);n&&(t.destroy(),n===t&&$(this,un).delete(t.queryHash),this.notify({type:"removed",query:t}))}clear(){Fe.batch(()=>{this.getAll().forEach(t=>{this.remove(t)})})}get(t){return $(this,un).get(t)}getAll(){return[...$(this,un).values()]}find(t){const n={exact:!0,...t};return this.getAll().find(r=>_g(n,r))}findAll(t={}){const n=this.getAll();return Object.keys(t).length>0?n.filter(r=>_g(t,r)):n}notify(t){Fe.batch(()=>{this.listeners.forEach(n=>{n(t)})})}onFocus(){Fe.batch(()=>{this.getAll().forEach(t=>{t.onFocus()})})}onOnline(){Fe.batch(()=>{this.getAll().forEach(t=>{t.onOnline()})})}},un=new WeakMap,jh),cn,Ls,wt,ni,dn,nr,zh,Gx=(zh=class extends cv{constructor(t){super();K(this,dn);K(this,cn,void 0);K(this,Ls,void 0);K(this,wt,void 0);K(this,ni,void 0);this.mutationId=t.mutationId,M(this,Ls,t.defaultOptions),M(this,wt,t.mutationCache),M(this,cn,[]),this.state=t.state||dv(),this.setOptions(t.options),this.scheduleGc()}setOptions(t){this.options={...$(this,Ls),...t},this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(t){$(this,cn).includes(t)||($(this,cn).push(t),this.clearGcTimeout(),$(this,wt).notify({type:"observerAdded",mutation:this,observer:t}))}removeObserver(t){M(this,cn,$(this,cn).filter(n=>n!==t)),this.scheduleGc(),$(this,wt).notify({type:"observerRemoved",mutation:this,observer:t})}optionalRemove(){$(this,cn).length||(this.state.status==="pending"?this.scheduleGc():$(this,wt).remove(this))}continue(){var t;return((t=$(this,ni))==null?void 0:t.continue())??this.execute(this.state.variables)}async execute(t){var o,i,s,a,l,u,c,d,f,h,y,v,x,m,g,w,C,E,_,P;const n=()=>(M(this,ni,uv({fn:()=>this.options.mutationFn?this.options.mutationFn(t):Promise.reject(new Error("No mutationFn found")),onFail:(R,O)=>{re(this,dn,nr).call(this,{type:"failed",failureCount:R,error:O})},onPause:()=>{re(this,dn,nr).call(this,{type:"pause"})},onContinue:()=>{re(this,dn,nr).call(this,{type:"continue"})},retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode})),$(this,ni).promise),r=this.state.status==="pending";try{if(!r){re(this,dn,nr).call(this,{type:"pending",variables:t}),await((i=(o=$(this,wt).config).onMutate)==null?void 0:i.call(o,t,this));const O=await((a=(s=this.options).onMutate)==null?void 0:a.call(s,t));O!==this.state.context&&re(this,dn,nr).call(this,{type:"pending",context:O,variables:t})}const R=await n();return await((u=(l=$(this,wt).config).onSuccess)==null?void 0:u.call(l,R,t,this.state.context,this)),await((d=(c=this.options).onSuccess)==null?void 0:d.call(c,R,t,this.state.context)),await((h=(f=$(this,wt).config).onSettled)==null?void 0:h.call(f,R,null,this.state.variables,this.state.context,this)),await((v=(y=this.options).onSettled)==null?void 0:v.call(y,R,null,t,this.state.context)),re(this,dn,nr).call(this,{type:"success",data:R}),R}catch(R){try{throw await((m=(x=$(this,wt).config).onError)==null?void 0:m.call(x,R,t,this.state.context,this)),await((w=(g=this.options).onError)==null?void 0:w.call(g,R,t,this.state.context)),await((E=(C=$(this,wt).config).onSettled)==null?void 0:E.call(C,void 0,R,this.state.variables,this.state.context,this)),await((P=(_=this.options).onSettled)==null?void 0:P.call(_,void 0,R,t,this.state.context)),R}finally{re(this,dn,nr).call(this,{type:"error",error:R})}}}},cn=new WeakMap,Ls=new WeakMap,wt=new WeakMap,ni=new WeakMap,dn=new WeakSet,nr=function(t){const n=r=>{switch(t.type){case"failed":return{...r,failureCount:t.failureCount,failureReason:t.error};case"pause":return{...r,isPaused:!0};case"continue":return{...r,isPaused:!1};case"pending":return{...r,context:t.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:!ru(this.options.networkMode),status:"pending",variables:t.variables,submittedAt:Date.now()};case"success":return{...r,data:t.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...r,data:void 0,error:t.error,failureCount:r.failureCount+1,failureReason:t.error,isPaused:!1,status:"error"}}};this.state=n(this.state),Fe.batch(()=>{$(this,cn).forEach(r=>{r.onMutationUpdate(t)}),$(this,wt).notify({mutation:this,type:"updated",action:t})})},zh);function dv(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}var Dt,Is,Yr,Vh,Kx=(Vh=class extends Ri{constructor(t={}){super();K(this,Dt,void 0);K(this,Is,void 0);K(this,Yr,void 0);this.config=t,M(this,Dt,[]),M(this,Is,0)}build(t,n,r){const o=new Gx({mutationCache:this,mutationId:++ta(this,Is)._,options:t.defaultMutationOptions(n),state:r});return this.add(o),o}add(t){$(this,Dt).push(t),this.notify({type:"added",mutation:t})}remove(t){M(this,Dt,$(this,Dt).filter(n=>n!==t)),this.notify({type:"removed",mutation:t})}clear(){Fe.batch(()=>{$(this,Dt).forEach(t=>{this.remove(t)})})}getAll(){return $(this,Dt)}find(t){const n={exact:!0,...t};return $(this,Dt).find(r=>kg(n,r))}findAll(t={}){return $(this,Dt).filter(n=>kg(t,n))}notify(t){Fe.batch(()=>{this.listeners.forEach(n=>{n(t)})})}resumePausedMutations(){return M(this,Yr,($(this,Yr)??Promise.resolve()).then(()=>{const t=$(this,Dt).filter(n=>n.state.isPaused);return Fe.batch(()=>t.reduce((n,r)=>n.then(()=>r.continue().catch(Lt)),Promise.resolve()))}).then(()=>{M(this,Yr,void 0)})),$(this,Yr)}},Dt=new WeakMap,Is=new WeakMap,Yr=new WeakMap,Vh);function id(e){return{onFetch:(t,n)=>{const r=async()=>{var y,v,x,m,g;const o=t.options,i=(x=(v=(y=t.fetchOptions)==null?void 0:y.meta)==null?void 0:v.fetchMore)==null?void 0:x.direction,s=((m=t.state.data)==null?void 0:m.pages)||[],a=((g=t.state.data)==null?void 0:g.pageParams)||[],l={pages:[],pageParams:[]};let u=!1;const c=w=>{Object.defineProperty(w,"signal",{enumerable:!0,get:()=>(t.signal.aborted?u=!0:t.signal.addEventListener("abort",()=>{u=!0}),t.signal)})},d=t.options.queryFn||(()=>Promise.reject(new Error(`Missing queryFn: '${t.options.queryHash}'`))),f=async(w,C,E)=>{if(u)return Promise.reject();if(C==null&&w.pages.length)return Promise.resolve(w);const _={queryKey:t.queryKey,pageParam:C,direction:E?"backward":"forward",meta:t.options.meta};c(_);const P=await d(_),{maxPages:R}=t.options,O=E?Fx:Ix;return{pages:O(w.pages,P,R),pageParams:O(w.pageParams,C,R)}};let h;if(i&&s.length){const w=i==="backward",C=w?fv:sd,E={pages:s,pageParams:a},_=C(o,E);h=await f(E,_,w)}else{h=await f(l,a[0]??o.initialPageParam);const w=e??s.length;for(let C=1;C<w;C++){const E=sd(o,h);h=await f(h,E)}}return h};t.options.persister?t.fetchFn=()=>{var o,i;return(i=(o=t.options).persister)==null?void 0:i.call(o,r,{queryKey:t.queryKey,meta:t.options.meta,signal:t.signal},n)}:t.fetchFn=r}}}function sd(e,{pages:t,pageParams:n}){const r=t.length-1;return e.getNextPageParam(t[r],t,n[r],n)}function fv(e,{pages:t,pageParams:n}){var r;return(r=e.getPreviousPageParam)==null?void 0:r.call(e,t[0],t,n[0],n)}function Qx(e,t){return t?sd(e,t)!=null:!1}function Yx(e,t){return!t||!e.getPreviousPageParam?!1:fv(e,t)!=null}var Ve,dr,fr,ri,oi,pr,ii,si,Hh,qx=(Hh=class{constructor(e={}){K(this,Ve,void 0);K(this,dr,void 0);K(this,fr,void 0);K(this,ri,void 0);K(this,oi,void 0);K(this,pr,void 0);K(this,ii,void 0);K(this,si,void 0);M(this,Ve,e.queryCache||new Wx),M(this,dr,e.mutationCache||new Kx),M(this,fr,e.defaultOptions||{}),M(this,ri,new Map),M(this,oi,new Map),M(this,pr,0)}mount(){ta(this,pr)._++,$(this,pr)===1&&(M(this,ii,$l.subscribe(()=>{$l.isFocused()&&(this.resumePausedMutations(),$(this,Ve).onFocus())})),M(this,si,El.subscribe(()=>{El.isOnline()&&(this.resumePausedMutations(),$(this,Ve).onOnline())})))}unmount(){var e,t;ta(this,pr)._--,$(this,pr)===0&&((e=$(this,ii))==null||e.call(this),M(this,ii,void 0),(t=$(this,si))==null||t.call(this),M(this,si,void 0))}isFetching(e){return $(this,Ve).findAll({...e,fetchStatus:"fetching"}).length}isMutating(e){return $(this,dr).findAll({...e,status:"pending"}).length}getQueryData(e){var t;return(t=$(this,Ve).find({queryKey:e}))==null?void 0:t.state.data}ensureQueryData(e){const t=this.getQueryData(e.queryKey);return t!==void 0?Promise.resolve(t):this.fetchQuery(e)}getQueriesData(e){return this.getQueryCache().findAll(e).map(({queryKey:t,state:n})=>{const r=n.data;return[t,r]})}setQueryData(e,t,n){const r=$(this,Ve).find({queryKey:e}),o=r==null?void 0:r.state.data,i=Lx(t,o);if(typeof i>"u")return;const s=this.defaultQueryOptions({queryKey:e});return $(this,Ve).build(this,s).setData(i,{...n,manual:!0})}setQueriesData(e,t,n){return Fe.batch(()=>this.getQueryCache().findAll(e).map(({queryKey:r})=>[r,this.setQueryData(r,t,n)]))}getQueryState(e){var t;return(t=$(this,Ve).find({queryKey:e}))==null?void 0:t.state}removeQueries(e){const t=$(this,Ve);Fe.batch(()=>{t.findAll(e).forEach(n=>{t.remove(n)})})}resetQueries(e,t){const n=$(this,Ve),r={type:"active",...e};return Fe.batch(()=>(n.findAll(e).forEach(o=>{o.reset()}),this.refetchQueries(r,t)))}cancelQueries(e={},t={}){const n={revert:!0,...t},r=Fe.batch(()=>$(this,Ve).findAll(e).map(o=>o.cancel(n)));return Promise.all(r).then(Lt).catch(Lt)}invalidateQueries(e={},t={}){return Fe.batch(()=>{if($(this,Ve).findAll(e).forEach(r=>{r.invalidate()}),e.refetchType==="none")return Promise.resolve();const n={...e,type:e.refetchType??e.type??"active"};return this.refetchQueries(n,t)})}refetchQueries(e={},t){const n={...t,cancelRefetch:(t==null?void 0:t.cancelRefetch)??!0},r=Fe.batch(()=>$(this,Ve).findAll(e).filter(o=>!o.isDisabled()).map(o=>{let i=o.fetch(void 0,n);return n.throwOnError||(i=i.catch(Lt)),o.state.fetchStatus==="paused"?Promise.resolve():i}));return Promise.all(r).then(Lt)}fetchQuery(e){const t=this.defaultQueryOptions(e);typeof t.retry>"u"&&(t.retry=!1);const n=$(this,Ve).build(this,t);return n.isStaleByTime(t.staleTime)?n.fetch(t):Promise.resolve(n.state.data)}prefetchQuery(e){return this.fetchQuery(e).then(Lt).catch(Lt)}fetchInfiniteQuery(e){return e.behavior=id(e.pages),this.fetchQuery(e)}prefetchInfiniteQuery(e){return this.fetchInfiniteQuery(e).then(Lt).catch(Lt)}resumePausedMutations(){return $(this,dr).resumePausedMutations()}getQueryCache(){return $(this,Ve)}getMutationCache(){return $(this,dr)}getDefaultOptions(){return $(this,fr)}setDefaultOptions(e){M(this,fr,e)}setQueryDefaults(e,t){$(this,ri).set(uo(e),{queryKey:e,defaultOptions:t})}getQueryDefaults(e){const t=[...$(this,ri).values()];let n={};return t.forEach(r=>{Ps(e,r.queryKey)&&(n={...n,...r.defaultOptions})}),n}setMutationDefaults(e,t){$(this,oi).set(uo(e),{mutationKey:e,defaultOptions:t})}getMutationDefaults(e){const t=[...$(this,oi).values()];let n={};return t.forEach(r=>{Ps(e,r.mutationKey)&&(n={...n,...r.defaultOptions})}),n}defaultQueryOptions(e){if(e!=null&&e._defaulted)return e;const t={...$(this,fr).queries,...(e==null?void 0:e.queryKey)&&this.getQueryDefaults(e.queryKey),...e,_defaulted:!0};return t.queryHash||(t.queryHash=Nf(t.queryKey,t)),typeof t.refetchOnReconnect>"u"&&(t.refetchOnReconnect=t.networkMode!=="always"),typeof t.throwOnError>"u"&&(t.throwOnError=!!t.suspense),typeof t.networkMode>"u"&&t.persister&&(t.networkMode="offlineFirst"),t}defaultMutationOptions(e){return e!=null&&e._defaulted?e:{...$(this,fr).mutations,...(e==null?void 0:e.mutationKey)&&this.getMutationDefaults(e.mutationKey),...e,_defaulted:!0}}clear(){$(this,Ve).clear(),$(this,dr).clear()}},Ve=new WeakMap,dr=new WeakMap,fr=new WeakMap,ri=new WeakMap,oi=new WeakMap,pr=new WeakMap,ii=new WeakMap,si=new WeakMap,Hh),dt,ye,ai,tt,qr,li,fn,Fs,ui,ci,Xr,Zr,gr,Jr,eo,es,js,ad,zs,ld,Vs,ud,Hs,cd,Us,dd,Bs,fd,Ws,pd,zl,gv,Uh,pv=(Uh=class extends Ri{constructor(t,n){super();K(this,eo);K(this,js);K(this,zs);K(this,Vs);K(this,Hs);K(this,Us);K(this,Bs);K(this,Ws);K(this,zl);K(this,dt,void 0);K(this,ye,void 0);K(this,ai,void 0);K(this,tt,void 0);K(this,qr,void 0);K(this,li,void 0);K(this,fn,void 0);K(this,Fs,void 0);K(this,ui,void 0);K(this,ci,void 0);K(this,Xr,void 0);K(this,Zr,void 0);K(this,gr,void 0);K(this,Jr,void 0);M(this,ye,void 0),M(this,ai,void 0),M(this,tt,void 0),M(this,Jr,new Set),M(this,dt,t),this.options=n,M(this,fn,null),this.bindMethods(),this.setOptions(n)}bindMethods(){this.refetch=this.refetch.bind(this)}onSubscribe(){this.listeners.size===1&&($(this,ye).addObserver(this),Ag($(this,ye),this.options)?re(this,eo,es).call(this):this.updateResult(),re(this,Hs,cd).call(this))}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return gd($(this,ye),this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return gd($(this,ye),this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,re(this,Us,dd).call(this),re(this,Bs,fd).call(this),$(this,ye).removeObserver(this)}setOptions(t,n){const r=this.options,o=$(this,ye);if(this.options=$(this,dt).defaultQueryOptions(t),bl(r,this.options)||$(this,dt).getQueryCache().notify({type:"observerOptionsUpdated",query:$(this,ye),observer:this}),typeof this.options.enabled<"u"&&typeof this.options.enabled!="boolean")throw new Error("Expected enabled to be a boolean");this.options.queryKey||(this.options.queryKey=r.queryKey),re(this,Ws,pd).call(this);const i=this.hasListeners();i&&Mg($(this,ye),o,this.options,r)&&re(this,eo,es).call(this),this.updateResult(n),i&&($(this,ye)!==o||this.options.enabled!==r.enabled||this.options.staleTime!==r.staleTime)&&re(this,js,ad).call(this);const s=re(this,zs,ld).call(this);i&&($(this,ye)!==o||this.options.enabled!==r.enabled||s!==$(this,gr))&&re(this,Vs,ud).call(this,s)}getOptimisticResult(t){const n=$(this,dt).getQueryCache().build($(this,dt),t),r=this.createResult(n,t);return Zx(this,r)&&(M(this,tt,r),M(this,li,this.options),M(this,qr,$(this,ye).state)),r}getCurrentResult(){return $(this,tt)}trackResult(t){const n={};return Object.keys(t).forEach(r=>{Object.defineProperty(n,r,{configurable:!1,enumerable:!0,get:()=>($(this,Jr).add(r),t[r])})}),n}getCurrentQuery(){return $(this,ye)}refetch({...t}={}){return this.fetch({...t})}fetchOptimistic(t){const n=$(this,dt).defaultQueryOptions(t),r=$(this,dt).getQueryCache().build($(this,dt),n);return r.isFetchingOptimistic=!0,r.fetch().then(()=>this.createResult(r,n))}fetch(t){return re(this,eo,es).call(this,{...t,cancelRefetch:t.cancelRefetch??!0}).then(()=>(this.updateResult(),$(this,tt)))}createResult(t,n){var _;const r=$(this,ye),o=this.options,i=$(this,tt),s=$(this,qr),a=$(this,li),u=t!==r?t.state:$(this,ai),{state:c}=t;let{error:d,errorUpdatedAt:f,fetchStatus:h,status:y}=c,v=!1,x;if(n._optimisticResults){const P=this.hasListeners(),R=!P&&Ag(t,n),O=P&&Mg(t,r,n,o);(R||O)&&(h=ru(t.options.networkMode)?"fetching":"paused",c.dataUpdatedAt||(y="pending")),n._optimisticResults==="isRestoring"&&(h="idle")}if(n.select&&typeof c.data<"u")if(i&&c.data===(s==null?void 0:s.data)&&n.select===$(this,Fs))x=$(this,ui);else try{M(this,Fs,n.select),x=n.select(c.data),x=od(i==null?void 0:i.data,x,n),M(this,ui,x),M(this,fn,null)}catch(P){M(this,fn,P)}else x=c.data;if(typeof n.placeholderData<"u"&&typeof x>"u"&&y==="pending"){let P;if(i!=null&&i.isPlaceholderData&&n.placeholderData===(a==null?void 0:a.placeholderData))P=i.data;else if(P=typeof n.placeholderData=="function"?n.placeholderData((_=$(this,ci))==null?void 0:_.state.data,$(this,ci)):n.placeholderData,n.select&&typeof P<"u")try{P=n.select(P),M(this,fn,null)}catch(R){M(this,fn,R)}typeof P<"u"&&(y="success",x=od(i==null?void 0:i.data,P,n),v=!0)}$(this,fn)&&(d=$(this,fn),x=$(this,ui),f=Date.now(),y="error");const m=h==="fetching",g=y==="pending",w=y==="error",C=g&&m;return{status:y,fetchStatus:h,isPending:g,isSuccess:y==="success",isError:w,isInitialLoading:C,isLoading:C,data:x,dataUpdatedAt:c.dataUpdatedAt,error:d,errorUpdatedAt:f,failureCount:c.fetchFailureCount,failureReason:c.fetchFailureReason,errorUpdateCount:c.errorUpdateCount,isFetched:c.dataUpdateCount>0||c.errorUpdateCount>0,isFetchedAfterMount:c.dataUpdateCount>u.dataUpdateCount||c.errorUpdateCount>u.errorUpdateCount,isFetching:m,isRefetching:m&&!g,isLoadingError:w&&c.dataUpdatedAt===0,isPaused:h==="paused",isPlaceholderData:v,isRefetchError:w&&c.dataUpdatedAt!==0,isStale:Tf(t,n),refetch:this.refetch}}updateResult(t){const n=$(this,tt),r=this.createResult($(this,ye),this.options);if(M(this,qr,$(this,ye).state),M(this,li,this.options),$(this,qr).data!==void 0&&M(this,ci,$(this,ye)),bl(r,n))return;M(this,tt,r);const o={},i=()=>{if(!n)return!0;const{notifyOnChangeProps:s}=this.options,a=typeof s=="function"?s():s;if(a==="all"||!a&&!$(this,Jr).size)return!0;const l=new Set(a??$(this,Jr));return this.options.throwOnError&&l.add("error"),Object.keys($(this,tt)).some(u=>{const c=u;return $(this,tt)[c]!==n[c]&&l.has(c)})};(t==null?void 0:t.listeners)!==!1&&i()&&(o.listeners=!0),re(this,zl,gv).call(this,{...o,...t})}onQueryUpdate(){this.updateResult(),this.hasListeners()&&re(this,Hs,cd).call(this)}},dt=new WeakMap,ye=new WeakMap,ai=new WeakMap,tt=new WeakMap,qr=new WeakMap,li=new WeakMap,fn=new WeakMap,Fs=new WeakMap,ui=new WeakMap,ci=new WeakMap,Xr=new WeakMap,Zr=new WeakMap,gr=new WeakMap,Jr=new WeakMap,eo=new WeakSet,es=function(t){re(this,Ws,pd).call(this);let n=$(this,ye).fetch(this.options,t);return t!=null&&t.throwOnError||(n=n.catch(Lt)),n},js=new WeakSet,ad=function(){if(re(this,Us,dd).call(this),wi||$(this,tt).isStale||!nd(this.options.staleTime))return;const n=sv($(this,tt).dataUpdatedAt,this.options.staleTime)+1;M(this,Xr,setTimeout(()=>{$(this,tt).isStale||this.updateResult()},n))},zs=new WeakSet,ld=function(){return(typeof this.options.refetchInterval=="function"?this.options.refetchInterval($(this,ye)):this.options.refetchInterval)??!1},Vs=new WeakSet,ud=function(t){re(this,Bs,fd).call(this),M(this,gr,t),!(wi||this.options.enabled===!1||!nd($(this,gr))||$(this,gr)===0)&&M(this,Zr,setInterval(()=>{(this.options.refetchIntervalInBackground||$l.isFocused())&&re(this,eo,es).call(this)},$(this,gr)))},Hs=new WeakSet,cd=function(){re(this,js,ad).call(this),re(this,Vs,ud).call(this,re(this,zs,ld).call(this))},Us=new WeakSet,dd=function(){$(this,Xr)&&(clearTimeout($(this,Xr)),M(this,Xr,void 0))},Bs=new WeakSet,fd=function(){$(this,Zr)&&(clearInterval($(this,Zr)),M(this,Zr,void 0))},Ws=new WeakSet,pd=function(){const t=$(this,dt).getQueryCache().build($(this,dt),this.options);if(t===$(this,ye))return;const n=$(this,ye);M(this,ye,t),M(this,ai,t.state),this.hasListeners()&&(n==null||n.removeObserver(this),t.addObserver(this))},zl=new WeakSet,gv=function(t){Fe.batch(()=>{t.listeners&&this.listeners.forEach(n=>{n($(this,tt))}),$(this,dt).getQueryCache().notify({query:$(this,ye),type:"observerResultsUpdated"})})},Uh);function Xx(e,t){return t.enabled!==!1&&!e.state.dataUpdatedAt&&!(e.state.status==="error"&&t.retryOnMount===!1)}function Ag(e,t){return Xx(e,t)||e.state.dataUpdatedAt>0&&gd(e,t,t.refetchOnMount)}function gd(e,t,n){if(t.enabled!==!1){const r=typeof n=="function"?n(e):n;return r==="always"||r!==!1&&Tf(e,t)}return!1}function Mg(e,t,n,r){return n.enabled!==!1&&(e!==t||r.enabled===!1)&&(!n.suspense||e.state.status!=="error")&&Tf(e,n)}function Tf(e,t){return e.isStaleByTime(t.staleTime)}function Zx(e,t){return!bl(e.getCurrentResult(),t)}var Jx=class extends pv{constructor(e,t){super(e,t)}bindMethods(){super.bindMethods(),this.fetchNextPage=this.fetchNextPage.bind(this),this.fetchPreviousPage=this.fetchPreviousPage.bind(this)}setOptions(e,t){super.setOptions({...e,behavior:id()},t)}getOptimisticResult(e){return e.behavior=id(),super.getOptimisticResult(e)}fetchNextPage(e){return this.fetch({...e,meta:{fetchMore:{direction:"forward"}}})}fetchPreviousPage(e){return this.fetch({...e,meta:{fetchMore:{direction:"backward"}}})}createResult(e,t){var l,u,c,d;const{state:n}=e,r=super.createResult(e,t),{isFetching:o,isRefetching:i}=r,s=o&&((u=(l=n.fetchMeta)==null?void 0:l.fetchMore)==null?void 0:u.direction)==="forward",a=o&&((d=(c=n.fetchMeta)==null?void 0:c.fetchMore)==null?void 0:d.direction)==="backward";return{...r,fetchNextPage:this.fetchNextPage,fetchPreviousPage:this.fetchPreviousPage,hasNextPage:Qx(t,n.data),hasPreviousPage:Yx(t,n.data),isFetchingNextPage:s,isFetchingPreviousPage:a,isRefetching:i&&!s&&!a}}},hr,An,xt,Mn,di,Qa,Gs,hd,Bh,eS=(Bh=class extends Ri{constructor(n,r){super();K(this,di);K(this,Gs);K(this,hr,void 0);K(this,An,void 0);K(this,xt,void 0);K(this,Mn,void 0);M(this,An,void 0),M(this,hr,n),this.setOptions(r),this.bindMethods(),re(this,di,Qa).call(this)}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(n){var o;const r=this.options;this.options=$(this,hr).defaultMutationOptions(n),bl(r,this.options)||$(this,hr).getMutationCache().notify({type:"observerOptionsUpdated",mutation:$(this,xt),observer:this}),(o=$(this,xt))==null||o.setOptions(this.options),r!=null&&r.mutationKey&&this.options.mutationKey&&uo(r.mutationKey)!==uo(this.options.mutationKey)&&this.reset()}onUnsubscribe(){var n;this.hasListeners()||(n=$(this,xt))==null||n.removeObserver(this)}onMutationUpdate(n){re(this,di,Qa).call(this),re(this,Gs,hd).call(this,n)}getCurrentResult(){return $(this,An)}reset(){var n;(n=$(this,xt))==null||n.removeObserver(this),M(this,xt,void 0),re(this,di,Qa).call(this),re(this,Gs,hd).call(this)}mutate(n,r){var o;return M(this,Mn,r),(o=$(this,xt))==null||o.removeObserver(this),M(this,xt,$(this,hr).getMutationCache().build($(this,hr),this.options)),$(this,xt).addObserver(this),$(this,xt).execute(n)}},hr=new WeakMap,An=new WeakMap,xt=new WeakMap,Mn=new WeakMap,di=new WeakSet,Qa=function(){var r;const n=((r=$(this,xt))==null?void 0:r.state)??dv();M(this,An,{...n,isPending:n.status==="pending",isSuccess:n.status==="success",isError:n.status==="error",isIdle:n.status==="idle",mutate:this.mutate,reset:this.reset})},Gs=new WeakSet,hd=function(n){Fe.batch(()=>{var r,o,i,s,a,l,u,c;if($(this,Mn)&&this.hasListeners()){const d=$(this,An).variables,f=$(this,An).context;(n==null?void 0:n.type)==="success"?((o=(r=$(this,Mn)).onSuccess)==null||o.call(r,n.data,d,f),(s=(i=$(this,Mn)).onSettled)==null||s.call(i,n.data,null,d,f)):(n==null?void 0:n.type)==="error"&&((l=(a=$(this,Mn)).onError)==null||l.call(a,n.error,d,f),(c=(u=$(this,Mn)).onSettled)==null||c.call(u,void 0,n.error,d,f))}this.listeners.forEach(d=>{d($(this,An))})})},Bh),hv=p.createContext(void 0),Zs=e=>{const t=p.useContext(hv);if(e)return e;if(!t)throw new Error("No QueryClient set, use QueryClientProvider to set one");return t},tS=({client:e,children:t})=>(p.useEffect(()=>(e.mount(),()=>{e.unmount()}),[e]),p.createElement(hv.Provider,{value:e},t)),mv=p.createContext(!1),nS=()=>p.useContext(mv);mv.Provider;function rS(){let e=!1;return{clearReset:()=>{e=!1},reset:()=>{e=!0},isReset:()=>e}}var oS=p.createContext(rS()),iS=()=>p.useContext(oS);function vv(e,t){return typeof e=="function"?e(...t):!!e}var sS=(e,t)=>{(e.suspense||e.throwOnError)&&(t.isReset()||(e.retryOnMount=!1))},aS=e=>{p.useEffect(()=>{e.clearReset()},[e])},lS=({result:e,errorResetBoundary:t,throwOnError:n,query:r})=>e.isError&&!t.isReset()&&!e.isFetching&&vv(n,[e.error,r]),uS=e=>{e.suspense&&typeof e.staleTime!="number"&&(e.staleTime=1e3)},cS=(e,t)=>(e==null?void 0:e.suspense)&&t.isPending,dS=(e,t,n)=>t.fetchOptimistic(e).catch(()=>{n.clearReset()});function yv(e,t,n){const r=Zs(n),o=nS(),i=iS(),s=r.defaultQueryOptions(e);s._optimisticResults=o?"isRestoring":"optimistic",uS(s),sS(s,i),aS(i);const[a]=p.useState(()=>new t(r,s)),l=a.getOptimisticResult(s);if(p.useSyncExternalStore(p.useCallback(u=>{const c=o?()=>{}:a.subscribe(Fe.batchCalls(u));return a.updateResult(),c},[a,o]),()=>a.getCurrentResult(),()=>a.getCurrentResult()),p.useEffect(()=>{a.setOptions(s,{listeners:!1})},[s,a]),cS(s,l))throw a.setOptions(s,{listeners:!1}),dS(s,a,i);if(lS({result:l,errorResetBoundary:i,throwOnError:s.throwOnError,query:a.getCurrentQuery()}))throw l.error;return s.notifyOnChangeProps?l:a.trackResult(l)}function fS(e,t){return yv(e,pv,t)}function Dg(e,t){return e.findAll(t.filters).map(n=>t.select?t.select(n):n.state)}function pS(e={},t){const n=Zs(t).getMutationCache(),r=p.useRef(e),o=p.useRef();return o.current||(o.current=Dg(n,e)),p.useEffect(()=>{r.current=e}),p.useSyncExternalStore(p.useCallback(i=>n.subscribe(()=>{const s=Of(o.current,Dg(n,r.current));o.current!==s&&(o.current=s,Fe.schedule(i))}),[n]),()=>o.current,()=>o.current)}function md(e,t){const n=Zs(t),[r]=p.useState(()=>new eS(n,e));p.useEffect(()=>{r.setOptions(e)},[r,e]);const o=p.useSyncExternalStore(p.useCallback(s=>r.subscribe(Fe.batchCalls(s)),[r]),()=>r.getCurrentResult(),()=>r.getCurrentResult()),i=p.useCallback((s,a)=>{r.mutate(s,a).catch(gS)},[r]);if(o.error&&vv(r.options.throwOnError,[o.error]))throw o.error;return{...o,mutate:i,mutateAsync:o.mutate}}function gS(){}function hS(e,t){return yv(e,Jx,t)}var{entries:wv,setPrototypeOf:Lg,isFrozen:mS,getPrototypeOf:vS,getOwnPropertyDescriptor:yS}=Object,{freeze:at,seal:Ut,create:xv}=Object,{apply:vd,construct:yd}=typeof Reflect<"u"&&Reflect;at||(at=function(e){return e});Ut||(Ut=function(e){return e});vd||(vd=function(e,t,n){return e.apply(t,n)});yd||(yd=function(e,t){return new e(...t)});var wa=Rt(Array.prototype.forEach),Ig=Rt(Array.prototype.pop),Ui=Rt(Array.prototype.push),Ya=Rt(String.prototype.toLowerCase),Uu=Rt(String.prototype.toString),Fg=Rt(String.prototype.match),Bi=Rt(String.prototype.replace),wS=Rt(String.prototype.indexOf),xS=Rt(String.prototype.trim),Wt=Rt(Object.prototype.hasOwnProperty),yt=Rt(RegExp.prototype.test),Wi=SS(TypeError);function Rt(e){return function(t){for(var n=arguments.length,r=new Array(n>1?n-1:0),o=1;o<n;o++)r[o-1]=arguments[o];return vd(e,t,r)}}function SS(e){return function(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];return yd(e,n)}}function le(e,t){let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:Ya;Lg&&Lg(e,null);let r=t.length;for(;r--;){let o=t[r];if(typeof o=="string"){let i=n(o);i!==o&&(mS(t)||(t[r]=i),o=i)}e[o]=!0}return e}function CS(e){for(let t=0;t<e.length;t++)Wt(e,t)||(e[t]=null);return e}function jr(e){let t=xv(null);for(let[n,r]of wv(e))Wt(e,n)&&(Array.isArray(r)?t[n]=CS(r):r&&typeof r=="object"&&r.constructor===Object?t[n]=jr(r):t[n]=r);return t}function xa(e,t){for(;e!==null;){let r=yS(e,t);if(r){if(r.get)return Rt(r.get);if(typeof r.value=="function")return Rt(r.value)}e=vS(e)}function n(){return null}return n}var jg=at(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),Bu=at(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),Wu=at(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),bS=at(["animate","color-profile","cursor","discard","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),Gu=at(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover","mprescripts"]),$S=at(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),zg=at(["#text"]),Vg=at(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","xmlns","slot"]),Ku=at(["accent-height","accumulate","additive","alignment-baseline","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),Hg=at(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),Sa=at(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),ES=Ut(/\{\{[\w\W]*|[\w\W]*\}\}/gm),RS=Ut(/<%[\w\W]*|[\w\W]*%>/gm),PS=Ut(/\${[\w\W]*}/gm),_S=Ut(/^data-[\-\w.\u00B7-\uFFFF]/),kS=Ut(/^aria-[\-\w]+$/),Sv=Ut(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),NS=Ut(/^(?:\w+script|data):/i),OS=Ut(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),Cv=Ut(/^html$/i),TS=Ut(/^[a-z][a-z\d]*(-[a-z\d]+)+$/i),Ug=Object.freeze({__proto__:null,MUSTACHE_EXPR:ES,ERB_EXPR:RS,TMPLIT_EXPR:PS,DATA_ATTR:_S,ARIA_ATTR:kS,IS_ALLOWED_URI:Sv,IS_SCRIPT_OR_DATA:NS,ATTR_WHITESPACE:OS,DOCTYPE_NAME:Cv,CUSTOM_ELEMENT:TS}),AS=function(){return typeof window>"u"?null:window},MS=function(e,t){if(typeof e!="object"||typeof e.createPolicy!="function")return null;let n=null,r="data-tt-policy-suffix";t&&t.hasAttribute(r)&&(n=t.getAttribute(r));let o="dompurify"+(n?"#"+n:"");try{return e.createPolicy(o,{createHTML(i){return i},createScriptURL(i){return i}})}catch{return console.warn("TrustedTypes policy "+o+" could not be created."),null}};function bv(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:AS(),t=b=>bv(b);if(t.version="3.0.10",t.removed=[],!e||!e.document||e.document.nodeType!==9)return t.isSupported=!1,t;let{document:n}=e,r=n,o=r.currentScript,{DocumentFragment:i,HTMLTemplateElement:s,Node:a,Element:l,NodeFilter:u,NamedNodeMap:c=e.NamedNodeMap||e.MozNamedAttrMap,HTMLFormElement:d,DOMParser:f,trustedTypes:h}=e,y=l.prototype,v=xa(y,"cloneNode"),x=xa(y,"nextSibling"),m=xa(y,"childNodes"),g=xa(y,"parentNode");if(typeof s=="function"){let b=n.createElement("template");b.content&&b.content.ownerDocument&&(n=b.content.ownerDocument)}let w,C="",{implementation:E,createNodeIterator:_,createDocumentFragment:P,getElementsByTagName:R}=n,{importNode:O}=r,T={};t.isSupported=typeof wv=="function"&&typeof g=="function"&&E&&E.createHTMLDocument!==void 0;let{MUSTACHE_EXPR:V,ERB_EXPR:j,TMPLIT_EXPR:Q,DATA_ATTR:z,ARIA_ATTR:Y,IS_SCRIPT_OR_DATA:U,ATTR_WHITESPACE:q,CUSTOM_ELEMENT:k}=Ug,{IS_ALLOWED_URI:I}=Ug,D=null,G=le({},[...jg,...Bu,...Wu,...Gu,...zg]),H=null,de=le({},[...Vg,...Ku,...Hg,...Sa]),J=Object.seal(xv(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),ge=null,ie=null,X=!0,se=!0,Re=!1,ce=!0,ae=!1,ne=!1,ze=!1,Ae=!1,xe=!1,Qe=!1,Nt=!1,yo=!0,xn=!1,Yn="user-content-",wo=!0,Sn=!1,vt={},Cn=null,bn=le({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]),Ai=null,ea=le({},["audio","video","img","source","image","track"]),xo=null,Mi=le({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),So="http://www.w3.org/1998/Math/MathML",qn="http://www.w3.org/2000/svg",Be="http://www.w3.org/1999/xhtml",Xn=Be,Co=!1,ee=null,Me=le({},[So,qn,Be],Uu),ve=null,Ot=["application/xhtml+xml","text/html"],Tt="text/html",Pe=null,Zn=null,i1=n.createElement("form"),ip=function(b){return b instanceof RegExp||b instanceof Function},pu=function(){let b=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};if(!(Zn&&Zn===b)){if((!b||typeof b!="object")&&(b={}),b=jr(b),ve=Ot.indexOf(b.PARSER_MEDIA_TYPE)===-1?Tt:b.PARSER_MEDIA_TYPE,Pe=ve==="application/xhtml+xml"?Uu:Ya,D=Wt(b,"ALLOWED_TAGS")?le({},b.ALLOWED_TAGS,Pe):G,H=Wt(b,"ALLOWED_ATTR")?le({},b.ALLOWED_ATTR,Pe):de,ee=Wt(b,"ALLOWED_NAMESPACES")?le({},b.ALLOWED_NAMESPACES,Uu):Me,xo=Wt(b,"ADD_URI_SAFE_ATTR")?le(jr(Mi),b.ADD_URI_SAFE_ATTR,Pe):Mi,Ai=Wt(b,"ADD_DATA_URI_TAGS")?le(jr(ea),b.ADD_DATA_URI_TAGS,Pe):ea,Cn=Wt(b,"FORBID_CONTENTS")?le({},b.FORBID_CONTENTS,Pe):bn,ge=Wt(b,"FORBID_TAGS")?le({},b.FORBID_TAGS,Pe):{},ie=Wt(b,"FORBID_ATTR")?le({},b.FORBID_ATTR,Pe):{},vt=Wt(b,"USE_PROFILES")?b.USE_PROFILES:!1,X=b.ALLOW_ARIA_ATTR!==!1,se=b.ALLOW_DATA_ATTR!==!1,Re=b.ALLOW_UNKNOWN_PROTOCOLS||!1,ce=b.ALLOW_SELF_CLOSE_IN_ATTR!==!1,ae=b.SAFE_FOR_TEMPLATES||!1,ne=b.WHOLE_DOCUMENT||!1,xe=b.RETURN_DOM||!1,Qe=b.RETURN_DOM_FRAGMENT||!1,Nt=b.RETURN_TRUSTED_TYPE||!1,Ae=b.FORCE_BODY||!1,yo=b.SANITIZE_DOM!==!1,xn=b.SANITIZE_NAMED_PROPS||!1,wo=b.KEEP_CONTENT!==!1,Sn=b.IN_PLACE||!1,I=b.ALLOWED_URI_REGEXP||Sv,Xn=b.NAMESPACE||Be,J=b.CUSTOM_ELEMENT_HANDLING||{},b.CUSTOM_ELEMENT_HANDLING&&ip(b.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(J.tagNameCheck=b.CUSTOM_ELEMENT_HANDLING.tagNameCheck),b.CUSTOM_ELEMENT_HANDLING&&ip(b.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(J.attributeNameCheck=b.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),b.CUSTOM_ELEMENT_HANDLING&&typeof b.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements=="boolean"&&(J.allowCustomizedBuiltInElements=b.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),ae&&(se=!1),Qe&&(xe=!0),vt&&(D=le({},zg),H=[],vt.html===!0&&(le(D,jg),le(H,Vg)),vt.svg===!0&&(le(D,Bu),le(H,Ku),le(H,Sa)),vt.svgFilters===!0&&(le(D,Wu),le(H,Ku),le(H,Sa)),vt.mathMl===!0&&(le(D,Gu),le(H,Hg),le(H,Sa))),b.ADD_TAGS&&(D===G&&(D=jr(D)),le(D,b.ADD_TAGS,Pe)),b.ADD_ATTR&&(H===de&&(H=jr(H)),le(H,b.ADD_ATTR,Pe)),b.ADD_URI_SAFE_ATTR&&le(xo,b.ADD_URI_SAFE_ATTR,Pe),b.FORBID_CONTENTS&&(Cn===bn&&(Cn=jr(Cn)),le(Cn,b.FORBID_CONTENTS,Pe)),wo&&(D["#text"]=!0),ne&&le(D,["html","head","body"]),D.table&&(le(D,["tbody"]),delete ge.tbody),b.TRUSTED_TYPES_POLICY){if(typeof b.TRUSTED_TYPES_POLICY.createHTML!="function")throw Wi('TRUSTED_TYPES_POLICY configuration option must provide a "createHTML" hook.');if(typeof b.TRUSTED_TYPES_POLICY.createScriptURL!="function")throw Wi('TRUSTED_TYPES_POLICY configuration option must provide a "createScriptURL" hook.');w=b.TRUSTED_TYPES_POLICY,C=w.createHTML("")}else w===void 0&&(w=MS(h,o)),w!==null&&typeof C=="string"&&(C=w.createHTML(""));at&&at(b),Zn=b}},sp=le({},["mi","mo","mn","ms","mtext"]),ap=le({},["foreignobject","desc","title","annotation-xml"]),s1=le({},["title","style","font","a","script"]),lp=le({},[...Bu,...Wu,...bS]),up=le({},[...Gu,...$S]),a1=function(b){let B=g(b);(!B||!B.tagName)&&(B={namespaceURI:Xn,tagName:"template"});let L=Ya(b.tagName),he=Ya(B.tagName);return ee[b.namespaceURI]?b.namespaceURI===qn?B.namespaceURI===Be?L==="svg":B.namespaceURI===So?L==="svg"&&(he==="annotation-xml"||sp[he]):!!lp[L]:b.namespaceURI===So?B.namespaceURI===Be?L==="math":B.namespaceURI===qn?L==="math"&&ap[he]:!!up[L]:b.namespaceURI===Be?B.namespaceURI===qn&&!ap[he]||B.namespaceURI===So&&!sp[he]?!1:!up[L]&&(s1[L]||!lp[L]):!!(ve==="application/xhtml+xml"&&ee[b.namespaceURI]):!1},bo=function(b){Ui(t.removed,{element:b});try{b.parentNode.removeChild(b)}catch{b.remove()}},gu=function(b,B){try{Ui(t.removed,{attribute:B.getAttributeNode(b),from:B})}catch{Ui(t.removed,{attribute:null,from:B})}if(B.removeAttribute(b),b==="is"&&!H[b])if(xe||Qe)try{bo(B)}catch{}else try{B.setAttribute(b,"")}catch{}},cp=function(b){let B=null,L=null;if(Ae)b="<remove></remove>"+b;else{let De=Fg(b,/^[\r\n\t ]+/);L=De&&De[0]}ve==="application/xhtml+xml"&&Xn===Be&&(b='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+b+"</body></html>");let he=w?w.createHTML(b):b;if(Xn===Be)try{B=new f().parseFromString(he,ve)}catch{}if(!B||!B.documentElement){B=E.createDocument(Xn,"template",null);try{B.documentElement.innerHTML=Co?C:he}catch{}}let Ye=B.body||B.documentElement;return b&&L&&Ye.insertBefore(n.createTextNode(L),Ye.childNodes[0]||null),Xn===Be?R.call(B,ne?"html":"body")[0]:ne?B.documentElement:Ye},dp=function(b){return _.call(b.ownerDocument||b,b,u.SHOW_ELEMENT|u.SHOW_COMMENT|u.SHOW_TEXT|u.SHOW_PROCESSING_INSTRUCTION,null)},l1=function(b){return b instanceof d&&(typeof b.nodeName!="string"||typeof b.textContent!="string"||typeof b.removeChild!="function"||!(b.attributes instanceof c)||typeof b.removeAttribute!="function"||typeof b.setAttribute!="function"||typeof b.namespaceURI!="string"||typeof b.insertBefore!="function"||typeof b.hasChildNodes!="function")},fp=function(b){return typeof a=="function"&&b instanceof a},$n=function(b,B,L){T[b]&&wa(T[b],he=>{he.call(t,B,L,Zn)})},pp=function(b){let B=null;if($n("beforeSanitizeElements",b,null),l1(b))return bo(b),!0;let L=Pe(b.nodeName);if($n("uponSanitizeElement",b,{tagName:L,allowedTags:D}),b.hasChildNodes()&&!fp(b.firstElementChild)&&yt(/<[/\w]/g,b.innerHTML)&&yt(/<[/\w]/g,b.textContent))return bo(b),!0;if(!D[L]||ge[L]){if(!ge[L]&&hp(L)&&(J.tagNameCheck instanceof RegExp&&yt(J.tagNameCheck,L)||J.tagNameCheck instanceof Function&&J.tagNameCheck(L)))return!1;if(wo&&!Cn[L]){let he=g(b)||b.parentNode,Ye=m(b)||b.childNodes;if(Ye&&he){let De=Ye.length;for(let En=De-1;En>=0;--En)he.insertBefore(v(Ye[En],!0),x(b))}}return bo(b),!0}return b instanceof l&&!a1(b)||(L==="noscript"||L==="noembed"||L==="noframes")&&yt(/<\/no(script|embed|frames)/i,b.innerHTML)?(bo(b),!0):(ae&&b.nodeType===3&&(B=b.textContent,wa([V,j,Q],he=>{B=Bi(B,he," ")}),b.textContent!==B&&(Ui(t.removed,{element:b.cloneNode()}),b.textContent=B)),$n("afterSanitizeElements",b,null),!1)},gp=function(b,B,L){if(yo&&(B==="id"||B==="name")&&(L in n||L in i1))return!1;if(!(se&&!ie[B]&&yt(z,B))&&!(X&&yt(Y,B))){if(!H[B]||ie[B]){if(!(hp(b)&&(J.tagNameCheck instanceof RegExp&&yt(J.tagNameCheck,b)||J.tagNameCheck instanceof Function&&J.tagNameCheck(b))&&(J.attributeNameCheck instanceof RegExp&&yt(J.attributeNameCheck,B)||J.attributeNameCheck instanceof Function&&J.attributeNameCheck(B))||B==="is"&&J.allowCustomizedBuiltInElements&&(J.tagNameCheck instanceof RegExp&&yt(J.tagNameCheck,L)||J.tagNameCheck instanceof Function&&J.tagNameCheck(L))))return!1}else if(!xo[B]&&!yt(I,Bi(L,q,""))&&!((B==="src"||B==="xlink:href"||B==="href")&&b!=="script"&&wS(L,"data:")===0&&Ai[b])&&!(Re&&!yt(U,Bi(L,q,"")))&&L)return!1}return!0},hp=function(b){return b!=="annotation-xml"&&Fg(b,k)},mp=function(b){$n("beforeSanitizeAttributes",b,null);let{attributes:B}=b;if(!B)return;let L={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:H},he=B.length;for(;he--;){let Ye=B[he],{name:De,namespaceURI:En,value:Rn}=Ye,rn=Pe(De),ct=De==="value"?Rn:xS(Rn);if(L.attrName=rn,L.attrValue=ct,L.keepAttr=!0,L.forceKeepAttr=void 0,$n("uponSanitizeAttribute",b,L),ct=L.attrValue,L.forceKeepAttr||(gu(De,b),!L.keepAttr))continue;if(!ce&&yt(/\/>/i,ct)){gu(De,b);continue}ae&&wa([V,j,Q],yp=>{ct=Bi(ct,yp," ")});let vp=Pe(b.nodeName);if(gp(vp,rn,ct)){if(xn&&(rn==="id"||rn==="name")&&(gu(De,b),ct=Yn+ct),w&&typeof h=="object"&&typeof h.getAttributeType=="function"&&!En)switch(h.getAttributeType(vp,rn)){case"TrustedHTML":{ct=w.createHTML(ct);break}case"TrustedScriptURL":{ct=w.createScriptURL(ct);break}}try{En?b.setAttributeNS(En,De,ct):b.setAttribute(De,ct),Ig(t.removed)}catch{}}}$n("afterSanitizeAttributes",b,null)},u1=function b(B){let L=null,he=dp(B);for($n("beforeSanitizeShadowDOM",B,null);L=he.nextNode();)$n("uponSanitizeShadowNode",L,null),!pp(L)&&(L.content instanceof i&&b(L.content),mp(L));$n("afterSanitizeShadowDOM",B,null)};return t.sanitize=function(b){let B=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},L=null,he=null,Ye=null,De=null;if(Co=!b,Co&&(b="<!-->"),typeof b!="string"&&!fp(b))if(typeof b.toString=="function"){if(b=b.toString(),typeof b!="string")throw Wi("dirty is not a string, aborting")}else throw Wi("toString is not a function");if(!t.isSupported)return b;if(ze||pu(B),t.removed=[],typeof b=="string"&&(Sn=!1),Sn){if(b.nodeName){let rn=Pe(b.nodeName);if(!D[rn]||ge[rn])throw Wi("root node is forbidden and cannot be sanitized in-place")}}else if(b instanceof a)L=cp("<!---->"),he=L.ownerDocument.importNode(b,!0),he.nodeType===1&&he.nodeName==="BODY"||he.nodeName==="HTML"?L=he:L.appendChild(he);else{if(!xe&&!ae&&!ne&&b.indexOf("<")===-1)return w&&Nt?w.createHTML(b):b;if(L=cp(b),!L)return xe?null:Nt?C:""}L&&Ae&&bo(L.firstChild);let En=dp(Sn?b:L);for(;Ye=En.nextNode();)pp(Ye)||(Ye.content instanceof i&&u1(Ye.content),mp(Ye));if(Sn)return b;if(xe){if(Qe)for(De=P.call(L.ownerDocument);L.firstChild;)De.appendChild(L.firstChild);else De=L;return(H.shadowroot||H.shadowrootmode)&&(De=O.call(r,De,!0)),De}let Rn=ne?L.outerHTML:L.innerHTML;return ne&&D["!doctype"]&&L.ownerDocument&&L.ownerDocument.doctype&&L.ownerDocument.doctype.name&&yt(Cv,L.ownerDocument.doctype.name)&&(Rn="<!DOCTYPE "+L.ownerDocument.doctype.name+`>
`+Rn),ae&&wa([V,j,Q],rn=>{Rn=Bi(Rn,rn," ")}),w&&Nt?w.createHTML(Rn):Rn},t.setConfig=function(){let b=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};pu(b),ze=!0},t.clearConfig=function(){Zn=null,ze=!1},t.isValidAttribute=function(b,B,L){Zn||pu({});let he=Pe(b),Ye=Pe(B);return gp(he,Ye,L)},t.addHook=function(b,B){typeof B=="function"&&(T[b]=T[b]||[],Ui(T[b],B))},t.removeHook=function(b){if(T[b])return Ig(T[b])},t.removeHooks=function(b){T[b]&&(T[b]=[])},t.removeAllHooks=function(){T={}},t}var DS=bv(),LS=e=>{switch(e){case"success":return jS;case"info":return VS;case"warning":return zS;case"error":return HS;default:return null}},IS=Array(12).fill(0),FS=({visible:e})=>A.createElement("div",{className:"sonner-loading-wrapper","data-visible":e},A.createElement("div",{className:"sonner-spinner"},IS.map((t,n)=>A.createElement("div",{className:"sonner-loading-bar",key:`spinner-bar-${n}`})))),jS=A.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},A.createElement("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z",clipRule:"evenodd"})),zS=A.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor",height:"20",width:"20"},A.createElement("path",{fillRule:"evenodd",d:"M9.401 3.003c1.155-2 4.043-2 5.197 0l7.355 12.748c1.154 2-.29 4.5-2.599 4.5H4.645c-2.309 0-3.752-2.5-2.598-4.5L9.4 3.003zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z",clipRule:"evenodd"})),VS=A.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},A.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z",clipRule:"evenodd"})),HS=A.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},A.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z",clipRule:"evenodd"})),US=()=>{let[e,t]=A.useState(!1);return A.useEffect(()=>{let n=()=>{t(document.hidden)};return document.addEventListener("visibilitychange",n),()=>window.removeEventListener("visibilitychange",n)},[]),e},wd=1,BS=class{constructor(){this.subscribe=e=>(this.subscribers.push(e),()=>{let t=this.subscribers.indexOf(e);this.subscribers.splice(t,1)}),this.publish=e=>{this.subscribers.forEach(t=>t(e))},this.addToast=e=>{this.publish(e),this.toasts=[...this.toasts,e]},this.create=e=>{var t;let{message:n,...r}=e,o=typeof(e==null?void 0:e.id)=="number"||((t=e.id)==null?void 0:t.length)>0?e.id:wd++,i=this.toasts.find(a=>a.id===o),s=e.dismissible===void 0?!0:e.dismissible;return i?this.toasts=this.toasts.map(a=>a.id===o?(this.publish({...a,...e,id:o,title:n}),{...a,...e,id:o,dismissible:s,title:n}):a):this.addToast({title:n,...r,dismissible:s,id:o}),o},this.dismiss=e=>(e||this.toasts.forEach(t=>{this.subscribers.forEach(n=>n({id:t.id,dismiss:!0}))}),this.subscribers.forEach(t=>t({id:e,dismiss:!0})),e),this.message=(e,t)=>this.create({...t,message:e}),this.error=(e,t)=>this.create({...t,message:e,type:"error"}),this.success=(e,t)=>this.create({...t,type:"success",message:e}),this.info=(e,t)=>this.create({...t,type:"info",message:e}),this.warning=(e,t)=>this.create({...t,type:"warning",message:e}),this.loading=(e,t)=>this.create({...t,type:"loading",message:e}),this.promise=(e,t)=>{if(!t)return;let n;t.loading!==void 0&&(n=this.create({...t,promise:e,type:"loading",message:t.loading,description:typeof t.description!="function"?t.description:void 0}));let r=e instanceof Promise?e:e(),o=n!==void 0;return r.then(i=>{if(i&&typeof i.ok=="boolean"&&!i.ok){o=!1;let s=typeof t.error=="function"?t.error(`HTTP error! status: ${i.status}`):t.error,a=typeof t.description=="function"?t.description(`HTTP error! status: ${i.status}`):t.description;this.create({id:n,type:"error",message:s,description:a})}else if(t.success!==void 0){o=!1;let s=typeof t.success=="function"?t.success(i):t.success,a=typeof t.description=="function"?t.description(i):t.description;this.create({id:n,type:"success",message:s,description:a})}}).catch(i=>{if(t.error!==void 0){o=!1;let s=typeof t.error=="function"?t.error(i):t.error,a=typeof t.description=="function"?t.description(i):t.description;this.create({id:n,type:"error",message:s,description:a})}}).finally(()=>{var i;o&&(this.dismiss(n),n=void 0),(i=t.finally)==null||i.call(t)}),n},this.custom=(e,t)=>{let n=(t==null?void 0:t.id)||wd++;return this.create({jsx:e(n),id:n,...t}),n},this.subscribers=[],this.toasts=[]}},Gt=new BS,WS=(e,t)=>{let n=(t==null?void 0:t.id)||wd++;return Gt.addToast({title:e,...t,id:n}),n},GS=WS,Rl=Object.assign(GS,{success:Gt.success,info:Gt.info,warning:Gt.warning,error:Gt.error,custom:Gt.custom,message:Gt.message,promise:Gt.promise,dismiss:Gt.dismiss,loading:Gt.loading});function KS(e,{insertAt:t}={}){if(!e||typeof document>"u")return;let n=document.head||document.getElementsByTagName("head")[0],r=document.createElement("style");r.type="text/css",t==="top"&&n.firstChild?n.insertBefore(r,n.firstChild):n.appendChild(r),r.styleSheet?r.styleSheet.cssText=e:r.appendChild(document.createTextNode(e))}KS(`:where(html[dir="ltr"]),:where([data-sonner-toaster][dir="ltr"]){--toast-icon-margin-start: -3px;--toast-icon-margin-end: 4px;--toast-svg-margin-start: -1px;--toast-svg-margin-end: 0px;--toast-button-margin-start: auto;--toast-button-margin-end: 0;--toast-close-button-start: 0;--toast-close-button-end: unset;--toast-close-button-transform: translate(-35%, -35%)}:where(html[dir="rtl"]),:where([data-sonner-toaster][dir="rtl"]){--toast-icon-margin-start: 4px;--toast-icon-margin-end: -3px;--toast-svg-margin-start: 0px;--toast-svg-margin-end: -1px;--toast-button-margin-start: 0;--toast-button-margin-end: auto;--toast-close-button-start: unset;--toast-close-button-end: 0;--toast-close-button-transform: translate(35%, -35%)}:where([data-sonner-toaster]){position:fixed;width:var(--width);font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;--gray1: hsl(0, 0%, 99%);--gray2: hsl(0, 0%, 97.3%);--gray3: hsl(0, 0%, 95.1%);--gray4: hsl(0, 0%, 93%);--gray5: hsl(0, 0%, 90.9%);--gray6: hsl(0, 0%, 88.7%);--gray7: hsl(0, 0%, 85.8%);--gray8: hsl(0, 0%, 78%);--gray9: hsl(0, 0%, 56.1%);--gray10: hsl(0, 0%, 52.3%);--gray11: hsl(0, 0%, 43.5%);--gray12: hsl(0, 0%, 9%);--border-radius: 8px;box-sizing:border-box;padding:0;margin:0;list-style:none;outline:none;z-index:999999999}:where([data-sonner-toaster][data-x-position="right"]){right:max(var(--offset),env(safe-area-inset-right))}:where([data-sonner-toaster][data-x-position="left"]){left:max(var(--offset),env(safe-area-inset-left))}:where([data-sonner-toaster][data-x-position="center"]){left:50%;transform:translate(-50%)}:where([data-sonner-toaster][data-y-position="top"]){top:max(var(--offset),env(safe-area-inset-top))}:where([data-sonner-toaster][data-y-position="bottom"]){bottom:max(var(--offset),env(safe-area-inset-bottom))}:where([data-sonner-toast]){--y: translateY(100%);--lift-amount: calc(var(--lift) * var(--gap));z-index:var(--z-index);position:absolute;opacity:0;transform:var(--y);filter:blur(0);touch-action:none;transition:transform .4s,opacity .4s,height .4s,box-shadow .2s;box-sizing:border-box;outline:none;overflow-wrap:anywhere}:where([data-sonner-toast][data-styled="true"]){padding:16px;background:var(--normal-bg);border:1px solid var(--normal-border);color:var(--normal-text);border-radius:var(--border-radius);box-shadow:0 4px 12px #0000001a;width:var(--width);font-size:13px;display:flex;align-items:center;gap:6px}:where([data-sonner-toast]:focus-visible){box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast][data-y-position="top"]){top:0;--y: translateY(-100%);--lift: 1;--lift-amount: calc(1 * var(--gap))}:where([data-sonner-toast][data-y-position="bottom"]){bottom:0;--y: translateY(100%);--lift: -1;--lift-amount: calc(var(--lift) * var(--gap))}:where([data-sonner-toast]) :where([data-description]){font-weight:400;line-height:1.4;color:inherit}:where([data-sonner-toast]) :where([data-title]){font-weight:500;line-height:1.5;color:inherit}:where([data-sonner-toast]) :where([data-icon]){display:flex;height:16px;width:16px;position:relative;justify-content:flex-start;align-items:center;flex-shrink:0;margin-left:var(--toast-icon-margin-start);margin-right:var(--toast-icon-margin-end)}:where([data-sonner-toast][data-promise="true"]) :where([data-icon])>svg{opacity:0;transform:scale(.8);transform-origin:center;animation:sonner-fade-in .3s ease forwards}:where([data-sonner-toast]) :where([data-icon])>*{flex-shrink:0}:where([data-sonner-toast]) :where([data-icon]) svg{margin-left:var(--toast-svg-margin-start);margin-right:var(--toast-svg-margin-end)}:where([data-sonner-toast]) :where([data-content]){display:flex;flex-direction:column;gap:2px}[data-sonner-toast][data-styled=true] [data-button]{border-radius:4px;padding-left:8px;padding-right:8px;height:24px;font-size:12px;color:var(--normal-bg);background:var(--normal-text);margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end);border:none;cursor:pointer;outline:none;display:flex;align-items:center;flex-shrink:0;transition:opacity .4s,box-shadow .2s}:where([data-sonner-toast]) :where([data-button]):focus-visible{box-shadow:0 0 0 2px #0006}:where([data-sonner-toast]) :where([data-button]):first-of-type{margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end)}:where([data-sonner-toast]) :where([data-cancel]){color:var(--normal-text);background:rgba(0,0,0,.08)}:where([data-sonner-toast][data-theme="dark"]) :where([data-cancel]){background:rgba(255,255,255,.3)}:where([data-sonner-toast]) :where([data-close-button]){position:absolute;left:var(--toast-close-button-start);right:var(--toast-close-button-end);top:0;height:20px;width:20px;display:flex;justify-content:center;align-items:center;padding:0;background:var(--gray1);color:var(--gray12);border:1px solid var(--gray4);transform:var(--toast-close-button-transform);border-radius:50%;cursor:pointer;z-index:1;transition:opacity .1s,background .2s,border-color .2s}:where([data-sonner-toast]) :where([data-close-button]):focus-visible{box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast]) :where([data-disabled="true"]){cursor:not-allowed}:where([data-sonner-toast]):hover :where([data-close-button]):hover{background:var(--gray2);border-color:var(--gray5)}:where([data-sonner-toast][data-swiping="true"]):before{content:"";position:absolute;left:0;right:0;height:100%;z-index:-1}:where([data-sonner-toast][data-y-position="top"][data-swiping="true"]):before{bottom:50%;transform:scaleY(3) translateY(50%)}:where([data-sonner-toast][data-y-position="bottom"][data-swiping="true"]):before{top:50%;transform:scaleY(3) translateY(-50%)}:where([data-sonner-toast][data-swiping="false"][data-removed="true"]):before{content:"";position:absolute;inset:0;transform:scaleY(2)}:where([data-sonner-toast]):after{content:"";position:absolute;left:0;height:calc(var(--gap) + 1px);bottom:100%;width:100%}:where([data-sonner-toast][data-mounted="true"]){--y: translateY(0);opacity:1}:where([data-sonner-toast][data-expanded="false"][data-front="false"]){--scale: var(--toasts-before) * .05 + 1;--y: translateY(calc(var(--lift-amount) * var(--toasts-before))) scale(calc(-1 * var(--scale)));height:var(--front-toast-height)}:where([data-sonner-toast])>*{transition:opacity .4s}:where([data-sonner-toast][data-expanded="false"][data-front="false"][data-styled="true"])>*{opacity:0}:where([data-sonner-toast][data-visible="false"]){opacity:0;pointer-events:none}:where([data-sonner-toast][data-mounted="true"][data-expanded="true"]){--y: translateY(calc(var(--lift) * var(--offset)));height:var(--initial-height)}:where([data-sonner-toast][data-removed="true"][data-front="true"][data-swipe-out="false"]){--y: translateY(calc(var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed="true"][data-front="false"][data-swipe-out="false"][data-expanded="true"]){--y: translateY(calc(var(--lift) * var(--offset) + var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed="true"][data-front="false"][data-swipe-out="false"][data-expanded="false"]){--y: translateY(40%);opacity:0;transition:transform .5s,opacity .2s}:where([data-sonner-toast][data-removed="true"][data-front="false"]):before{height:calc(var(--initial-height) + 20%)}[data-sonner-toast][data-swiping=true]{transform:var(--y) translateY(var(--swipe-amount, 0px));transition:none}[data-sonner-toast][data-swipe-out=true][data-y-position=bottom],[data-sonner-toast][data-swipe-out=true][data-y-position=top]{animation:swipe-out .2s ease-out forwards}@keyframes swipe-out{0%{transform:translateY(calc(var(--lift) * var(--offset) + var(--swipe-amount)));opacity:1}to{transform:translateY(calc(var(--lift) * var(--offset) + var(--swipe-amount) + var(--lift) * -100%));opacity:0}}@media (max-width: 600px){[data-sonner-toaster]{position:fixed;--mobile-offset: 16px;right:var(--mobile-offset);left:var(--mobile-offset);width:100%}[data-sonner-toaster] [data-sonner-toast]{left:0;right:0;width:calc(100% - var(--mobile-offset) * 2)}[data-sonner-toaster][data-x-position=left]{left:var(--mobile-offset)}[data-sonner-toaster][data-y-position=bottom]{bottom:20px}[data-sonner-toaster][data-y-position=top]{top:20px}[data-sonner-toaster][data-x-position=center]{left:var(--mobile-offset);right:var(--mobile-offset);transform:none}}[data-sonner-toaster][data-theme=light]{--normal-bg: #fff;--normal-border: var(--gray4);--normal-text: var(--gray12);--success-bg: hsl(143, 85%, 96%);--success-border: hsl(145, 92%, 91%);--success-text: hsl(140, 100%, 27%);--info-bg: hsl(208, 100%, 97%);--info-border: hsl(221, 91%, 91%);--info-text: hsl(210, 92%, 45%);--warning-bg: hsl(49, 100%, 97%);--warning-border: hsl(49, 91%, 91%);--warning-text: hsl(31, 92%, 45%);--error-bg: hsl(359, 100%, 97%);--error-border: hsl(359, 100%, 94%);--error-text: hsl(360, 100%, 45%)}[data-sonner-toaster][data-theme=light] [data-sonner-toast][data-invert=true]{--normal-bg: #000;--normal-border: hsl(0, 0%, 20%);--normal-text: var(--gray1)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast][data-invert=true]{--normal-bg: #fff;--normal-border: var(--gray3);--normal-text: var(--gray12)}[data-sonner-toaster][data-theme=dark]{--normal-bg: #000;--normal-border: hsl(0, 0%, 20%);--normal-text: var(--gray1);--success-bg: hsl(150, 100%, 6%);--success-border: hsl(147, 100%, 12%);--success-text: hsl(150, 86%, 65%);--info-bg: hsl(215, 100%, 6%);--info-border: hsl(223, 100%, 12%);--info-text: hsl(216, 87%, 65%);--warning-bg: hsl(64, 100%, 6%);--warning-border: hsl(60, 100%, 12%);--warning-text: hsl(46, 87%, 65%);--error-bg: hsl(358, 76%, 10%);--error-border: hsl(357, 89%, 16%);--error-text: hsl(358, 100%, 81%)}[data-rich-colors=true] [data-sonner-toast][data-type=success],[data-rich-colors=true] [data-sonner-toast][data-type=success] [data-close-button]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true] [data-sonner-toast][data-type=info],[data-rich-colors=true] [data-sonner-toast][data-type=info] [data-close-button]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true] [data-sonner-toast][data-type=warning],[data-rich-colors=true] [data-sonner-toast][data-type=warning] [data-close-button]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true] [data-sonner-toast][data-type=error],[data-rich-colors=true] [data-sonner-toast][data-type=error] [data-close-button]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}.sonner-loading-wrapper{--size: 16px;height:var(--size);width:var(--size);position:absolute;inset:0;z-index:10}.sonner-loading-wrapper[data-visible=false]{transform-origin:center;animation:sonner-fade-out .2s ease forwards}.sonner-spinner{position:relative;top:50%;left:50%;height:var(--size);width:var(--size)}.sonner-loading-bar{animation:sonner-spin 1.2s linear infinite;background:var(--gray11);border-radius:6px;height:8%;left:-10%;position:absolute;top:-3.9%;width:24%}.sonner-loading-bar:nth-child(1){animation-delay:-1.2s;transform:rotate(.0001deg) translate(146%)}.sonner-loading-bar:nth-child(2){animation-delay:-1.1s;transform:rotate(30deg) translate(146%)}.sonner-loading-bar:nth-child(3){animation-delay:-1s;transform:rotate(60deg) translate(146%)}.sonner-loading-bar:nth-child(4){animation-delay:-.9s;transform:rotate(90deg) translate(146%)}.sonner-loading-bar:nth-child(5){animation-delay:-.8s;transform:rotate(120deg) translate(146%)}.sonner-loading-bar:nth-child(6){animation-delay:-.7s;transform:rotate(150deg) translate(146%)}.sonner-loading-bar:nth-child(7){animation-delay:-.6s;transform:rotate(180deg) translate(146%)}.sonner-loading-bar:nth-child(8){animation-delay:-.5s;transform:rotate(210deg) translate(146%)}.sonner-loading-bar:nth-child(9){animation-delay:-.4s;transform:rotate(240deg) translate(146%)}.sonner-loading-bar:nth-child(10){animation-delay:-.3s;transform:rotate(270deg) translate(146%)}.sonner-loading-bar:nth-child(11){animation-delay:-.2s;transform:rotate(300deg) translate(146%)}.sonner-loading-bar:nth-child(12){animation-delay:-.1s;transform:rotate(330deg) translate(146%)}@keyframes sonner-fade-in{0%{opacity:0;transform:scale(.8)}to{opacity:1;transform:scale(1)}}@keyframes sonner-fade-out{0%{opacity:1;transform:scale(1)}to{opacity:0;transform:scale(.8)}}@keyframes sonner-spin{0%{opacity:1}to{opacity:.15}}@media (prefers-reduced-motion){[data-sonner-toast],[data-sonner-toast]>*,.sonner-loading-bar{transition:none!important;animation:none!important}}.sonner-loader{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);transform-origin:center;transition:opacity .2s,transform .2s}.sonner-loader[data-visible=false]{opacity:0;transform:scale(.8) translate(-50%,-50%)}
`);function Ca(e){return e.label!==void 0&&typeof e.onClick=="function"}var QS=3,YS="32px",qS=4e3,XS=356,ZS=14,JS=20,eC=200;function tC(...e){return e.filter(Boolean).join(" ")}var nC=e=>{var t,n,r,o,i,s,a;let{invert:l,toast:u,unstyled:c,interacting:d,setHeights:f,visibleToasts:h,heights:y,index:v,toasts:x,expanded:m,removeToast:g,closeButton:w,style:C,cancelButtonStyle:E,actionButtonStyle:_,className:P="",descriptionClassName:R="",duration:O,position:T,gap:V,loadingIcon:j,expandByDefault:Q,classNames:z,icons:Y,closeButtonAriaLabel:U="Close toast",pauseWhenPageIsHidden:q,cn:k}=e,[I,D]=A.useState(!1),[G,H]=A.useState(!1),[de,J]=A.useState(!1),[ge,ie]=A.useState(!1),[X,se]=A.useState(0),[Re,ce]=A.useState(0),ae=A.useRef(null),ne=A.useRef(null),ze=v===0,Ae=v+1<=h,xe=u.type,Qe=u.dismissible!==!1,Nt=u.className||"",yo=u.descriptionClassName||"",xn=A.useMemo(()=>y.findIndex(ee=>ee.toastId===u.id)||0,[y,u.id]),Yn=A.useMemo(()=>{var ee;return(ee=u.closeButton)!=null?ee:w},[u.closeButton,w]),wo=A.useMemo(()=>u.duration||O||qS,[u.duration,O]),Sn=A.useRef(0),vt=A.useRef(0),Cn=A.useRef(0),bn=A.useRef(null),[Ai,ea]=T.split("-"),xo=A.useMemo(()=>y.reduce((ee,Me,ve)=>ve>=xn?ee:ee+Me.height,0),[y,xn]),Mi=US(),So=u.invert||l,qn=xe==="loading";vt.current=A.useMemo(()=>xn*V+xo,[xn,xo]),A.useEffect(()=>{D(!0)},[]),A.useLayoutEffect(()=>{if(!I)return;let ee=ne.current,Me=ee.style.height;ee.style.height="auto";let ve=ee.getBoundingClientRect().height;ee.style.height=Me,ce(ve),f(Ot=>Ot.find(Tt=>Tt.toastId===u.id)?Ot.map(Tt=>Tt.toastId===u.id?{...Tt,height:ve}:Tt):[{toastId:u.id,height:ve,position:u.position},...Ot])},[I,u.title,u.description,f,u.id]);let Be=A.useCallback(()=>{H(!0),se(vt.current),f(ee=>ee.filter(Me=>Me.toastId!==u.id)),setTimeout(()=>{g(u)},eC)},[u,g,f,vt]);A.useEffect(()=>{if(u.promise&&xe==="loading"||u.duration===1/0||u.type==="loading")return;let ee,Me=wo;return m||d||q&&Mi?(()=>{if(Cn.current<Sn.current){let ve=new Date().getTime()-Sn.current;Me=Me-ve}Cn.current=new Date().getTime()})():Me!==1/0&&(Sn.current=new Date().getTime(),ee=setTimeout(()=>{var ve;(ve=u.onAutoClose)==null||ve.call(u,u),Be()},Me)),()=>clearTimeout(ee)},[m,d,Q,u,wo,Be,u.promise,xe,q,Mi]),A.useEffect(()=>{let ee=ne.current;if(ee){let Me=ee.getBoundingClientRect().height;return ce(Me),f(ve=>[{toastId:u.id,height:Me,position:u.position},...ve]),()=>f(ve=>ve.filter(Ot=>Ot.toastId!==u.id))}},[f,u.id]),A.useEffect(()=>{u.delete&&Be()},[Be,u.delete]);function Xn(){return Y!=null&&Y.loading?A.createElement("div",{className:"sonner-loader","data-visible":xe==="loading"},Y.loading):j?A.createElement("div",{className:"sonner-loader","data-visible":xe==="loading"},j):A.createElement(FS,{visible:xe==="loading"})}function Co(ee){return{__html:DS.sanitize(ee)}}return A.createElement("li",{"aria-live":u.important?"assertive":"polite","aria-atomic":"true",role:"status",tabIndex:0,ref:ne,className:k(P,Nt,z==null?void 0:z.toast,(t=u==null?void 0:u.classNames)==null?void 0:t.toast,z==null?void 0:z.default,z==null?void 0:z[xe],(n=u==null?void 0:u.classNames)==null?void 0:n[xe]),"data-sonner-toast":"","data-styled":!(u.jsx||u.unstyled||c),"data-mounted":I,"data-promise":!!u.promise,"data-removed":G,"data-visible":Ae,"data-y-position":Ai,"data-x-position":ea,"data-index":v,"data-front":ze,"data-swiping":de,"data-dismissible":Qe,"data-type":xe,"data-invert":So,"data-swipe-out":ge,"data-expanded":!!(m||Q&&I),style:{"--index":v,"--toasts-before":v,"--z-index":x.length-v,"--offset":`${G?X:vt.current}px`,"--initial-height":Q?"auto":`${Re}px`,...C,...u.style},onPointerDown:ee=>{qn||!Qe||(ae.current=new Date,se(vt.current),ee.target.setPointerCapture(ee.pointerId),ee.target.tagName!=="BUTTON"&&(J(!0),bn.current={x:ee.clientX,y:ee.clientY}))},onPointerUp:()=>{var ee,Me,ve,Ot;if(ge||!Qe)return;bn.current=null;let Tt=Number(((ee=ne.current)==null?void 0:ee.style.getPropertyValue("--swipe-amount").replace("px",""))||0),Pe=new Date().getTime()-((Me=ae.current)==null?void 0:Me.getTime()),Zn=Math.abs(Tt)/Pe;if(Math.abs(Tt)>=JS||Zn>.11){se(vt.current),(ve=u.onDismiss)==null||ve.call(u,u),Be(),ie(!0);return}(Ot=ne.current)==null||Ot.style.setProperty("--swipe-amount","0px"),J(!1)},onPointerMove:ee=>{var Me;if(!bn.current||!Qe)return;let ve=ee.clientY-bn.current.y,Ot=ee.clientX-bn.current.x,Tt=(Ai==="top"?Math.min:Math.max)(0,ve),Pe=ee.pointerType==="touch"?10:2;Math.abs(Tt)>Pe?(Me=ne.current)==null||Me.style.setProperty("--swipe-amount",`${ve}px`):Math.abs(Ot)>Pe&&(bn.current=null)}},Yn&&!u.jsx?A.createElement("button",{"aria-label":U,"data-disabled":qn,"data-close-button":!0,onClick:qn||!Qe?()=>{}:()=>{var ee;Be(),(ee=u.onDismiss)==null||ee.call(u,u)},className:k(z==null?void 0:z.closeButton,(r=u==null?void 0:u.classNames)==null?void 0:r.closeButton)},A.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"12",height:"12",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"},A.createElement("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),A.createElement("line",{x1:"6",y1:"6",x2:"18",y2:"18"}))):null,u.jsx||A.isValidElement(u.title)?u.jsx||u.title:A.createElement(A.Fragment,null,xe||u.icon||u.promise?A.createElement("div",{"data-icon":"",className:k(z==null?void 0:z.icon)},u.promise||u.type==="loading"&&!u.icon?u.icon||Xn():null,u.type!=="loading"?u.icon||(Y==null?void 0:Y[xe])||LS(xe):null):null,A.createElement("div",{"data-content":"",className:k(z==null?void 0:z.content)},A.createElement("div",{"data-title":"",className:k(z==null?void 0:z.title,(o=u==null?void 0:u.classNames)==null?void 0:o.title),dangerouslySetInnerHTML:Co(u.title)}),u.description?A.createElement("div",{"data-description":"",className:k(R,yo,z==null?void 0:z.description,(i=u==null?void 0:u.classNames)==null?void 0:i.description),dangerouslySetInnerHTML:Co(u.description)}):null),A.isValidElement(u.cancel)?u.cancel:u.cancel&&Ca(u.cancel)?A.createElement("button",{"data-button":!0,"data-cancel":!0,style:u.cancelButtonStyle||E,onClick:ee=>{Ca(u.cancel)&&Qe&&(Be(),u.cancel.onClick(ee))},className:k(z==null?void 0:z.cancelButton,(s=u==null?void 0:u.classNames)==null?void 0:s.cancelButton)},u.cancel.label):null,A.isValidElement(u.action)?u.action:u.action&&Ca(u.action)?A.createElement("button",{"data-button":"",style:u.actionButtonStyle||_,onClick:ee=>{Ca(u.action)&&(u.action.onClick(ee),!ee.defaultPrevented&&Be())},className:k(z==null?void 0:z.actionButton,(a=u==null?void 0:u.classNames)==null?void 0:a.actionButton)},u.action.label):null))};function Bg(){if(typeof window>"u"||typeof document>"u")return"ltr";let e=document.documentElement.getAttribute("dir");return e==="auto"||!e?window.getComputedStyle(document.documentElement).direction:e}var rC=e=>{let{invert:t,position:n="bottom-right",hotkey:r=["altKey","KeyT"],expand:o,closeButton:i,className:s,offset:a,theme:l="light",richColors:u,duration:c,style:d,visibleToasts:f=QS,toastOptions:h,dir:y=Bg(),gap:v=ZS,loadingIcon:x,icons:m,containerAriaLabel:g="Notifications",pauseWhenPageIsHidden:w,cn:C=tC}=e,[E,_]=A.useState([]),P=A.useMemo(()=>Array.from(new Set([n].concat(E.filter(G=>G.position).map(G=>G.position)))),[E,n]),[R,O]=A.useState([]),[T,V]=A.useState(!1),[j,Q]=A.useState(!1),[z,Y]=A.useState(l!=="system"?l:typeof window<"u"&&window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"),U=A.useRef(null),q=r.join("+").replace(/Key/g,"").replace(/Digit/g,""),k=A.useRef(null),I=A.useRef(!1),D=A.useCallback(G=>_(H=>H.filter(({id:de})=>de!==G.id)),[]);return A.useEffect(()=>Gt.subscribe(G=>{if(G.dismiss){_(H=>H.map(de=>de.id===G.id?{...de,delete:!0}:de));return}setTimeout(()=>{iv.flushSync(()=>{_(H=>{let de=H.findIndex(J=>J.id===G.id);return de!==-1?[...H.slice(0,de),{...H[de],...G},...H.slice(de+1)]:[G,...H]})})})}),[]),A.useEffect(()=>{if(l!=="system"){Y(l);return}l==="system"&&(window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?Y("dark"):Y("light")),typeof window<"u"&&window.matchMedia("(prefers-color-scheme: dark)").addEventListener("change",({matches:G})=>{Y(G?"dark":"light")})},[l]),A.useEffect(()=>{E.length<=1&&V(!1)},[E]),A.useEffect(()=>{let G=H=>{var de,J;r.every(ge=>H[ge]||H.code===ge)&&(V(!0),(de=U.current)==null||de.focus()),H.code==="Escape"&&(document.activeElement===U.current||(J=U.current)!=null&&J.contains(document.activeElement))&&V(!1)};return document.addEventListener("keydown",G),()=>document.removeEventListener("keydown",G)},[r]),A.useEffect(()=>{if(U.current)return()=>{k.current&&(k.current.focus({preventScroll:!0}),k.current=null,I.current=!1)}},[U.current]),E.length?A.createElement("section",{"aria-label":`${g} ${q}`,tabIndex:-1},P.map((G,H)=>{var de;let[J,ge]=G.split("-");return A.createElement("ol",{key:G,dir:y==="auto"?Bg():y,tabIndex:-1,ref:U,className:s,"data-sonner-toaster":!0,"data-theme":z,"data-rich-colors":u,"data-y-position":J,"data-x-position":ge,style:{"--front-toast-height":`${((de=R[0])==null?void 0:de.height)||0}px`,"--offset":typeof a=="number"?`${a}px`:a||YS,"--width":`${XS}px`,"--gap":`${v}px`,...d},onBlur:ie=>{I.current&&!ie.currentTarget.contains(ie.relatedTarget)&&(I.current=!1,k.current&&(k.current.focus({preventScroll:!0}),k.current=null))},onFocus:ie=>{ie.target instanceof HTMLElement&&ie.target.dataset.dismissible==="false"||I.current||(I.current=!0,k.current=ie.relatedTarget)},onMouseEnter:()=>V(!0),onMouseMove:()=>V(!0),onMouseLeave:()=>{j||V(!1)},onPointerDown:ie=>{ie.target instanceof HTMLElement&&ie.target.dataset.dismissible==="false"||Q(!0)},onPointerUp:()=>Q(!1)},E.filter(ie=>!ie.position&&H===0||ie.position===G).map((ie,X)=>{var se,Re;return A.createElement(nC,{key:ie.id,icons:m,index:X,toast:ie,duration:(se=h==null?void 0:h.duration)!=null?se:c,className:h==null?void 0:h.className,descriptionClassName:h==null?void 0:h.descriptionClassName,invert:t,visibleToasts:f,closeButton:(Re=h==null?void 0:h.closeButton)!=null?Re:i,interacting:j,position:G,style:h==null?void 0:h.style,unstyled:h==null?void 0:h.unstyled,classNames:h==null?void 0:h.classNames,cancelButtonStyle:h==null?void 0:h.cancelButtonStyle,actionButtonStyle:h==null?void 0:h.actionButtonStyle,removeToast:D,toasts:E.filter(ce=>ce.position==ie.position),heights:R.filter(ce=>ce.position==ie.position),setHeights:O,expandByDefault:o,gap:v,loadingIcon:x,expanded:T,pauseWhenPageIsHidden:w,cn:C})}))})):null};/*! Bundled license information:

dompurify/dist/purify.es.mjs:
  (*! @license DOMPurify 3.0.10 | (c) Cure53 and other contributors | Released under the Apache license 2.0 and Mozilla Public License 2.0 | github.com/cure53/DOMPurify/blob/3.0.10/LICENSE *)
*/const oC={type:"logger",log(e){this.output("log",e)},warn(e){this.output("warn",e)},error(e){this.output("error",e)},output(e,t){console&&console[e]&&console[e].apply(console,t)}};class Pl{constructor(t){let n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};this.init(t,n)}init(t){let n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};this.prefix=n.prefix||"i18next:",this.logger=t||oC,this.options=n,this.debug=n.debug}log(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];return this.forward(n,"log","",!0)}warn(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];return this.forward(n,"warn","",!0)}error(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];return this.forward(n,"error","")}deprecate(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];return this.forward(n,"warn","WARNING DEPRECATED: ",!0)}forward(t,n,r,o){return o&&!this.debug?null:(typeof t[0]=="string"&&(t[0]=`${r}${this.prefix} ${t[0]}`),this.logger[n](t))}create(t){return new Pl(this.logger,{prefix:`${this.prefix}:${t}:`,...this.options})}clone(t){return t=t||this.options,t.prefix=t.prefix||this.prefix,new Pl(this.logger,t)}}var hn=new Pl;class ou{constructor(){this.observers={}}on(t,n){return t.split(" ").forEach(r=>{this.observers[r]=this.observers[r]||[],this.observers[r].push(n)}),this}off(t,n){if(this.observers[t]){if(!n){delete this.observers[t];return}this.observers[t]=this.observers[t].filter(r=>r!==n)}}emit(t){for(var n=arguments.length,r=new Array(n>1?n-1:0),o=1;o<n;o++)r[o-1]=arguments[o];this.observers[t]&&[].concat(this.observers[t]).forEach(s=>{s(...r)}),this.observers["*"]&&[].concat(this.observers["*"]).forEach(s=>{s.apply(s,[t,...r])})}}function Gi(){let e,t;const n=new Promise((r,o)=>{e=r,t=o});return n.resolve=e,n.reject=t,n}function Wg(e){return e==null?"":""+e}function iC(e,t,n){e.forEach(r=>{t[r]&&(n[r]=t[r])})}function Af(e,t,n){function r(s){return s&&s.indexOf("###")>-1?s.replace(/###/g,"."):s}function o(){return!e||typeof e=="string"}const i=typeof t!="string"?[].concat(t):t.split(".");for(;i.length>1;){if(o())return{};const s=r(i.shift());!e[s]&&n&&(e[s]=new n),Object.prototype.hasOwnProperty.call(e,s)?e=e[s]:e={}}return o()?{}:{obj:e,k:r(i.shift())}}function Gg(e,t,n){const{obj:r,k:o}=Af(e,t,Object);r[o]=n}function sC(e,t,n,r){const{obj:o,k:i}=Af(e,t,Object);o[i]=o[i]||[],r&&(o[i]=o[i].concat(n)),r||o[i].push(n)}function _l(e,t){const{obj:n,k:r}=Af(e,t);if(n)return n[r]}function aC(e,t,n){const r=_l(e,n);return r!==void 0?r:_l(t,n)}function $v(e,t,n){for(const r in t)r!=="__proto__"&&r!=="constructor"&&(r in e?typeof e[r]=="string"||e[r]instanceof String||typeof t[r]=="string"||t[r]instanceof String?n&&(e[r]=t[r]):$v(e[r],t[r],n):e[r]=t[r]);return e}function Eo(e){return e.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&")}var lC={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#x2F;"};function uC(e){return typeof e=="string"?e.replace(/[&<>"'\/]/g,t=>lC[t]):e}const cC=[" ",",","?","!",";"];function dC(e,t,n){t=t||"",n=n||"";const r=cC.filter(s=>t.indexOf(s)<0&&n.indexOf(s)<0);if(r.length===0)return!0;const o=new RegExp(`(${r.map(s=>s==="?"?"\\?":s).join("|")})`);let i=!o.test(e);if(!i){const s=e.indexOf(n);s>0&&!o.test(e.substring(0,s))&&(i=!0)}return i}function kl(e,t){let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:".";if(!e)return;if(e[t])return e[t];const r=t.split(n);let o=e;for(let i=0;i<r.length;++i){if(!o||typeof o[r[i]]=="string"&&i+1<r.length)return;if(o[r[i]]===void 0){let s=2,a=r.slice(i,i+s).join(n),l=o[a];for(;l===void 0&&r.length>i+s;)s++,a=r.slice(i,i+s).join(n),l=o[a];if(l===void 0)return;if(l===null)return null;if(t.endsWith(a)){if(typeof l=="string")return l;if(a&&typeof l[a]=="string")return l[a]}const u=r.slice(i+s).join(n);return u?kl(l,u,n):void 0}o=o[r[i]]}return o}function Nl(e){return e&&e.indexOf("_")>0?e.replace("_","-"):e}class Kg extends ou{constructor(t){let n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{ns:["translation"],defaultNS:"translation"};super(),this.data=t||{},this.options=n,this.options.keySeparator===void 0&&(this.options.keySeparator="."),this.options.ignoreJSONStructure===void 0&&(this.options.ignoreJSONStructure=!0)}addNamespaces(t){this.options.ns.indexOf(t)<0&&this.options.ns.push(t)}removeNamespaces(t){const n=this.options.ns.indexOf(t);n>-1&&this.options.ns.splice(n,1)}getResource(t,n,r){let o=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{};const i=o.keySeparator!==void 0?o.keySeparator:this.options.keySeparator,s=o.ignoreJSONStructure!==void 0?o.ignoreJSONStructure:this.options.ignoreJSONStructure;let a=[t,n];r&&typeof r!="string"&&(a=a.concat(r)),r&&typeof r=="string"&&(a=a.concat(i?r.split(i):r)),t.indexOf(".")>-1&&(a=t.split("."));const l=_l(this.data,a);return l||!s||typeof r!="string"?l:kl(this.data&&this.data[t]&&this.data[t][n],r,i)}addResource(t,n,r,o){let i=arguments.length>4&&arguments[4]!==void 0?arguments[4]:{silent:!1};const s=i.keySeparator!==void 0?i.keySeparator:this.options.keySeparator;let a=[t,n];r&&(a=a.concat(s?r.split(s):r)),t.indexOf(".")>-1&&(a=t.split("."),o=n,n=a[1]),this.addNamespaces(n),Gg(this.data,a,o),i.silent||this.emit("added",t,n,r,o)}addResources(t,n,r){let o=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{silent:!1};for(const i in r)(typeof r[i]=="string"||Object.prototype.toString.apply(r[i])==="[object Array]")&&this.addResource(t,n,i,r[i],{silent:!0});o.silent||this.emit("added",t,n,r)}addResourceBundle(t,n,r,o,i){let s=arguments.length>5&&arguments[5]!==void 0?arguments[5]:{silent:!1},a=[t,n];t.indexOf(".")>-1&&(a=t.split("."),o=r,r=n,n=a[1]),this.addNamespaces(n);let l=_l(this.data,a)||{};o?$v(l,r,i):l={...l,...r},Gg(this.data,a,l),s.silent||this.emit("added",t,n,r)}removeResourceBundle(t,n){this.hasResourceBundle(t,n)&&delete this.data[t][n],this.removeNamespaces(n),this.emit("removed",t,n)}hasResourceBundle(t,n){return this.getResource(t,n)!==void 0}getResourceBundle(t,n){return n||(n=this.options.defaultNS),this.options.compatibilityAPI==="v1"?{...this.getResource(t,n)}:this.getResource(t,n)}getDataByLanguage(t){return this.data[t]}hasLanguageSomeTranslations(t){const n=this.getDataByLanguage(t);return!!(n&&Object.keys(n)||[]).find(o=>n[o]&&Object.keys(n[o]).length>0)}toJSON(){return this.data}}var Ev={processors:{},addPostProcessor(e){this.processors[e.name]=e},handle(e,t,n,r,o){return e.forEach(i=>{this.processors[i]&&(t=this.processors[i].process(t,n,r,o))}),t}};const Qg={};class Ol extends ou{constructor(t){let n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};super(),iC(["resourceStore","languageUtils","pluralResolver","interpolator","backendConnector","i18nFormat","utils"],t,this),this.options=n,this.options.keySeparator===void 0&&(this.options.keySeparator="."),this.logger=hn.create("translator")}changeLanguage(t){t&&(this.language=t)}exists(t){let n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{interpolation:{}};if(t==null)return!1;const r=this.resolve(t,n);return r&&r.res!==void 0}extractFromKey(t,n){let r=n.nsSeparator!==void 0?n.nsSeparator:this.options.nsSeparator;r===void 0&&(r=":");const o=n.keySeparator!==void 0?n.keySeparator:this.options.keySeparator;let i=n.ns||this.options.defaultNS||[];const s=r&&t.indexOf(r)>-1,a=!this.options.userDefinedKeySeparator&&!n.keySeparator&&!this.options.userDefinedNsSeparator&&!n.nsSeparator&&!dC(t,r,o);if(s&&!a){const l=t.match(this.interpolator.nestingRegexp);if(l&&l.length>0)return{key:t,namespaces:i};const u=t.split(r);(r!==o||r===o&&this.options.ns.indexOf(u[0])>-1)&&(i=u.shift()),t=u.join(o)}return typeof i=="string"&&(i=[i]),{key:t,namespaces:i}}translate(t,n,r){if(typeof n!="object"&&this.options.overloadTranslationOptionHandler&&(n=this.options.overloadTranslationOptionHandler(arguments)),typeof n=="object"&&(n={...n}),n||(n={}),t==null)return"";Array.isArray(t)||(t=[String(t)]);const o=n.returnDetails!==void 0?n.returnDetails:this.options.returnDetails,i=n.keySeparator!==void 0?n.keySeparator:this.options.keySeparator,{key:s,namespaces:a}=this.extractFromKey(t[t.length-1],n),l=a[a.length-1],u=n.lng||this.language,c=n.appendNamespaceToCIMode||this.options.appendNamespaceToCIMode;if(u&&u.toLowerCase()==="cimode"){if(c){const C=n.nsSeparator||this.options.nsSeparator;return o?{res:`${l}${C}${s}`,usedKey:s,exactUsedKey:s,usedLng:u,usedNS:l,usedParams:this.getUsedParamsDetails(n)}:`${l}${C}${s}`}return o?{res:s,usedKey:s,exactUsedKey:s,usedLng:u,usedNS:l,usedParams:this.getUsedParamsDetails(n)}:s}const d=this.resolve(t,n);let f=d&&d.res;const h=d&&d.usedKey||s,y=d&&d.exactUsedKey||s,v=Object.prototype.toString.apply(f),x=["[object Number]","[object Function]","[object RegExp]"],m=n.joinArrays!==void 0?n.joinArrays:this.options.joinArrays,g=!this.i18nFormat||this.i18nFormat.handleAsObject;if(g&&f&&(typeof f!="string"&&typeof f!="boolean"&&typeof f!="number")&&x.indexOf(v)<0&&!(typeof m=="string"&&v==="[object Array]")){if(!n.returnObjects&&!this.options.returnObjects){this.options.returnedObjectHandler||this.logger.warn("accessing an object - but returnObjects options is not enabled!");const C=this.options.returnedObjectHandler?this.options.returnedObjectHandler(h,f,{...n,ns:a}):`key '${s} (${this.language})' returned an object instead of string.`;return o?(d.res=C,d.usedParams=this.getUsedParamsDetails(n),d):C}if(i){const C=v==="[object Array]",E=C?[]:{},_=C?y:h;for(const P in f)if(Object.prototype.hasOwnProperty.call(f,P)){const R=`${_}${i}${P}`;E[P]=this.translate(R,{...n,joinArrays:!1,ns:a}),E[P]===R&&(E[P]=f[P])}f=E}}else if(g&&typeof m=="string"&&v==="[object Array]")f=f.join(m),f&&(f=this.extendTranslation(f,t,n,r));else{let C=!1,E=!1;const _=n.count!==void 0&&typeof n.count!="string",P=Ol.hasDefaultValue(n),R=_?this.pluralResolver.getSuffix(u,n.count,n):"",O=n.ordinal&&_?this.pluralResolver.getSuffix(u,n.count,{ordinal:!1}):"",T=n[`defaultValue${R}`]||n[`defaultValue${O}`]||n.defaultValue;!this.isValidLookup(f)&&P&&(C=!0,f=T),this.isValidLookup(f)||(E=!0,f=s);const j=(n.missingKeyNoValueFallbackToKey||this.options.missingKeyNoValueFallbackToKey)&&E?void 0:f,Q=P&&T!==f&&this.options.updateMissing;if(E||C||Q){if(this.logger.log(Q?"updateKey":"missingKey",u,l,s,Q?T:f),i){const q=this.resolve(s,{...n,keySeparator:!1});q&&q.res&&this.logger.warn("Seems the loaded translations were in flat JSON format instead of nested. Either set keySeparator: false on init or make sure your translations are published in nested format.")}let z=[];const Y=this.languageUtils.getFallbackCodes(this.options.fallbackLng,n.lng||this.language);if(this.options.saveMissingTo==="fallback"&&Y&&Y[0])for(let q=0;q<Y.length;q++)z.push(Y[q]);else this.options.saveMissingTo==="all"?z=this.languageUtils.toResolveHierarchy(n.lng||this.language):z.push(n.lng||this.language);const U=(q,k,I)=>{const D=P&&I!==f?I:j;this.options.missingKeyHandler?this.options.missingKeyHandler(q,l,k,D,Q,n):this.backendConnector&&this.backendConnector.saveMissing&&this.backendConnector.saveMissing(q,l,k,D,Q,n),this.emit("missingKey",q,l,k,f)};this.options.saveMissing&&(this.options.saveMissingPlurals&&_?z.forEach(q=>{this.pluralResolver.getSuffixes(q,n).forEach(k=>{U([q],s+k,n[`defaultValue${k}`]||T)})}):U(z,s,T))}f=this.extendTranslation(f,t,n,d,r),E&&f===s&&this.options.appendNamespaceToMissingKey&&(f=`${l}:${s}`),(E||C)&&this.options.parseMissingKeyHandler&&(this.options.compatibilityAPI!=="v1"?f=this.options.parseMissingKeyHandler(this.options.appendNamespaceToMissingKey?`${l}:${s}`:s,C?f:void 0):f=this.options.parseMissingKeyHandler(f))}return o?(d.res=f,d.usedParams=this.getUsedParamsDetails(n),d):f}extendTranslation(t,n,r,o,i){var s=this;if(this.i18nFormat&&this.i18nFormat.parse)t=this.i18nFormat.parse(t,{...this.options.interpolation.defaultVariables,...r},r.lng||this.language||o.usedLng,o.usedNS,o.usedKey,{resolved:o});else if(!r.skipInterpolation){r.interpolation&&this.interpolator.init({...r,interpolation:{...this.options.interpolation,...r.interpolation}});const u=typeof t=="string"&&(r&&r.interpolation&&r.interpolation.skipOnVariables!==void 0?r.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables);let c;if(u){const f=t.match(this.interpolator.nestingRegexp);c=f&&f.length}let d=r.replace&&typeof r.replace!="string"?r.replace:r;if(this.options.interpolation.defaultVariables&&(d={...this.options.interpolation.defaultVariables,...d}),t=this.interpolator.interpolate(t,d,r.lng||this.language,r),u){const f=t.match(this.interpolator.nestingRegexp),h=f&&f.length;c<h&&(r.nest=!1)}!r.lng&&this.options.compatibilityAPI!=="v1"&&o&&o.res&&(r.lng=o.usedLng),r.nest!==!1&&(t=this.interpolator.nest(t,function(){for(var f=arguments.length,h=new Array(f),y=0;y<f;y++)h[y]=arguments[y];return i&&i[0]===h[0]&&!r.context?(s.logger.warn(`It seems you are nesting recursively key: ${h[0]} in key: ${n[0]}`),null):s.translate(...h,n)},r)),r.interpolation&&this.interpolator.reset()}const a=r.postProcess||this.options.postProcess,l=typeof a=="string"?[a]:a;return t!=null&&l&&l.length&&r.applyPostProcessor!==!1&&(t=Ev.handle(l,t,n,this.options&&this.options.postProcessPassResolved?{i18nResolved:{...o,usedParams:this.getUsedParamsDetails(r)},...r}:r,this)),t}resolve(t){let n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},r,o,i,s,a;return typeof t=="string"&&(t=[t]),t.forEach(l=>{if(this.isValidLookup(r))return;const u=this.extractFromKey(l,n),c=u.key;o=c;let d=u.namespaces;this.options.fallbackNS&&(d=d.concat(this.options.fallbackNS));const f=n.count!==void 0&&typeof n.count!="string",h=f&&!n.ordinal&&n.count===0&&this.pluralResolver.shouldUseIntlApi(),y=n.context!==void 0&&(typeof n.context=="string"||typeof n.context=="number")&&n.context!=="",v=n.lngs?n.lngs:this.languageUtils.toResolveHierarchy(n.lng||this.language,n.fallbackLng);d.forEach(x=>{this.isValidLookup(r)||(a=x,!Qg[`${v[0]}-${x}`]&&this.utils&&this.utils.hasLoadedNamespace&&!this.utils.hasLoadedNamespace(a)&&(Qg[`${v[0]}-${x}`]=!0,this.logger.warn(`key "${o}" for languages "${v.join(", ")}" won't get resolved as namespace "${a}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!")),v.forEach(m=>{if(this.isValidLookup(r))return;s=m;const g=[c];if(this.i18nFormat&&this.i18nFormat.addLookupKeys)this.i18nFormat.addLookupKeys(g,c,m,x,n);else{let C;f&&(C=this.pluralResolver.getSuffix(m,n.count,n));const E=`${this.options.pluralSeparator}zero`,_=`${this.options.pluralSeparator}ordinal${this.options.pluralSeparator}`;if(f&&(g.push(c+C),n.ordinal&&C.indexOf(_)===0&&g.push(c+C.replace(_,this.options.pluralSeparator)),h&&g.push(c+E)),y){const P=`${c}${this.options.contextSeparator}${n.context}`;g.push(P),f&&(g.push(P+C),n.ordinal&&C.indexOf(_)===0&&g.push(P+C.replace(_,this.options.pluralSeparator)),h&&g.push(P+E))}}let w;for(;w=g.pop();)this.isValidLookup(r)||(i=w,r=this.getResource(m,x,w,n))}))})}),{res:r,usedKey:o,exactUsedKey:i,usedLng:s,usedNS:a}}isValidLookup(t){return t!==void 0&&!(!this.options.returnNull&&t===null)&&!(!this.options.returnEmptyString&&t==="")}getResource(t,n,r){let o=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{};return this.i18nFormat&&this.i18nFormat.getResource?this.i18nFormat.getResource(t,n,r,o):this.resourceStore.getResource(t,n,r,o)}getUsedParamsDetails(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};const n=["defaultValue","ordinal","context","replace","lng","lngs","fallbackLng","ns","keySeparator","nsSeparator","returnObjects","returnDetails","joinArrays","postProcess","interpolation"],r=t.replace&&typeof t.replace!="string";let o=r?t.replace:t;if(r&&typeof t.count<"u"&&(o.count=t.count),this.options.interpolation.defaultVariables&&(o={...this.options.interpolation.defaultVariables,...o}),!r){o={...o};for(const i of n)delete o[i]}return o}static hasDefaultValue(t){const n="defaultValue";for(const r in t)if(Object.prototype.hasOwnProperty.call(t,r)&&n===r.substring(0,n.length)&&t[r]!==void 0)return!0;return!1}}function Qu(e){return e.charAt(0).toUpperCase()+e.slice(1)}class Yg{constructor(t){this.options=t,this.supportedLngs=this.options.supportedLngs||!1,this.logger=hn.create("languageUtils")}getScriptPartFromCode(t){if(t=Nl(t),!t||t.indexOf("-")<0)return null;const n=t.split("-");return n.length===2||(n.pop(),n[n.length-1].toLowerCase()==="x")?null:this.formatLanguageCode(n.join("-"))}getLanguagePartFromCode(t){if(t=Nl(t),!t||t.indexOf("-")<0)return t;const n=t.split("-");return this.formatLanguageCode(n[0])}formatLanguageCode(t){if(typeof t=="string"&&t.indexOf("-")>-1){const n=["hans","hant","latn","cyrl","cans","mong","arab"];let r=t.split("-");return this.options.lowerCaseLng?r=r.map(o=>o.toLowerCase()):r.length===2?(r[0]=r[0].toLowerCase(),r[1]=r[1].toUpperCase(),n.indexOf(r[1].toLowerCase())>-1&&(r[1]=Qu(r[1].toLowerCase()))):r.length===3&&(r[0]=r[0].toLowerCase(),r[1].length===2&&(r[1]=r[1].toUpperCase()),r[0]!=="sgn"&&r[2].length===2&&(r[2]=r[2].toUpperCase()),n.indexOf(r[1].toLowerCase())>-1&&(r[1]=Qu(r[1].toLowerCase())),n.indexOf(r[2].toLowerCase())>-1&&(r[2]=Qu(r[2].toLowerCase()))),r.join("-")}return this.options.cleanCode||this.options.lowerCaseLng?t.toLowerCase():t}isSupportedCode(t){return(this.options.load==="languageOnly"||this.options.nonExplicitSupportedLngs)&&(t=this.getLanguagePartFromCode(t)),!this.supportedLngs||!this.supportedLngs.length||this.supportedLngs.indexOf(t)>-1}getBestMatchFromCodes(t){if(!t)return null;let n;return t.forEach(r=>{if(n)return;const o=this.formatLanguageCode(r);(!this.options.supportedLngs||this.isSupportedCode(o))&&(n=o)}),!n&&this.options.supportedLngs&&t.forEach(r=>{if(n)return;const o=this.getLanguagePartFromCode(r);if(this.isSupportedCode(o))return n=o;n=this.options.supportedLngs.find(i=>{if(i===o)return i;if(!(i.indexOf("-")<0&&o.indexOf("-")<0)&&i.indexOf(o)===0)return i})}),n||(n=this.getFallbackCodes(this.options.fallbackLng)[0]),n}getFallbackCodes(t,n){if(!t)return[];if(typeof t=="function"&&(t=t(n)),typeof t=="string"&&(t=[t]),Object.prototype.toString.apply(t)==="[object Array]")return t;if(!n)return t.default||[];let r=t[n];return r||(r=t[this.getScriptPartFromCode(n)]),r||(r=t[this.formatLanguageCode(n)]),r||(r=t[this.getLanguagePartFromCode(n)]),r||(r=t.default),r||[]}toResolveHierarchy(t,n){const r=this.getFallbackCodes(n||this.options.fallbackLng||[],t),o=[],i=s=>{s&&(this.isSupportedCode(s)?o.push(s):this.logger.warn(`rejecting language code not found in supportedLngs: ${s}`))};return typeof t=="string"&&(t.indexOf("-")>-1||t.indexOf("_")>-1)?(this.options.load!=="languageOnly"&&i(this.formatLanguageCode(t)),this.options.load!=="languageOnly"&&this.options.load!=="currentOnly"&&i(this.getScriptPartFromCode(t)),this.options.load!=="currentOnly"&&i(this.getLanguagePartFromCode(t))):typeof t=="string"&&i(this.formatLanguageCode(t)),r.forEach(s=>{o.indexOf(s)<0&&i(this.formatLanguageCode(s))}),o}}let fC=[{lngs:["ach","ak","am","arn","br","fil","gun","ln","mfe","mg","mi","oc","pt","pt-BR","tg","tl","ti","tr","uz","wa"],nr:[1,2],fc:1},{lngs:["af","an","ast","az","bg","bn","ca","da","de","dev","el","en","eo","es","et","eu","fi","fo","fur","fy","gl","gu","ha","hi","hu","hy","ia","it","kk","kn","ku","lb","mai","ml","mn","mr","nah","nap","nb","ne","nl","nn","no","nso","pa","pap","pms","ps","pt-PT","rm","sco","se","si","so","son","sq","sv","sw","ta","te","tk","ur","yo"],nr:[1,2],fc:2},{lngs:["ay","bo","cgg","fa","ht","id","ja","jbo","ka","km","ko","ky","lo","ms","sah","su","th","tt","ug","vi","wo","zh"],nr:[1],fc:3},{lngs:["be","bs","cnr","dz","hr","ru","sr","uk"],nr:[1,2,5],fc:4},{lngs:["ar"],nr:[0,1,2,3,11,100],fc:5},{lngs:["cs","sk"],nr:[1,2,5],fc:6},{lngs:["csb","pl"],nr:[1,2,5],fc:7},{lngs:["cy"],nr:[1,2,3,8],fc:8},{lngs:["fr"],nr:[1,2],fc:9},{lngs:["ga"],nr:[1,2,3,7,11],fc:10},{lngs:["gd"],nr:[1,2,3,20],fc:11},{lngs:["is"],nr:[1,2],fc:12},{lngs:["jv"],nr:[0,1],fc:13},{lngs:["kw"],nr:[1,2,3,4],fc:14},{lngs:["lt"],nr:[1,2,10],fc:15},{lngs:["lv"],nr:[1,2,0],fc:16},{lngs:["mk"],nr:[1,2],fc:17},{lngs:["mnk"],nr:[0,1,2],fc:18},{lngs:["mt"],nr:[1,2,11,20],fc:19},{lngs:["or"],nr:[2,1],fc:2},{lngs:["ro"],nr:[1,2,20],fc:20},{lngs:["sl"],nr:[5,1,2,3],fc:21},{lngs:["he","iw"],nr:[1,2,20,21],fc:22}],pC={1:function(e){return+(e>1)},2:function(e){return+(e!=1)},3:function(e){return 0},4:function(e){return e%10==1&&e%100!=11?0:e%10>=2&&e%10<=4&&(e%100<10||e%100>=20)?1:2},5:function(e){return e==0?0:e==1?1:e==2?2:e%100>=3&&e%100<=10?3:e%100>=11?4:5},6:function(e){return e==1?0:e>=2&&e<=4?1:2},7:function(e){return e==1?0:e%10>=2&&e%10<=4&&(e%100<10||e%100>=20)?1:2},8:function(e){return e==1?0:e==2?1:e!=8&&e!=11?2:3},9:function(e){return+(e>=2)},10:function(e){return e==1?0:e==2?1:e<7?2:e<11?3:4},11:function(e){return e==1||e==11?0:e==2||e==12?1:e>2&&e<20?2:3},12:function(e){return+(e%10!=1||e%100==11)},13:function(e){return+(e!==0)},14:function(e){return e==1?0:e==2?1:e==3?2:3},15:function(e){return e%10==1&&e%100!=11?0:e%10>=2&&(e%100<10||e%100>=20)?1:2},16:function(e){return e%10==1&&e%100!=11?0:e!==0?1:2},17:function(e){return e==1||e%10==1&&e%100!=11?0:1},18:function(e){return e==0?0:e==1?1:2},19:function(e){return e==1?0:e==0||e%100>1&&e%100<11?1:e%100>10&&e%100<20?2:3},20:function(e){return e==1?0:e==0||e%100>0&&e%100<20?1:2},21:function(e){return e%100==1?1:e%100==2?2:e%100==3||e%100==4?3:0},22:function(e){return e==1?0:e==2?1:(e<0||e>10)&&e%10==0?2:3}};const gC=["v1","v2","v3"],hC=["v4"],qg={zero:0,one:1,two:2,few:3,many:4,other:5};function mC(){const e={};return fC.forEach(t=>{t.lngs.forEach(n=>{e[n]={numbers:t.nr,plurals:pC[t.fc]}})}),e}class vC{constructor(t){let n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};this.languageUtils=t,this.options=n,this.logger=hn.create("pluralResolver"),(!this.options.compatibilityJSON||hC.includes(this.options.compatibilityJSON))&&(typeof Intl>"u"||!Intl.PluralRules)&&(this.options.compatibilityJSON="v3",this.logger.error("Your environment seems not to be Intl API compatible, use an Intl.PluralRules polyfill. Will fallback to the compatibilityJSON v3 format handling.")),this.rules=mC()}addRule(t,n){this.rules[t]=n}getRule(t){let n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(this.shouldUseIntlApi())try{return new Intl.PluralRules(Nl(t),{type:n.ordinal?"ordinal":"cardinal"})}catch{return}return this.rules[t]||this.rules[this.languageUtils.getLanguagePartFromCode(t)]}needsPlural(t){let n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};const r=this.getRule(t,n);return this.shouldUseIntlApi()?r&&r.resolvedOptions().pluralCategories.length>1:r&&r.numbers.length>1}getPluralFormsOfKey(t,n){let r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};return this.getSuffixes(t,r).map(o=>`${n}${o}`)}getSuffixes(t){let n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};const r=this.getRule(t,n);return r?this.shouldUseIntlApi()?r.resolvedOptions().pluralCategories.sort((o,i)=>qg[o]-qg[i]).map(o=>`${this.options.prepend}${n.ordinal?`ordinal${this.options.prepend}`:""}${o}`):r.numbers.map(o=>this.getSuffix(t,o,n)):[]}getSuffix(t,n){let r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};const o=this.getRule(t,r);return o?this.shouldUseIntlApi()?`${this.options.prepend}${r.ordinal?`ordinal${this.options.prepend}`:""}${o.select(n)}`:this.getSuffixRetroCompatible(o,n):(this.logger.warn(`no plural rule found for: ${t}`),"")}getSuffixRetroCompatible(t,n){const r=t.noAbs?t.plurals(n):t.plurals(Math.abs(n));let o=t.numbers[r];this.options.simplifyPluralSuffix&&t.numbers.length===2&&t.numbers[0]===1&&(o===2?o="plural":o===1&&(o=""));const i=()=>this.options.prepend&&o.toString()?this.options.prepend+o.toString():o.toString();return this.options.compatibilityJSON==="v1"?o===1?"":typeof o=="number"?`_plural_${o.toString()}`:i():this.options.compatibilityJSON==="v2"||this.options.simplifyPluralSuffix&&t.numbers.length===2&&t.numbers[0]===1?i():this.options.prepend&&r.toString()?this.options.prepend+r.toString():r.toString()}shouldUseIntlApi(){return!gC.includes(this.options.compatibilityJSON)}}function Xg(e,t,n){let r=arguments.length>3&&arguments[3]!==void 0?arguments[3]:".",o=arguments.length>4&&arguments[4]!==void 0?arguments[4]:!0,i=aC(e,t,n);return!i&&o&&typeof n=="string"&&(i=kl(e,n,r),i===void 0&&(i=kl(t,n,r))),i}class yC{constructor(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};this.logger=hn.create("interpolator"),this.options=t,this.format=t.interpolation&&t.interpolation.format||(n=>n),this.init(t)}init(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};t.interpolation||(t.interpolation={escapeValue:!0});const n=t.interpolation;this.escape=n.escape!==void 0?n.escape:uC,this.escapeValue=n.escapeValue!==void 0?n.escapeValue:!0,this.useRawValueToEscape=n.useRawValueToEscape!==void 0?n.useRawValueToEscape:!1,this.prefix=n.prefix?Eo(n.prefix):n.prefixEscaped||"{{",this.suffix=n.suffix?Eo(n.suffix):n.suffixEscaped||"}}",this.formatSeparator=n.formatSeparator?n.formatSeparator:n.formatSeparator||",",this.unescapePrefix=n.unescapeSuffix?"":n.unescapePrefix||"-",this.unescapeSuffix=this.unescapePrefix?"":n.unescapeSuffix||"",this.nestingPrefix=n.nestingPrefix?Eo(n.nestingPrefix):n.nestingPrefixEscaped||Eo("$t("),this.nestingSuffix=n.nestingSuffix?Eo(n.nestingSuffix):n.nestingSuffixEscaped||Eo(")"),this.nestingOptionsSeparator=n.nestingOptionsSeparator?n.nestingOptionsSeparator:n.nestingOptionsSeparator||",",this.maxReplaces=n.maxReplaces?n.maxReplaces:1e3,this.alwaysFormat=n.alwaysFormat!==void 0?n.alwaysFormat:!1,this.resetRegExp()}reset(){this.options&&this.init(this.options)}resetRegExp(){const t=`${this.prefix}(.+?)${this.suffix}`;this.regexp=new RegExp(t,"g");const n=`${this.prefix}${this.unescapePrefix}(.+?)${this.unescapeSuffix}${this.suffix}`;this.regexpUnescape=new RegExp(n,"g");const r=`${this.nestingPrefix}(.+?)${this.nestingSuffix}`;this.nestingRegexp=new RegExp(r,"g")}interpolate(t,n,r,o){let i,s,a;const l=this.options&&this.options.interpolation&&this.options.interpolation.defaultVariables||{};function u(y){return y.replace(/\$/g,"$$$$")}const c=y=>{if(y.indexOf(this.formatSeparator)<0){const g=Xg(n,l,y,this.options.keySeparator,this.options.ignoreJSONStructure);return this.alwaysFormat?this.format(g,void 0,r,{...o,...n,interpolationkey:y}):g}const v=y.split(this.formatSeparator),x=v.shift().trim(),m=v.join(this.formatSeparator).trim();return this.format(Xg(n,l,x,this.options.keySeparator,this.options.ignoreJSONStructure),m,r,{...o,...n,interpolationkey:x})};this.resetRegExp();const d=o&&o.missingInterpolationHandler||this.options.missingInterpolationHandler,f=o&&o.interpolation&&o.interpolation.skipOnVariables!==void 0?o.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables;return[{regex:this.regexpUnescape,safeValue:y=>u(y)},{regex:this.regexp,safeValue:y=>this.escapeValue?u(this.escape(y)):u(y)}].forEach(y=>{for(a=0;i=y.regex.exec(t);){const v=i[1].trim();if(s=c(v),s===void 0)if(typeof d=="function"){const m=d(t,i,o);s=typeof m=="string"?m:""}else if(o&&Object.prototype.hasOwnProperty.call(o,v))s="";else if(f){s=i[0];continue}else this.logger.warn(`missed to pass in variable ${v} for interpolating ${t}`),s="";else typeof s!="string"&&!this.useRawValueToEscape&&(s=Wg(s));const x=y.safeValue(s);if(t=t.replace(i[0],x),f?(y.regex.lastIndex+=s.length,y.regex.lastIndex-=i[0].length):y.regex.lastIndex=0,a++,a>=this.maxReplaces)break}}),t}nest(t,n){let r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},o,i,s;function a(l,u){const c=this.nestingOptionsSeparator;if(l.indexOf(c)<0)return l;const d=l.split(new RegExp(`${c}[ ]*{`));let f=`{${d[1]}`;l=d[0],f=this.interpolate(f,s);const h=f.match(/'/g),y=f.match(/"/g);(h&&h.length%2===0&&!y||y.length%2!==0)&&(f=f.replace(/'/g,'"'));try{s=JSON.parse(f),u&&(s={...u,...s})}catch(v){return this.logger.warn(`failed parsing options string in nesting for key ${l}`,v),`${l}${c}${f}`}return delete s.defaultValue,l}for(;o=this.nestingRegexp.exec(t);){let l=[];s={...r},s=s.replace&&typeof s.replace!="string"?s.replace:s,s.applyPostProcessor=!1,delete s.defaultValue;let u=!1;if(o[0].indexOf(this.formatSeparator)!==-1&&!/{.*}/.test(o[1])){const c=o[1].split(this.formatSeparator).map(d=>d.trim());o[1]=c.shift(),l=c,u=!0}if(i=n(a.call(this,o[1].trim(),s),s),i&&o[0]===t&&typeof i!="string")return i;typeof i!="string"&&(i=Wg(i)),i||(this.logger.warn(`missed to resolve ${o[1]} for nesting ${t}`),i=""),u&&(i=l.reduce((c,d)=>this.format(c,d,r.lng,{...r,interpolationkey:o[1].trim()}),i.trim())),t=t.replace(o[0],i),this.regexp.lastIndex=0}return t}}function wC(e){let t=e.toLowerCase().trim();const n={};if(e.indexOf("(")>-1){const r=e.split("(");t=r[0].toLowerCase().trim();const o=r[1].substring(0,r[1].length-1);t==="currency"&&o.indexOf(":")<0?n.currency||(n.currency=o.trim()):t==="relativetime"&&o.indexOf(":")<0?n.range||(n.range=o.trim()):o.split(";").forEach(s=>{if(!s)return;const[a,...l]=s.split(":"),u=l.join(":").trim().replace(/^'+|'+$/g,"");n[a.trim()]||(n[a.trim()]=u),u==="false"&&(n[a.trim()]=!1),u==="true"&&(n[a.trim()]=!0),isNaN(u)||(n[a.trim()]=parseInt(u,10))})}return{formatName:t,formatOptions:n}}function Ro(e){const t={};return function(r,o,i){const s=o+JSON.stringify(i);let a=t[s];return a||(a=e(Nl(o),i),t[s]=a),a(r)}}class xC{constructor(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};this.logger=hn.create("formatter"),this.options=t,this.formats={number:Ro((n,r)=>{const o=new Intl.NumberFormat(n,{...r});return i=>o.format(i)}),currency:Ro((n,r)=>{const o=new Intl.NumberFormat(n,{...r,style:"currency"});return i=>o.format(i)}),datetime:Ro((n,r)=>{const o=new Intl.DateTimeFormat(n,{...r});return i=>o.format(i)}),relativetime:Ro((n,r)=>{const o=new Intl.RelativeTimeFormat(n,{...r});return i=>o.format(i,r.range||"day")}),list:Ro((n,r)=>{const o=new Intl.ListFormat(n,{...r});return i=>o.format(i)})},this.init(t)}init(t){const r=(arguments.length>1&&arguments[1]!==void 0?arguments[1]:{interpolation:{}}).interpolation;this.formatSeparator=r.formatSeparator?r.formatSeparator:r.formatSeparator||","}add(t,n){this.formats[t.toLowerCase().trim()]=n}addCached(t,n){this.formats[t.toLowerCase().trim()]=Ro(n)}format(t,n,r){let o=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{};return n.split(this.formatSeparator).reduce((a,l)=>{const{formatName:u,formatOptions:c}=wC(l);if(this.formats[u]){let d=a;try{const f=o&&o.formatParams&&o.formatParams[o.interpolationkey]||{},h=f.locale||f.lng||o.locale||o.lng||r;d=this.formats[u](a,h,{...c,...o,...f})}catch(f){this.logger.warn(f)}return d}else this.logger.warn(`there was no format function for ${u}`);return a},t)}}function SC(e,t){e.pending[t]!==void 0&&(delete e.pending[t],e.pendingCount--)}class CC extends ou{constructor(t,n,r){let o=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{};super(),this.backend=t,this.store=n,this.services=r,this.languageUtils=r.languageUtils,this.options=o,this.logger=hn.create("backendConnector"),this.waitingReads=[],this.maxParallelReads=o.maxParallelReads||10,this.readingCalls=0,this.maxRetries=o.maxRetries>=0?o.maxRetries:5,this.retryTimeout=o.retryTimeout>=1?o.retryTimeout:350,this.state={},this.queue=[],this.backend&&this.backend.init&&this.backend.init(r,o.backend,o)}queueLoad(t,n,r,o){const i={},s={},a={},l={};return t.forEach(u=>{let c=!0;n.forEach(d=>{const f=`${u}|${d}`;!r.reload&&this.store.hasResourceBundle(u,d)?this.state[f]=2:this.state[f]<0||(this.state[f]===1?s[f]===void 0&&(s[f]=!0):(this.state[f]=1,c=!1,s[f]===void 0&&(s[f]=!0),i[f]===void 0&&(i[f]=!0),l[d]===void 0&&(l[d]=!0)))}),c||(a[u]=!0)}),(Object.keys(i).length||Object.keys(s).length)&&this.queue.push({pending:s,pendingCount:Object.keys(s).length,loaded:{},errors:[],callback:o}),{toLoad:Object.keys(i),pending:Object.keys(s),toLoadLanguages:Object.keys(a),toLoadNamespaces:Object.keys(l)}}loaded(t,n,r){const o=t.split("|"),i=o[0],s=o[1];n&&this.emit("failedLoading",i,s,n),r&&this.store.addResourceBundle(i,s,r),this.state[t]=n?-1:2;const a={};this.queue.forEach(l=>{sC(l.loaded,[i],s),SC(l,t),n&&l.errors.push(n),l.pendingCount===0&&!l.done&&(Object.keys(l.loaded).forEach(u=>{a[u]||(a[u]={});const c=l.loaded[u];c.length&&c.forEach(d=>{a[u][d]===void 0&&(a[u][d]=!0)})}),l.done=!0,l.errors.length?l.callback(l.errors):l.callback())}),this.emit("loaded",a),this.queue=this.queue.filter(l=>!l.done)}read(t,n,r){let o=arguments.length>3&&arguments[3]!==void 0?arguments[3]:0,i=arguments.length>4&&arguments[4]!==void 0?arguments[4]:this.retryTimeout,s=arguments.length>5?arguments[5]:void 0;if(!t.length)return s(null,{});if(this.readingCalls>=this.maxParallelReads){this.waitingReads.push({lng:t,ns:n,fcName:r,tried:o,wait:i,callback:s});return}this.readingCalls++;const a=(u,c)=>{if(this.readingCalls--,this.waitingReads.length>0){const d=this.waitingReads.shift();this.read(d.lng,d.ns,d.fcName,d.tried,d.wait,d.callback)}if(u&&c&&o<this.maxRetries){setTimeout(()=>{this.read.call(this,t,n,r,o+1,i*2,s)},i);return}s(u,c)},l=this.backend[r].bind(this.backend);if(l.length===2){try{const u=l(t,n);u&&typeof u.then=="function"?u.then(c=>a(null,c)).catch(a):a(null,u)}catch(u){a(u)}return}return l(t,n,a)}prepareLoading(t,n){let r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},o=arguments.length>3?arguments[3]:void 0;if(!this.backend)return this.logger.warn("No backend was added via i18next.use. Will not load resources."),o&&o();typeof t=="string"&&(t=this.languageUtils.toResolveHierarchy(t)),typeof n=="string"&&(n=[n]);const i=this.queueLoad(t,n,r,o);if(!i.toLoad.length)return i.pending.length||o(),null;i.toLoad.forEach(s=>{this.loadOne(s)})}load(t,n,r){this.prepareLoading(t,n,{},r)}reload(t,n,r){this.prepareLoading(t,n,{reload:!0},r)}loadOne(t){let n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"";const r=t.split("|"),o=r[0],i=r[1];this.read(o,i,"read",void 0,void 0,(s,a)=>{s&&this.logger.warn(`${n}loading namespace ${i} for language ${o} failed`,s),!s&&a&&this.logger.log(`${n}loaded namespace ${i} for language ${o}`,a),this.loaded(t,s,a)})}saveMissing(t,n,r,o,i){let s=arguments.length>5&&arguments[5]!==void 0?arguments[5]:{},a=arguments.length>6&&arguments[6]!==void 0?arguments[6]:()=>{};if(this.services.utils&&this.services.utils.hasLoadedNamespace&&!this.services.utils.hasLoadedNamespace(n)){this.logger.warn(`did not save key "${r}" as the namespace "${n}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!");return}if(!(r==null||r==="")){if(this.backend&&this.backend.create){const l={...s,isUpdate:i},u=this.backend.create.bind(this.backend);if(u.length<6)try{let c;u.length===5?c=u(t,n,r,o,l):c=u(t,n,r,o),c&&typeof c.then=="function"?c.then(d=>a(null,d)).catch(a):a(null,c)}catch(c){a(c)}else u(t,n,r,o,a,l)}!t||!t[0]||this.store.addResource(t[0],n,r,o)}}}function Zg(){return{debug:!1,initImmediate:!0,ns:["translation"],defaultNS:["translation"],fallbackLng:["dev"],fallbackNS:!1,supportedLngs:!1,nonExplicitSupportedLngs:!1,load:"all",preload:!1,simplifyPluralSuffix:!0,keySeparator:".",nsSeparator:":",pluralSeparator:"_",contextSeparator:"_",partialBundledLanguages:!1,saveMissing:!1,updateMissing:!1,saveMissingTo:"fallback",saveMissingPlurals:!0,missingKeyHandler:!1,missingInterpolationHandler:!1,postProcess:!1,postProcessPassResolved:!1,returnNull:!1,returnEmptyString:!0,returnObjects:!1,joinArrays:!1,returnedObjectHandler:!1,parseMissingKeyHandler:!1,appendNamespaceToMissingKey:!1,appendNamespaceToCIMode:!1,overloadTranslationOptionHandler:function(t){let n={};if(typeof t[1]=="object"&&(n=t[1]),typeof t[1]=="string"&&(n.defaultValue=t[1]),typeof t[2]=="string"&&(n.tDescription=t[2]),typeof t[2]=="object"||typeof t[3]=="object"){const r=t[3]||t[2];Object.keys(r).forEach(o=>{n[o]=r[o]})}return n},interpolation:{escapeValue:!0,format:(e,t,n,r)=>e,prefix:"{{",suffix:"}}",formatSeparator:",",unescapePrefix:"-",nestingPrefix:"$t(",nestingSuffix:")",nestingOptionsSeparator:",",maxReplaces:1e3,skipOnVariables:!0}}}function Jg(e){return typeof e.ns=="string"&&(e.ns=[e.ns]),typeof e.fallbackLng=="string"&&(e.fallbackLng=[e.fallbackLng]),typeof e.fallbackNS=="string"&&(e.fallbackNS=[e.fallbackNS]),e.supportedLngs&&e.supportedLngs.indexOf("cimode")<0&&(e.supportedLngs=e.supportedLngs.concat(["cimode"])),e}function ba(){}function bC(e){Object.getOwnPropertyNames(Object.getPrototypeOf(e)).forEach(n=>{typeof e[n]=="function"&&(e[n]=e[n].bind(e))})}class _s extends ou{constructor(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},n=arguments.length>1?arguments[1]:void 0;if(super(),this.options=Jg(t),this.services={},this.logger=hn,this.modules={external:[]},bC(this),n&&!this.isInitialized&&!t.isClone){if(!this.options.initImmediate)return this.init(t,n),this;setTimeout(()=>{this.init(t,n)},0)}}init(){var t=this;let n=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},r=arguments.length>1?arguments[1]:void 0;typeof n=="function"&&(r=n,n={}),!n.defaultNS&&n.defaultNS!==!1&&n.ns&&(typeof n.ns=="string"?n.defaultNS=n.ns:n.ns.indexOf("translation")<0&&(n.defaultNS=n.ns[0]));const o=Zg();this.options={...o,...this.options,...Jg(n)},this.options.compatibilityAPI!=="v1"&&(this.options.interpolation={...o.interpolation,...this.options.interpolation}),n.keySeparator!==void 0&&(this.options.userDefinedKeySeparator=n.keySeparator),n.nsSeparator!==void 0&&(this.options.userDefinedNsSeparator=n.nsSeparator);function i(c){return c?typeof c=="function"?new c:c:null}if(!this.options.isClone){this.modules.logger?hn.init(i(this.modules.logger),this.options):hn.init(null,this.options);let c;this.modules.formatter?c=this.modules.formatter:typeof Intl<"u"&&(c=xC);const d=new Yg(this.options);this.store=new Kg(this.options.resources,this.options);const f=this.services;f.logger=hn,f.resourceStore=this.store,f.languageUtils=d,f.pluralResolver=new vC(d,{prepend:this.options.pluralSeparator,compatibilityJSON:this.options.compatibilityJSON,simplifyPluralSuffix:this.options.simplifyPluralSuffix}),c&&(!this.options.interpolation.format||this.options.interpolation.format===o.interpolation.format)&&(f.formatter=i(c),f.formatter.init(f,this.options),this.options.interpolation.format=f.formatter.format.bind(f.formatter)),f.interpolator=new yC(this.options),f.utils={hasLoadedNamespace:this.hasLoadedNamespace.bind(this)},f.backendConnector=new CC(i(this.modules.backend),f.resourceStore,f,this.options),f.backendConnector.on("*",function(h){for(var y=arguments.length,v=new Array(y>1?y-1:0),x=1;x<y;x++)v[x-1]=arguments[x];t.emit(h,...v)}),this.modules.languageDetector&&(f.languageDetector=i(this.modules.languageDetector),f.languageDetector.init&&f.languageDetector.init(f,this.options.detection,this.options)),this.modules.i18nFormat&&(f.i18nFormat=i(this.modules.i18nFormat),f.i18nFormat.init&&f.i18nFormat.init(this)),this.translator=new Ol(this.services,this.options),this.translator.on("*",function(h){for(var y=arguments.length,v=new Array(y>1?y-1:0),x=1;x<y;x++)v[x-1]=arguments[x];t.emit(h,...v)}),this.modules.external.forEach(h=>{h.init&&h.init(this)})}if(this.format=this.options.interpolation.format,r||(r=ba),this.options.fallbackLng&&!this.services.languageDetector&&!this.options.lng){const c=this.services.languageUtils.getFallbackCodes(this.options.fallbackLng);c.length>0&&c[0]!=="dev"&&(this.options.lng=c[0])}!this.services.languageDetector&&!this.options.lng&&this.logger.warn("init: no languageDetector is used and no lng is defined"),["getResource","hasResourceBundle","getResourceBundle","getDataByLanguage"].forEach(c=>{this[c]=function(){return t.store[c](...arguments)}}),["addResource","addResources","addResourceBundle","removeResourceBundle"].forEach(c=>{this[c]=function(){return t.store[c](...arguments),t}});const l=Gi(),u=()=>{const c=(d,f)=>{this.isInitialized&&!this.initializedStoreOnce&&this.logger.warn("init: i18next is already initialized. You should call init just once!"),this.isInitialized=!0,this.options.isClone||this.logger.log("initialized",this.options),this.emit("initialized",this.options),l.resolve(f),r(d,f)};if(this.languages&&this.options.compatibilityAPI!=="v1"&&!this.isInitialized)return c(null,this.t.bind(this));this.changeLanguage(this.options.lng,c)};return this.options.resources||!this.options.initImmediate?u():setTimeout(u,0),l}loadResources(t){let r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:ba;const o=typeof t=="string"?t:this.language;if(typeof t=="function"&&(r=t),!this.options.resources||this.options.partialBundledLanguages){if(o&&o.toLowerCase()==="cimode"&&(!this.options.preload||this.options.preload.length===0))return r();const i=[],s=a=>{if(!a||a==="cimode")return;this.services.languageUtils.toResolveHierarchy(a).forEach(u=>{u!=="cimode"&&i.indexOf(u)<0&&i.push(u)})};o?s(o):this.services.languageUtils.getFallbackCodes(this.options.fallbackLng).forEach(l=>s(l)),this.options.preload&&this.options.preload.forEach(a=>s(a)),this.services.backendConnector.load(i,this.options.ns,a=>{!a&&!this.resolvedLanguage&&this.language&&this.setResolvedLanguage(this.language),r(a)})}else r(null)}reloadResources(t,n,r){const o=Gi();return t||(t=this.languages),n||(n=this.options.ns),r||(r=ba),this.services.backendConnector.reload(t,n,i=>{o.resolve(),r(i)}),o}use(t){if(!t)throw new Error("You are passing an undefined module! Please check the object you are passing to i18next.use()");if(!t.type)throw new Error("You are passing a wrong module! Please check the object you are passing to i18next.use()");return t.type==="backend"&&(this.modules.backend=t),(t.type==="logger"||t.log&&t.warn&&t.error)&&(this.modules.logger=t),t.type==="languageDetector"&&(this.modules.languageDetector=t),t.type==="i18nFormat"&&(this.modules.i18nFormat=t),t.type==="postProcessor"&&Ev.addPostProcessor(t),t.type==="formatter"&&(this.modules.formatter=t),t.type==="3rdParty"&&this.modules.external.push(t),this}setResolvedLanguage(t){if(!(!t||!this.languages)&&!(["cimode","dev"].indexOf(t)>-1))for(let n=0;n<this.languages.length;n++){const r=this.languages[n];if(!(["cimode","dev"].indexOf(r)>-1)&&this.store.hasLanguageSomeTranslations(r)){this.resolvedLanguage=r;break}}}changeLanguage(t,n){var r=this;this.isLanguageChangingTo=t;const o=Gi();this.emit("languageChanging",t);const i=l=>{this.language=l,this.languages=this.services.languageUtils.toResolveHierarchy(l),this.resolvedLanguage=void 0,this.setResolvedLanguage(l)},s=(l,u)=>{u?(i(u),this.translator.changeLanguage(u),this.isLanguageChangingTo=void 0,this.emit("languageChanged",u),this.logger.log("languageChanged",u)):this.isLanguageChangingTo=void 0,o.resolve(function(){return r.t(...arguments)}),n&&n(l,function(){return r.t(...arguments)})},a=l=>{!t&&!l&&this.services.languageDetector&&(l=[]);const u=typeof l=="string"?l:this.services.languageUtils.getBestMatchFromCodes(l);u&&(this.language||i(u),this.translator.language||this.translator.changeLanguage(u),this.services.languageDetector&&this.services.languageDetector.cacheUserLanguage&&this.services.languageDetector.cacheUserLanguage(u)),this.loadResources(u,c=>{s(c,u)})};return!t&&this.services.languageDetector&&!this.services.languageDetector.async?a(this.services.languageDetector.detect()):!t&&this.services.languageDetector&&this.services.languageDetector.async?this.services.languageDetector.detect.length===0?this.services.languageDetector.detect().then(a):this.services.languageDetector.detect(a):a(t),o}getFixedT(t,n,r){var o=this;const i=function(s,a){let l;if(typeof a!="object"){for(var u=arguments.length,c=new Array(u>2?u-2:0),d=2;d<u;d++)c[d-2]=arguments[d];l=o.options.overloadTranslationOptionHandler([s,a].concat(c))}else l={...a};l.lng=l.lng||i.lng,l.lngs=l.lngs||i.lngs,l.ns=l.ns||i.ns,l.keyPrefix=l.keyPrefix||r||i.keyPrefix;const f=o.options.keySeparator||".";let h;return l.keyPrefix&&Array.isArray(s)?h=s.map(y=>`${l.keyPrefix}${f}${y}`):h=l.keyPrefix?`${l.keyPrefix}${f}${s}`:s,o.t(h,l)};return typeof t=="string"?i.lng=t:i.lngs=t,i.ns=n,i.keyPrefix=r,i}t(){return this.translator&&this.translator.translate(...arguments)}exists(){return this.translator&&this.translator.exists(...arguments)}setDefaultNamespace(t){this.options.defaultNS=t}hasLoadedNamespace(t){let n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(!this.isInitialized)return this.logger.warn("hasLoadedNamespace: i18next was not initialized",this.languages),!1;if(!this.languages||!this.languages.length)return this.logger.warn("hasLoadedNamespace: i18n.languages were undefined or empty",this.languages),!1;const r=n.lng||this.resolvedLanguage||this.languages[0],o=this.options?this.options.fallbackLng:!1,i=this.languages[this.languages.length-1];if(r.toLowerCase()==="cimode")return!0;const s=(a,l)=>{const u=this.services.backendConnector.state[`${a}|${l}`];return u===-1||u===2};if(n.precheck){const a=n.precheck(this,s);if(a!==void 0)return a}return!!(this.hasResourceBundle(r,t)||!this.services.backendConnector.backend||this.options.resources&&!this.options.partialBundledLanguages||s(r,t)&&(!o||s(i,t)))}loadNamespaces(t,n){const r=Gi();return this.options.ns?(typeof t=="string"&&(t=[t]),t.forEach(o=>{this.options.ns.indexOf(o)<0&&this.options.ns.push(o)}),this.loadResources(o=>{r.resolve(),n&&n(o)}),r):(n&&n(),Promise.resolve())}loadLanguages(t,n){const r=Gi();typeof t=="string"&&(t=[t]);const o=this.options.preload||[],i=t.filter(s=>o.indexOf(s)<0);return i.length?(this.options.preload=o.concat(i),this.loadResources(s=>{r.resolve(),n&&n(s)}),r):(n&&n(),Promise.resolve())}dir(t){if(t||(t=this.resolvedLanguage||(this.languages&&this.languages.length>0?this.languages[0]:this.language)),!t)return"rtl";const n=["ar","shu","sqr","ssh","xaa","yhd","yud","aao","abh","abv","acm","acq","acw","acx","acy","adf","ads","aeb","aec","afb","ajp","apc","apd","arb","arq","ars","ary","arz","auz","avl","ayh","ayl","ayn","ayp","bbz","pga","he","iw","ps","pbt","pbu","pst","prp","prd","ug","ur","ydd","yds","yih","ji","yi","hbo","men","xmn","fa","jpr","peo","pes","prs","dv","sam","ckb"],r=this.services&&this.services.languageUtils||new Yg(Zg());return n.indexOf(r.getLanguagePartFromCode(t))>-1||t.toLowerCase().indexOf("-arab")>1?"rtl":"ltr"}static createInstance(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},n=arguments.length>1?arguments[1]:void 0;return new _s(t,n)}cloneInstance(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:ba;const r=t.forkResourceStore;r&&delete t.forkResourceStore;const o={...this.options,...t,isClone:!0},i=new _s(o);return(t.debug!==void 0||t.prefix!==void 0)&&(i.logger=i.logger.clone(t)),["store","services","language"].forEach(a=>{i[a]=this[a]}),i.services={...this.services},i.services.utils={hasLoadedNamespace:i.hasLoadedNamespace.bind(i)},r&&(i.store=new Kg(this.store.data,o),i.services.resourceStore=i.store),i.translator=new Ol(i.services,o),i.translator.on("*",function(a){for(var l=arguments.length,u=new Array(l>1?l-1:0),c=1;c<l;c++)u[c-1]=arguments[c];i.emit(a,...u)}),i.init(o,n),i.translator.options=o,i.translator.backendConnector.services.utils={hasLoadedNamespace:i.hasLoadedNamespace.bind(i)},i}toJSON(){return{options:this.options,store:this.store,language:this.language,languages:this.languages,resolvedLanguage:this.resolvedLanguage}}}const ot=_s.createInstance();ot.createInstance=_s.createInstance;ot.createInstance;ot.dir;ot.init;ot.loadResources;ot.reloadResources;ot.use;const $C=ot.changeLanguage;ot.getFixedT;const F=ot.t;ot.exists;ot.setDefaultNamespace;ot.hasLoadedNamespace;ot.loadNamespaces;ot.loadLanguages;function ks({title:e,children:t}){return S.jsxs("div",{className:"shadow-card overflow-hidden rounded-md border border-accent-200 bg-white",children:[S.jsx("p",{className:"border-b border-accent-200 px-4 py-2.5 font-semibold",children:e}),t]})}const Mf="-";function EC(e){const t=PC(e),{conflictingClassGroups:n,conflictingClassGroupModifiers:r}=e;function o(s){const a=s.split(Mf);return a[0]===""&&a.length!==1&&a.shift(),Rv(a,t)||RC(s)}function i(s,a){const l=n[s]||[];return a&&r[s]?[...l,...r[s]]:l}return{getClassGroupId:o,getConflictingClassGroupIds:i}}function Rv(e,t){var s;if(e.length===0)return t.classGroupId;const n=e[0],r=t.nextPart.get(n),o=r?Rv(e.slice(1),r):void 0;if(o)return o;if(t.validators.length===0)return;const i=e.join(Mf);return(s=t.validators.find(({validator:a})=>a(i)))==null?void 0:s.classGroupId}const eh=/^\[(.+)\]$/;function RC(e){if(eh.test(e)){const t=eh.exec(e)[1],n=t==null?void 0:t.substring(0,t.indexOf(":"));if(n)return"arbitrary.."+n}}function PC(e){const{theme:t,prefix:n}=e,r={nextPart:new Map,validators:[]};return kC(Object.entries(e.classGroups),n).forEach(([i,s])=>{xd(s,r,i,t)}),r}function xd(e,t,n,r){e.forEach(o=>{if(typeof o=="string"){const i=o===""?t:th(t,o);i.classGroupId=n;return}if(typeof o=="function"){if(_C(o)){xd(o(r),t,n,r);return}t.validators.push({validator:o,classGroupId:n});return}Object.entries(o).forEach(([i,s])=>{xd(s,th(t,i),n,r)})})}function th(e,t){let n=e;return t.split(Mf).forEach(r=>{n.nextPart.has(r)||n.nextPart.set(r,{nextPart:new Map,validators:[]}),n=n.nextPart.get(r)}),n}function _C(e){return e.isThemeGetter}function kC(e,t){return t?e.map(([n,r])=>{const o=r.map(i=>typeof i=="string"?t+i:typeof i=="object"?Object.fromEntries(Object.entries(i).map(([s,a])=>[t+s,a])):i);return[n,o]}):e}function NC(e){if(e<1)return{get:()=>{},set:()=>{}};let t=0,n=new Map,r=new Map;function o(i,s){n.set(i,s),t++,t>e&&(t=0,r=n,n=new Map)}return{get(i){let s=n.get(i);if(s!==void 0)return s;if((s=r.get(i))!==void 0)return o(i,s),s},set(i,s){n.has(i)?n.set(i,s):o(i,s)}}}const Pv="!";function OC(e){const t=e.separator,n=t.length===1,r=t[0],o=t.length;return function(s){const a=[];let l=0,u=0,c;for(let v=0;v<s.length;v++){let x=s[v];if(l===0){if(x===r&&(n||s.slice(v,v+o)===t)){a.push(s.slice(u,v)),u=v+o;continue}if(x==="/"){c=v;continue}}x==="["?l++:x==="]"&&l--}const d=a.length===0?s:s.substring(u),f=d.startsWith(Pv),h=f?d.substring(1):d,y=c&&c>u?c-u:void 0;return{modifiers:a,hasImportantModifier:f,baseClassName:h,maybePostfixModifierPosition:y}}}function TC(e){if(e.length<=1)return e;const t=[];let n=[];return e.forEach(r=>{r[0]==="["?(t.push(...n.sort(),r),n=[]):n.push(r)}),t.push(...n.sort()),t}function AC(e){return{cache:NC(e.cacheSize),splitModifiers:OC(e),...EC(e)}}const MC=/\s+/;function DC(e,t){const{splitModifiers:n,getClassGroupId:r,getConflictingClassGroupIds:o}=t,i=new Set;return e.trim().split(MC).map(s=>{const{modifiers:a,hasImportantModifier:l,baseClassName:u,maybePostfixModifierPosition:c}=n(s);let d=r(c?u.substring(0,c):u),f=!!c;if(!d){if(!c)return{isTailwindClass:!1,originalClassName:s};if(d=r(u),!d)return{isTailwindClass:!1,originalClassName:s};f=!1}const h=TC(a).join(":");return{isTailwindClass:!0,modifierId:l?h+Pv:h,classGroupId:d,originalClassName:s,hasPostfixModifier:f}}).reverse().filter(s=>{if(!s.isTailwindClass)return!0;const{modifierId:a,classGroupId:l,hasPostfixModifier:u}=s,c=a+l;return i.has(c)?!1:(i.add(c),o(l,u).forEach(d=>i.add(a+d)),!0)}).reverse().map(s=>s.originalClassName).join(" ")}function LC(){let e=0,t,n,r="";for(;e<arguments.length;)(t=arguments[e++])&&(n=_v(t))&&(r&&(r+=" "),r+=n);return r}function _v(e){if(typeof e=="string")return e;let t,n="";for(let r=0;r<e.length;r++)e[r]&&(t=_v(e[r]))&&(n&&(n+=" "),n+=t);return n}function IC(e,...t){let n,r,o,i=s;function s(l){const u=t.reduce((c,d)=>d(c),e());return n=AC(u),r=n.cache.get,o=n.cache.set,i=a,a(l)}function a(l){const u=r(l);if(u)return u;const c=DC(l,n);return o(l,c),c}return function(){return i(LC.apply(null,arguments))}}function Se(e){const t=n=>n[e]||[];return t.isThemeGetter=!0,t}const kv=/^\[(?:([a-z-]+):)?(.+)\]$/i,FC=/^\d+\/\d+$/,jC=new Set(["px","full","screen"]),zC=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,VC=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,HC=/^-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,UC=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/;function Pn(e){return Wr(e)||jC.has(e)||FC.test(e)}function er(e){return Pi(e,"length",XC)}function Wr(e){return!!e&&!Number.isNaN(Number(e))}function $a(e){return Pi(e,"number",Wr)}function Ki(e){return!!e&&Number.isInteger(Number(e))}function BC(e){return e.endsWith("%")&&Wr(e.slice(0,-1))}function oe(e){return kv.test(e)}function tr(e){return zC.test(e)}const WC=new Set(["length","size","percentage"]);function GC(e){return Pi(e,WC,Nv)}function KC(e){return Pi(e,"position",Nv)}const QC=new Set(["image","url"]);function YC(e){return Pi(e,QC,JC)}function qC(e){return Pi(e,"",ZC)}function Qi(){return!0}function Pi(e,t,n){const r=kv.exec(e);return r?r[1]?typeof t=="string"?r[1]===t:t.has(r[1]):n(r[2]):!1}function XC(e){return VC.test(e)}function Nv(){return!1}function ZC(e){return HC.test(e)}function JC(e){return UC.test(e)}function eb(){const e=Se("colors"),t=Se("spacing"),n=Se("blur"),r=Se("brightness"),o=Se("borderColor"),i=Se("borderRadius"),s=Se("borderSpacing"),a=Se("borderWidth"),l=Se("contrast"),u=Se("grayscale"),c=Se("hueRotate"),d=Se("invert"),f=Se("gap"),h=Se("gradientColorStops"),y=Se("gradientColorStopPositions"),v=Se("inset"),x=Se("margin"),m=Se("opacity"),g=Se("padding"),w=Se("saturate"),C=Se("scale"),E=Se("sepia"),_=Se("skew"),P=Se("space"),R=Se("translate"),O=()=>["auto","contain","none"],T=()=>["auto","hidden","clip","visible","scroll"],V=()=>["auto",oe,t],j=()=>[oe,t],Q=()=>["",Pn,er],z=()=>["auto",Wr,oe],Y=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],U=()=>["solid","dashed","dotted","double","none"],q=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity","plus-lighter"],k=()=>["start","end","center","between","around","evenly","stretch"],I=()=>["","0",oe],D=()=>["auto","avoid","all","avoid-page","page","left","right","column"],G=()=>[Wr,$a],H=()=>[Wr,oe];return{cacheSize:500,separator:":",theme:{colors:[Qi],spacing:[Pn,er],blur:["none","",tr,oe],brightness:G(),borderColor:[e],borderRadius:["none","","full",tr,oe],borderSpacing:j(),borderWidth:Q(),contrast:G(),grayscale:I(),hueRotate:H(),invert:I(),gap:j(),gradientColorStops:[e],gradientColorStopPositions:[BC,er],inset:V(),margin:V(),opacity:G(),padding:j(),saturate:G(),scale:G(),sepia:I(),skew:H(),space:j(),translate:j()},classGroups:{aspect:[{aspect:["auto","square","video",oe]}],container:["container"],columns:[{columns:[tr]}],"break-after":[{"break-after":D()}],"break-before":[{"break-before":D()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...Y(),oe]}],overflow:[{overflow:T()}],"overflow-x":[{"overflow-x":T()}],"overflow-y":[{"overflow-y":T()}],overscroll:[{overscroll:O()}],"overscroll-x":[{"overscroll-x":O()}],"overscroll-y":[{"overscroll-y":O()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[v]}],"inset-x":[{"inset-x":[v]}],"inset-y":[{"inset-y":[v]}],start:[{start:[v]}],end:[{end:[v]}],top:[{top:[v]}],right:[{right:[v]}],bottom:[{bottom:[v]}],left:[{left:[v]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",Ki,oe]}],basis:[{basis:V()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",oe]}],grow:[{grow:I()}],shrink:[{shrink:I()}],order:[{order:["first","last","none",Ki,oe]}],"grid-cols":[{"grid-cols":[Qi]}],"col-start-end":[{col:["auto",{span:["full",Ki,oe]},oe]}],"col-start":[{"col-start":z()}],"col-end":[{"col-end":z()}],"grid-rows":[{"grid-rows":[Qi]}],"row-start-end":[{row:["auto",{span:[Ki,oe]},oe]}],"row-start":[{"row-start":z()}],"row-end":[{"row-end":z()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",oe]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",oe]}],gap:[{gap:[f]}],"gap-x":[{"gap-x":[f]}],"gap-y":[{"gap-y":[f]}],"justify-content":[{justify:["normal",...k()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...k(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...k(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[g]}],px:[{px:[g]}],py:[{py:[g]}],ps:[{ps:[g]}],pe:[{pe:[g]}],pt:[{pt:[g]}],pr:[{pr:[g]}],pb:[{pb:[g]}],pl:[{pl:[g]}],m:[{m:[x]}],mx:[{mx:[x]}],my:[{my:[x]}],ms:[{ms:[x]}],me:[{me:[x]}],mt:[{mt:[x]}],mr:[{mr:[x]}],mb:[{mb:[x]}],ml:[{ml:[x]}],"space-x":[{"space-x":[P]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[P]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",oe,t]}],"min-w":[{"min-w":[oe,t,"min","max","fit"]}],"max-w":[{"max-w":[oe,t,"none","full","min","max","fit","prose",{screen:[tr]},tr]}],h:[{h:[oe,t,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[oe,t,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[oe,t,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[oe,t,"auto","min","max","fit"]}],"font-size":[{text:["base",tr,er]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",$a]}],"font-family":[{font:[Qi]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractons"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",oe]}],"line-clamp":[{"line-clamp":["none",Wr,$a]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",Pn,oe]}],"list-image":[{"list-image":["none",oe]}],"list-style-type":[{list:["none","disc","decimal",oe]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[m]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[m]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...U(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",Pn,er]}],"underline-offset":[{"underline-offset":["auto",Pn,oe]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:j()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",oe]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",oe]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[m]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...Y(),KC]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",GC]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},YC]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[y]}],"gradient-via-pos":[{via:[y]}],"gradient-to-pos":[{to:[y]}],"gradient-from":[{from:[h]}],"gradient-via":[{via:[h]}],"gradient-to":[{to:[h]}],rounded:[{rounded:[i]}],"rounded-s":[{"rounded-s":[i]}],"rounded-e":[{"rounded-e":[i]}],"rounded-t":[{"rounded-t":[i]}],"rounded-r":[{"rounded-r":[i]}],"rounded-b":[{"rounded-b":[i]}],"rounded-l":[{"rounded-l":[i]}],"rounded-ss":[{"rounded-ss":[i]}],"rounded-se":[{"rounded-se":[i]}],"rounded-ee":[{"rounded-ee":[i]}],"rounded-es":[{"rounded-es":[i]}],"rounded-tl":[{"rounded-tl":[i]}],"rounded-tr":[{"rounded-tr":[i]}],"rounded-br":[{"rounded-br":[i]}],"rounded-bl":[{"rounded-bl":[i]}],"border-w":[{border:[a]}],"border-w-x":[{"border-x":[a]}],"border-w-y":[{"border-y":[a]}],"border-w-s":[{"border-s":[a]}],"border-w-e":[{"border-e":[a]}],"border-w-t":[{"border-t":[a]}],"border-w-r":[{"border-r":[a]}],"border-w-b":[{"border-b":[a]}],"border-w-l":[{"border-l":[a]}],"border-opacity":[{"border-opacity":[m]}],"border-style":[{border:[...U(),"hidden"]}],"divide-x":[{"divide-x":[a]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[a]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[m]}],"divide-style":[{divide:U()}],"border-color":[{border:[o]}],"border-color-x":[{"border-x":[o]}],"border-color-y":[{"border-y":[o]}],"border-color-t":[{"border-t":[o]}],"border-color-r":[{"border-r":[o]}],"border-color-b":[{"border-b":[o]}],"border-color-l":[{"border-l":[o]}],"divide-color":[{divide:[o]}],"outline-style":[{outline:["",...U()]}],"outline-offset":[{"outline-offset":[Pn,oe]}],"outline-w":[{outline:[Pn,er]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:Q()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[m]}],"ring-offset-w":[{"ring-offset":[Pn,er]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",tr,qC]}],"shadow-color":[{shadow:[Qi]}],opacity:[{opacity:[m]}],"mix-blend":[{"mix-blend":q()}],"bg-blend":[{"bg-blend":q()}],filter:[{filter:["","none"]}],blur:[{blur:[n]}],brightness:[{brightness:[r]}],contrast:[{contrast:[l]}],"drop-shadow":[{"drop-shadow":["","none",tr,oe]}],grayscale:[{grayscale:[u]}],"hue-rotate":[{"hue-rotate":[c]}],invert:[{invert:[d]}],saturate:[{saturate:[w]}],sepia:[{sepia:[E]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[n]}],"backdrop-brightness":[{"backdrop-brightness":[r]}],"backdrop-contrast":[{"backdrop-contrast":[l]}],"backdrop-grayscale":[{"backdrop-grayscale":[u]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[c]}],"backdrop-invert":[{"backdrop-invert":[d]}],"backdrop-opacity":[{"backdrop-opacity":[m]}],"backdrop-saturate":[{"backdrop-saturate":[w]}],"backdrop-sepia":[{"backdrop-sepia":[E]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[s]}],"border-spacing-x":[{"border-spacing-x":[s]}],"border-spacing-y":[{"border-spacing-y":[s]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",oe]}],duration:[{duration:H()}],ease:[{ease:["linear","in","out","in-out",oe]}],delay:[{delay:H()}],animate:[{animate:["none","spin","ping","pulse","bounce",oe]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[C]}],"scale-x":[{"scale-x":[C]}],"scale-y":[{"scale-y":[C]}],rotate:[{rotate:[Ki,oe]}],"translate-x":[{"translate-x":[R]}],"translate-y":[{"translate-y":[R]}],"skew-x":[{"skew-x":[_]}],"skew-y":[{"skew-y":[_]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",oe]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",oe]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":j()}],"scroll-mx":[{"scroll-mx":j()}],"scroll-my":[{"scroll-my":j()}],"scroll-ms":[{"scroll-ms":j()}],"scroll-me":[{"scroll-me":j()}],"scroll-mt":[{"scroll-mt":j()}],"scroll-mr":[{"scroll-mr":j()}],"scroll-mb":[{"scroll-mb":j()}],"scroll-ml":[{"scroll-ml":j()}],"scroll-p":[{"scroll-p":j()}],"scroll-px":[{"scroll-px":j()}],"scroll-py":[{"scroll-py":j()}],"scroll-ps":[{"scroll-ps":j()}],"scroll-pe":[{"scroll-pe":j()}],"scroll-pt":[{"scroll-pt":j()}],"scroll-pr":[{"scroll-pr":j()}],"scroll-pb":[{"scroll-pb":j()}],"scroll-pl":[{"scroll-pl":j()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",oe]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[Pn,er,$a]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}}const pe=IC(eb),Js=window.location.origin;async function iu({url:e,method:t,payload:n,headers:r}){const o={method:t,headers:{"Content-Type":"application/json",...r}};n&&(o.body=JSON.stringify(n));const i=await fetch(`${Js}${e}`,o);return i.ok?await i.json():i.json().then(s=>{throw new Error(s.code)})}async function tb(e,t=750){const[n]=await Promise.all([e,new Promise(r=>setTimeout(r,t))]);return n}function zr(e,t){return Intl.NumberFormat(e,{style:"decimal",minimumFractionDigits:2,maximumFractionDigits:2}).format(t)}const Df=p.forwardRef(({className:e,rootClassName:t,...n},r)=>S.jsx("div",{className:pe("w-full overflow-auto",t),children:S.jsx("table",{ref:r,className:pe("w-full caption-bottom text-sm",e),...n})}));Df.displayName="Table";const Lf=p.forwardRef(({className:e,...t},n)=>S.jsx("thead",{ref:n,className:pe("[&_tr]:border-b",e),...t}));Lf.displayName="TableHeader";const If=p.forwardRef(({className:e,...t},n)=>S.jsx("tbody",{ref:n,className:pe("[&_tr:last-child]:border-0",e),...t}));If.displayName="TableBody";const nb=p.forwardRef(({className:e,...t},n)=>S.jsx("tfoot",{ref:n,className:pe("bg-primary font-medium",e),...t}));nb.displayName="TableFooter";const Ns=p.forwardRef(({className:e,...t},n)=>S.jsx("tr",{ref:n,className:pe("border-b",e),...t}));Ns.displayName="TableRow";const an=p.forwardRef(({className:e,...t},n)=>S.jsx("th",{ref:n,className:pe("h-12 px-4 text-left align-middle font-medium",e),...t}));an.displayName="TableHead";const ln=p.forwardRef(({className:e,...t},n)=>S.jsx("td",{ref:n,className:pe("p-4 align-middle",e),...t}));ln.displayName="TableCell";const rb=p.forwardRef(({className:e,...t},n)=>S.jsx("caption",{ref:n,className:pe("mt-4 text-sm",e),...t}));rb.displayName="TableCaption";function Dr(e){return new URLSearchParams(window.location.search).get(e)??""}function Qn(){const e=Dr("reconciliationId");return fS({queryKey:["reconciliation-details"],queryFn:async()=>{if(!e)throw new Error("common_error");return await tb(iu({url:`/api/eops/online-reconciliation/details/${e}`}))}})}const nh={"waiting-for-approval":{key:"waiting-for-approval",color:"bg-gray-200 text-gray-700"},"partially-denied":{key:"partially-denied",color:"bg-red-200 text-red-700"},approved:{key:"approved",color:"bg-green-200 text-green-700"},denied:{key:"denied",color:"bg-red-200 text-red-700"}};function ob(){var n,r;const{data:e}=Qn(),t=Dr("locale")||"en";return S.jsx(ks,{title:F("reconciliation-info"),children:S.jsx("div",{className:"card-padding",children:S.jsxs(Df,{children:[S.jsx(Lf,{children:S.jsxs(Ns,{className:"bg-accent text-xs [&>th]:whitespace-nowrap [&>th]:border [&>th]:p-4",children:[S.jsx(an,{children:F("reconciliation-code")}),S.jsx(an,{children:F("reconciliation-date")}),S.jsx(an,{children:F("amount")}),S.jsx(an,{children:F("balance-type")}),S.jsx(an,{children:F("document-count")}),S.jsx(an,{children:F("period")}),S.jsx(an,{children:F("status")}),S.jsx(an,{children:F("expiry-date")})]})}),S.jsx(If,{children:S.jsxs(Ns,{className:"cursor-default bg-white text-sm transition hover:bg-accent/50 [&>td]:border [&>td]:p-4",children:[S.jsx(ln,{children:e==null?void 0:e.code}),S.jsx(ln,{children:e!=null&&e.reconciliationDate?new Date(e.reconciliationDate).toLocaleDateString(t,{hour:"numeric",minute:"numeric"}):"---"}),S.jsxs(ln,{className:"text-right font-semibold",children:[zr(t,(e==null?void 0:e.balance)??0),"₺"]}),S.jsx(ln,{children:F(e==null?void 0:e.balanceType)}),S.jsx(ln,{children:e==null?void 0:e.documentCount}),S.jsx(ln,{children:e==null?void 0:e.periodName}),S.jsx(ln,{children:S.jsx("span",{className:pe("whitespace-nowrap rounded-full p-2 text-xs font-semibold",(n=nh[(e==null?void 0:e.status)??"waiting-for-approval"])==null?void 0:n.color),children:F((r=nh[(e==null?void 0:e.status)??"waiting-for-approval"])==null?void 0:r.key)})}),S.jsx(ln,{children:e!=null&&e.expiryDate?new Date(e.expiryDate).toLocaleDateString(t,{hour:"numeric",minute:"numeric"}):"---"})]})})]})})})}function Z(){return Z=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Z.apply(this,arguments)}function rh(e,[t,n]){return Math.min(n,Math.max(t,e))}function Ee(e,t,{checkForDefaultPrevented:n=!0}={}){return function(o){if(e==null||e(o),n===!1||!o.defaultPrevented)return t==null?void 0:t(o)}}function ib(e,t){const n=p.createContext(t);function r(i){const{children:s,...a}=i,l=p.useMemo(()=>a,Object.values(a));return p.createElement(n.Provider,{value:l},s)}function o(i){const s=p.useContext(n);if(s)return s;if(t!==void 0)return t;throw new Error(`\`${i}\` must be used within \`${e}\``)}return r.displayName=e+"Provider",[r,o]}function _i(e,t=[]){let n=[];function r(i,s){const a=p.createContext(s),l=n.length;n=[...n,s];function u(d){const{scope:f,children:h,...y}=d,v=(f==null?void 0:f[e][l])||a,x=p.useMemo(()=>y,Object.values(y));return p.createElement(v.Provider,{value:x},h)}function c(d,f){const h=(f==null?void 0:f[e][l])||a,y=p.useContext(h);if(y)return y;if(s!==void 0)return s;throw new Error(`\`${d}\` must be used within \`${i}\``)}return u.displayName=i+"Provider",[u,c]}const o=()=>{const i=n.map(s=>p.createContext(s));return function(a){const l=(a==null?void 0:a[e])||i;return p.useMemo(()=>({[`__scope${e}`]:{...a,[e]:l}}),[a,l])}};return o.scopeName=e,[r,sb(o,...t)]}function sb(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(i){const s=r.reduce((a,{useScope:l,scopeName:u})=>{const d=l(i)[`__scope${u}`];return{...a,...d}},{});return p.useMemo(()=>({[`__scope${t.scopeName}`]:s}),[s])}};return n.scopeName=t.scopeName,n}function ab(e,t){typeof e=="function"?e(t):e!=null&&(e.current=t)}function Ov(...e){return t=>e.forEach(n=>ab(n,t))}function Ie(...e){return p.useCallback(Ov(...e),e)}const co=p.forwardRef((e,t)=>{const{children:n,...r}=e,o=p.Children.toArray(n),i=o.find(lb);if(i){const s=i.props.children,a=o.map(l=>l===i?p.Children.count(s)>1?p.Children.only(null):p.isValidElement(s)?s.props.children:null:l);return p.createElement(Sd,Z({},r,{ref:t}),p.isValidElement(s)?p.cloneElement(s,void 0,a):null)}return p.createElement(Sd,Z({},r,{ref:t}),n)});co.displayName="Slot";const Sd=p.forwardRef((e,t)=>{const{children:n,...r}=e;return p.isValidElement(n)?p.cloneElement(n,{...ub(r,n.props),ref:t?Ov(t,n.ref):n.ref}):p.Children.count(n)>1?p.Children.only(null):null});Sd.displayName="SlotClone";const Tv=({children:e})=>p.createElement(p.Fragment,null,e);function lb(e){return p.isValidElement(e)&&e.type===Tv}function ub(e,t){const n={...t};for(const r in t){const o=e[r],i=t[r];/^on[A-Z]/.test(r)?o&&i?n[r]=(...a)=>{i(...a),o(...a)}:o&&(n[r]=o):r==="style"?n[r]={...o,...i}:r==="className"&&(n[r]=[o,i].filter(Boolean).join(" "))}return{...e,...n}}function cb(e){const t=e+"CollectionProvider",[n,r]=_i(t),[o,i]=n(t,{collectionRef:{current:null},itemMap:new Map}),s=h=>{const{scope:y,children:v}=h,x=A.useRef(null),m=A.useRef(new Map).current;return A.createElement(o,{scope:y,itemMap:m,collectionRef:x},v)},a=e+"CollectionSlot",l=A.forwardRef((h,y)=>{const{scope:v,children:x}=h,m=i(a,v),g=Ie(y,m.collectionRef);return A.createElement(co,{ref:g},x)}),u=e+"CollectionItemSlot",c="data-radix-collection-item",d=A.forwardRef((h,y)=>{const{scope:v,children:x,...m}=h,g=A.useRef(null),w=Ie(y,g),C=i(u,v);return A.useEffect(()=>(C.itemMap.set(g,{ref:g,...m}),()=>void C.itemMap.delete(g))),A.createElement(co,{[c]:"",ref:w},x)});function f(h){const y=i(e+"CollectionConsumer",h);return A.useCallback(()=>{const x=y.collectionRef.current;if(!x)return[];const m=Array.from(x.querySelectorAll(`[${c}]`));return Array.from(y.itemMap.values()).sort((C,E)=>m.indexOf(C.ref.current)-m.indexOf(E.ref.current))},[y.collectionRef,y.itemMap])}return[{Provider:s,Slot:l,ItemSlot:d},f,r]}const db=p.createContext(void 0);function fb(e){const t=p.useContext(db);return e||t||"ltr"}const pb=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"],Te=pb.reduce((e,t)=>{const n=p.forwardRef((r,o)=>{const{asChild:i,...s}=r,a=i?co:t;return p.useEffect(()=>{window[Symbol.for("radix-ui")]=!0},[]),p.createElement(a,Z({},s,{ref:o}))});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{});function gb(e,t){e&&ho.flushSync(()=>e.dispatchEvent(t))}function Wn(e){const t=p.useRef(e);return p.useEffect(()=>{t.current=e}),p.useMemo(()=>(...n)=>{var r;return(r=t.current)===null||r===void 0?void 0:r.call(t,...n)},[])}function hb(e,t=globalThis==null?void 0:globalThis.document){const n=Wn(e);p.useEffect(()=>{const r=o=>{o.key==="Escape"&&n(o)};return t.addEventListener("keydown",r),()=>t.removeEventListener("keydown",r)},[n,t])}const Cd="dismissableLayer.update",mb="dismissableLayer.pointerDownOutside",vb="dismissableLayer.focusOutside";let oh;const yb=p.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),Ff=p.forwardRef((e,t)=>{var n;const{disableOutsidePointerEvents:r=!1,onEscapeKeyDown:o,onPointerDownOutside:i,onFocusOutside:s,onInteractOutside:a,onDismiss:l,...u}=e,c=p.useContext(yb),[d,f]=p.useState(null),h=(n=d==null?void 0:d.ownerDocument)!==null&&n!==void 0?n:globalThis==null?void 0:globalThis.document,[,y]=p.useState({}),v=Ie(t,R=>f(R)),x=Array.from(c.layers),[m]=[...c.layersWithOutsidePointerEventsDisabled].slice(-1),g=x.indexOf(m),w=d?x.indexOf(d):-1,C=c.layersWithOutsidePointerEventsDisabled.size>0,E=w>=g,_=wb(R=>{const O=R.target,T=[...c.branches].some(V=>V.contains(O));!E||T||(i==null||i(R),a==null||a(R),R.defaultPrevented||l==null||l())},h),P=xb(R=>{const O=R.target;[...c.branches].some(V=>V.contains(O))||(s==null||s(R),a==null||a(R),R.defaultPrevented||l==null||l())},h);return hb(R=>{w===c.layers.size-1&&(o==null||o(R),!R.defaultPrevented&&l&&(R.preventDefault(),l()))},h),p.useEffect(()=>{if(d)return r&&(c.layersWithOutsidePointerEventsDisabled.size===0&&(oh=h.body.style.pointerEvents,h.body.style.pointerEvents="none"),c.layersWithOutsidePointerEventsDisabled.add(d)),c.layers.add(d),ih(),()=>{r&&c.layersWithOutsidePointerEventsDisabled.size===1&&(h.body.style.pointerEvents=oh)}},[d,h,r,c]),p.useEffect(()=>()=>{d&&(c.layers.delete(d),c.layersWithOutsidePointerEventsDisabled.delete(d),ih())},[d,c]),p.useEffect(()=>{const R=()=>y({});return document.addEventListener(Cd,R),()=>document.removeEventListener(Cd,R)},[]),p.createElement(Te.div,Z({},u,{ref:v,style:{pointerEvents:C?E?"auto":"none":void 0,...e.style},onFocusCapture:Ee(e.onFocusCapture,P.onFocusCapture),onBlurCapture:Ee(e.onBlurCapture,P.onBlurCapture),onPointerDownCapture:Ee(e.onPointerDownCapture,_.onPointerDownCapture)}))});function wb(e,t=globalThis==null?void 0:globalThis.document){const n=Wn(e),r=p.useRef(!1),o=p.useRef(()=>{});return p.useEffect(()=>{const i=a=>{if(a.target&&!r.current){let c=function(){Av(mb,n,u,{discrete:!0})};var l=c;const u={originalEvent:a};a.pointerType==="touch"?(t.removeEventListener("click",o.current),o.current=c,t.addEventListener("click",o.current,{once:!0})):c()}r.current=!1},s=window.setTimeout(()=>{t.addEventListener("pointerdown",i)},0);return()=>{window.clearTimeout(s),t.removeEventListener("pointerdown",i),t.removeEventListener("click",o.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}function xb(e,t=globalThis==null?void 0:globalThis.document){const n=Wn(e),r=p.useRef(!1);return p.useEffect(()=>{const o=i=>{i.target&&!r.current&&Av(vb,n,{originalEvent:i},{discrete:!1})};return t.addEventListener("focusin",o),()=>t.removeEventListener("focusin",o)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}function ih(){const e=new CustomEvent(Cd);document.dispatchEvent(e)}function Av(e,t,n,{discrete:r}){const o=n.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?gb(o,i):o.dispatchEvent(i)}let Yu=0;function jf(){p.useEffect(()=>{var e,t;const n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",(e=n[0])!==null&&e!==void 0?e:sh()),document.body.insertAdjacentElement("beforeend",(t=n[1])!==null&&t!==void 0?t:sh()),Yu++,()=>{Yu===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(r=>r.remove()),Yu--}},[])}function sh(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.cssText="outline: none; opacity: 0; position: fixed; pointer-events: none",e}const qu="focusScope.autoFocusOnMount",Xu="focusScope.autoFocusOnUnmount",ah={bubbles:!1,cancelable:!0},zf=p.forwardRef((e,t)=>{const{loop:n=!1,trapped:r=!1,onMountAutoFocus:o,onUnmountAutoFocus:i,...s}=e,[a,l]=p.useState(null),u=Wn(o),c=Wn(i),d=p.useRef(null),f=Ie(t,v=>l(v)),h=p.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;p.useEffect(()=>{if(r){let g=function(_){if(h.paused||!a)return;const P=_.target;a.contains(P)?d.current=P:rr(d.current,{select:!0})},w=function(_){if(h.paused||!a)return;const P=_.relatedTarget;P!==null&&(a.contains(P)||rr(d.current,{select:!0}))},C=function(_){const P=document.activeElement;for(const R of _)R.removedNodes.length>0&&(a!=null&&a.contains(P)||rr(a))};var v=g,x=w,m=C;document.addEventListener("focusin",g),document.addEventListener("focusout",w);const E=new MutationObserver(C);return a&&E.observe(a,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",g),document.removeEventListener("focusout",w),E.disconnect()}}},[r,a,h.paused]),p.useEffect(()=>{if(a){uh.add(h);const v=document.activeElement;if(!a.contains(v)){const m=new CustomEvent(qu,ah);a.addEventListener(qu,u),a.dispatchEvent(m),m.defaultPrevented||(Sb(Rb(Mv(a)),{select:!0}),document.activeElement===v&&rr(a))}return()=>{a.removeEventListener(qu,u),setTimeout(()=>{const m=new CustomEvent(Xu,ah);a.addEventListener(Xu,c),a.dispatchEvent(m),m.defaultPrevented||rr(v??document.body,{select:!0}),a.removeEventListener(Xu,c),uh.remove(h)},0)}}},[a,u,c,h]);const y=p.useCallback(v=>{if(!n&&!r||h.paused)return;const x=v.key==="Tab"&&!v.altKey&&!v.ctrlKey&&!v.metaKey,m=document.activeElement;if(x&&m){const g=v.currentTarget,[w,C]=Cb(g);w&&C?!v.shiftKey&&m===C?(v.preventDefault(),n&&rr(w,{select:!0})):v.shiftKey&&m===w&&(v.preventDefault(),n&&rr(C,{select:!0})):m===g&&v.preventDefault()}},[n,r,h.paused]);return p.createElement(Te.div,Z({tabIndex:-1},s,{ref:f,onKeyDown:y}))});function Sb(e,{select:t=!1}={}){const n=document.activeElement;for(const r of e)if(rr(r,{select:t}),document.activeElement!==n)return}function Cb(e){const t=Mv(e),n=lh(t,e),r=lh(t.reverse(),e);return[n,r]}function Mv(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:r=>{const o=r.tagName==="INPUT"&&r.type==="hidden";return r.disabled||r.hidden||o?NodeFilter.FILTER_SKIP:r.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function lh(e,t){for(const n of e)if(!bb(n,{upTo:t}))return n}function bb(e,{upTo:t}){if(getComputedStyle(e).visibility==="hidden")return!0;for(;e;){if(t!==void 0&&e===t)return!1;if(getComputedStyle(e).display==="none")return!0;e=e.parentElement}return!1}function $b(e){return e instanceof HTMLInputElement&&"select"in e}function rr(e,{select:t=!1}={}){if(e&&e.focus){const n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&$b(e)&&t&&e.select()}}const uh=Eb();function Eb(){let e=[];return{add(t){const n=e[0];t!==n&&(n==null||n.pause()),e=ch(e,t),e.unshift(t)},remove(t){var n;e=ch(e,t),(n=e[0])===null||n===void 0||n.resume()}}}function ch(e,t){const n=[...e],r=n.indexOf(t);return r!==-1&&n.splice(r,1),n}function Rb(e){return e.filter(t=>t.tagName!=="A")}const tn=globalThis!=null&&globalThis.document?p.useLayoutEffect:()=>{},Pb=R1.useId||(()=>{});let _b=0;function ro(e){const[t,n]=p.useState(Pb());return tn(()=>{e||n(r=>r??String(_b++))},[e]),e||(t?`radix-${t}`:"")}function ki(e){return e.split("-")[1]}function Vf(e){return e==="y"?"height":"width"}function Fn(e){return e.split("-")[0]}function mo(e){return["top","bottom"].includes(Fn(e))?"x":"y"}function dh(e,t,n){let{reference:r,floating:o}=e;const i=r.x+r.width/2-o.width/2,s=r.y+r.height/2-o.height/2,a=mo(t),l=Vf(a),u=r[l]/2-o[l]/2,c=a==="x";let d;switch(Fn(t)){case"top":d={x:i,y:r.y-o.height};break;case"bottom":d={x:i,y:r.y+r.height};break;case"right":d={x:r.x+r.width,y:s};break;case"left":d={x:r.x-o.width,y:s};break;default:d={x:r.x,y:r.y}}switch(ki(t)){case"start":d[a]-=u*(n&&c?-1:1);break;case"end":d[a]+=u*(n&&c?-1:1)}return d}const kb=async(e,t,n)=>{const{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:s}=n,a=i.filter(Boolean),l=await(s.isRTL==null?void 0:s.isRTL(t));let u=await s.getElementRects({reference:e,floating:t,strategy:o}),{x:c,y:d}=dh(u,r,l),f=r,h={},y=0;for(let v=0;v<a.length;v++){const{name:x,fn:m}=a[v],{x:g,y:w,data:C,reset:E}=await m({x:c,y:d,initialPlacement:r,placement:f,strategy:o,middlewareData:h,rects:u,platform:s,elements:{reference:e,floating:t}});c=g??c,d=w??d,h={...h,[x]:{...h[x],...C}},E&&y<=50&&(y++,typeof E=="object"&&(E.placement&&(f=E.placement),E.rects&&(u=E.rects===!0?await s.getElementRects({reference:e,floating:t,strategy:o}):E.rects),{x:c,y:d}=dh(u,f,l)),v=-1)}return{x:c,y:d,placement:f,strategy:o,middlewareData:h}};function Gn(e,t){return typeof e=="function"?e(t):e}function Dv(e){return typeof e!="number"?function(t){return{top:0,right:0,bottom:0,left:0,...t}}(e):{top:e,right:e,bottom:e,left:e}}function Tl(e){return{...e,top:e.y,left:e.x,right:e.x+e.width,bottom:e.y+e.height}}async function Os(e,t){var n;t===void 0&&(t={});const{x:r,y:o,platform:i,rects:s,elements:a,strategy:l}=e,{boundary:u="clippingAncestors",rootBoundary:c="viewport",elementContext:d="floating",altBoundary:f=!1,padding:h=0}=Gn(t,e),y=Dv(h),v=a[f?d==="floating"?"reference":"floating":d],x=Tl(await i.getClippingRect({element:(n=await(i.isElement==null?void 0:i.isElement(v)))==null||n?v:v.contextElement||await(i.getDocumentElement==null?void 0:i.getDocumentElement(a.floating)),boundary:u,rootBoundary:c,strategy:l})),m=d==="floating"?{...s.floating,x:r,y:o}:s.reference,g=await(i.getOffsetParent==null?void 0:i.getOffsetParent(a.floating)),w=await(i.isElement==null?void 0:i.isElement(g))&&await(i.getScale==null?void 0:i.getScale(g))||{x:1,y:1},C=Tl(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({rect:m,offsetParent:g,strategy:l}):m);return{top:(x.top-C.top+y.top)/w.y,bottom:(C.bottom-x.bottom+y.bottom)/w.y,left:(x.left-C.left+y.left)/w.x,right:(C.right-x.right+y.right)/w.x}}const Ts=Math.min,Vr=Math.max;function bd(e,t,n){return Vr(e,Ts(t,n))}const fh=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:r,placement:o,rects:i,platform:s,elements:a}=t,{element:l,padding:u=0}=Gn(e,t)||{};if(l==null)return{};const c=Dv(u),d={x:n,y:r},f=mo(o),h=Vf(f),y=await s.getDimensions(l),v=f==="y",x=v?"top":"left",m=v?"bottom":"right",g=v?"clientHeight":"clientWidth",w=i.reference[h]+i.reference[f]-d[f]-i.floating[h],C=d[f]-i.reference[f],E=await(s.getOffsetParent==null?void 0:s.getOffsetParent(l));let _=E?E[g]:0;_&&await(s.isElement==null?void 0:s.isElement(E))||(_=a.floating[g]||i.floating[h]);const P=w/2-C/2,R=_/2-y[h]/2-1,O=Ts(c[x],R),T=Ts(c[m],R),V=O,j=_-y[h]-T,Q=_/2-y[h]/2+P,z=bd(V,Q,j),Y=ki(o)!=null&&Q!=z&&i.reference[h]/2-(Q<V?O:T)-y[h]/2<0?Q<V?V-Q:j-Q:0;return{[f]:d[f]-Y,data:{[f]:z,centerOffset:Q-z+Y}}}}),Lv=["top","right","bottom","left"];Lv.reduce((e,t)=>e.concat(t,t+"-start",t+"-end"),[]);const Nb={left:"right",right:"left",bottom:"top",top:"bottom"};function Al(e){return e.replace(/left|right|bottom|top/g,t=>Nb[t])}function Ob(e,t,n){n===void 0&&(n=!1);const r=ki(e),o=mo(e),i=Vf(o);let s=o==="x"?r===(n?"end":"start")?"right":"left":r==="start"?"bottom":"top";return t.reference[i]>t.floating[i]&&(s=Al(s)),{main:s,cross:Al(s)}}const Tb={start:"end",end:"start"};function Zu(e){return e.replace(/start|end/g,t=>Tb[t])}const Ab=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(t){var n;const{placement:r,middlewareData:o,rects:i,initialPlacement:s,platform:a,elements:l}=t,{mainAxis:u=!0,crossAxis:c=!0,fallbackPlacements:d,fallbackStrategy:f="bestFit",fallbackAxisSideDirection:h="none",flipAlignment:y=!0,...v}=Gn(e,t),x=Fn(r),m=Fn(s)===s,g=await(a.isRTL==null?void 0:a.isRTL(l.floating)),w=d||(m||!y?[Al(s)]:function(V){const j=Al(V);return[Zu(V),j,Zu(j)]}(s));d||h==="none"||w.push(...function(V,j,Q,z){const Y=ki(V);let U=function(q,k,I){const D=["left","right"],G=["right","left"],H=["top","bottom"],de=["bottom","top"];switch(q){case"top":case"bottom":return I?k?G:D:k?D:G;case"left":case"right":return k?H:de;default:return[]}}(Fn(V),Q==="start",z);return Y&&(U=U.map(q=>q+"-"+Y),j&&(U=U.concat(U.map(Zu)))),U}(s,y,h,g));const C=[s,...w],E=await Os(t,v),_=[];let P=((n=o.flip)==null?void 0:n.overflows)||[];if(u&&_.push(E[x]),c){const{main:V,cross:j}=Ob(r,i,g);_.push(E[V],E[j])}if(P=[...P,{placement:r,overflows:_}],!_.every(V=>V<=0)){var R,O;const V=(((R=o.flip)==null?void 0:R.index)||0)+1,j=C[V];if(j)return{data:{index:V,overflows:P},reset:{placement:j}};let Q=(O=P.filter(z=>z.overflows[0]<=0).sort((z,Y)=>z.overflows[1]-Y.overflows[1])[0])==null?void 0:O.placement;if(!Q)switch(f){case"bestFit":{var T;const z=(T=P.map(Y=>[Y.placement,Y.overflows.filter(U=>U>0).reduce((U,q)=>U+q,0)]).sort((Y,U)=>Y[1]-U[1])[0])==null?void 0:T[0];z&&(Q=z);break}case"initialPlacement":Q=s}if(r!==Q)return{reset:{placement:Q}}}return{}}}};function ph(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function gh(e){return Lv.some(t=>e[t]>=0)}const Mb=function(e){return e===void 0&&(e={}),{name:"hide",options:e,async fn(t){const{rects:n}=t,{strategy:r="referenceHidden",...o}=Gn(e,t);switch(r){case"referenceHidden":{const i=ph(await Os(t,{...o,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:i,referenceHidden:gh(i)}}}case"escaped":{const i=ph(await Os(t,{...o,altBoundary:!0}),n.floating);return{data:{escapedOffsets:i,escaped:gh(i)}}}default:return{}}}}},Db=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(t){const{x:n,y:r}=t,o=await async function(i,s){const{placement:a,platform:l,elements:u}=i,c=await(l.isRTL==null?void 0:l.isRTL(u.floating)),d=Fn(a),f=ki(a),h=mo(a)==="x",y=["left","top"].includes(d)?-1:1,v=c&&h?-1:1,x=Gn(s,i);let{mainAxis:m,crossAxis:g,alignmentAxis:w}=typeof x=="number"?{mainAxis:x,crossAxis:0,alignmentAxis:null}:{mainAxis:0,crossAxis:0,alignmentAxis:null,...x};return f&&typeof w=="number"&&(g=f==="end"?-1*w:w),h?{x:g*v,y:m*y}:{x:m*y,y:g*v}}(t,e);return{x:n+o.x,y:r+o.y,data:o}}}};function Iv(e){return e==="x"?"y":"x"}const Lb=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:r,placement:o}=t,{mainAxis:i=!0,crossAxis:s=!1,limiter:a={fn:x=>{let{x:m,y:g}=x;return{x:m,y:g}}},...l}=Gn(e,t),u={x:n,y:r},c=await Os(t,l),d=mo(Fn(o)),f=Iv(d);let h=u[d],y=u[f];if(i){const x=d==="y"?"bottom":"right";h=bd(h+c[d==="y"?"top":"left"],h,h-c[x])}if(s){const x=f==="y"?"bottom":"right";y=bd(y+c[f==="y"?"top":"left"],y,y-c[x])}const v=a.fn({...t,[d]:h,[f]:y});return{...v,data:{x:v.x-n,y:v.y-r}}}}},Ib=function(e){return e===void 0&&(e={}),{options:e,fn(t){const{x:n,y:r,placement:o,rects:i,middlewareData:s}=t,{offset:a=0,mainAxis:l=!0,crossAxis:u=!0}=Gn(e,t),c={x:n,y:r},d=mo(o),f=Iv(d);let h=c[d],y=c[f];const v=Gn(a,t),x=typeof v=="number"?{mainAxis:v,crossAxis:0}:{mainAxis:0,crossAxis:0,...v};if(l){const w=d==="y"?"height":"width",C=i.reference[d]-i.floating[w]+x.mainAxis,E=i.reference[d]+i.reference[w]-x.mainAxis;h<C?h=C:h>E&&(h=E)}if(u){var m,g;const w=d==="y"?"width":"height",C=["top","left"].includes(Fn(o)),E=i.reference[f]-i.floating[w]+(C&&((m=s.offset)==null?void 0:m[f])||0)+(C?0:x.crossAxis),_=i.reference[f]+i.reference[w]+(C?0:((g=s.offset)==null?void 0:g[f])||0)-(C?x.crossAxis:0);y<E?y=E:y>_&&(y=_)}return{[d]:h,[f]:y}}}},Fb=function(e){return e===void 0&&(e={}),{name:"size",options:e,async fn(t){const{placement:n,rects:r,platform:o,elements:i}=t,{apply:s=()=>{},...a}=Gn(e,t),l=await Os(t,a),u=Fn(n),c=ki(n),d=mo(n)==="x",{width:f,height:h}=r.floating;let y,v;u==="top"||u==="bottom"?(y=u,v=c===(await(o.isRTL==null?void 0:o.isRTL(i.floating))?"start":"end")?"left":"right"):(v=u,y=c==="end"?"top":"bottom");const x=h-l[y],m=f-l[v],g=!t.middlewareData.shift;let w=x,C=m;if(d){const _=f-l.left-l.right;C=c||g?Ts(m,_):_}else{const _=h-l.top-l.bottom;w=c||g?Ts(x,_):_}if(g&&!c){const _=Vr(l.left,0),P=Vr(l.right,0),R=Vr(l.top,0),O=Vr(l.bottom,0);d?C=f-2*(_!==0||P!==0?_+P:Vr(l.left,l.right)):w=h-2*(R!==0||O!==0?R+O:Vr(l.top,l.bottom))}await s({...t,availableWidth:C,availableHeight:w});const E=await o.getDimensions(i.floating);return f!==E.width||h!==E.height?{reset:{rects:!0}}:{}}}};function bt(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function yn(e){return bt(e).getComputedStyle(e)}function Fv(e){return e instanceof bt(e).Node}function kr(e){return Fv(e)?(e.nodeName||"").toLowerCase():"#document"}function nn(e){return e instanceof bt(e).HTMLElement}function hh(e){return typeof ShadowRoot<"u"&&(e instanceof bt(e).ShadowRoot||e instanceof ShadowRoot)}function As(e){const{overflow:t,overflowX:n,overflowY:r,display:o}=yn(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function jb(e){return["table","td","th"].includes(kr(e))}function $d(e){const t=Hf(),n=yn(e);return n.transform!=="none"||n.perspective!=="none"||!!n.containerType&&n.containerType!=="normal"||!t&&!!n.backdropFilter&&n.backdropFilter!=="none"||!t&&!!n.filter&&n.filter!=="none"||["transform","perspective","filter"].some(r=>(n.willChange||"").includes(r))||["paint","layout","strict","content"].some(r=>(n.contain||"").includes(r))}function Hf(){return!(typeof CSS>"u"||!CSS.supports)&&CSS.supports("-webkit-backdrop-filter","none")}function su(e){return["html","body","#document"].includes(kr(e))}const Ed=Math.min,Qo=Math.max,Ml=Math.round,Ea=Math.floor,Nr=e=>({x:e,y:e});function jv(e){const t=yn(e);let n=parseFloat(t.width)||0,r=parseFloat(t.height)||0;const o=nn(e),i=o?e.offsetWidth:n,s=o?e.offsetHeight:r,a=Ml(n)!==i||Ml(r)!==s;return a&&(n=i,r=s),{width:n,height:r,$:a}}function jn(e){return e instanceof bt(e).Element}function Uf(e){return jn(e)?e:e.contextElement}function Yo(e){const t=Uf(e);if(!nn(t))return Nr(1);const n=t.getBoundingClientRect(),{width:r,height:o,$:i}=jv(t);let s=(i?Ml(n.width):n.width)/r,a=(i?Ml(n.height):n.height)/o;return s&&Number.isFinite(s)||(s=1),a&&Number.isFinite(a)||(a=1),{x:s,y:a}}const zb=Nr(0);function zv(e){const t=bt(e);return Hf()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:zb}function fo(e,t,n,r){t===void 0&&(t=!1),n===void 0&&(n=!1);const o=e.getBoundingClientRect(),i=Uf(e);let s=Nr(1);t&&(r?jn(r)&&(s=Yo(r)):s=Yo(e));const a=function(f,h,y){return h===void 0&&(h=!1),!(!y||h&&y!==bt(f))&&h}(i,n,r)?zv(i):Nr(0);let l=(o.left+a.x)/s.x,u=(o.top+a.y)/s.y,c=o.width/s.x,d=o.height/s.y;if(i){const f=bt(i),h=r&&jn(r)?bt(r):r;let y=f.frameElement;for(;y&&r&&h!==f;){const v=Yo(y),x=y.getBoundingClientRect(),m=getComputedStyle(y),g=x.left+(y.clientLeft+parseFloat(m.paddingLeft))*v.x,w=x.top+(y.clientTop+parseFloat(m.paddingTop))*v.y;l*=v.x,u*=v.y,c*=v.x,d*=v.y,l+=g,u+=w,y=bt(y).frameElement}}return Tl({width:c,height:d,x:l,y:u})}function au(e){return jn(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.pageXOffset,scrollTop:e.pageYOffset}}function zn(e){return((Fv(e)?e.ownerDocument:e.document)||window.document).documentElement}function Vv(e){return fo(zn(e)).left+au(e).scrollLeft}function xi(e){if(kr(e)==="html")return e;const t=e.assignedSlot||e.parentNode||hh(e)&&e.host||zn(e);return hh(t)?t.host:t}function Hv(e){const t=xi(e);return su(t)?e.ownerDocument?e.ownerDocument.body:e.body:nn(t)&&As(t)?t:Hv(t)}function Dl(e,t){var n;t===void 0&&(t=[]);const r=Hv(e),o=r===((n=e.ownerDocument)==null?void 0:n.body),i=bt(r);return o?t.concat(i,i.visualViewport||[],As(r)?r:[]):t.concat(r,Dl(r))}function mh(e,t,n){let r;if(t==="viewport")r=function(o,i){const s=bt(o),a=zn(o),l=s.visualViewport;let u=a.clientWidth,c=a.clientHeight,d=0,f=0;if(l){u=l.width,c=l.height;const h=Hf();(!h||h&&i==="fixed")&&(d=l.offsetLeft,f=l.offsetTop)}return{width:u,height:c,x:d,y:f}}(e,n);else if(t==="document")r=function(o){const i=zn(o),s=au(o),a=o.ownerDocument.body,l=Qo(i.scrollWidth,i.clientWidth,a.scrollWidth,a.clientWidth),u=Qo(i.scrollHeight,i.clientHeight,a.scrollHeight,a.clientHeight);let c=-s.scrollLeft+Vv(o);const d=-s.scrollTop;return yn(a).direction==="rtl"&&(c+=Qo(i.clientWidth,a.clientWidth)-l),{width:l,height:u,x:c,y:d}}(zn(e));else if(jn(t))r=function(o,i){const s=fo(o,!0,i==="fixed"),a=s.top+o.clientTop,l=s.left+o.clientLeft,u=nn(o)?Yo(o):Nr(1);return{width:o.clientWidth*u.x,height:o.clientHeight*u.y,x:l*u.x,y:a*u.y}}(t,n);else{const o=zv(e);r={...t,x:t.x-o.x,y:t.y-o.y}}return Tl(r)}function Uv(e,t){const n=xi(e);return!(n===t||!jn(n)||su(n))&&(yn(n).position==="fixed"||Uv(n,t))}function Vb(e,t,n){const r=nn(t),o=zn(t),i=n==="fixed",s=fo(e,!0,i,t);let a={scrollLeft:0,scrollTop:0};const l=Nr(0);if(r||!r&&!i)if((kr(t)!=="body"||As(o))&&(a=au(t)),nn(t)){const u=fo(t,!0,i,t);l.x=u.x+t.clientLeft,l.y=u.y+t.clientTop}else o&&(l.x=Vv(o));return{x:s.left+a.scrollLeft-l.x,y:s.top+a.scrollTop-l.y,width:s.width,height:s.height}}function vh(e,t){return nn(e)&&yn(e).position!=="fixed"?t?t(e):e.offsetParent:null}function yh(e,t){const n=bt(e);if(!nn(e))return n;let r=vh(e,t);for(;r&&jb(r)&&yn(r).position==="static";)r=vh(r,t);return r&&(kr(r)==="html"||kr(r)==="body"&&yn(r).position==="static"&&!$d(r))?n:r||function(o){let i=xi(o);for(;nn(i)&&!su(i);){if($d(i))return i;i=xi(i)}return null}(e)||n}const Hb={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{rect:t,offsetParent:n,strategy:r}=e;const o=nn(n),i=zn(n);if(n===i)return t;let s={scrollLeft:0,scrollTop:0},a=Nr(1);const l=Nr(0);if((o||!o&&r!=="fixed")&&((kr(n)!=="body"||As(i))&&(s=au(n)),nn(n))){const u=fo(n);a=Yo(n),l.x=u.x+n.clientLeft,l.y=u.y+n.clientTop}return{width:t.width*a.x,height:t.height*a.y,x:t.x*a.x-s.scrollLeft*a.x+l.x,y:t.y*a.y-s.scrollTop*a.y+l.y}},getDocumentElement:zn,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e;const i=[...n==="clippingAncestors"?function(l,u){const c=u.get(l);if(c)return c;let d=Dl(l).filter(v=>jn(v)&&kr(v)!=="body"),f=null;const h=yn(l).position==="fixed";let y=h?xi(l):l;for(;jn(y)&&!su(y);){const v=yn(y),x=$d(y);x||v.position!=="fixed"||(f=null),(h?!x&&!f:!x&&v.position==="static"&&f&&["absolute","fixed"].includes(f.position)||As(y)&&!x&&Uv(l,y))?d=d.filter(m=>m!==y):f=v,y=xi(y)}return u.set(l,d),d}(t,this._c):[].concat(n),r],s=i[0],a=i.reduce((l,u)=>{const c=mh(t,u,o);return l.top=Qo(c.top,l.top),l.right=Ed(c.right,l.right),l.bottom=Ed(c.bottom,l.bottom),l.left=Qo(c.left,l.left),l},mh(t,s,o));return{width:a.right-a.left,height:a.bottom-a.top,x:a.left,y:a.top}},getOffsetParent:yh,getElementRects:async function(e){let{reference:t,floating:n,strategy:r}=e;const o=this.getOffsetParent||yh,i=this.getDimensions;return{reference:Vb(t,await o(n),r),floating:{x:0,y:0,...await i(n)}}},getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){return jv(e)},getScale:Yo,isElement:jn,isRTL:function(e){return getComputedStyle(e).direction==="rtl"}};function Ub(e,t,n,r){r===void 0&&(r={});const{ancestorScroll:o=!0,ancestorResize:i=!0,elementResize:s=typeof ResizeObserver=="function",layoutShift:a=typeof IntersectionObserver=="function",animationFrame:l=!1}=r,u=Uf(e),c=o||i?[...u?Dl(u):[],...Dl(t)]:[];c.forEach(x=>{o&&x.addEventListener("scroll",n,{passive:!0}),i&&x.addEventListener("resize",n)});const d=u&&a?function(x,m){let g,w=null;const C=zn(x);function E(){clearTimeout(g),w&&w.disconnect(),w=null}return function _(P,R){P===void 0&&(P=!1),R===void 0&&(R=1),E();const{left:O,top:T,width:V,height:j}=x.getBoundingClientRect();if(P||m(),!V||!j)return;const Q={rootMargin:-Ea(T)+"px "+-Ea(C.clientWidth-(O+V))+"px "+-Ea(C.clientHeight-(T+j))+"px "+-Ea(O)+"px",threshold:Qo(0,Ed(1,R))||1};let z=!0;function Y(U){const q=U[0].intersectionRatio;if(q!==R){if(!z)return _();q?_(!1,q):g=setTimeout(()=>{_(!1,1e-7)},100)}z=!1}try{w=new IntersectionObserver(Y,{...Q,root:C.ownerDocument})}catch{w=new IntersectionObserver(Y,Q)}w.observe(x)}(!0),E}(u,n):null;let f,h=-1,y=null;s&&(y=new ResizeObserver(x=>{let[m]=x;m&&m.target===u&&y&&(y.unobserve(t),cancelAnimationFrame(h),h=requestAnimationFrame(()=>{y&&y.observe(t)})),n()}),u&&!l&&y.observe(u),y.observe(t));let v=l?fo(e):null;return l&&function x(){const m=fo(e);!v||m.x===v.x&&m.y===v.y&&m.width===v.width&&m.height===v.height||n(),v=m,f=requestAnimationFrame(x)}(),n(),()=>{c.forEach(x=>{o&&x.removeEventListener("scroll",n),i&&x.removeEventListener("resize",n)}),d&&d(),y&&y.disconnect(),y=null,l&&cancelAnimationFrame(f)}}const Bb=(e,t,n)=>{const r=new Map,o={platform:Hb,...n},i={...o.platform,_c:r};return kb(e,t,{...o,platform:i})},Wb=e=>{function t(n){return{}.hasOwnProperty.call(n,"current")}return{name:"arrow",options:e,fn(n){const{element:r,padding:o}=typeof e=="function"?e(n):e;return r&&t(r)?r.current!=null?fh({element:r.current,padding:o}).fn(n):{}:r?fh({element:r,padding:o}).fn(n):{}}}};var qa=typeof document<"u"?p.useLayoutEffect:p.useEffect;function Ll(e,t){if(e===t)return!0;if(typeof e!=typeof t)return!1;if(typeof e=="function"&&e.toString()===t.toString())return!0;let n,r,o;if(e&&t&&typeof e=="object"){if(Array.isArray(e)){if(n=e.length,n!=t.length)return!1;for(r=n;r--!==0;)if(!Ll(e[r],t[r]))return!1;return!0}if(o=Object.keys(e),n=o.length,n!==Object.keys(t).length)return!1;for(r=n;r--!==0;)if(!{}.hasOwnProperty.call(t,o[r]))return!1;for(r=n;r--!==0;){const i=o[r];if(!(i==="_owner"&&e.$$typeof)&&!Ll(e[i],t[i]))return!1}return!0}return e!==e&&t!==t}function Bv(e){return typeof window>"u"?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function wh(e,t){const n=Bv(e);return Math.round(t*n)/n}function xh(e){const t=p.useRef(e);return qa(()=>{t.current=e}),t}function Gb(e){e===void 0&&(e={});const{placement:t="bottom",strategy:n="absolute",middleware:r=[],platform:o,elements:{reference:i,floating:s}={},transform:a=!0,whileElementsMounted:l,open:u}=e,[c,d]=p.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[f,h]=p.useState(r);Ll(f,r)||h(r);const[y,v]=p.useState(null),[x,m]=p.useState(null),g=p.useCallback(U=>{U!=_.current&&(_.current=U,v(U))},[v]),w=p.useCallback(U=>{U!==P.current&&(P.current=U,m(U))},[m]),C=i||y,E=s||x,_=p.useRef(null),P=p.useRef(null),R=p.useRef(c),O=xh(l),T=xh(o),V=p.useCallback(()=>{if(!_.current||!P.current)return;const U={placement:t,strategy:n,middleware:f};T.current&&(U.platform=T.current),Bb(_.current,P.current,U).then(q=>{const k={...q,isPositioned:!0};j.current&&!Ll(R.current,k)&&(R.current=k,ho.flushSync(()=>{d(k)}))})},[f,t,n,T]);qa(()=>{u===!1&&R.current.isPositioned&&(R.current.isPositioned=!1,d(U=>({...U,isPositioned:!1})))},[u]);const j=p.useRef(!1);qa(()=>(j.current=!0,()=>{j.current=!1}),[]),qa(()=>{if(C&&(_.current=C),E&&(P.current=E),C&&E){if(O.current)return O.current(C,E,V);V()}},[C,E,V,O]);const Q=p.useMemo(()=>({reference:_,floating:P,setReference:g,setFloating:w}),[g,w]),z=p.useMemo(()=>({reference:C,floating:E}),[C,E]),Y=p.useMemo(()=>{const U={position:n,left:0,top:0};if(!z.floating)return U;const q=wh(z.floating,c.x),k=wh(z.floating,c.y);return a?{...U,transform:"translate("+q+"px, "+k+"px)",...Bv(z.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:q,top:k}},[n,a,z.floating,c.x,c.y]);return p.useMemo(()=>({...c,update:V,refs:Q,elements:z,floatingStyles:Y}),[c,V,Q,z,Y])}function Kb(e){const[t,n]=p.useState(void 0);return tn(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});const r=new ResizeObserver(o=>{if(!Array.isArray(o)||!o.length)return;const i=o[0];let s,a;if("borderBoxSize"in i){const l=i.borderBoxSize,u=Array.isArray(l)?l[0]:l;s=u.inlineSize,a=u.blockSize}else s=e.offsetWidth,a=e.offsetHeight;n({width:s,height:a})});return r.observe(e,{box:"border-box"}),()=>r.unobserve(e)}else n(void 0)},[e]),t}const Wv="Popper",[Gv,lu]=_i(Wv),[Qb,Kv]=Gv(Wv),Yb=e=>{const{__scopePopper:t,children:n}=e,[r,o]=p.useState(null);return p.createElement(Qb,{scope:t,anchor:r,onAnchorChange:o},n)},qb="PopperAnchor",Xb=p.forwardRef((e,t)=>{const{__scopePopper:n,virtualRef:r,...o}=e,i=Kv(qb,n),s=p.useRef(null),a=Ie(t,s);return p.useEffect(()=>{i.onAnchorChange((r==null?void 0:r.current)||s.current)}),r?null:p.createElement(Te.div,Z({},o,{ref:a}))}),Qv="PopperContent",[Zb,r_]=Gv(Qv),Jb=p.forwardRef((e,t)=>{var n,r,o,i,s,a,l,u;const{__scopePopper:c,side:d="bottom",sideOffset:f=0,align:h="center",alignOffset:y=0,arrowPadding:v=0,collisionBoundary:x=[],collisionPadding:m=0,sticky:g="partial",hideWhenDetached:w=!1,avoidCollisions:C=!0,onPlaced:E,..._}=e,P=Kv(Qv,c),[R,O]=p.useState(null),T=Ie(t,Ae=>O(Ae)),[V,j]=p.useState(null),Q=Kb(V),z=(n=Q==null?void 0:Q.width)!==null&&n!==void 0?n:0,Y=(r=Q==null?void 0:Q.height)!==null&&r!==void 0?r:0,U=d+(h!=="center"?"-"+h:""),q=typeof m=="number"?m:{top:0,right:0,bottom:0,left:0,...m},k=Array.isArray(x)?x:[x],I=k.length>0,D={padding:q,boundary:k.filter(e$),altBoundary:I},{refs:G,floatingStyles:H,placement:de,isPositioned:J,middlewareData:ge}=Gb({strategy:"fixed",placement:U,whileElementsMounted:Ub,elements:{reference:P.anchor},middleware:[Db({mainAxis:f+Y,alignmentAxis:y}),C&&Lb({mainAxis:!0,crossAxis:!1,limiter:g==="partial"?Ib():void 0,...D}),C&&Ab({...D}),Fb({...D,apply:({elements:Ae,rects:xe,availableWidth:Qe,availableHeight:Nt})=>{const{width:yo,height:xn}=xe.reference,Yn=Ae.floating.style;Yn.setProperty("--radix-popper-available-width",`${Qe}px`),Yn.setProperty("--radix-popper-available-height",`${Nt}px`),Yn.setProperty("--radix-popper-anchor-width",`${yo}px`),Yn.setProperty("--radix-popper-anchor-height",`${xn}px`)}}),V&&Wb({element:V,padding:v}),t$({arrowWidth:z,arrowHeight:Y}),w&&Mb({strategy:"referenceHidden"})]}),[ie,X]=Yv(de),se=Wn(E);tn(()=>{J&&(se==null||se())},[J,se]);const Re=(o=ge.arrow)===null||o===void 0?void 0:o.x,ce=(i=ge.arrow)===null||i===void 0?void 0:i.y,ae=((s=ge.arrow)===null||s===void 0?void 0:s.centerOffset)!==0,[ne,ze]=p.useState();return tn(()=>{R&&ze(window.getComputedStyle(R).zIndex)},[R]),p.createElement("div",{ref:G.setFloating,"data-radix-popper-content-wrapper":"",style:{...H,transform:J?H.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:ne,"--radix-popper-transform-origin":[(a=ge.transformOrigin)===null||a===void 0?void 0:a.x,(l=ge.transformOrigin)===null||l===void 0?void 0:l.y].join(" ")},dir:e.dir},p.createElement(Zb,{scope:c,placedSide:ie,onArrowChange:j,arrowX:Re,arrowY:ce,shouldHideArrow:ae},p.createElement(Te.div,Z({"data-side":ie,"data-align":X},_,{ref:T,style:{..._.style,animation:J?void 0:"none",opacity:(u=ge.hide)!==null&&u!==void 0&&u.referenceHidden?0:void 0}}))))});function e$(e){return e!==null}const t$=e=>({name:"transformOrigin",options:e,fn(t){var n,r,o,i,s;const{placement:a,rects:l,middlewareData:u}=t,d=((n=u.arrow)===null||n===void 0?void 0:n.centerOffset)!==0,f=d?0:e.arrowWidth,h=d?0:e.arrowHeight,[y,v]=Yv(a),x={start:"0%",center:"50%",end:"100%"}[v],m=((r=(o=u.arrow)===null||o===void 0?void 0:o.x)!==null&&r!==void 0?r:0)+f/2,g=((i=(s=u.arrow)===null||s===void 0?void 0:s.y)!==null&&i!==void 0?i:0)+h/2;let w="",C="";return y==="bottom"?(w=d?x:`${m}px`,C=`${-h}px`):y==="top"?(w=d?x:`${m}px`,C=`${l.floating.height+h}px`):y==="right"?(w=`${-h}px`,C=d?x:`${g}px`):y==="left"&&(w=`${l.floating.width+h}px`,C=d?x:`${g}px`),{data:{x:w,y:C}}}});function Yv(e){const[t,n="center"]=e.split("-");return[t,n]}const qv=Yb,Xv=Xb,Zv=Jb,Bf=p.forwardRef((e,t)=>{var n;const{container:r=globalThis==null||(n=globalThis.document)===null||n===void 0?void 0:n.body,...o}=e;return r?iv.createPortal(p.createElement(Te.div,Z({},o,{ref:t})),r):null});function Il({prop:e,defaultProp:t,onChange:n=()=>{}}){const[r,o]=n$({defaultProp:t,onChange:n}),i=e!==void 0,s=i?e:r,a=Wn(n),l=p.useCallback(u=>{if(i){const d=typeof u=="function"?u(e):u;d!==e&&a(d)}else o(u)},[i,e,o,a]);return[s,l]}function n$({defaultProp:e,onChange:t}){const n=p.useState(e),[r]=n,o=p.useRef(r),i=Wn(t);return p.useEffect(()=>{o.current!==r&&(i(r),o.current=r)},[r,o,i]),n}function r$(e){const t=p.useRef({value:e,previous:e});return p.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}const o$=p.forwardRef((e,t)=>p.createElement(Te.span,Z({},e,{ref:t,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...e.style}})));var i$=function(e){if(typeof document>"u")return null;var t=Array.isArray(e)?e[0]:e;return t.ownerDocument.body},Po=new WeakMap,Ra=new WeakMap,Pa={},Ju=0,Jv=function(e){return e&&(e.host||Jv(e.parentNode))},s$=function(e,t){return t.map(function(n){if(e.contains(n))return n;var r=Jv(n);return r&&e.contains(r)?r:(console.error("aria-hidden",n,"in not contained inside",e,". Doing nothing"),null)}).filter(function(n){return!!n})},a$=function(e,t,n,r){var o=s$(t,Array.isArray(e)?e:[e]);Pa[n]||(Pa[n]=new WeakMap);var i=Pa[n],s=[],a=new Set,l=new Set(o),u=function(d){!d||a.has(d)||(a.add(d),u(d.parentNode))};o.forEach(u);var c=function(d){!d||l.has(d)||Array.prototype.forEach.call(d.children,function(f){if(a.has(f))c(f);else{var h=f.getAttribute(r),y=h!==null&&h!=="false",v=(Po.get(f)||0)+1,x=(i.get(f)||0)+1;Po.set(f,v),i.set(f,x),s.push(f),v===1&&y&&Ra.set(f,!0),x===1&&f.setAttribute(n,"true"),y||f.setAttribute(r,"true")}})};return c(t),a.clear(),Ju++,function(){s.forEach(function(d){var f=Po.get(d)-1,h=i.get(d)-1;Po.set(d,f),i.set(d,h),f||(Ra.has(d)||d.removeAttribute(r),Ra.delete(d)),h||d.removeAttribute(n)}),Ju--,Ju||(Po=new WeakMap,Po=new WeakMap,Ra=new WeakMap,Pa={})}},Wf=function(e,t,n){n===void 0&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=t||i$(e);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live]"))),a$(r,o,n,"aria-hidden")):function(){return null}},gn=function(){return gn=Object.assign||function(t){for(var n,r=1,o=arguments.length;r<o;r++){n=arguments[r];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(t[i]=n[i])}return t},gn.apply(this,arguments)};function ey(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}function l$(e,t,n){if(n||arguments.length===2)for(var r=0,o=t.length,i;r<o;r++)(i||!(r in t))&&(i||(i=Array.prototype.slice.call(t,0,r)),i[r]=t[r]);return e.concat(i||Array.prototype.slice.call(t))}var Xa="right-scroll-bar-position",Za="width-before-scroll-bar",u$="with-scroll-bars-hidden",c$="--removed-body-scroll-bar-size";function d$(e,t){return typeof e=="function"?e(t):e&&(e.current=t),e}function f$(e,t){var n=p.useState(function(){return{value:e,callback:t,facade:{get current(){return n.value},set current(r){var o=n.value;o!==r&&(n.value=r,n.callback(r,o))}}}})[0];return n.callback=t,n.facade}function p$(e,t){return f$(t||null,function(n){return e.forEach(function(r){return d$(r,n)})})}function g$(e){return e}function h$(e,t){t===void 0&&(t=g$);var n=[],r=!1,o={read:function(){if(r)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:e},useMedium:function(i){var s=t(i,r);return n.push(s),function(){n=n.filter(function(a){return a!==s})}},assignSyncMedium:function(i){for(r=!0;n.length;){var s=n;n=[],s.forEach(i)}n={push:function(a){return i(a)},filter:function(){return n}}},assignMedium:function(i){r=!0;var s=[];if(n.length){var a=n;n=[],a.forEach(i),s=n}var l=function(){var c=s;s=[],c.forEach(i)},u=function(){return Promise.resolve().then(l)};u(),n={push:function(c){s.push(c),u()},filter:function(c){return s=s.filter(c),n}}}};return o}function m$(e){e===void 0&&(e={});var t=h$(null);return t.options=gn({async:!0,ssr:!1},e),t}var ty=function(e){var t=e.sideCar,n=ey(e,["sideCar"]);if(!t)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw new Error("Sidecar medium not found");return p.createElement(r,gn({},n))};ty.isSideCarExport=!0;function v$(e,t){return e.useMedium(t),ty}var ny=m$(),ec=function(){},uu=p.forwardRef(function(e,t){var n=p.useRef(null),r=p.useState({onScrollCapture:ec,onWheelCapture:ec,onTouchMoveCapture:ec}),o=r[0],i=r[1],s=e.forwardProps,a=e.children,l=e.className,u=e.removeScrollBar,c=e.enabled,d=e.shards,f=e.sideCar,h=e.noIsolation,y=e.inert,v=e.allowPinchZoom,x=e.as,m=x===void 0?"div":x,g=ey(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as"]),w=f,C=p$([n,t]),E=gn(gn({},g),o);return p.createElement(p.Fragment,null,c&&p.createElement(w,{sideCar:ny,removeScrollBar:u,shards:d,noIsolation:h,inert:y,setCallbacks:i,allowPinchZoom:!!v,lockRef:n}),s?p.cloneElement(p.Children.only(a),gn(gn({},E),{ref:C})):p.createElement(m,gn({},E,{className:l,ref:C}),a))});uu.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};uu.classNames={fullWidth:Za,zeroRight:Xa};var Sh,y$=function(){if(Sh)return Sh;if(typeof __webpack_nonce__<"u")return __webpack_nonce__};function w$(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=y$();return t&&e.setAttribute("nonce",t),e}function x$(e,t){e.styleSheet?e.styleSheet.cssText=t:e.appendChild(document.createTextNode(t))}function S$(e){var t=document.head||document.getElementsByTagName("head")[0];t.appendChild(e)}var C$=function(){var e=0,t=null;return{add:function(n){e==0&&(t=w$())&&(x$(t,n),S$(t)),e++},remove:function(){e--,!e&&t&&(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},b$=function(){var e=C$();return function(t,n){p.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},ry=function(){var e=b$(),t=function(n){var r=n.styles,o=n.dynamic;return e(r,o),null};return t},$$={left:0,top:0,right:0,gap:0},tc=function(e){return parseInt(e||"",10)||0},E$=function(e){var t=window.getComputedStyle(document.body),n=t[e==="padding"?"paddingLeft":"marginLeft"],r=t[e==="padding"?"paddingTop":"marginTop"],o=t[e==="padding"?"paddingRight":"marginRight"];return[tc(n),tc(r),tc(o)]},R$=function(e){if(e===void 0&&(e="margin"),typeof window>"u")return $$;var t=E$(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},P$=ry(),_$=function(e,t,n,r){var o=e.left,i=e.top,s=e.right,a=e.gap;return n===void 0&&(n="margin"),`
  .`.concat(u$,` {
   overflow: hidden `).concat(r,`;
   padding-right: `).concat(a,"px ").concat(r,`;
  }
  body {
    overflow: hidden `).concat(r,`;
    overscroll-behavior: contain;
    `).concat([t&&"position: relative ".concat(r,";"),n==="margin"&&`
    padding-left: `.concat(o,`px;
    padding-top: `).concat(i,`px;
    padding-right: `).concat(s,`px;
    margin-left:0;
    margin-top:0;
    margin-right: `).concat(a,"px ").concat(r,`;
    `),n==="padding"&&"padding-right: ".concat(a,"px ").concat(r,";")].filter(Boolean).join(""),`
  }
  
  .`).concat(Xa,` {
    right: `).concat(a,"px ").concat(r,`;
  }
  
  .`).concat(Za,` {
    margin-right: `).concat(a,"px ").concat(r,`;
  }
  
  .`).concat(Xa," .").concat(Xa,` {
    right: 0 `).concat(r,`;
  }
  
  .`).concat(Za," .").concat(Za,` {
    margin-right: 0 `).concat(r,`;
  }
  
  body {
    `).concat(c$,": ").concat(a,`px;
  }
`)},k$=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=r===void 0?"margin":r,i=p.useMemo(function(){return R$(o)},[o]);return p.createElement(P$,{styles:_$(i,!t,o,n?"":"!important")})},Rd=!1;if(typeof window<"u")try{var _a=Object.defineProperty({},"passive",{get:function(){return Rd=!0,!0}});window.addEventListener("test",_a,_a),window.removeEventListener("test",_a,_a)}catch{Rd=!1}var _o=Rd?{passive:!1}:!1,N$=function(e){return e.tagName==="TEXTAREA"},oy=function(e,t){var n=window.getComputedStyle(e);return n[t]!=="hidden"&&!(n.overflowY===n.overflowX&&!N$(e)&&n[t]==="visible")},O$=function(e){return oy(e,"overflowY")},T$=function(e){return oy(e,"overflowX")},Ch=function(e,t){var n=t;do{typeof ShadowRoot<"u"&&n instanceof ShadowRoot&&(n=n.host);var r=iy(e,n);if(r){var o=sy(e,n),i=o[1],s=o[2];if(i>s)return!0}n=n.parentNode}while(n&&n!==document.body);return!1},A$=function(e){var t=e.scrollTop,n=e.scrollHeight,r=e.clientHeight;return[t,n,r]},M$=function(e){var t=e.scrollLeft,n=e.scrollWidth,r=e.clientWidth;return[t,n,r]},iy=function(e,t){return e==="v"?O$(t):T$(t)},sy=function(e,t){return e==="v"?A$(t):M$(t)},D$=function(e,t){return e==="h"&&t==="rtl"?-1:1},L$=function(e,t,n,r,o){var i=D$(e,window.getComputedStyle(t).direction),s=i*r,a=n.target,l=t.contains(a),u=!1,c=s>0,d=0,f=0;do{var h=sy(e,a),y=h[0],v=h[1],x=h[2],m=v-x-i*y;(y||m)&&iy(e,a)&&(d+=m,f+=y),a=a.parentNode}while(!l&&a!==document.body||l&&(t.contains(a)||t===a));return(c&&(o&&d===0||!o&&s>d)||!c&&(o&&f===0||!o&&-s>f))&&(u=!0),u},ka=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},bh=function(e){return[e.deltaX,e.deltaY]},$h=function(e){return e&&"current"in e?e.current:e},I$=function(e,t){return e[0]===t[0]&&e[1]===t[1]},F$=function(e){return`
  .block-interactivity-`.concat(e,` {pointer-events: none;}
  .allow-interactivity-`).concat(e,` {pointer-events: all;}
`)},j$=0,ko=[];function z$(e){var t=p.useRef([]),n=p.useRef([0,0]),r=p.useRef(),o=p.useState(j$++)[0],i=p.useState(function(){return ry()})[0],s=p.useRef(e);p.useEffect(function(){s.current=e},[e]),p.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var v=l$([e.lockRef.current],(e.shards||[]).map($h),!0).filter(Boolean);return v.forEach(function(x){return x.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),v.forEach(function(x){return x.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var a=p.useCallback(function(v,x){if("touches"in v&&v.touches.length===2)return!s.current.allowPinchZoom;var m=ka(v),g=n.current,w="deltaX"in v?v.deltaX:g[0]-m[0],C="deltaY"in v?v.deltaY:g[1]-m[1],E,_=v.target,P=Math.abs(w)>Math.abs(C)?"h":"v";if("touches"in v&&P==="h"&&_.type==="range")return!1;var R=Ch(P,_);if(!R)return!0;if(R?E=P:(E=P==="v"?"h":"v",R=Ch(P,_)),!R)return!1;if(!r.current&&"changedTouches"in v&&(w||C)&&(r.current=E),!E)return!0;var O=r.current||E;return L$(O,x,v,O==="h"?w:C,!0)},[]),l=p.useCallback(function(v){var x=v;if(!(!ko.length||ko[ko.length-1]!==i)){var m="deltaY"in x?bh(x):ka(x),g=t.current.filter(function(E){return E.name===x.type&&E.target===x.target&&I$(E.delta,m)})[0];if(g&&g.should){x.cancelable&&x.preventDefault();return}if(!g){var w=(s.current.shards||[]).map($h).filter(Boolean).filter(function(E){return E.contains(x.target)}),C=w.length>0?a(x,w[0]):!s.current.noIsolation;C&&x.cancelable&&x.preventDefault()}}},[]),u=p.useCallback(function(v,x,m,g){var w={name:v,delta:x,target:m,should:g};t.current.push(w),setTimeout(function(){t.current=t.current.filter(function(C){return C!==w})},1)},[]),c=p.useCallback(function(v){n.current=ka(v),r.current=void 0},[]),d=p.useCallback(function(v){u(v.type,bh(v),v.target,a(v,e.lockRef.current))},[]),f=p.useCallback(function(v){u(v.type,ka(v),v.target,a(v,e.lockRef.current))},[]);p.useEffect(function(){return ko.push(i),e.setCallbacks({onScrollCapture:d,onWheelCapture:d,onTouchMoveCapture:f}),document.addEventListener("wheel",l,_o),document.addEventListener("touchmove",l,_o),document.addEventListener("touchstart",c,_o),function(){ko=ko.filter(function(v){return v!==i}),document.removeEventListener("wheel",l,_o),document.removeEventListener("touchmove",l,_o),document.removeEventListener("touchstart",c,_o)}},[]);var h=e.removeScrollBar,y=e.inert;return p.createElement(p.Fragment,null,y?p.createElement(i,{styles:F$(o)}):null,h?p.createElement(k$,{gapMode:"margin"}):null)}const V$=v$(ny,z$);var ay=p.forwardRef(function(e,t){return p.createElement(uu,gn({},e,{ref:t,sideCar:V$}))});ay.classNames=uu.classNames;const Gf=ay,H$=[" ","Enter","ArrowUp","ArrowDown"],U$=[" ","Enter"],cu="Select",[du,Kf,B$]=cb(cu),[Ni,o_]=_i(cu,[B$,lu]),Qf=lu(),[W$,vo]=Ni(cu),[G$,K$]=Ni(cu),Q$=e=>{const{__scopeSelect:t,children:n,open:r,defaultOpen:o,onOpenChange:i,value:s,defaultValue:a,onValueChange:l,dir:u,name:c,autoComplete:d,disabled:f,required:h}=e,y=Qf(t),[v,x]=p.useState(null),[m,g]=p.useState(null),[w,C]=p.useState(!1),E=fb(u),[_=!1,P]=Il({prop:r,defaultProp:o,onChange:i}),[R,O]=Il({prop:s,defaultProp:a,onChange:l}),T=p.useRef(null),V=v?!!v.closest("form"):!0,[j,Q]=p.useState(new Set),z=Array.from(j).map(Y=>Y.props.value).join(";");return p.createElement(qv,y,p.createElement(W$,{required:h,scope:t,trigger:v,onTriggerChange:x,valueNode:m,onValueNodeChange:g,valueNodeHasChildren:w,onValueNodeHasChildrenChange:C,contentId:ro(),value:R,onValueChange:O,open:_,onOpenChange:P,dir:E,triggerPointerDownPosRef:T,disabled:f},p.createElement(du.Provider,{scope:t},p.createElement(G$,{scope:e.__scopeSelect,onNativeOptionAdd:p.useCallback(Y=>{Q(U=>new Set(U).add(Y))},[]),onNativeOptionRemove:p.useCallback(Y=>{Q(U=>{const q=new Set(U);return q.delete(Y),q})},[])},n)),V?p.createElement(cy,{key:z,"aria-hidden":!0,required:h,tabIndex:-1,name:c,autoComplete:d,value:R,onChange:Y=>O(Y.target.value),disabled:f},R===void 0?p.createElement("option",{value:""}):null,Array.from(j)):null))},Y$="SelectTrigger",q$=p.forwardRef((e,t)=>{const{__scopeSelect:n,disabled:r=!1,...o}=e,i=Qf(n),s=vo(Y$,n),a=s.disabled||r,l=Ie(t,s.onTriggerChange),u=Kf(n),[c,d,f]=dy(y=>{const v=u().filter(g=>!g.disabled),x=v.find(g=>g.value===s.value),m=fy(v,y,x);m!==void 0&&s.onValueChange(m.value)}),h=()=>{a||(s.onOpenChange(!0),f())};return p.createElement(Xv,Z({asChild:!0},i),p.createElement(Te.button,Z({type:"button",role:"combobox","aria-controls":s.contentId,"aria-expanded":s.open,"aria-required":s.required,"aria-autocomplete":"none",dir:s.dir,"data-state":s.open?"open":"closed",disabled:a,"data-disabled":a?"":void 0,"data-placeholder":s.value===void 0?"":void 0},o,{ref:l,onClick:Ee(o.onClick,y=>{y.currentTarget.focus()}),onPointerDown:Ee(o.onPointerDown,y=>{const v=y.target;v.hasPointerCapture(y.pointerId)&&v.releasePointerCapture(y.pointerId),y.button===0&&y.ctrlKey===!1&&(h(),s.triggerPointerDownPosRef.current={x:Math.round(y.pageX),y:Math.round(y.pageY)},y.preventDefault())}),onKeyDown:Ee(o.onKeyDown,y=>{const v=c.current!=="";!(y.ctrlKey||y.altKey||y.metaKey)&&y.key.length===1&&d(y.key),!(v&&y.key===" ")&&H$.includes(y.key)&&(h(),y.preventDefault())})})))}),X$="SelectValue",Z$=p.forwardRef((e,t)=>{const{__scopeSelect:n,className:r,style:o,children:i,placeholder:s,...a}=e,l=vo(X$,n),{onValueNodeHasChildrenChange:u}=l,c=i!==void 0,d=Ie(t,l.onValueNodeChange);return tn(()=>{u(c)},[u,c]),p.createElement(Te.span,Z({},a,{ref:d,style:{pointerEvents:"none"}}),l.value===void 0&&s!==void 0?s:i)}),J$=p.forwardRef((e,t)=>{const{__scopeSelect:n,children:r,...o}=e;return p.createElement(Te.span,Z({"aria-hidden":!0},o,{ref:t}),r||"▼")}),e2=e=>p.createElement(Bf,Z({asChild:!0},e)),Si="SelectContent",t2=p.forwardRef((e,t)=>{const n=vo(Si,e.__scopeSelect),[r,o]=p.useState();if(tn(()=>{o(new DocumentFragment)},[]),!n.open){const i=r;return i?ho.createPortal(p.createElement(ly,{scope:e.__scopeSelect},p.createElement(du.Slot,{scope:e.__scopeSelect},p.createElement("div",null,e.children))),i):null}return p.createElement(n2,Z({},e,{ref:t}))}),On=10,[ly,fu]=Ni(Si),n2=p.forwardRef((e,t)=>{const{__scopeSelect:n,position:r="item-aligned",onCloseAutoFocus:o,onEscapeKeyDown:i,onPointerDownOutside:s,side:a,sideOffset:l,align:u,alignOffset:c,arrowPadding:d,collisionBoundary:f,collisionPadding:h,sticky:y,hideWhenDetached:v,avoidCollisions:x,...m}=e,g=vo(Si,n),[w,C]=p.useState(null),[E,_]=p.useState(null),P=Ie(t,X=>C(X)),[R,O]=p.useState(null),[T,V]=p.useState(null),j=Kf(n),[Q,z]=p.useState(!1),Y=p.useRef(!1);p.useEffect(()=>{if(w)return Wf(w)},[w]),jf();const U=p.useCallback(X=>{const[se,...Re]=j().map(ne=>ne.ref.current),[ce]=Re.slice(-1),ae=document.activeElement;for(const ne of X)if(ne===ae||(ne==null||ne.scrollIntoView({block:"nearest"}),ne===se&&E&&(E.scrollTop=0),ne===ce&&E&&(E.scrollTop=E.scrollHeight),ne==null||ne.focus(),document.activeElement!==ae))return},[j,E]),q=p.useCallback(()=>U([R,w]),[U,R,w]);p.useEffect(()=>{Q&&q()},[Q,q]);const{onOpenChange:k,triggerPointerDownPosRef:I}=g;p.useEffect(()=>{if(w){let X={x:0,y:0};const se=ce=>{var ae,ne,ze,Ae;X={x:Math.abs(Math.round(ce.pageX)-((ae=(ne=I.current)===null||ne===void 0?void 0:ne.x)!==null&&ae!==void 0?ae:0)),y:Math.abs(Math.round(ce.pageY)-((ze=(Ae=I.current)===null||Ae===void 0?void 0:Ae.y)!==null&&ze!==void 0?ze:0))}},Re=ce=>{X.x<=10&&X.y<=10?ce.preventDefault():w.contains(ce.target)||k(!1),document.removeEventListener("pointermove",se),I.current=null};return I.current!==null&&(document.addEventListener("pointermove",se),document.addEventListener("pointerup",Re,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",se),document.removeEventListener("pointerup",Re,{capture:!0})}}},[w,k,I]),p.useEffect(()=>{const X=()=>k(!1);return window.addEventListener("blur",X),window.addEventListener("resize",X),()=>{window.removeEventListener("blur",X),window.removeEventListener("resize",X)}},[k]);const[D,G]=dy(X=>{const se=j().filter(ae=>!ae.disabled),Re=se.find(ae=>ae.ref.current===document.activeElement),ce=fy(se,X,Re);ce&&setTimeout(()=>ce.ref.current.focus())}),H=p.useCallback((X,se,Re)=>{const ce=!Y.current&&!Re;(g.value!==void 0&&g.value===se||ce)&&(O(X),ce&&(Y.current=!0))},[g.value]),de=p.useCallback(()=>w==null?void 0:w.focus(),[w]),J=p.useCallback((X,se,Re)=>{const ce=!Y.current&&!Re;(g.value!==void 0&&g.value===se||ce)&&V(X)},[g.value]),ge=r==="popper"?Eh:r2,ie=ge===Eh?{side:a,sideOffset:l,align:u,alignOffset:c,arrowPadding:d,collisionBoundary:f,collisionPadding:h,sticky:y,hideWhenDetached:v,avoidCollisions:x}:{};return p.createElement(ly,{scope:n,content:w,viewport:E,onViewportChange:_,itemRefCallback:H,selectedItem:R,onItemLeave:de,itemTextRefCallback:J,focusSelectedItem:q,selectedItemText:T,position:r,isPositioned:Q,searchRef:D},p.createElement(Gf,{as:co,allowPinchZoom:!0},p.createElement(zf,{asChild:!0,trapped:g.open,onMountAutoFocus:X=>{X.preventDefault()},onUnmountAutoFocus:Ee(o,X=>{var se;(se=g.trigger)===null||se===void 0||se.focus({preventScroll:!0}),X.preventDefault()})},p.createElement(Ff,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:i,onPointerDownOutside:s,onFocusOutside:X=>X.preventDefault(),onDismiss:()=>g.onOpenChange(!1)},p.createElement(ge,Z({role:"listbox",id:g.contentId,"data-state":g.open?"open":"closed",dir:g.dir,onContextMenu:X=>X.preventDefault()},m,ie,{onPlaced:()=>z(!0),ref:P,style:{display:"flex",flexDirection:"column",outline:"none",...m.style},onKeyDown:Ee(m.onKeyDown,X=>{const se=X.ctrlKey||X.altKey||X.metaKey;if(X.key==="Tab"&&X.preventDefault(),!se&&X.key.length===1&&G(X.key),["ArrowUp","ArrowDown","Home","End"].includes(X.key)){let ce=j().filter(ae=>!ae.disabled).map(ae=>ae.ref.current);if(["ArrowUp","End"].includes(X.key)&&(ce=ce.slice().reverse()),["ArrowUp","ArrowDown"].includes(X.key)){const ae=X.target,ne=ce.indexOf(ae);ce=ce.slice(ne+1)}setTimeout(()=>U(ce)),X.preventDefault()}})}))))))}),r2=p.forwardRef((e,t)=>{const{__scopeSelect:n,onPlaced:r,...o}=e,i=vo(Si,n),s=fu(Si,n),[a,l]=p.useState(null),[u,c]=p.useState(null),d=Ie(t,P=>c(P)),f=Kf(n),h=p.useRef(!1),y=p.useRef(!0),{viewport:v,selectedItem:x,selectedItemText:m,focusSelectedItem:g}=s,w=p.useCallback(()=>{if(i.trigger&&i.valueNode&&a&&u&&v&&x&&m){const P=i.trigger.getBoundingClientRect(),R=u.getBoundingClientRect(),O=i.valueNode.getBoundingClientRect(),T=m.getBoundingClientRect();if(i.dir!=="rtl"){const ae=T.left-R.left,ne=O.left-ae,ze=P.left-ne,Ae=P.width+ze,xe=Math.max(Ae,R.width),Qe=window.innerWidth-On,Nt=rh(ne,[On,Qe-xe]);a.style.minWidth=Ae+"px",a.style.left=Nt+"px"}else{const ae=R.right-T.right,ne=window.innerWidth-O.right-ae,ze=window.innerWidth-P.right-ne,Ae=P.width+ze,xe=Math.max(Ae,R.width),Qe=window.innerWidth-On,Nt=rh(ne,[On,Qe-xe]);a.style.minWidth=Ae+"px",a.style.right=Nt+"px"}const V=f(),j=window.innerHeight-On*2,Q=v.scrollHeight,z=window.getComputedStyle(u),Y=parseInt(z.borderTopWidth,10),U=parseInt(z.paddingTop,10),q=parseInt(z.borderBottomWidth,10),k=parseInt(z.paddingBottom,10),I=Y+U+Q+k+q,D=Math.min(x.offsetHeight*5,I),G=window.getComputedStyle(v),H=parseInt(G.paddingTop,10),de=parseInt(G.paddingBottom,10),J=P.top+P.height/2-On,ge=j-J,ie=x.offsetHeight/2,X=x.offsetTop+ie,se=Y+U+X,Re=I-se;if(se<=J){const ae=x===V[V.length-1].ref.current;a.style.bottom="0px";const ne=u.clientHeight-v.offsetTop-v.offsetHeight,ze=Math.max(ge,ie+(ae?de:0)+ne+q),Ae=se+ze;a.style.height=Ae+"px"}else{const ae=x===V[0].ref.current;a.style.top="0px";const ze=Math.max(J,Y+v.offsetTop+(ae?H:0)+ie)+Re;a.style.height=ze+"px",v.scrollTop=se-J+v.offsetTop}a.style.margin=`${On}px 0`,a.style.minHeight=D+"px",a.style.maxHeight=j+"px",r==null||r(),requestAnimationFrame(()=>h.current=!0)}},[f,i.trigger,i.valueNode,a,u,v,x,m,i.dir,r]);tn(()=>w(),[w]);const[C,E]=p.useState();tn(()=>{u&&E(window.getComputedStyle(u).zIndex)},[u]);const _=p.useCallback(P=>{P&&y.current===!0&&(w(),g==null||g(),y.current=!1)},[w,g]);return p.createElement(o2,{scope:n,contentWrapper:a,shouldExpandOnScrollRef:h,onScrollButtonChange:_},p.createElement("div",{ref:l,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:C}},p.createElement(Te.div,Z({},o,{ref:d,style:{boxSizing:"border-box",maxHeight:"100%",...o.style}}))))}),Eh=p.forwardRef((e,t)=>{const{__scopeSelect:n,align:r="start",collisionPadding:o=On,...i}=e,s=Qf(n);return p.createElement(Zv,Z({},s,i,{ref:t,align:r,collisionPadding:o,style:{boxSizing:"border-box",...i.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}}))}),[o2,i2]=Ni(Si,{}),Rh="SelectViewport",s2=p.forwardRef((e,t)=>{const{__scopeSelect:n,...r}=e,o=fu(Rh,n),i=i2(Rh,n),s=Ie(t,o.onViewportChange),a=p.useRef(0);return p.createElement(p.Fragment,null,p.createElement("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"}}),p.createElement(du.Slot,{scope:n},p.createElement(Te.div,Z({"data-radix-select-viewport":"",role:"presentation"},r,{ref:s,style:{position:"relative",flex:1,overflow:"auto",...r.style},onScroll:Ee(r.onScroll,l=>{const u=l.currentTarget,{contentWrapper:c,shouldExpandOnScrollRef:d}=i;if(d!=null&&d.current&&c){const f=Math.abs(a.current-u.scrollTop);if(f>0){const h=window.innerHeight-On*2,y=parseFloat(c.style.minHeight),v=parseFloat(c.style.height),x=Math.max(y,v);if(x<h){const m=x+f,g=Math.min(h,m),w=m-g;c.style.height=g+"px",c.style.bottom==="0px"&&(u.scrollTop=w>0?w:0,c.style.justifyContent="flex-end")}}}a.current=u.scrollTop})}))))}),a2="SelectGroup",[l2,u2]=Ni(a2),c2=p.forwardRef((e,t)=>{const{__scopeSelect:n,...r}=e,o=ro();return p.createElement(l2,{scope:n,id:o},p.createElement(Te.div,Z({role:"group","aria-labelledby":o},r,{ref:t})))}),d2="SelectLabel",f2=p.forwardRef((e,t)=>{const{__scopeSelect:n,...r}=e,o=u2(d2,n);return p.createElement(Te.div,Z({id:o.id},r,{ref:t}))}),Pd="SelectItem",[p2,uy]=Ni(Pd),g2=p.forwardRef((e,t)=>{const{__scopeSelect:n,value:r,disabled:o=!1,textValue:i,...s}=e,a=vo(Pd,n),l=fu(Pd,n),u=a.value===r,[c,d]=p.useState(i??""),[f,h]=p.useState(!1),y=Ie(t,m=>{var g;return(g=l.itemRefCallback)===null||g===void 0?void 0:g.call(l,m,r,o)}),v=ro(),x=()=>{o||(a.onValueChange(r),a.onOpenChange(!1))};return p.createElement(p2,{scope:n,value:r,disabled:o,textId:v,isSelected:u,onItemTextChange:p.useCallback(m=>{d(g=>{var w;return g||((w=m==null?void 0:m.textContent)!==null&&w!==void 0?w:"").trim()})},[])},p.createElement(du.ItemSlot,{scope:n,value:r,disabled:o,textValue:c},p.createElement(Te.div,Z({role:"option","aria-labelledby":v,"data-highlighted":f?"":void 0,"aria-selected":u&&f,"data-state":u?"checked":"unchecked","aria-disabled":o||void 0,"data-disabled":o?"":void 0,tabIndex:o?void 0:-1},s,{ref:y,onFocus:Ee(s.onFocus,()=>h(!0)),onBlur:Ee(s.onBlur,()=>h(!1)),onPointerUp:Ee(s.onPointerUp,x),onPointerMove:Ee(s.onPointerMove,m=>{if(o){var g;(g=l.onItemLeave)===null||g===void 0||g.call(l)}else m.currentTarget.focus({preventScroll:!0})}),onPointerLeave:Ee(s.onPointerLeave,m=>{if(m.currentTarget===document.activeElement){var g;(g=l.onItemLeave)===null||g===void 0||g.call(l)}}),onKeyDown:Ee(s.onKeyDown,m=>{var g;((g=l.searchRef)===null||g===void 0?void 0:g.current)!==""&&m.key===" "||(U$.includes(m.key)&&x(),m.key===" "&&m.preventDefault())})}))))}),Na="SelectItemText",h2=p.forwardRef((e,t)=>{const{__scopeSelect:n,className:r,style:o,...i}=e,s=vo(Na,n),a=fu(Na,n),l=uy(Na,n),u=K$(Na,n),[c,d]=p.useState(null),f=Ie(t,m=>d(m),l.onItemTextChange,m=>{var g;return(g=a.itemTextRefCallback)===null||g===void 0?void 0:g.call(a,m,l.value,l.disabled)}),h=c==null?void 0:c.textContent,y=p.useMemo(()=>p.createElement("option",{key:l.value,value:l.value,disabled:l.disabled},h),[l.disabled,l.value,h]),{onNativeOptionAdd:v,onNativeOptionRemove:x}=u;return tn(()=>(v(y),()=>x(y)),[v,x,y]),p.createElement(p.Fragment,null,p.createElement(Te.span,Z({id:l.textId},i,{ref:f})),l.isSelected&&s.valueNode&&!s.valueNodeHasChildren?ho.createPortal(i.children,s.valueNode):null)}),m2="SelectItemIndicator",v2=p.forwardRef((e,t)=>{const{__scopeSelect:n,...r}=e;return uy(m2,n).isSelected?p.createElement(Te.span,Z({"aria-hidden":!0},r,{ref:t})):null}),y2=p.forwardRef((e,t)=>{const{__scopeSelect:n,...r}=e;return p.createElement(Te.div,Z({"aria-hidden":!0},r,{ref:t}))}),cy=p.forwardRef((e,t)=>{const{value:n,...r}=e,o=p.useRef(null),i=Ie(t,o),s=r$(n);return p.useEffect(()=>{const a=o.current,l=window.HTMLSelectElement.prototype,c=Object.getOwnPropertyDescriptor(l,"value").set;if(s!==n&&c){const d=new Event("change",{bubbles:!0});c.call(a,n),a.dispatchEvent(d)}},[s,n]),p.createElement(o$,{asChild:!0},p.createElement("select",Z({},r,{ref:i,defaultValue:n})))});cy.displayName="BubbleSelect";function dy(e){const t=Wn(e),n=p.useRef(""),r=p.useRef(0),o=p.useCallback(s=>{const a=n.current+s;t(a),function l(u){n.current=u,window.clearTimeout(r.current),u!==""&&(r.current=window.setTimeout(()=>l(""),1e3))}(a)},[t]),i=p.useCallback(()=>{n.current="",window.clearTimeout(r.current)},[]);return p.useEffect(()=>()=>window.clearTimeout(r.current),[]),[n,o,i]}function fy(e,t,n){const o=t.length>1&&Array.from(t).every(u=>u===t[0])?t[0]:t,i=n?e.indexOf(n):-1;let s=w2(e,Math.max(i,0));o.length===1&&(s=s.filter(u=>u!==n));const l=s.find(u=>u.textValue.toLowerCase().startsWith(o.toLowerCase()));return l!==n?l:void 0}function w2(e,t){return e.map((n,r)=>e[(t+r)%e.length])}const x2=Q$,py=q$,S2=Z$,C2=J$,b2=e2,gy=t2,$2=s2,E2=c2,hy=f2,my=g2,R2=h2,P2=v2,vy=y2,yy=x2,wy=E2,xy=S2,Yf=p.forwardRef(({className:e,children:t,...n},r)=>S.jsxs(py,{ref:r,className:pe("flex h-10 w-full items-center justify-between rounded-md border bg-transparent px-3 py-2 text-sm placeholder:text-accent-500 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),...n,children:[t,S.jsx(C2,{asChild:!0,children:S.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",className:"h-4 w-4 opacity-60",children:S.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19.5 8.25l-7.5 7.5-7.5-7.5"})})})]}));Yf.displayName=py.displayName;const qf=p.forwardRef(({className:e,children:t,position:n="popper",...r},o)=>S.jsx(b2,{children:S.jsx(gy,{ref:o,className:pe("data-[state=open]:fade-in data-[state=closed]:fade-out shadow-card relative z-50 min-w-[8rem] overflow-hidden rounded-md border bg-white",n==="popper"&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:n,...r,children:S.jsx($2,{className:pe("p-1",n==="popper"&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:t})})}));qf.displayName=gy.displayName;const Xf=p.forwardRef(({className:e,...t},n)=>S.jsx(hy,{ref:n,className:pe("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...t}));Xf.displayName=hy.displayName;const Zf=p.forwardRef(({className:e,children:t,...n},r)=>S.jsxs(my,{ref:r,className:pe("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...n,children:[S.jsx(R2,{children:t}),S.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:S.jsx(P2,{children:S.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",className:"h-4 w-4",children:S.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M4.5 12.75l6 6 9-13.5"})})})})]}));Zf.displayName=my.displayName;const _2=p.forwardRef(({className:e,...t},n)=>S.jsx(vy,{ref:n,className:pe("-mx-1 my-1 h-px bg-accent",e),...t}));_2.displayName=vy.displayName;const Jf=p.forwardRef(({className:e,...t},n)=>S.jsx("textarea",{className:pe("flex min-h-[80px] w-full rounded-md border bg-transparent px-3 py-2 text-sm placeholder:text-accent-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:n,...t}));Jf.displayName="Textarea";const ep=p.forwardRef(({className:e,type:t,...n},r)=>S.jsx("input",{type:t,className:pe("flex h-10 w-full rounded-md border px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-accent-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:r,...n}));ep.displayName="Input";const Fl=[{value:"1",text:"unprocessed-invoice"},{value:"2",text:"unprocessed-payment"},{value:"3",text:"unprocessed-return-invoice"},{value:"4",text:"incorrect-record-entry"},{value:"5",text:"different-turnover"}];function k2(){const[e,t]=p.useState(),[n,r]=p.useState(""),[o,i]=p.useState(""),[s,a]=p.useState(!1),[l,u]=p.useState(!1),c=Zs(),{data:d,isFetching:f}=Qn(),h=g=>{var w;t((w=g.target.files)==null?void 0:w[0])},y=Dr("reconciliationId"),v=md({mutationFn:async({status:g,reconciliationId:w,denyReasonMessage:C,note:E,selectedFile:_})=>{if(_){const P=new FormData;P.append("file",_),P.append("reconciliationId",w),await fetch(`${Js}/api/eops/online-reconciliation/file-upload`,{method:"POST",body:P})}await iu({url:"/api/eops/online-reconciliation/status",method:"PATCH",payload:{status:g,reconciliationId:w,rejectionReason:F(C)??"",note:E??""}})},onSuccess:()=>{Rl.success(F("document-success")),c.invalidateQueries({queryKey:["record-items"]}),c.invalidateQueries({queryKey:["reconciliation-details"]})},onError:g=>{Rl.error(F(g.message))}}),x=async g=>{var w;if(g==="approved"){await v.mutateAsync({status:g,reconciliationId:y});return}if(g==="denied"){a(!1),u(!1),n.trim()||a(!0),o.trim()||u(!0);const C=(w=Fl.find(E=>E.value===n))==null?void 0:w.text;if(!n.trim()||!o.trim()||!C)return;await v.mutateAsync({reconciliationId:y,status:g,denyReasonMessage:C,note:o,selectedFile:e}),i(""),r("")}},m=(d==null?void 0:d.status)==="approved"||(d==null?void 0:d.status)==="denied"||(d==null?void 0:d.isExpired)||v.isPending||f;return S.jsxs(S.Fragment,{children:[S.jsx(ks,{title:F("reconciliation-action-info"),children:S.jsxs("fieldset",{className:"card-padding space-y-4",disabled:m,children:[S.jsxs("div",{className:"grid gap-2 md:grid-cols-12",children:[S.jsx("p",{className:"text-sm font-semibold md:col-span-4",children:F("deny-reason")}),S.jsxs(yy,{disabled:m,onValueChange:g=>{r(g),a(!1)},value:n,children:[S.jsx(Yf,{className:pe("md:col-span-8",s&&"border-red-600"),children:S.jsx(xy,{})}),S.jsx(qf,{children:S.jsxs(wy,{children:[S.jsx(Xf,{children:F("choose-deny-reason")}),Fl.map(g=>S.jsx(Zf,{value:g.value,children:F(g.text)},g.value))]})})]})]}),S.jsxs("div",{className:"grid gap-2 md:grid-cols-12",children:[S.jsx("p",{className:"text-sm font-semibold md:col-span-4",children:F("reconciliation-note")}),S.jsx(Jf,{className:pe("col-span-8 resize-none",l&&"border-red-600"),value:o,onChange:g=>{i(g.target.value),g.target.value.trim().length>0&&u(!1)}})]}),S.jsxs("div",{className:"grid gap-2 md:grid-cols-12",children:[S.jsx("p",{className:"text-sm font-semibold md:col-span-4",children:F("file")}),S.jsx(ep,{type:"file",className:"col-span-8",onChange:h})]})]})}),S.jsxs("div",{className:"shadow-card card-padding flex items-center justify-around gap-4 rounded-md border border-accent-200 bg-white max-md:flex-wrap md:gap-12",children:[S.jsxs("button",{onClick:()=>x("approved"),className:"btn inline-flex w-full items-center gap-2 border-0 bg-green-600 text-white hover:bg-green-600/85 focus:outline-green-600 active:bg-green-600 sm:w-fit",disabled:m||(d==null?void 0:d.status)==="partially-denied",children:[S.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:2,stroke:"currentColor",className:"h-6 w-6",children:S.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),S.jsx("span",{children:F("approve-btn")})]}),S.jsxs("button",{onClick:()=>x("denied"),className:"btn btn-danger inline-flex w-full items-center gap-2 sm:w-fit",disabled:m,children:[S.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:2,stroke:"currentColor",className:"h-6 w-6",children:S.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9.75 9.75l4.5 4.5m0-4.5l-4.5 4.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),S.jsx("span",{children:F("deny-btn")})]})]})]})}function N2(){const{data:e}=Qn();return S.jsxs("div",{className:"grid gap-4 lg:grid-cols-2",children:[S.jsx(ks,{title:F("sender-info"),children:S.jsxs(Ph,{children:[S.jsxs("li",{children:[S.jsxs("span",{children:[F("title"),":"]}),S.jsx("span",{children:e==null?void 0:e.company.legalName})]}),S.jsxs("li",{children:[S.jsxs("span",{children:[F("tax-no"),":"]}),S.jsx("span",{children:e==null?void 0:e.company.tin})]}),S.jsxs("li",{children:[S.jsxs("span",{children:[F("tax-department"),":"]}),S.jsx("span",{children:e==null?void 0:e.company.taxDepartment})]}),S.jsxs("li",{children:[S.jsxs("span",{children:[F("e-mail"),":"]}),S.jsx("span",{children:e==null?void 0:e.company.email})]}),S.jsxs("li",{children:[S.jsxs("span",{children:[F("phone"),":"]}),S.jsx("span",{children:e==null?void 0:e.company.phone})]}),S.jsxs("li",{children:[S.jsxs("span",{children:[F("address"),":"]}),S.jsx("span",{children:e==null?void 0:e.company.address.address})]})]})}),S.jsx(ks,{title:F("receiver-info"),children:S.jsxs(Ph,{children:[S.jsxs("li",{children:[S.jsxs("span",{children:[F("title"),":"]}),S.jsx("span",{children:e==null?void 0:e.partner.name})]}),(e==null?void 0:e.partner.identity)&&S.jsxs("li",{children:[S.jsxs("span",{children:[F("identity-no"),":"]}),S.jsx("span",{children:e==null?void 0:e.partner.identity})]}),!(e!=null&&e.partner.identity)&&(e==null?void 0:e.partner.tin)&&(e==null?void 0:e.partner.taxDepartment)&&S.jsxs(S.Fragment,{children:[S.jsxs("li",{children:[S.jsxs("span",{children:[F("tax-no"),":"]}),S.jsx("span",{children:e==null?void 0:e.partner.tin})]}),S.jsxs("li",{children:[S.jsxs("span",{children:[F("tax-department"),":"]}),S.jsx("span",{children:e==null?void 0:e.partner.taxDepartment})]})]}),S.jsxs("li",{children:[S.jsxs("span",{children:[F("e-mail"),":"]}),S.jsx("span",{children:e==null?void 0:e.partner.email})]}),S.jsxs("li",{children:[S.jsxs("span",{children:[F("phone"),":"]}),S.jsx("span",{children:e==null?void 0:e.partner.phone})]}),S.jsxs("li",{children:[S.jsxs("span",{children:[F("address"),":"]}),S.jsx("span",{children:e==null?void 0:e.partner.address.address})]})]})})]})}function Ph({children:e}){return S.jsx("ul",{className:"card-padding text-sm [&>*:first-child]:pt-0 [&>*:last-child]:pb-0 [&>li>span:first-child]:min-w-[8rem] [&>li>span:first-child]:font-semibold [&>li]:flex [&>li]:py-2",children:e})}/**
 * table-core
 *
 * Copyright (c) TanStack
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function yr(e,t){return typeof e=="function"?e(t):e}function Pt(e,t){return n=>{t.setState(r=>({...r,[e]:yr(n,r[e])}))}}function jl(e){return e instanceof Function}function O2(e){return Array.isArray(e)&&e.every(t=>typeof t=="number")}function T2(e,t){const n=[],r=o=>{o.forEach(i=>{n.push(i);const s=t(i);s!=null&&s.length&&r(s)})};return r(e),n}function te(e,t,n){let r=[],o;return()=>{let i;n.key&&n.debug&&(i=Date.now());const s=e();if(!(s.length!==r.length||s.some((u,c)=>r[c]!==u)))return o;r=s;let l;if(n.key&&n.debug&&(l=Date.now()),o=t(...s),n==null||n.onChange==null||n.onChange(o),n.key&&n.debug&&n!=null&&n.debug()){const u=Math.round((Date.now()-i)*100)/100,c=Math.round((Date.now()-l)*100)/100,d=c/16,f=(h,y)=>{for(h=String(h);h.length<y;)h=" "+h;return h};console.info(`%c⏱ ${f(c,5)} /${f(u,5)} ms`,`
            font-size: .6rem;
            font-weight: bold;
            color: hsl(${Math.max(0,Math.min(120-120*d,120))}deg 100% 31%);`,n==null?void 0:n.key)}return o}}function A2(e,t,n,r){var o,i;const a={...e._getDefaultColumnDef(),...t},l=a.accessorKey;let u=(o=(i=a.id)!=null?i:l?l.replace(".","_"):void 0)!=null?o:typeof a.header=="string"?a.header:void 0,c;if(a.accessorFn?c=a.accessorFn:l&&(l.includes(".")?c=f=>{let h=f;for(const v of l.split(".")){var y;h=(y=h)==null?void 0:y[v]}return h}:c=f=>f[a.accessorKey]),!u)throw new Error;let d={id:`${String(u)}`,accessorFn:c,parent:r,depth:n,columnDef:a,columns:[],getFlatColumns:te(()=>[!0],()=>{var f;return[d,...(f=d.columns)==null?void 0:f.flatMap(h=>h.getFlatColumns())]},{key:"column.getFlatColumns",debug:()=>{var f;return(f=e.options.debugAll)!=null?f:e.options.debugColumns}}),getLeafColumns:te(()=>[e._getOrderColumnsFn()],f=>{var h;if((h=d.columns)!=null&&h.length){let y=d.columns.flatMap(v=>v.getLeafColumns());return f(y)}return[d]},{key:"column.getLeafColumns",debug:()=>{var f;return(f=e.options.debugAll)!=null?f:e.options.debugColumns}})};for(const f of e._features)f.createColumn==null||f.createColumn(d,e);return d}function _h(e,t,n){var r;let i={id:(r=n.id)!=null?r:t.id,column:t,index:n.index,isPlaceholder:!!n.isPlaceholder,placeholderId:n.placeholderId,depth:n.depth,subHeaders:[],colSpan:0,rowSpan:0,headerGroup:null,getLeafHeaders:()=>{const s=[],a=l=>{l.subHeaders&&l.subHeaders.length&&l.subHeaders.map(a),s.push(l)};return a(i),s},getContext:()=>({table:e,header:i,column:t})};return e._features.forEach(s=>{s.createHeader==null||s.createHeader(i,e)}),i}const M2={createTable:e=>{e.getHeaderGroups=te(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right],(t,n,r,o)=>{var i,s;const a=(i=r==null?void 0:r.map(d=>n.find(f=>f.id===d)).filter(Boolean))!=null?i:[],l=(s=o==null?void 0:o.map(d=>n.find(f=>f.id===d)).filter(Boolean))!=null?s:[],u=n.filter(d=>!(r!=null&&r.includes(d.id))&&!(o!=null&&o.includes(d.id)));return Oa(t,[...a,...u,...l],e)},{key:!1,debug:()=>{var t;return(t=e.options.debugAll)!=null?t:e.options.debugHeaders}}),e.getCenterHeaderGroups=te(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right],(t,n,r,o)=>(n=n.filter(i=>!(r!=null&&r.includes(i.id))&&!(o!=null&&o.includes(i.id))),Oa(t,n,e,"center")),{key:!1,debug:()=>{var t;return(t=e.options.debugAll)!=null?t:e.options.debugHeaders}}),e.getLeftHeaderGroups=te(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left],(t,n,r)=>{var o;const i=(o=r==null?void 0:r.map(s=>n.find(a=>a.id===s)).filter(Boolean))!=null?o:[];return Oa(t,i,e,"left")},{key:!1,debug:()=>{var t;return(t=e.options.debugAll)!=null?t:e.options.debugHeaders}}),e.getRightHeaderGroups=te(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.right],(t,n,r)=>{var o;const i=(o=r==null?void 0:r.map(s=>n.find(a=>a.id===s)).filter(Boolean))!=null?o:[];return Oa(t,i,e,"right")},{key:!1,debug:()=>{var t;return(t=e.options.debugAll)!=null?t:e.options.debugHeaders}}),e.getFooterGroups=te(()=>[e.getHeaderGroups()],t=>[...t].reverse(),{key:!1,debug:()=>{var t;return(t=e.options.debugAll)!=null?t:e.options.debugHeaders}}),e.getLeftFooterGroups=te(()=>[e.getLeftHeaderGroups()],t=>[...t].reverse(),{key:!1,debug:()=>{var t;return(t=e.options.debugAll)!=null?t:e.options.debugHeaders}}),e.getCenterFooterGroups=te(()=>[e.getCenterHeaderGroups()],t=>[...t].reverse(),{key:!1,debug:()=>{var t;return(t=e.options.debugAll)!=null?t:e.options.debugHeaders}}),e.getRightFooterGroups=te(()=>[e.getRightHeaderGroups()],t=>[...t].reverse(),{key:!1,debug:()=>{var t;return(t=e.options.debugAll)!=null?t:e.options.debugHeaders}}),e.getFlatHeaders=te(()=>[e.getHeaderGroups()],t=>t.map(n=>n.headers).flat(),{key:!1,debug:()=>{var t;return(t=e.options.debugAll)!=null?t:e.options.debugHeaders}}),e.getLeftFlatHeaders=te(()=>[e.getLeftHeaderGroups()],t=>t.map(n=>n.headers).flat(),{key:!1,debug:()=>{var t;return(t=e.options.debugAll)!=null?t:e.options.debugHeaders}}),e.getCenterFlatHeaders=te(()=>[e.getCenterHeaderGroups()],t=>t.map(n=>n.headers).flat(),{key:!1,debug:()=>{var t;return(t=e.options.debugAll)!=null?t:e.options.debugHeaders}}),e.getRightFlatHeaders=te(()=>[e.getRightHeaderGroups()],t=>t.map(n=>n.headers).flat(),{key:!1,debug:()=>{var t;return(t=e.options.debugAll)!=null?t:e.options.debugHeaders}}),e.getCenterLeafHeaders=te(()=>[e.getCenterFlatHeaders()],t=>t.filter(n=>{var r;return!((r=n.subHeaders)!=null&&r.length)}),{key:!1,debug:()=>{var t;return(t=e.options.debugAll)!=null?t:e.options.debugHeaders}}),e.getLeftLeafHeaders=te(()=>[e.getLeftFlatHeaders()],t=>t.filter(n=>{var r;return!((r=n.subHeaders)!=null&&r.length)}),{key:!1,debug:()=>{var t;return(t=e.options.debugAll)!=null?t:e.options.debugHeaders}}),e.getRightLeafHeaders=te(()=>[e.getRightFlatHeaders()],t=>t.filter(n=>{var r;return!((r=n.subHeaders)!=null&&r.length)}),{key:!1,debug:()=>{var t;return(t=e.options.debugAll)!=null?t:e.options.debugHeaders}}),e.getLeafHeaders=te(()=>[e.getLeftHeaderGroups(),e.getCenterHeaderGroups(),e.getRightHeaderGroups()],(t,n,r)=>{var o,i,s,a,l,u;return[...(o=(i=t[0])==null?void 0:i.headers)!=null?o:[],...(s=(a=n[0])==null?void 0:a.headers)!=null?s:[],...(l=(u=r[0])==null?void 0:u.headers)!=null?l:[]].map(c=>c.getLeafHeaders()).flat()},{key:!1,debug:()=>{var t;return(t=e.options.debugAll)!=null?t:e.options.debugHeaders}})}};function Oa(e,t,n,r){var o,i;let s=0;const a=function(f,h){h===void 0&&(h=1),s=Math.max(s,h),f.filter(y=>y.getIsVisible()).forEach(y=>{var v;(v=y.columns)!=null&&v.length&&a(y.columns,h+1)},0)};a(e);let l=[];const u=(f,h)=>{const y={depth:h,id:[r,`${h}`].filter(Boolean).join("_"),headers:[]},v=[];f.forEach(x=>{const m=[...v].reverse()[0],g=x.column.depth===y.depth;let w,C=!1;if(g&&x.column.parent?w=x.column.parent:(w=x.column,C=!0),m&&(m==null?void 0:m.column)===w)m.subHeaders.push(x);else{const E=_h(n,w,{id:[r,h,w.id,x==null?void 0:x.id].filter(Boolean).join("_"),isPlaceholder:C,placeholderId:C?`${v.filter(_=>_.column===w).length}`:void 0,depth:h,index:v.length});E.subHeaders.push(x),v.push(E)}y.headers.push(x),x.headerGroup=y}),l.push(y),h>0&&u(v,h-1)},c=t.map((f,h)=>_h(n,f,{depth:s,index:h}));u(c,s-1),l.reverse();const d=f=>f.filter(y=>y.column.getIsVisible()).map(y=>{let v=0,x=0,m=[0];y.subHeaders&&y.subHeaders.length?(m=[],d(y.subHeaders).forEach(w=>{let{colSpan:C,rowSpan:E}=w;v+=C,m.push(E)})):v=1;const g=Math.min(...m);return x=x+g,y.colSpan=v,y.rowSpan=x,{colSpan:v,rowSpan:x}});return d((o=(i=l[0])==null?void 0:i.headers)!=null?o:[]),l}const Ta={size:150,minSize:20,maxSize:Number.MAX_SAFE_INTEGER},nc=()=>({startOffset:null,startSize:null,deltaOffset:null,deltaPercentage:null,isResizingColumn:!1,columnSizingStart:[]}),D2={getDefaultColumnDef:()=>Ta,getInitialState:e=>({columnSizing:{},columnSizingInfo:nc(),...e}),getDefaultOptions:e=>({columnResizeMode:"onEnd",columnResizeDirection:"ltr",onColumnSizingChange:Pt("columnSizing",e),onColumnSizingInfoChange:Pt("columnSizingInfo",e)}),createColumn:(e,t)=>{e.getSize=()=>{var n,r,o;const i=t.getState().columnSizing[e.id];return Math.min(Math.max((n=e.columnDef.minSize)!=null?n:Ta.minSize,(r=i??e.columnDef.size)!=null?r:Ta.size),(o=e.columnDef.maxSize)!=null?o:Ta.maxSize)},e.getStart=n=>{const r=n?n==="left"?t.getLeftVisibleLeafColumns():t.getRightVisibleLeafColumns():t.getVisibleLeafColumns(),o=r.findIndex(i=>i.id===e.id);if(o>0){const i=r[o-1];return i.getStart(n)+i.getSize()}return 0},e.resetSize=()=>{t.setColumnSizing(n=>{let{[e.id]:r,...o}=n;return o})},e.getCanResize=()=>{var n,r;return((n=e.columnDef.enableResizing)!=null?n:!0)&&((r=t.options.enableColumnResizing)!=null?r:!0)},e.getIsResizing=()=>t.getState().columnSizingInfo.isResizingColumn===e.id},createHeader:(e,t)=>{e.getSize=()=>{let n=0;const r=o=>{if(o.subHeaders.length)o.subHeaders.forEach(r);else{var i;n+=(i=o.column.getSize())!=null?i:0}};return r(e),n},e.getStart=()=>{if(e.index>0){const n=e.headerGroup.headers[e.index-1];return n.getStart()+n.getSize()}return 0},e.getResizeHandler=()=>{const n=t.getColumn(e.column.id),r=n==null?void 0:n.getCanResize();return o=>{if(!n||!r||(o.persist==null||o.persist(),rc(o)&&o.touches&&o.touches.length>1))return;const i=e.getSize(),s=e?e.getLeafHeaders().map(v=>[v.column.id,v.column.getSize()]):[[n.id,n.getSize()]],a=rc(o)?Math.round(o.touches[0].clientX):o.clientX,l={},u=(v,x)=>{typeof x=="number"&&(t.setColumnSizingInfo(m=>{var g,w;const C=t.options.columnResizeDirection==="rtl"?-1:1,E=(x-((g=m==null?void 0:m.startOffset)!=null?g:0))*C,_=Math.max(E/((w=m==null?void 0:m.startSize)!=null?w:0),-.999999);return m.columnSizingStart.forEach(P=>{let[R,O]=P;l[R]=Math.round(Math.max(O+O*_,0)*100)/100}),{...m,deltaOffset:E,deltaPercentage:_}}),(t.options.columnResizeMode==="onChange"||v==="end")&&t.setColumnSizing(m=>({...m,...l})))},c=v=>u("move",v),d=v=>{u("end",v),t.setColumnSizingInfo(x=>({...x,isResizingColumn:!1,startOffset:null,startSize:null,deltaOffset:null,deltaPercentage:null,columnSizingStart:[]}))},f={moveHandler:v=>c(v.clientX),upHandler:v=>{document.removeEventListener("mousemove",f.moveHandler),document.removeEventListener("mouseup",f.upHandler),d(v.clientX)}},h={moveHandler:v=>(v.cancelable&&(v.preventDefault(),v.stopPropagation()),c(v.touches[0].clientX),!1),upHandler:v=>{var x;document.removeEventListener("touchmove",h.moveHandler),document.removeEventListener("touchend",h.upHandler),v.cancelable&&(v.preventDefault(),v.stopPropagation()),d((x=v.touches[0])==null?void 0:x.clientX)}},y=L2()?{passive:!1}:!1;rc(o)?(document.addEventListener("touchmove",h.moveHandler,y),document.addEventListener("touchend",h.upHandler,y)):(document.addEventListener("mousemove",f.moveHandler,y),document.addEventListener("mouseup",f.upHandler,y)),t.setColumnSizingInfo(v=>({...v,startOffset:a,startSize:i,deltaOffset:0,deltaPercentage:0,columnSizingStart:s,isResizingColumn:n.id}))}}},createTable:e=>{e.setColumnSizing=t=>e.options.onColumnSizingChange==null?void 0:e.options.onColumnSizingChange(t),e.setColumnSizingInfo=t=>e.options.onColumnSizingInfoChange==null?void 0:e.options.onColumnSizingInfoChange(t),e.resetColumnSizing=t=>{var n;e.setColumnSizing(t?{}:(n=e.initialState.columnSizing)!=null?n:{})},e.resetHeaderSizeInfo=t=>{var n;e.setColumnSizingInfo(t?nc():(n=e.initialState.columnSizingInfo)!=null?n:nc())},e.getTotalSize=()=>{var t,n;return(t=(n=e.getHeaderGroups()[0])==null?void 0:n.headers.reduce((r,o)=>r+o.getSize(),0))!=null?t:0},e.getLeftTotalSize=()=>{var t,n;return(t=(n=e.getLeftHeaderGroups()[0])==null?void 0:n.headers.reduce((r,o)=>r+o.getSize(),0))!=null?t:0},e.getCenterTotalSize=()=>{var t,n;return(t=(n=e.getCenterHeaderGroups()[0])==null?void 0:n.headers.reduce((r,o)=>r+o.getSize(),0))!=null?t:0},e.getRightTotalSize=()=>{var t,n;return(t=(n=e.getRightHeaderGroups()[0])==null?void 0:n.headers.reduce((r,o)=>r+o.getSize(),0))!=null?t:0}}};let Aa=null;function L2(){if(typeof Aa=="boolean")return Aa;let e=!1;try{const t={get passive(){return e=!0,!1}},n=()=>{};window.addEventListener("test",n,t),window.removeEventListener("test",n)}catch{e=!1}return Aa=e,Aa}function rc(e){return e.type==="touchstart"}const I2={getInitialState:e=>({expanded:{},...e}),getDefaultOptions:e=>({onExpandedChange:Pt("expanded",e),paginateExpandedRows:!0}),createTable:e=>{let t=!1,n=!1;e._autoResetExpanded=()=>{var r,o;if(!t){e._queue(()=>{t=!0});return}if((r=(o=e.options.autoResetAll)!=null?o:e.options.autoResetExpanded)!=null?r:!e.options.manualExpanding){if(n)return;n=!0,e._queue(()=>{e.resetExpanded(),n=!1})}},e.setExpanded=r=>e.options.onExpandedChange==null?void 0:e.options.onExpandedChange(r),e.toggleAllRowsExpanded=r=>{r??!e.getIsAllRowsExpanded()?e.setExpanded(!0):e.setExpanded({})},e.resetExpanded=r=>{var o,i;e.setExpanded(r?{}:(o=(i=e.initialState)==null?void 0:i.expanded)!=null?o:{})},e.getCanSomeRowsExpand=()=>e.getPrePaginationRowModel().flatRows.some(r=>r.getCanExpand()),e.getToggleAllRowsExpandedHandler=()=>r=>{r.persist==null||r.persist(),e.toggleAllRowsExpanded()},e.getIsSomeRowsExpanded=()=>{const r=e.getState().expanded;return r===!0||Object.values(r).some(Boolean)},e.getIsAllRowsExpanded=()=>{const r=e.getState().expanded;return typeof r=="boolean"?r===!0:!(!Object.keys(r).length||e.getRowModel().flatRows.some(o=>!o.getIsExpanded()))},e.getExpandedDepth=()=>{let r=0;return(e.getState().expanded===!0?Object.keys(e.getRowModel().rowsById):Object.keys(e.getState().expanded)).forEach(i=>{const s=i.split(".");r=Math.max(r,s.length)}),r},e.getPreExpandedRowModel=()=>e.getSortedRowModel(),e.getExpandedRowModel=()=>(!e._getExpandedRowModel&&e.options.getExpandedRowModel&&(e._getExpandedRowModel=e.options.getExpandedRowModel(e)),e.options.manualExpanding||!e._getExpandedRowModel?e.getPreExpandedRowModel():e._getExpandedRowModel())},createRow:(e,t)=>{e.toggleExpanded=n=>{t.setExpanded(r=>{var o;const i=r===!0?!0:!!(r!=null&&r[e.id]);let s={};if(r===!0?Object.keys(t.getRowModel().rowsById).forEach(a=>{s[a]=!0}):s=r,n=(o=n)!=null?o:!i,!i&&n)return{...s,[e.id]:!0};if(i&&!n){const{[e.id]:a,...l}=s;return l}return r})},e.getIsExpanded=()=>{var n;const r=t.getState().expanded;return!!((n=t.options.getIsRowExpanded==null?void 0:t.options.getIsRowExpanded(e))!=null?n:r===!0||r!=null&&r[e.id])},e.getCanExpand=()=>{var n,r,o;return(n=t.options.getRowCanExpand==null?void 0:t.options.getRowCanExpand(e))!=null?n:((r=t.options.enableExpanding)!=null?r:!0)&&!!((o=e.subRows)!=null&&o.length)},e.getIsAllParentsExpanded=()=>{let n=!0,r=e;for(;n&&r.parentId;)r=t.getRow(r.parentId,!0),n=r.getIsExpanded();return n},e.getToggleExpandedHandler=()=>{const n=e.getCanExpand();return()=>{n&&e.toggleExpanded()}}}},Sy=(e,t,n)=>{var r;const o=n.toLowerCase();return!!(!((r=e.getValue(t))==null||(r=r.toString())==null||(r=r.toLowerCase())==null)&&r.includes(o))};Sy.autoRemove=e=>Jt(e);const Cy=(e,t,n)=>{var r;return!!(!((r=e.getValue(t))==null||(r=r.toString())==null)&&r.includes(n))};Cy.autoRemove=e=>Jt(e);const by=(e,t,n)=>{var r;return((r=e.getValue(t))==null||(r=r.toString())==null?void 0:r.toLowerCase())===(n==null?void 0:n.toLowerCase())};by.autoRemove=e=>Jt(e);const $y=(e,t,n)=>{var r;return(r=e.getValue(t))==null?void 0:r.includes(n)};$y.autoRemove=e=>Jt(e)||!(e!=null&&e.length);const Ey=(e,t,n)=>!n.some(r=>{var o;return!((o=e.getValue(t))!=null&&o.includes(r))});Ey.autoRemove=e=>Jt(e)||!(e!=null&&e.length);const Ry=(e,t,n)=>n.some(r=>{var o;return(o=e.getValue(t))==null?void 0:o.includes(r)});Ry.autoRemove=e=>Jt(e)||!(e!=null&&e.length);const Py=(e,t,n)=>e.getValue(t)===n;Py.autoRemove=e=>Jt(e);const _y=(e,t,n)=>e.getValue(t)==n;_y.autoRemove=e=>Jt(e);const tp=(e,t,n)=>{let[r,o]=n;const i=e.getValue(t);return i>=r&&i<=o};tp.resolveFilterValue=e=>{let[t,n]=e,r=typeof t!="number"?parseFloat(t):t,o=typeof n!="number"?parseFloat(n):n,i=t===null||Number.isNaN(r)?-1/0:r,s=n===null||Number.isNaN(o)?1/0:o;if(i>s){const a=i;i=s,s=a}return[i,s]};tp.autoRemove=e=>Jt(e)||Jt(e[0])&&Jt(e[1]);const _n={includesString:Sy,includesStringSensitive:Cy,equalsString:by,arrIncludes:$y,arrIncludesAll:Ey,arrIncludesSome:Ry,equals:Py,weakEquals:_y,inNumberRange:tp};function Jt(e){return e==null||e===""}const F2={getDefaultColumnDef:()=>({filterFn:"auto"}),getInitialState:e=>({columnFilters:[],globalFilter:void 0,...e}),getDefaultOptions:e=>({onColumnFiltersChange:Pt("columnFilters",e),onGlobalFilterChange:Pt("globalFilter",e),filterFromLeafRows:!1,maxLeafRowFilterDepth:100,globalFilterFn:"auto",getColumnCanGlobalFilter:t=>{var n;const r=(n=e.getCoreRowModel().flatRows[0])==null||(n=n._getAllCellsByColumnId()[t.id])==null?void 0:n.getValue();return typeof r=="string"||typeof r=="number"}}),createColumn:(e,t)=>{e.getAutoFilterFn=()=>{const n=t.getCoreRowModel().flatRows[0],r=n==null?void 0:n.getValue(e.id);return typeof r=="string"?_n.includesString:typeof r=="number"?_n.inNumberRange:typeof r=="boolean"||r!==null&&typeof r=="object"?_n.equals:Array.isArray(r)?_n.arrIncludes:_n.weakEquals},e.getFilterFn=()=>{var n,r;return jl(e.columnDef.filterFn)?e.columnDef.filterFn:e.columnDef.filterFn==="auto"?e.getAutoFilterFn():(n=(r=t.options.filterFns)==null?void 0:r[e.columnDef.filterFn])!=null?n:_n[e.columnDef.filterFn]},e.getCanFilter=()=>{var n,r,o;return((n=e.columnDef.enableColumnFilter)!=null?n:!0)&&((r=t.options.enableColumnFilters)!=null?r:!0)&&((o=t.options.enableFilters)!=null?o:!0)&&!!e.accessorFn},e.getCanGlobalFilter=()=>{var n,r,o,i;return((n=e.columnDef.enableGlobalFilter)!=null?n:!0)&&((r=t.options.enableGlobalFilter)!=null?r:!0)&&((o=t.options.enableFilters)!=null?o:!0)&&((i=t.options.getColumnCanGlobalFilter==null?void 0:t.options.getColumnCanGlobalFilter(e))!=null?i:!0)&&!!e.accessorFn},e.getIsFiltered=()=>e.getFilterIndex()>-1,e.getFilterValue=()=>{var n;return(n=t.getState().columnFilters)==null||(n=n.find(r=>r.id===e.id))==null?void 0:n.value},e.getFilterIndex=()=>{var n,r;return(n=(r=t.getState().columnFilters)==null?void 0:r.findIndex(o=>o.id===e.id))!=null?n:-1},e.setFilterValue=n=>{t.setColumnFilters(r=>{const o=e.getFilterFn(),i=r==null?void 0:r.find(c=>c.id===e.id),s=yr(n,i?i.value:void 0);if(kh(o,s,e)){var a;return(a=r==null?void 0:r.filter(c=>c.id!==e.id))!=null?a:[]}const l={id:e.id,value:s};if(i){var u;return(u=r==null?void 0:r.map(c=>c.id===e.id?l:c))!=null?u:[]}return r!=null&&r.length?[...r,l]:[l]})},e._getFacetedRowModel=t.options.getFacetedRowModel&&t.options.getFacetedRowModel(t,e.id),e.getFacetedRowModel=()=>e._getFacetedRowModel?e._getFacetedRowModel():t.getPreFilteredRowModel(),e._getFacetedUniqueValues=t.options.getFacetedUniqueValues&&t.options.getFacetedUniqueValues(t,e.id),e.getFacetedUniqueValues=()=>e._getFacetedUniqueValues?e._getFacetedUniqueValues():new Map,e._getFacetedMinMaxValues=t.options.getFacetedMinMaxValues&&t.options.getFacetedMinMaxValues(t,e.id),e.getFacetedMinMaxValues=()=>{if(e._getFacetedMinMaxValues)return e._getFacetedMinMaxValues()}},createRow:(e,t)=>{e.columnFilters={},e.columnFiltersMeta={}},createTable:e=>{e.getGlobalAutoFilterFn=()=>_n.includesString,e.getGlobalFilterFn=()=>{var t,n;const{globalFilterFn:r}=e.options;return jl(r)?r:r==="auto"?e.getGlobalAutoFilterFn():(t=(n=e.options.filterFns)==null?void 0:n[r])!=null?t:_n[r]},e.setColumnFilters=t=>{const n=e.getAllLeafColumns(),r=o=>{var i;return(i=yr(t,o))==null?void 0:i.filter(s=>{const a=n.find(l=>l.id===s.id);if(a){const l=a.getFilterFn();if(kh(l,s.value,a))return!1}return!0})};e.options.onColumnFiltersChange==null||e.options.onColumnFiltersChange(r)},e.setGlobalFilter=t=>{e.options.onGlobalFilterChange==null||e.options.onGlobalFilterChange(t)},e.resetGlobalFilter=t=>{e.setGlobalFilter(t?void 0:e.initialState.globalFilter)},e.resetColumnFilters=t=>{var n,r;e.setColumnFilters(t?[]:(n=(r=e.initialState)==null?void 0:r.columnFilters)!=null?n:[])},e.getPreFilteredRowModel=()=>e.getCoreRowModel(),e.getFilteredRowModel=()=>(!e._getFilteredRowModel&&e.options.getFilteredRowModel&&(e._getFilteredRowModel=e.options.getFilteredRowModel(e)),e.options.manualFiltering||!e._getFilteredRowModel?e.getPreFilteredRowModel():e._getFilteredRowModel()),e._getGlobalFacetedRowModel=e.options.getFacetedRowModel&&e.options.getFacetedRowModel(e,"__global__"),e.getGlobalFacetedRowModel=()=>e.options.manualFiltering||!e._getGlobalFacetedRowModel?e.getPreFilteredRowModel():e._getGlobalFacetedRowModel(),e._getGlobalFacetedUniqueValues=e.options.getFacetedUniqueValues&&e.options.getFacetedUniqueValues(e,"__global__"),e.getGlobalFacetedUniqueValues=()=>e._getGlobalFacetedUniqueValues?e._getGlobalFacetedUniqueValues():new Map,e._getGlobalFacetedMinMaxValues=e.options.getFacetedMinMaxValues&&e.options.getFacetedMinMaxValues(e,"__global__"),e.getGlobalFacetedMinMaxValues=()=>{if(e._getGlobalFacetedMinMaxValues)return e._getGlobalFacetedMinMaxValues()}}};function kh(e,t,n){return(e&&e.autoRemove?e.autoRemove(t,n):!1)||typeof t>"u"||typeof t=="string"&&!t}const j2=(e,t,n)=>n.reduce((r,o)=>{const i=o.getValue(e);return r+(typeof i=="number"?i:0)},0),z2=(e,t,n)=>{let r;return n.forEach(o=>{const i=o.getValue(e);i!=null&&(r>i||r===void 0&&i>=i)&&(r=i)}),r},V2=(e,t,n)=>{let r;return n.forEach(o=>{const i=o.getValue(e);i!=null&&(r<i||r===void 0&&i>=i)&&(r=i)}),r},H2=(e,t,n)=>{let r,o;return n.forEach(i=>{const s=i.getValue(e);s!=null&&(r===void 0?s>=s&&(r=o=s):(r>s&&(r=s),o<s&&(o=s)))}),[r,o]},U2=(e,t)=>{let n=0,r=0;if(t.forEach(o=>{let i=o.getValue(e);i!=null&&(i=+i)>=i&&(++n,r+=i)}),n)return r/n},B2=(e,t)=>{if(!t.length)return;const n=t.map(i=>i.getValue(e));if(!O2(n))return;if(n.length===1)return n[0];const r=Math.floor(n.length/2),o=n.sort((i,s)=>i-s);return n.length%2!==0?o[r]:(o[r-1]+o[r])/2},W2=(e,t)=>Array.from(new Set(t.map(n=>n.getValue(e))).values()),G2=(e,t)=>new Set(t.map(n=>n.getValue(e))).size,K2=(e,t)=>t.length,oc={sum:j2,min:z2,max:V2,extent:H2,mean:U2,median:B2,unique:W2,uniqueCount:G2,count:K2},Q2={getDefaultColumnDef:()=>({aggregatedCell:e=>{var t,n;return(t=(n=e.getValue())==null||n.toString==null?void 0:n.toString())!=null?t:null},aggregationFn:"auto"}),getInitialState:e=>({grouping:[],...e}),getDefaultOptions:e=>({onGroupingChange:Pt("grouping",e),groupedColumnMode:"reorder"}),createColumn:(e,t)=>{e.toggleGrouping=()=>{t.setGrouping(n=>n!=null&&n.includes(e.id)?n.filter(r=>r!==e.id):[...n??[],e.id])},e.getCanGroup=()=>{var n,r,o,i;return(n=(r=(o=(i=e.columnDef.enableGrouping)!=null?i:!0)!=null?o:t.options.enableGrouping)!=null?r:!0)!=null?n:!!e.accessorFn},e.getIsGrouped=()=>{var n;return(n=t.getState().grouping)==null?void 0:n.includes(e.id)},e.getGroupedIndex=()=>{var n;return(n=t.getState().grouping)==null?void 0:n.indexOf(e.id)},e.getToggleGroupingHandler=()=>{const n=e.getCanGroup();return()=>{n&&e.toggleGrouping()}},e.getAutoAggregationFn=()=>{const n=t.getCoreRowModel().flatRows[0],r=n==null?void 0:n.getValue(e.id);if(typeof r=="number")return oc.sum;if(Object.prototype.toString.call(r)==="[object Date]")return oc.extent},e.getAggregationFn=()=>{var n,r;if(!e)throw new Error;return jl(e.columnDef.aggregationFn)?e.columnDef.aggregationFn:e.columnDef.aggregationFn==="auto"?e.getAutoAggregationFn():(n=(r=t.options.aggregationFns)==null?void 0:r[e.columnDef.aggregationFn])!=null?n:oc[e.columnDef.aggregationFn]}},createTable:e=>{e.setGrouping=t=>e.options.onGroupingChange==null?void 0:e.options.onGroupingChange(t),e.resetGrouping=t=>{var n,r;e.setGrouping(t?[]:(n=(r=e.initialState)==null?void 0:r.grouping)!=null?n:[])},e.getPreGroupedRowModel=()=>e.getFilteredRowModel(),e.getGroupedRowModel=()=>(!e._getGroupedRowModel&&e.options.getGroupedRowModel&&(e._getGroupedRowModel=e.options.getGroupedRowModel(e)),e.options.manualGrouping||!e._getGroupedRowModel?e.getPreGroupedRowModel():e._getGroupedRowModel())},createRow:(e,t)=>{e.getIsGrouped=()=>!!e.groupingColumnId,e.getGroupingValue=n=>{if(e._groupingValuesCache.hasOwnProperty(n))return e._groupingValuesCache[n];const r=t.getColumn(n);return r!=null&&r.columnDef.getGroupingValue?(e._groupingValuesCache[n]=r.columnDef.getGroupingValue(e.original),e._groupingValuesCache[n]):e.getValue(n)},e._groupingValuesCache={}},createCell:(e,t,n,r)=>{e.getIsGrouped=()=>t.getIsGrouped()&&t.id===n.groupingColumnId,e.getIsPlaceholder=()=>!e.getIsGrouped()&&t.getIsGrouped(),e.getIsAggregated=()=>{var o;return!e.getIsGrouped()&&!e.getIsPlaceholder()&&!!((o=n.subRows)!=null&&o.length)}}};function Y2(e,t,n){if(!(t!=null&&t.length)||!n)return e;const r=e.filter(i=>!t.includes(i.id));return n==="remove"?r:[...t.map(i=>e.find(s=>s.id===i)).filter(Boolean),...r]}const q2={getInitialState:e=>({columnOrder:[],...e}),getDefaultOptions:e=>({onColumnOrderChange:Pt("columnOrder",e)}),createTable:e=>{e.setColumnOrder=t=>e.options.onColumnOrderChange==null?void 0:e.options.onColumnOrderChange(t),e.resetColumnOrder=t=>{var n;e.setColumnOrder(t?[]:(n=e.initialState.columnOrder)!=null?n:[])},e._getOrderColumnsFn=te(()=>[e.getState().columnOrder,e.getState().grouping,e.options.groupedColumnMode],(t,n,r)=>o=>{let i=[];if(!(t!=null&&t.length))i=o;else{const s=[...t],a=[...o];for(;a.length&&s.length;){const l=s.shift(),u=a.findIndex(c=>c.id===l);u>-1&&i.push(a.splice(u,1)[0])}i=[...i,...a]}return Y2(i,n,r)},{key:!1})}},_d=0,kd=10,ic=()=>({pageIndex:_d,pageSize:kd}),X2={getInitialState:e=>({...e,pagination:{...ic(),...e==null?void 0:e.pagination}}),getDefaultOptions:e=>({onPaginationChange:Pt("pagination",e)}),createTable:e=>{let t=!1,n=!1;e._autoResetPageIndex=()=>{var r,o;if(!t){e._queue(()=>{t=!0});return}if((r=(o=e.options.autoResetAll)!=null?o:e.options.autoResetPageIndex)!=null?r:!e.options.manualPagination){if(n)return;n=!0,e._queue(()=>{e.resetPageIndex(),n=!1})}},e.setPagination=r=>{const o=i=>yr(r,i);return e.options.onPaginationChange==null?void 0:e.options.onPaginationChange(o)},e.resetPagination=r=>{var o;e.setPagination(r?ic():(o=e.initialState.pagination)!=null?o:ic())},e.setPageIndex=r=>{e.setPagination(o=>{let i=yr(r,o.pageIndex);const s=typeof e.options.pageCount>"u"||e.options.pageCount===-1?Number.MAX_SAFE_INTEGER:e.options.pageCount-1;return i=Math.max(0,Math.min(i,s)),{...o,pageIndex:i}})},e.resetPageIndex=r=>{var o,i;e.setPageIndex(r?_d:(o=(i=e.initialState)==null||(i=i.pagination)==null?void 0:i.pageIndex)!=null?o:_d)},e.resetPageSize=r=>{var o,i;e.setPageSize(r?kd:(o=(i=e.initialState)==null||(i=i.pagination)==null?void 0:i.pageSize)!=null?o:kd)},e.setPageSize=r=>{e.setPagination(o=>{const i=Math.max(1,yr(r,o.pageSize)),s=o.pageSize*o.pageIndex,a=Math.floor(s/i);return{...o,pageIndex:a,pageSize:i}})},e.setPageCount=r=>e.setPagination(o=>{var i;let s=yr(r,(i=e.options.pageCount)!=null?i:-1);return typeof s=="number"&&(s=Math.max(-1,s)),{...o,pageCount:s}}),e.getPageOptions=te(()=>[e.getPageCount()],r=>{let o=[];return r&&r>0&&(o=[...new Array(r)].fill(null).map((i,s)=>s)),o},{key:!1,debug:()=>{var r;return(r=e.options.debugAll)!=null?r:e.options.debugTable}}),e.getCanPreviousPage=()=>e.getState().pagination.pageIndex>0,e.getCanNextPage=()=>{const{pageIndex:r}=e.getState().pagination,o=e.getPageCount();return o===-1?!0:o===0?!1:r<o-1},e.previousPage=()=>e.setPageIndex(r=>r-1),e.nextPage=()=>e.setPageIndex(r=>r+1),e.getPrePaginationRowModel=()=>e.getExpandedRowModel(),e.getPaginationRowModel=()=>(!e._getPaginationRowModel&&e.options.getPaginationRowModel&&(e._getPaginationRowModel=e.options.getPaginationRowModel(e)),e.options.manualPagination||!e._getPaginationRowModel?e.getPrePaginationRowModel():e._getPaginationRowModel()),e.getPageCount=()=>{var r;return(r=e.options.pageCount)!=null?r:Math.ceil(e.getPrePaginationRowModel().rows.length/e.getState().pagination.pageSize)}}},sc=()=>({left:[],right:[]}),ac=()=>({top:[],bottom:[]}),Z2={getInitialState:e=>({columnPinning:sc(),rowPinning:ac(),...e}),getDefaultOptions:e=>({onColumnPinningChange:Pt("columnPinning",e),onRowPinningChange:Pt("rowPinning",e)}),createColumn:(e,t)=>{e.pin=n=>{const r=e.getLeafColumns().map(o=>o.id).filter(Boolean);t.setColumnPinning(o=>{var i,s;if(n==="right"){var a,l;return{left:((a=o==null?void 0:o.left)!=null?a:[]).filter(d=>!(r!=null&&r.includes(d))),right:[...((l=o==null?void 0:o.right)!=null?l:[]).filter(d=>!(r!=null&&r.includes(d))),...r]}}if(n==="left"){var u,c;return{left:[...((u=o==null?void 0:o.left)!=null?u:[]).filter(d=>!(r!=null&&r.includes(d))),...r],right:((c=o==null?void 0:o.right)!=null?c:[]).filter(d=>!(r!=null&&r.includes(d)))}}return{left:((i=o==null?void 0:o.left)!=null?i:[]).filter(d=>!(r!=null&&r.includes(d))),right:((s=o==null?void 0:o.right)!=null?s:[]).filter(d=>!(r!=null&&r.includes(d)))}})},e.getCanPin=()=>e.getLeafColumns().some(r=>{var o,i,s;return((o=r.columnDef.enablePinning)!=null?o:!0)&&((i=(s=t.options.enableColumnPinning)!=null?s:t.options.enablePinning)!=null?i:!0)}),e.getIsPinned=()=>{const n=e.getLeafColumns().map(a=>a.id),{left:r,right:o}=t.getState().columnPinning,i=n.some(a=>r==null?void 0:r.includes(a)),s=n.some(a=>o==null?void 0:o.includes(a));return i?"left":s?"right":!1},e.getPinnedIndex=()=>{var n,r;const o=e.getIsPinned();return o?(n=(r=t.getState().columnPinning)==null||(r=r[o])==null?void 0:r.indexOf(e.id))!=null?n:-1:0}},createRow:(e,t)=>{e.pin=(n,r,o)=>{const i=r?e.getLeafRows().map(l=>{let{id:u}=l;return u}):[],s=o?e.getParentRows().map(l=>{let{id:u}=l;return u}):[],a=new Set([...s,e.id,...i]);t.setRowPinning(l=>{var u,c;if(n==="bottom"){var d,f;return{top:((d=l==null?void 0:l.top)!=null?d:[]).filter(v=>!(a!=null&&a.has(v))),bottom:[...((f=l==null?void 0:l.bottom)!=null?f:[]).filter(v=>!(a!=null&&a.has(v))),...Array.from(a)]}}if(n==="top"){var h,y;return{top:[...((h=l==null?void 0:l.top)!=null?h:[]).filter(v=>!(a!=null&&a.has(v))),...Array.from(a)],bottom:((y=l==null?void 0:l.bottom)!=null?y:[]).filter(v=>!(a!=null&&a.has(v)))}}return{top:((u=l==null?void 0:l.top)!=null?u:[]).filter(v=>!(a!=null&&a.has(v))),bottom:((c=l==null?void 0:l.bottom)!=null?c:[]).filter(v=>!(a!=null&&a.has(v)))}})},e.getCanPin=()=>{var n;const{enableRowPinning:r,enablePinning:o}=t.options;return typeof r=="function"?r(e):(n=r??o)!=null?n:!0},e.getIsPinned=()=>{const n=[e.id],{top:r,bottom:o}=t.getState().rowPinning,i=n.some(a=>r==null?void 0:r.includes(a)),s=n.some(a=>o==null?void 0:o.includes(a));return i?"top":s?"bottom":!1},e.getPinnedIndex=()=>{var n,r;const o=e.getIsPinned();if(!o)return-1;const i=(n=t._getPinnedRows(o))==null?void 0:n.map(s=>{let{id:a}=s;return a});return(r=i==null?void 0:i.indexOf(e.id))!=null?r:-1},e.getCenterVisibleCells=te(()=>[e._getAllVisibleCells(),t.getState().columnPinning.left,t.getState().columnPinning.right],(n,r,o)=>{const i=[...r??[],...o??[]];return n.filter(s=>!i.includes(s.column.id))},{key:!1,debug:()=>{var n;return(n=t.options.debugAll)!=null?n:t.options.debugRows}}),e.getLeftVisibleCells=te(()=>[e._getAllVisibleCells(),t.getState().columnPinning.left,,],(n,r)=>(r??[]).map(i=>n.find(s=>s.column.id===i)).filter(Boolean).map(i=>({...i,position:"left"})),{key:!1,debug:()=>{var n;return(n=t.options.debugAll)!=null?n:t.options.debugRows}}),e.getRightVisibleCells=te(()=>[e._getAllVisibleCells(),t.getState().columnPinning.right],(n,r)=>(r??[]).map(i=>n.find(s=>s.column.id===i)).filter(Boolean).map(i=>({...i,position:"right"})),{key:!1,debug:()=>{var n;return(n=t.options.debugAll)!=null?n:t.options.debugRows}})},createTable:e=>{e.setColumnPinning=t=>e.options.onColumnPinningChange==null?void 0:e.options.onColumnPinningChange(t),e.resetColumnPinning=t=>{var n,r;return e.setColumnPinning(t?sc():(n=(r=e.initialState)==null?void 0:r.columnPinning)!=null?n:sc())},e.getIsSomeColumnsPinned=t=>{var n;const r=e.getState().columnPinning;if(!t){var o,i;return!!((o=r.left)!=null&&o.length||(i=r.right)!=null&&i.length)}return!!((n=r[t])!=null&&n.length)},e.getLeftLeafColumns=te(()=>[e.getAllLeafColumns(),e.getState().columnPinning.left],(t,n)=>(n??[]).map(r=>t.find(o=>o.id===r)).filter(Boolean),{key:!1,debug:()=>{var t;return(t=e.options.debugAll)!=null?t:e.options.debugColumns}}),e.getRightLeafColumns=te(()=>[e.getAllLeafColumns(),e.getState().columnPinning.right],(t,n)=>(n??[]).map(r=>t.find(o=>o.id===r)).filter(Boolean),{key:!1,debug:()=>{var t;return(t=e.options.debugAll)!=null?t:e.options.debugColumns}}),e.getCenterLeafColumns=te(()=>[e.getAllLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right],(t,n,r)=>{const o=[...n??[],...r??[]];return t.filter(i=>!o.includes(i.id))},{key:!1,debug:()=>{var t;return(t=e.options.debugAll)!=null?t:e.options.debugColumns}}),e.setRowPinning=t=>e.options.onRowPinningChange==null?void 0:e.options.onRowPinningChange(t),e.resetRowPinning=t=>{var n,r;return e.setRowPinning(t?ac():(n=(r=e.initialState)==null?void 0:r.rowPinning)!=null?n:ac())},e.getIsSomeRowsPinned=t=>{var n;const r=e.getState().rowPinning;if(!t){var o,i;return!!((o=r.top)!=null&&o.length||(i=r.bottom)!=null&&i.length)}return!!((n=r[t])!=null&&n.length)},e._getPinnedRows=t=>te(()=>[e.getRowModel().rows,e.getState().rowPinning[t]],(n,r)=>{var o;return((o=e.options.keepPinnedRows)==null||o?(r??[]).map(s=>{const a=e.getRow(s,!0);return a.getIsAllParentsExpanded()?a:null}):(r??[]).map(s=>n.find(a=>a.id===s))).filter(Boolean).map(s=>({...s,position:t}))},{key:!1,debug:()=>{var n;return(n=e.options.debugAll)!=null?n:e.options.debugRows}})(),e.getTopRows=()=>e._getPinnedRows("top"),e.getBottomRows=()=>e._getPinnedRows("bottom"),e.getCenterRows=te(()=>[e.getRowModel().rows,e.getState().rowPinning.top,e.getState().rowPinning.bottom],(t,n,r)=>{const o=new Set([...n??[],...r??[]]);return t.filter(i=>!o.has(i.id))},{key:!1,debug:()=>{var t;return(t=e.options.debugAll)!=null?t:e.options.debugRows}})}},J2={getInitialState:e=>({rowSelection:{},...e}),getDefaultOptions:e=>({onRowSelectionChange:Pt("rowSelection",e),enableRowSelection:!0,enableMultiRowSelection:!0,enableSubRowSelection:!0}),createTable:e=>{e.setRowSelection=t=>e.options.onRowSelectionChange==null?void 0:e.options.onRowSelectionChange(t),e.resetRowSelection=t=>{var n;return e.setRowSelection(t?{}:(n=e.initialState.rowSelection)!=null?n:{})},e.toggleAllRowsSelected=t=>{e.setRowSelection(n=>{t=typeof t<"u"?t:!e.getIsAllRowsSelected();const r={...n},o=e.getPreGroupedRowModel().flatRows;return t?o.forEach(i=>{i.getCanSelect()&&(r[i.id]=!0)}):o.forEach(i=>{delete r[i.id]}),r})},e.toggleAllPageRowsSelected=t=>e.setRowSelection(n=>{const r=typeof t<"u"?t:!e.getIsAllPageRowsSelected(),o={...n};return e.getRowModel().rows.forEach(i=>{Nd(o,i.id,r,!0,e)}),o}),e.getPreSelectedRowModel=()=>e.getCoreRowModel(),e.getSelectedRowModel=te(()=>[e.getState().rowSelection,e.getCoreRowModel()],(t,n)=>Object.keys(t).length?lc(e,n):{rows:[],flatRows:[],rowsById:{}},{key:!1,debug:()=>{var t;return(t=e.options.debugAll)!=null?t:e.options.debugTable}}),e.getFilteredSelectedRowModel=te(()=>[e.getState().rowSelection,e.getFilteredRowModel()],(t,n)=>Object.keys(t).length?lc(e,n):{rows:[],flatRows:[],rowsById:{}},{key:"getFilteredSelectedRowModel",debug:()=>{var t;return(t=e.options.debugAll)!=null?t:e.options.debugTable}}),e.getGroupedSelectedRowModel=te(()=>[e.getState().rowSelection,e.getSortedRowModel()],(t,n)=>Object.keys(t).length?lc(e,n):{rows:[],flatRows:[],rowsById:{}},{key:"getGroupedSelectedRowModel",debug:()=>{var t;return(t=e.options.debugAll)!=null?t:e.options.debugTable}}),e.getIsAllRowsSelected=()=>{const t=e.getFilteredRowModel().flatRows,{rowSelection:n}=e.getState();let r=!!(t.length&&Object.keys(n).length);return r&&t.some(o=>o.getCanSelect()&&!n[o.id])&&(r=!1),r},e.getIsAllPageRowsSelected=()=>{const t=e.getPaginationRowModel().flatRows.filter(o=>o.getCanSelect()),{rowSelection:n}=e.getState();let r=!!t.length;return r&&t.some(o=>!n[o.id])&&(r=!1),r},e.getIsSomeRowsSelected=()=>{var t;const n=Object.keys((t=e.getState().rowSelection)!=null?t:{}).length;return n>0&&n<e.getFilteredRowModel().flatRows.length},e.getIsSomePageRowsSelected=()=>{const t=e.getPaginationRowModel().flatRows;return e.getIsAllPageRowsSelected()?!1:t.filter(n=>n.getCanSelect()).some(n=>n.getIsSelected()||n.getIsSomeSelected())},e.getToggleAllRowsSelectedHandler=()=>t=>{e.toggleAllRowsSelected(t.target.checked)},e.getToggleAllPageRowsSelectedHandler=()=>t=>{e.toggleAllPageRowsSelected(t.target.checked)}},createRow:(e,t)=>{e.toggleSelected=(n,r)=>{const o=e.getIsSelected();t.setRowSelection(i=>{var s;if(n=typeof n<"u"?n:!o,e.getCanSelect()&&o===n)return i;const a={...i};return Nd(a,e.id,n,(s=r==null?void 0:r.selectChildren)!=null?s:!0,t),a})},e.getIsSelected=()=>{const{rowSelection:n}=t.getState();return np(e,n)},e.getIsSomeSelected=()=>{const{rowSelection:n}=t.getState();return Od(e,n)==="some"},e.getIsAllSubRowsSelected=()=>{const{rowSelection:n}=t.getState();return Od(e,n)==="all"},e.getCanSelect=()=>{var n;return typeof t.options.enableRowSelection=="function"?t.options.enableRowSelection(e):(n=t.options.enableRowSelection)!=null?n:!0},e.getCanSelectSubRows=()=>{var n;return typeof t.options.enableSubRowSelection=="function"?t.options.enableSubRowSelection(e):(n=t.options.enableSubRowSelection)!=null?n:!0},e.getCanMultiSelect=()=>{var n;return typeof t.options.enableMultiRowSelection=="function"?t.options.enableMultiRowSelection(e):(n=t.options.enableMultiRowSelection)!=null?n:!0},e.getToggleSelectedHandler=()=>{const n=e.getCanSelect();return r=>{var o;n&&e.toggleSelected((o=r.target)==null?void 0:o.checked)}}}},Nd=(e,t,n,r,o)=>{var i;const s=o.getRow(t,!0);n?(s.getCanMultiSelect()||Object.keys(e).forEach(a=>delete e[a]),s.getCanSelect()&&(e[t]=!0)):delete e[t],r&&(i=s.subRows)!=null&&i.length&&s.getCanSelectSubRows()&&s.subRows.forEach(a=>Nd(e,a.id,n,r,o))};function lc(e,t){const n=e.getState().rowSelection,r=[],o={},i=function(s,a){return s.map(l=>{var u;const c=np(l,n);if(c&&(r.push(l),o[l.id]=l),(u=l.subRows)!=null&&u.length&&(l={...l,subRows:i(l.subRows)}),c)return l}).filter(Boolean)};return{rows:i(t.rows),flatRows:r,rowsById:o}}function np(e,t){var n;return(n=t[e.id])!=null?n:!1}function Od(e,t,n){var r;if(!((r=e.subRows)!=null&&r.length))return!1;let o=!0,i=!1;return e.subRows.forEach(s=>{if(!(i&&!o)&&(s.getCanSelect()&&(np(s,t)?i=!0:o=!1),s.subRows&&s.subRows.length)){const a=Od(s,t);a==="all"?i=!0:(a==="some"&&(i=!0),o=!1)}}),o?"all":i?"some":!1}const Td=/([0-9]+)/gm,eE=(e,t,n)=>ky(Or(e.getValue(n)).toLowerCase(),Or(t.getValue(n)).toLowerCase()),tE=(e,t,n)=>ky(Or(e.getValue(n)),Or(t.getValue(n))),nE=(e,t,n)=>rp(Or(e.getValue(n)).toLowerCase(),Or(t.getValue(n)).toLowerCase()),rE=(e,t,n)=>rp(Or(e.getValue(n)),Or(t.getValue(n))),oE=(e,t,n)=>{const r=e.getValue(n),o=t.getValue(n);return r>o?1:r<o?-1:0},iE=(e,t,n)=>rp(e.getValue(n),t.getValue(n));function rp(e,t){return e===t?0:e>t?1:-1}function Or(e){return typeof e=="number"?isNaN(e)||e===1/0||e===-1/0?"":String(e):typeof e=="string"?e:""}function ky(e,t){const n=e.split(Td).filter(Boolean),r=t.split(Td).filter(Boolean);for(;n.length&&r.length;){const o=n.shift(),i=r.shift(),s=parseInt(o,10),a=parseInt(i,10),l=[s,a].sort();if(isNaN(l[0])){if(o>i)return 1;if(i>o)return-1;continue}if(isNaN(l[1]))return isNaN(s)?-1:1;if(s>a)return 1;if(a>s)return-1}return n.length-r.length}const Yi={alphanumeric:eE,alphanumericCaseSensitive:tE,text:nE,textCaseSensitive:rE,datetime:oE,basic:iE},sE={getInitialState:e=>({sorting:[],...e}),getDefaultColumnDef:()=>({sortingFn:"auto",sortUndefined:1}),getDefaultOptions:e=>({onSortingChange:Pt("sorting",e),isMultiSortEvent:t=>t.shiftKey}),createColumn:(e,t)=>{e.getAutoSortingFn=()=>{const n=t.getFilteredRowModel().flatRows.slice(10);let r=!1;for(const o of n){const i=o==null?void 0:o.getValue(e.id);if(Object.prototype.toString.call(i)==="[object Date]")return Yi.datetime;if(typeof i=="string"&&(r=!0,i.split(Td).length>1))return Yi.alphanumeric}return r?Yi.text:Yi.basic},e.getAutoSortDir=()=>{const n=t.getFilteredRowModel().flatRows[0];return typeof(n==null?void 0:n.getValue(e.id))=="string"?"asc":"desc"},e.getSortingFn=()=>{var n,r;if(!e)throw new Error;return jl(e.columnDef.sortingFn)?e.columnDef.sortingFn:e.columnDef.sortingFn==="auto"?e.getAutoSortingFn():(n=(r=t.options.sortingFns)==null?void 0:r[e.columnDef.sortingFn])!=null?n:Yi[e.columnDef.sortingFn]},e.toggleSorting=(n,r)=>{const o=e.getNextSortingOrder(),i=typeof n<"u"&&n!==null;t.setSorting(s=>{const a=s==null?void 0:s.find(h=>h.id===e.id),l=s==null?void 0:s.findIndex(h=>h.id===e.id);let u=[],c,d=i?n:o==="desc";if(s!=null&&s.length&&e.getCanMultiSort()&&r?a?c="toggle":c="add":s!=null&&s.length&&l!==s.length-1?c="replace":a?c="toggle":c="replace",c==="toggle"&&(i||o||(c="remove")),c==="add"){var f;u=[...s,{id:e.id,desc:d}],u.splice(0,u.length-((f=t.options.maxMultiSortColCount)!=null?f:Number.MAX_SAFE_INTEGER))}else c==="toggle"?u=s.map(h=>h.id===e.id?{...h,desc:d}:h):c==="remove"?u=s.filter(h=>h.id!==e.id):u=[{id:e.id,desc:d}];return u})},e.getFirstSortDir=()=>{var n,r;return((n=(r=e.columnDef.sortDescFirst)!=null?r:t.options.sortDescFirst)!=null?n:e.getAutoSortDir()==="desc")?"desc":"asc"},e.getNextSortingOrder=n=>{var r,o;const i=e.getFirstSortDir(),s=e.getIsSorted();return s?s!==i&&((r=t.options.enableSortingRemoval)==null||r)&&(!(n&&(o=t.options.enableMultiRemove)!=null)||o)?!1:s==="desc"?"asc":"desc":i},e.getCanSort=()=>{var n,r;return((n=e.columnDef.enableSorting)!=null?n:!0)&&((r=t.options.enableSorting)!=null?r:!0)&&!!e.accessorFn},e.getCanMultiSort=()=>{var n,r;return(n=(r=e.columnDef.enableMultiSort)!=null?r:t.options.enableMultiSort)!=null?n:!!e.accessorFn},e.getIsSorted=()=>{var n;const r=(n=t.getState().sorting)==null?void 0:n.find(o=>o.id===e.id);return r?r.desc?"desc":"asc":!1},e.getSortIndex=()=>{var n,r;return(n=(r=t.getState().sorting)==null?void 0:r.findIndex(o=>o.id===e.id))!=null?n:-1},e.clearSorting=()=>{t.setSorting(n=>n!=null&&n.length?n.filter(r=>r.id!==e.id):[])},e.getToggleSortingHandler=()=>{const n=e.getCanSort();return r=>{n&&(r.persist==null||r.persist(),e.toggleSorting==null||e.toggleSorting(void 0,e.getCanMultiSort()?t.options.isMultiSortEvent==null?void 0:t.options.isMultiSortEvent(r):!1))}}},createTable:e=>{e.setSorting=t=>e.options.onSortingChange==null?void 0:e.options.onSortingChange(t),e.resetSorting=t=>{var n,r;e.setSorting(t?[]:(n=(r=e.initialState)==null?void 0:r.sorting)!=null?n:[])},e.getPreSortedRowModel=()=>e.getGroupedRowModel(),e.getSortedRowModel=()=>(!e._getSortedRowModel&&e.options.getSortedRowModel&&(e._getSortedRowModel=e.options.getSortedRowModel(e)),e.options.manualSorting||!e._getSortedRowModel?e.getPreSortedRowModel():e._getSortedRowModel())}},aE={getInitialState:e=>({columnVisibility:{},...e}),getDefaultOptions:e=>({onColumnVisibilityChange:Pt("columnVisibility",e)}),createColumn:(e,t)=>{e.toggleVisibility=n=>{e.getCanHide()&&t.setColumnVisibility(r=>({...r,[e.id]:n??!e.getIsVisible()}))},e.getIsVisible=()=>{var n,r;return(n=(r=t.getState().columnVisibility)==null?void 0:r[e.id])!=null?n:!0},e.getCanHide=()=>{var n,r;return((n=e.columnDef.enableHiding)!=null?n:!0)&&((r=t.options.enableHiding)!=null?r:!0)},e.getToggleVisibilityHandler=()=>n=>{e.toggleVisibility==null||e.toggleVisibility(n.target.checked)}},createRow:(e,t)=>{e._getAllVisibleCells=te(()=>[e.getAllCells(),t.getState().columnVisibility],n=>n.filter(r=>r.column.getIsVisible()),{key:"row._getAllVisibleCells",debug:()=>{var n;return(n=t.options.debugAll)!=null?n:t.options.debugRows}}),e.getVisibleCells=te(()=>[e.getLeftVisibleCells(),e.getCenterVisibleCells(),e.getRightVisibleCells()],(n,r,o)=>[...n,...r,...o],{key:!1,debug:()=>{var n;return(n=t.options.debugAll)!=null?n:t.options.debugRows}})},createTable:e=>{const t=(n,r)=>te(()=>[r(),r().filter(o=>o.getIsVisible()).map(o=>o.id).join("_")],o=>o.filter(i=>i.getIsVisible==null?void 0:i.getIsVisible()),{key:n,debug:()=>{var o;return(o=e.options.debugAll)!=null?o:e.options.debugColumns}});e.getVisibleFlatColumns=t("getVisibleFlatColumns",()=>e.getAllFlatColumns()),e.getVisibleLeafColumns=t("getVisibleLeafColumns",()=>e.getAllLeafColumns()),e.getLeftVisibleLeafColumns=t("getLeftVisibleLeafColumns",()=>e.getLeftLeafColumns()),e.getRightVisibleLeafColumns=t("getRightVisibleLeafColumns",()=>e.getRightLeafColumns()),e.getCenterVisibleLeafColumns=t("getCenterVisibleLeafColumns",()=>e.getCenterLeafColumns()),e.setColumnVisibility=n=>e.options.onColumnVisibilityChange==null?void 0:e.options.onColumnVisibilityChange(n),e.resetColumnVisibility=n=>{var r;e.setColumnVisibility(n?{}:(r=e.initialState.columnVisibility)!=null?r:{})},e.toggleAllColumnsVisible=n=>{var r;n=(r=n)!=null?r:!e.getIsAllColumnsVisible(),e.setColumnVisibility(e.getAllLeafColumns().reduce((o,i)=>({...o,[i.id]:n||!(i.getCanHide!=null&&i.getCanHide())}),{}))},e.getIsAllColumnsVisible=()=>!e.getAllLeafColumns().some(n=>!(n.getIsVisible!=null&&n.getIsVisible())),e.getIsSomeColumnsVisible=()=>e.getAllLeafColumns().some(n=>n.getIsVisible==null?void 0:n.getIsVisible()),e.getToggleAllColumnsVisibilityHandler=()=>n=>{var r;e.toggleAllColumnsVisible((r=n.target)==null?void 0:r.checked)}}},Nh=[M2,aE,q2,Z2,F2,sE,Q2,I2,X2,J2,D2];function lE(e){var t;(e.debugAll||e.debugTable)&&console.info("Creating Table Instance...");let n={_features:Nh};const r=n._features.reduce((c,d)=>Object.assign(c,d.getDefaultOptions==null?void 0:d.getDefaultOptions(n)),{}),o=c=>n.options.mergeOptions?n.options.mergeOptions(r,c):{...r,...c};let s={...{},...(t=e.initialState)!=null?t:{}};n._features.forEach(c=>{var d;s=(d=c.getInitialState==null?void 0:c.getInitialState(s))!=null?d:s});const a=[];let l=!1;const u={_features:Nh,options:{...r,...e},initialState:s,_queue:c=>{a.push(c),l||(l=!0,Promise.resolve().then(()=>{for(;a.length;)a.shift()();l=!1}).catch(d=>setTimeout(()=>{throw d})))},reset:()=>{n.setState(n.initialState)},setOptions:c=>{const d=yr(c,n.options);n.options=o(d)},getState:()=>n.options.state,setState:c=>{n.options.onStateChange==null||n.options.onStateChange(c)},_getRowId:(c,d,f)=>{var h;return(h=n.options.getRowId==null?void 0:n.options.getRowId(c,d,f))!=null?h:`${f?[f.id,d].join("."):d}`},getCoreRowModel:()=>(n._getCoreRowModel||(n._getCoreRowModel=n.options.getCoreRowModel(n)),n._getCoreRowModel()),getRowModel:()=>n.getPaginationRowModel(),getRow:(c,d)=>{const f=(d?n.getCoreRowModel():n.getRowModel()).rowsById[c];if(!f)throw new Error;return f},_getDefaultColumnDef:te(()=>[n.options.defaultColumn],c=>{var d;return c=(d=c)!=null?d:{},{header:f=>{const h=f.header.column.columnDef;return h.accessorKey?h.accessorKey:h.accessorFn?h.id:null},cell:f=>{var h,y;return(h=(y=f.renderValue())==null||y.toString==null?void 0:y.toString())!=null?h:null},...n._features.reduce((f,h)=>Object.assign(f,h.getDefaultColumnDef==null?void 0:h.getDefaultColumnDef()),{}),...c}},{debug:()=>{var c;return(c=n.options.debugAll)!=null?c:n.options.debugColumns},key:!1}),_getColumnDefs:()=>n.options.columns,getAllColumns:te(()=>[n._getColumnDefs()],c=>{const d=function(f,h,y){return y===void 0&&(y=0),f.map(v=>{const x=A2(n,v,y,h),m=v;return x.columns=m.columns?d(m.columns,x,y+1):[],x})};return d(c)},{key:!1,debug:()=>{var c;return(c=n.options.debugAll)!=null?c:n.options.debugColumns}}),getAllFlatColumns:te(()=>[n.getAllColumns()],c=>c.flatMap(d=>d.getFlatColumns()),{key:!1,debug:()=>{var c;return(c=n.options.debugAll)!=null?c:n.options.debugColumns}}),_getAllFlatColumnsById:te(()=>[n.getAllFlatColumns()],c=>c.reduce((d,f)=>(d[f.id]=f,d),{}),{key:!1,debug:()=>{var c;return(c=n.options.debugAll)!=null?c:n.options.debugColumns}}),getAllLeafColumns:te(()=>[n.getAllColumns(),n._getOrderColumnsFn()],(c,d)=>{let f=c.flatMap(h=>h.getLeafColumns());return d(f)},{key:!1,debug:()=>{var c;return(c=n.options.debugAll)!=null?c:n.options.debugColumns}}),getColumn:c=>n._getAllFlatColumnsById()[c]};Object.assign(n,u);for(let c=0;c<n._features.length;c++){const d=n._features[c];d==null||d.createTable==null||d.createTable(n)}return n}function uE(e,t,n,r){const o=()=>{var s;return(s=i.getValue())!=null?s:e.options.renderFallbackValue},i={id:`${t.id}_${n.id}`,row:t,column:n,getValue:()=>t.getValue(r),renderValue:o,getContext:te(()=>[e,n,t,i],(s,a,l,u)=>({table:s,column:a,row:l,cell:u,getValue:u.getValue,renderValue:u.renderValue}),{key:!1,debug:()=>e.options.debugAll})};return e._features.forEach(s=>{s.createCell==null||s.createCell(i,n,t,e)},{}),i}const cE=(e,t,n,r,o,i,s)=>{let a={id:t,index:r,original:n,depth:o,parentId:s,_valuesCache:{},_uniqueValuesCache:{},getValue:l=>{if(a._valuesCache.hasOwnProperty(l))return a._valuesCache[l];const u=e.getColumn(l);if(u!=null&&u.accessorFn)return a._valuesCache[l]=u.accessorFn(a.original,r),a._valuesCache[l]},getUniqueValues:l=>{if(a._uniqueValuesCache.hasOwnProperty(l))return a._uniqueValuesCache[l];const u=e.getColumn(l);if(u!=null&&u.accessorFn)return u.columnDef.getUniqueValues?(a._uniqueValuesCache[l]=u.columnDef.getUniqueValues(a.original,r),a._uniqueValuesCache[l]):(a._uniqueValuesCache[l]=[a.getValue(l)],a._uniqueValuesCache[l])},renderValue:l=>{var u;return(u=a.getValue(l))!=null?u:e.options.renderFallbackValue},subRows:i??[],getLeafRows:()=>T2(a.subRows,l=>l.subRows),getParentRow:()=>a.parentId?e.getRow(a.parentId,!0):void 0,getParentRows:()=>{let l=[],u=a;for(;;){const c=u.getParentRow();if(!c)break;l.push(c),u=c}return l.reverse()},getAllCells:te(()=>[e.getAllLeafColumns()],l=>l.map(u=>uE(e,a,u,u.id)),{key:!1,debug:()=>{var l;return(l=e.options.debugAll)!=null?l:e.options.debugRows}}),_getAllCellsByColumnId:te(()=>[a.getAllCells()],l=>l.reduce((u,c)=>(u[c.column.id]=c,u),{}),{key:"row.getAllCellsByColumnId",debug:()=>{var l;return(l=e.options.debugAll)!=null?l:e.options.debugRows}})};for(let l=0;l<e._features.length;l++){const u=e._features[l];u==null||u.createRow==null||u.createRow(a,e)}return a};function dE(){return e=>te(()=>[e.options.data],t=>{const n={rows:[],flatRows:[],rowsById:{}},r=function(o,i,s){i===void 0&&(i=0);const a=[];for(let u=0;u<o.length;u++){const c=cE(e,e._getRowId(o[u],u,s),o[u],u,i,void 0,s==null?void 0:s.id);if(n.flatRows.push(c),n.rowsById[c.id]=c,a.push(c),e.options.getSubRows){var l;c.originalSubRows=e.options.getSubRows(o[u],u),(l=c.originalSubRows)!=null&&l.length&&(c.subRows=r(c.originalSubRows,i+1,c))}}return a};return n.rows=r(t),n},{key:!1,debug:()=>{var t;return(t=e.options.debugAll)!=null?t:e.options.debugTable},onChange:()=>{e._autoResetPageIndex()}})}function fE(){return e=>te(()=>[e.getState().sorting,e.getPreSortedRowModel()],(t,n)=>{if(!n.rows.length||!(t!=null&&t.length))return n;const r=e.getState().sorting,o=[],i=r.filter(l=>{var u;return(u=e.getColumn(l.id))==null?void 0:u.getCanSort()}),s={};i.forEach(l=>{const u=e.getColumn(l.id);u&&(s[l.id]={sortUndefined:u.columnDef.sortUndefined,invertSorting:u.columnDef.invertSorting,sortingFn:u.getSortingFn()})});const a=l=>{const u=l.map(c=>({...c}));return u.sort((c,d)=>{for(let h=0;h<i.length;h+=1){var f;const y=i[h],v=s[y.id],x=(f=y==null?void 0:y.desc)!=null?f:!1;let m=0;if(v.sortUndefined){const g=c.getValue(y.id),w=d.getValue(y.id),C=g===void 0,E=w===void 0;(C||E)&&(m=C&&E?0:C?v.sortUndefined:-v.sortUndefined)}if(m===0&&(m=v.sortingFn(c,d,y.id)),m!==0)return x&&(m*=-1),v.invertSorting&&(m*=-1),m}return c.index-d.index}),u.forEach(c=>{var d;o.push(c),(d=c.subRows)!=null&&d.length&&(c.subRows=a(c.subRows))}),u};return{rows:a(n.rows),flatRows:o,rowsById:n.rowsById}},{key:!1,debug:()=>{var t;return(t=e.options.debugAll)!=null?t:e.options.debugTable},onChange:()=>{e._autoResetPageIndex()}})}/**
 * react-table
 *
 * Copyright (c) TanStack
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Oh(e,t){return e?pE(e)?p.createElement(e,t):e:null}function pE(e){return gE(e)||typeof e=="function"||hE(e)}function gE(e){return typeof e=="function"&&(()=>{const t=Object.getPrototypeOf(e);return t.prototype&&t.prototype.isReactComponent})()}function hE(e){return typeof e=="object"&&typeof e.$$typeof=="symbol"&&["react.memo","react.forward_ref"].includes(e.$$typeof.description)}function mE(e){const t={state:{},onStateChange:()=>{},renderFallbackValue:null,...e},[n]=p.useState(()=>({current:lE(t)})),[r,o]=p.useState(()=>n.current.initialState);return n.current.setOptions(i=>({...i,...e,state:{...r,...e.state},onStateChange:s=>{o(s),e.onStateChange==null||e.onStateChange(s)}})),n.current}function vE(e,t){return p.useReducer((n,r)=>{const o=t[n][r];return o??n},e)}const Oi=e=>{const{present:t,children:n}=e,r=yE(t),o=typeof n=="function"?n({present:r.isPresent}):p.Children.only(n),i=Ie(r.ref,o.ref);return typeof n=="function"||r.isPresent?p.cloneElement(o,{ref:i}):null};Oi.displayName="Presence";function yE(e){const[t,n]=p.useState(),r=p.useRef({}),o=p.useRef(e),i=p.useRef("none"),s=e?"mounted":"unmounted",[a,l]=vE(s,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return p.useEffect(()=>{const u=Ma(r.current);i.current=a==="mounted"?u:"none"},[a]),tn(()=>{const u=r.current,c=o.current;if(c!==e){const f=i.current,h=Ma(u);e?l("MOUNT"):h==="none"||(u==null?void 0:u.display)==="none"?l("UNMOUNT"):l(c&&f!==h?"ANIMATION_OUT":"UNMOUNT"),o.current=e}},[e,l]),tn(()=>{if(t){const u=d=>{const h=Ma(r.current).includes(d.animationName);d.target===t&&h&&ho.flushSync(()=>l("ANIMATION_END"))},c=d=>{d.target===t&&(i.current=Ma(r.current))};return t.addEventListener("animationstart",c),t.addEventListener("animationcancel",u),t.addEventListener("animationend",u),()=>{t.removeEventListener("animationstart",c),t.removeEventListener("animationcancel",u),t.removeEventListener("animationend",u)}}else l("ANIMATION_END")},[t,l]),{isPresent:["mounted","unmountSuspended"].includes(a),ref:p.useCallback(u=>{u&&(r.current=getComputedStyle(u)),n(u)},[])}}function Ma(e){return(e==null?void 0:e.animationName)||"none"}const Ny="Dialog",[Oy,Ty]=_i(Ny),[wE,wn]=Oy(Ny),xE=e=>{const{__scopeDialog:t,children:n,open:r,defaultOpen:o,onOpenChange:i,modal:s=!0}=e,a=p.useRef(null),l=p.useRef(null),[u=!1,c]=Il({prop:r,defaultProp:o,onChange:i});return p.createElement(wE,{scope:t,triggerRef:a,contentRef:l,contentId:ro(),titleId:ro(),descriptionId:ro(),open:u,onOpenChange:c,onOpenToggle:p.useCallback(()=>c(d=>!d),[c]),modal:s},n)},Ay="DialogPortal",[SE,My]=Oy(Ay,{forceMount:void 0}),CE=e=>{const{__scopeDialog:t,forceMount:n,children:r,container:o}=e,i=wn(Ay,t);return p.createElement(SE,{scope:t,forceMount:n},p.Children.map(r,s=>p.createElement(Oi,{present:n||i.open},p.createElement(Bf,{asChild:!0,container:o},s))))},Ad="DialogOverlay",bE=p.forwardRef((e,t)=>{const n=My(Ad,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,i=wn(Ad,e.__scopeDialog);return i.modal?p.createElement(Oi,{present:r||i.open},p.createElement($E,Z({},o,{ref:t}))):null}),$E=p.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=wn(Ad,n);return p.createElement(Gf,{as:co,allowPinchZoom:!0,shards:[o.contentRef]},p.createElement(Te.div,Z({"data-state":Iy(o.open)},r,{ref:t,style:{pointerEvents:"auto",...r.style}})))}),Ci="DialogContent",EE=p.forwardRef((e,t)=>{const n=My(Ci,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,i=wn(Ci,e.__scopeDialog);return p.createElement(Oi,{present:r||i.open},i.modal?p.createElement(RE,Z({},o,{ref:t})):p.createElement(PE,Z({},o,{ref:t})))}),RE=p.forwardRef((e,t)=>{const n=wn(Ci,e.__scopeDialog),r=p.useRef(null),o=Ie(t,n.contentRef,r);return p.useEffect(()=>{const i=r.current;if(i)return Wf(i)},[]),p.createElement(Dy,Z({},e,{ref:o,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:Ee(e.onCloseAutoFocus,i=>{var s;i.preventDefault(),(s=n.triggerRef.current)===null||s===void 0||s.focus()}),onPointerDownOutside:Ee(e.onPointerDownOutside,i=>{const s=i.detail.originalEvent,a=s.button===0&&s.ctrlKey===!0;(s.button===2||a)&&i.preventDefault()}),onFocusOutside:Ee(e.onFocusOutside,i=>i.preventDefault())}))}),PE=p.forwardRef((e,t)=>{const n=wn(Ci,e.__scopeDialog),r=p.useRef(!1),o=p.useRef(!1);return p.createElement(Dy,Z({},e,{ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:i=>{var s;if((s=e.onCloseAutoFocus)===null||s===void 0||s.call(e,i),!i.defaultPrevented){var a;r.current||(a=n.triggerRef.current)===null||a===void 0||a.focus(),i.preventDefault()}r.current=!1,o.current=!1},onInteractOutside:i=>{var s,a;(s=e.onInteractOutside)===null||s===void 0||s.call(e,i),i.defaultPrevented||(r.current=!0,i.detail.originalEvent.type==="pointerdown"&&(o.current=!0));const l=i.target;((a=n.triggerRef.current)===null||a===void 0?void 0:a.contains(l))&&i.preventDefault(),i.detail.originalEvent.type==="focusin"&&o.current&&i.preventDefault()}}))}),Dy=p.forwardRef((e,t)=>{const{__scopeDialog:n,trapFocus:r,onOpenAutoFocus:o,onCloseAutoFocus:i,...s}=e,a=wn(Ci,n),l=p.useRef(null),u=Ie(t,l);return jf(),p.createElement(p.Fragment,null,p.createElement(zf,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:o,onUnmountAutoFocus:i},p.createElement(Ff,Z({role:"dialog",id:a.contentId,"aria-describedby":a.descriptionId,"aria-labelledby":a.titleId,"data-state":Iy(a.open)},s,{ref:u,onDismiss:()=>a.onOpenChange(!1)}))),!1)}),Ly="DialogTitle",_E=p.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=wn(Ly,n);return p.createElement(Te.h2,Z({id:o.titleId},r,{ref:t}))}),kE="DialogDescription",NE=p.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=wn(kE,n);return p.createElement(Te.p,Z({id:o.descriptionId},r,{ref:t}))}),OE="DialogClose",TE=p.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=wn(OE,n);return p.createElement(Te.button,Z({type:"button"},r,{ref:t,onClick:Ee(e.onClick,()=>o.onOpenChange(!1))}))});function Iy(e){return e?"open":"closed"}const AE="DialogTitleWarning",[ME,i_]=ib(AE,{contentName:Ci,titleName:Ly,docsSlug:"dialog"}),DE=xE,LE=CE,IE=bE,FE=EE,jE=_E,zE=NE,Fy=TE,VE="AlertDialog",[HE,s_]=_i(VE,[Ty]),Lr=Ty(),UE=e=>{const{__scopeAlertDialog:t,...n}=e,r=Lr(t);return p.createElement(DE,Z({},r,n,{modal:!0}))},BE=e=>{const{__scopeAlertDialog:t,...n}=e,r=Lr(t);return p.createElement(LE,Z({},r,n))},WE=p.forwardRef((e,t)=>{const{__scopeAlertDialog:n,...r}=e,o=Lr(n);return p.createElement(IE,Z({},o,r,{ref:t}))}),jy="AlertDialogContent",[GE,KE]=HE(jy),QE=p.forwardRef((e,t)=>{const{__scopeAlertDialog:n,children:r,...o}=e,i=Lr(n),s=p.useRef(null),a=Ie(t,s),l=p.useRef(null);return p.createElement(ME,{contentName:jy,titleName:YE,docsSlug:"alert-dialog"},p.createElement(GE,{scope:n,cancelRef:l},p.createElement(FE,Z({role:"alertdialog"},i,o,{ref:a,onOpenAutoFocus:Ee(o.onOpenAutoFocus,u=>{var c;u.preventDefault(),(c=l.current)===null||c===void 0||c.focus({preventScroll:!0})}),onPointerDownOutside:u=>u.preventDefault(),onInteractOutside:u=>u.preventDefault()}),p.createElement(Tv,null,r),!1)))}),YE="AlertDialogTitle",qE=p.forwardRef((e,t)=>{const{__scopeAlertDialog:n,...r}=e,o=Lr(n);return p.createElement(jE,Z({},o,r,{ref:t}))}),XE=p.forwardRef((e,t)=>{const{__scopeAlertDialog:n,...r}=e,o=Lr(n);return p.createElement(zE,Z({},o,r,{ref:t}))}),ZE=p.forwardRef((e,t)=>{const{__scopeAlertDialog:n,...r}=e,o=Lr(n);return p.createElement(Fy,Z({},o,r,{ref:t}))}),JE="AlertDialogCancel",eR=p.forwardRef((e,t)=>{const{__scopeAlertDialog:n,...r}=e,{cancelRef:o}=KE(JE,n),i=Lr(n),s=Ie(t,o);return p.createElement(Fy,Z({},i,r,{ref:s}))}),tR=UE,zy=BE,Vy=WE,Hy=QE,Uy=ZE,By=eR,Wy=qE,Gy=XE,nR=tR,Ky=({className:e,...t})=>S.jsx(zy,{className:pe(e),...t});Ky.displayName=zy.displayName;const Qy=p.forwardRef(({className:e,children:t,...n},r)=>S.jsx(Vy,{className:pe("data-[state=open]:fade-in data-[state=closed]:fade-out fixed inset-0 z-50 backdrop-blur-md",e),...n,ref:r}));Qy.displayName=Vy.displayName;const Yy=p.forwardRef(({className:e,...t},n)=>S.jsxs(Ky,{children:[S.jsx(Qy,{}),S.jsx(Hy,{ref:n,className:pe("data-[state=open]:fade-in data-[state=closed]:fade-out shadow-card fixed left-1/2 top-1/2 z-50 grid w-11/12 max-w-lg -translate-x-1/2 -translate-y-1/2 gap-4 rounded-lg border border-accent-200 bg-white p-4",e),...t})]}));Yy.displayName=Hy.displayName;const qy=({className:e,...t})=>S.jsx("div",{className:pe("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...t});qy.displayName="AlertDialogFooter";const rR=p.forwardRef(({className:e,...t},n)=>S.jsx(Wy,{ref:n,className:pe("text-lg font-semibold",e),...t}));rR.displayName=Wy.displayName;const oR=p.forwardRef(({className:e,...t},n)=>S.jsx(Gy,{ref:n,className:pe("text-sm",e),...t}));oR.displayName=Gy.displayName;const iR=p.forwardRef(({className:e,...t},n)=>S.jsx(Uy,{ref:n,className:pe(e),...t}));iR.displayName=Uy.displayName;const sR=p.forwardRef(({className:e,...t},n)=>S.jsx(By,{ref:n,className:pe("mt-2 sm:mt-0",e),...t}));sR.displayName=By.displayName;function aR({row:e}){const[t,n]=p.useState(!1),[r,o]=p.useState(""),[i,s]=p.useState(""),[a,l]=p.useState(""),[u,c]=p.useState(!1),[d,f]=p.useState(!1),[h,y]=p.useState(),v=R=>{var O;y((O=R.target.files)==null?void 0:O[0])},x=Zs(),m=md({mutationFn:async()=>{await iu({url:"/api/eops/online-reconciliation/record-item",method:"PATCH",payload:{recordId:e.original._id,reconciliationId:e.original.recordId,rejectionReason:a,note:i}})},onSuccess:()=>{Rl.success(F("record-item-success")),x.invalidateQueries({queryKey:["record-items"]}),x.invalidateQueries({queryKey:["reconciliation-details"]})},onError:R=>{Rl.error(F(R.message))}}),g=md({mutationFn:async()=>{const R=new FormData;R.append("file",h),R.append("recordId",e.original._id),await fetch(`${Js}/api/eops/online-reconciliation/file-upload`,{method:"POST",body:R})}}),w=async()=>{var O;c(!1),f(!1),r.trim()||c(!0),i.trim()||f(!0);const R=(O=Fl.find(T=>T.value===r))==null?void 0:O.text;!r.trim()||!i.trim()||!R||(l(F(R)),h&&await g.mutateAsync(),await m.mutateAsync(),n(!1))},{data:C}=Qn(),E=pS({filters:{status:"pending"}}),_=e.original.status!=="waiting-for-approval"||E.length>0||(C==null?void 0:C.isExpired),P=m.isPending||g.isPending||_;return S.jsxs(nR,{open:t,children:[S.jsx("button",{onClick:()=>n(!0),className:"btn btn-success px-4 py-2",disabled:e.original.status!=="waiting-for-approval"||_,children:F("transaction")}),S.jsxs(Yy,{children:[S.jsxs("fieldset",{className:"grid gap-4 py-4",disabled:P,children:[S.jsxs("div",{className:"gap-2 md:grid md:grid-cols-12",children:[S.jsxs("span",{className:"font-semibold md:col-span-4",children:[F("document-no"),":"]}),S.jsx("span",{className:"md:col-span-8",children:e.original.documentNo})]}),S.jsxs("div",{className:"gap-2 md:grid md:grid-cols-12",children:[S.jsxs("span",{className:"font-semibold md:col-span-4",children:[F("account-slip-no"),":"]}),S.jsx("span",{className:"md:col-span-8",children:e.original.voucherNo})]}),S.jsxs("div",{className:"grid gap-2 md:grid-cols-12",children:[S.jsx("p",{className:"font-semibold md:col-span-4",children:F("deny-reason")}),S.jsxs(yy,{disabled:P,onValueChange:R=>{o(R),c(!1)},children:[S.jsx(Yf,{className:pe("md:col-span-8",u&&"border-red-600"),children:S.jsx(xy,{})}),S.jsx(qf,{children:S.jsxs(wy,{children:[S.jsx(Xf,{children:F("choose-deny-reason")}),Fl.map(R=>S.jsx(Zf,{value:R.value,children:F(R.text)},R.value))]})})]})]}),S.jsxs("div",{className:"grid gap-2 md:grid-cols-12",children:[S.jsx("p",{className:"font-semibold md:col-span-4",children:F("reconciliation-note")}),S.jsx(Jf,{className:pe("col-span-8 resize-none",d&&"border-red-600"),onChange:R=>{s(R.target.value),R.target.value.trim().length>0&&f(!1)}})]}),S.jsxs("div",{className:"grid gap-2 md:grid-cols-12",children:[S.jsx("p",{className:"font-semibold md:col-span-4",children:F("file")}),S.jsx(ep,{type:"file",className:"col-span-8",onChange:v})]})]}),S.jsxs(qy,{children:[S.jsx("button",{onClick:()=>{c(!1),f(!1),o(""),s(""),n(!1)},className:"btn border bg-white px-4 py-2 text-black hover:bg-accent",disabled:P,children:F("cancel")}),S.jsxs("button",{onClick:w,className:"btn btn-danger group inline-flex items-center justify-center border border-transparent px-4 py-2",disabled:P,children:[S.jsx("span",{className:"loading-spinner absolute h-5 w-5 border-white border-r-green-700 group-enabled:opacity-0"}),S.jsx("span",{className:"group-disabled:opacity-0",children:F("deny-record")})]})]})]})]})}const Th={"waiting-for-approval":{key:"waiting-for-approval",color:"bg-gray-200 text-gray-700"},approved:{key:"approved",color:"bg-green-200 text-green-700"},denied:{key:"denied",color:"bg-red-200 text-red-700"}};function lR(){const e=Dr("locale")||"en";return p.useMemo(()=>[{header:"No",id:"id",cell:({row:n,table:r})=>{var o,i;return S.jsx("p",{className:"text-center",children:(((i=(o=r.getSortedRowModel())==null?void 0:o.flatRows)==null?void 0:i.findIndex(s=>s.id===n.id))||0)+1})}},{header:"",accessorKey:"_id",size:96,cell:({row:n})=>S.jsx(aR,{row:n})},{header:F("document-no"),accessorKey:"documentNo"},{header:F("issue-date"),accessorKey:"issueDate",cell:({row:n})=>S.jsx("p",{className:"whitespace-nowrap",children:new Date(n.original.issueDate).toLocaleDateString(e)})},{header:F("due-date"),accessorKey:"dueDate",cell:({row:n})=>S.jsx("p",{className:"whitespace-nowrap",children:new Date(n.original.dueDate).toLocaleDateString(e)})},
 {header: F("overdue-days"), accessorKey: "overdueDays", cell: ({ row: n }) => { const dueDate = n.original.dueDate; let overdueDays = 0; if (dueDate && new Date(dueDate)) {const today = new Date(); const dueDateObj = new Date(dueDate); today.setHours(0, 0, 0, 0); dueDateObj.setHours(0, 0, 0, 0); const diffTime = today - dueDateObj; overdueDays = Math.round(diffTime / (1000 * 60 * 60 * 24));} return S.jsx("p", {className: `whitespace-nowrap ${overdueDays > 0 ? 'text-red-600 font-semibold' : ''}`, children: overdueDays});}},{header:F("voucher-no"),accessorKey:"voucherNo",cell:({row:n})=>S.jsx("p",{className:"whitespace-nowrap",children:n.original.voucherNo})},{header:F("description"),accessorKey:"description",cell:({row:n})=>S.jsx("p",{className:"min-w-80",children:n.original.description})},{header:F("reference"),accessorKey:"reference"},{header:F("status"),accessorKey:"status",cell:({row:n})=>{var r,o;return S.jsx("span",{className:pe("whitespace-nowrap rounded-full p-2 text-xs font-semibold",(r=Th[n.original.status??"waiting-for-approval"])==null?void 0:r.color),children:F((o=Th[n.original.status??"waiting-for-approval"])==null?void 0:o.key)})}},{header:F("currency"),accessorKey:"currency.name"},{header:F("debit"),accessorKey:"debit",cell:({row:n})=>S.jsxs("p",{className:"whitespace-nowrap text-right font-bold text-green-600",children:[zr(e,n.original.debit),'₺']})},{header:F("credit"),accessorKey:"credit",cell:({row:n})=>S.jsxs("p",{className:"whitespace-nowrap text-right font-bold text-red-600",children:[zr(e,n.original.credit),n.original.currency.symbol]})},{header:F("balance"),accessorKey:"balance",cell:({row:n})=>S.jsxs("p",{className:"whitespace-nowrap text-right font-bold",children:[zr(e,n.original.balance),'₺']})},{header:F("debit-fc"),accessorKey:"debitFC",cell:({row:n})=>S.jsxs("p",{className:"whitespace-nowrap text-right font-bold text-green-600",children:[zr(e,n.original.debitFC),n.original.currency.symbol]})},{header:F("credit-fc"),accessorKey:"creditFC",cell:({row:n})=>S.jsxs("p",{className:"whitespace-nowrap text-right font-bold text-red-600",children:[zr(e,n.original.creditFC),n.original.currency.symbol]})},{header:F("balance-fc"),accessorKey:"balanceFC",cell:({row:n})=>S.jsxs("p",{className:"whitespace-nowrap text-right font-bold",children:[zr(e,n.original.balanceFC),n.original.currency.symbol]})}],[])}const Xy="Popover",[Zy,a_]=_i(Xy,[lu]),op=lu(),[uR,Ti]=Zy(Xy),cR=e=>{const{__scopePopover:t,children:n,open:r,defaultOpen:o,onOpenChange:i,modal:s=!1}=e,a=op(t),l=p.useRef(null),[u,c]=p.useState(!1),[d=!1,f]=Il({prop:r,defaultProp:o,onChange:i});return p.createElement(qv,a,p.createElement(uR,{scope:t,contentId:ro(),triggerRef:l,open:d,onOpenChange:f,onOpenToggle:p.useCallback(()=>f(h=>!h),[f]),hasCustomAnchor:u,onCustomAnchorAdd:p.useCallback(()=>c(!0),[]),onCustomAnchorRemove:p.useCallback(()=>c(!1),[]),modal:s},n))},dR="PopoverTrigger",fR=p.forwardRef((e,t)=>{const{__scopePopover:n,...r}=e,o=Ti(dR,n),i=op(n),s=Ie(t,o.triggerRef),a=p.createElement(Te.button,Z({type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":t1(o.open)},r,{ref:s,onClick:Ee(e.onClick,o.onOpenToggle)}));return o.hasCustomAnchor?a:p.createElement(Xv,Z({asChild:!0},i),a)}),Jy="PopoverPortal",[pR,gR]=Zy(Jy,{forceMount:void 0}),hR=e=>{const{__scopePopover:t,forceMount:n,children:r,container:o}=e,i=Ti(Jy,t);return p.createElement(pR,{scope:t,forceMount:n},p.createElement(Oi,{present:n||i.open},p.createElement(Bf,{asChild:!0,container:o},r)))},Ms="PopoverContent",mR=p.forwardRef((e,t)=>{const n=gR(Ms,e.__scopePopover),{forceMount:r=n.forceMount,...o}=e,i=Ti(Ms,e.__scopePopover);return p.createElement(Oi,{present:r||i.open},i.modal?p.createElement(vR,Z({},o,{ref:t})):p.createElement(yR,Z({},o,{ref:t})))}),vR=p.forwardRef((e,t)=>{const n=Ti(Ms,e.__scopePopover),r=p.useRef(null),o=Ie(t,r),i=p.useRef(!1);return p.useEffect(()=>{const s=r.current;if(s)return Wf(s)},[]),p.createElement(Gf,{as:co,allowPinchZoom:!0},p.createElement(e1,Z({},e,{ref:o,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:Ee(e.onCloseAutoFocus,s=>{var a;s.preventDefault(),i.current||(a=n.triggerRef.current)===null||a===void 0||a.focus()}),onPointerDownOutside:Ee(e.onPointerDownOutside,s=>{const a=s.detail.originalEvent,l=a.button===0&&a.ctrlKey===!0,u=a.button===2||l;i.current=u},{checkForDefaultPrevented:!1}),onFocusOutside:Ee(e.onFocusOutside,s=>s.preventDefault(),{checkForDefaultPrevented:!1})})))}),yR=p.forwardRef((e,t)=>{const n=Ti(Ms,e.__scopePopover),r=p.useRef(!1),o=p.useRef(!1);return p.createElement(e1,Z({},e,{ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:i=>{var s;if((s=e.onCloseAutoFocus)===null||s===void 0||s.call(e,i),!i.defaultPrevented){var a;r.current||(a=n.triggerRef.current)===null||a===void 0||a.focus(),i.preventDefault()}r.current=!1,o.current=!1},onInteractOutside:i=>{var s,a;(s=e.onInteractOutside)===null||s===void 0||s.call(e,i),i.defaultPrevented||(r.current=!0,i.detail.originalEvent.type==="pointerdown"&&(o.current=!0));const l=i.target;((a=n.triggerRef.current)===null||a===void 0?void 0:a.contains(l))&&i.preventDefault(),i.detail.originalEvent.type==="focusin"&&o.current&&i.preventDefault()}}))}),e1=p.forwardRef((e,t)=>{const{__scopePopover:n,trapFocus:r,onOpenAutoFocus:o,onCloseAutoFocus:i,disableOutsidePointerEvents:s,onEscapeKeyDown:a,onPointerDownOutside:l,onFocusOutside:u,onInteractOutside:c,...d}=e,f=Ti(Ms,n),h=op(n);return jf(),p.createElement(zf,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:o,onUnmountAutoFocus:i},p.createElement(Ff,{asChild:!0,disableOutsidePointerEvents:s,onInteractOutside:c,onEscapeKeyDown:a,onPointerDownOutside:l,onFocusOutside:u,onDismiss:()=>f.onOpenChange(!1)},p.createElement(Zv,Z({"data-state":t1(f.open),role:"dialog",id:f.contentId},h,d,{ref:t,style:{...d.style,"--radix-popover-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-popover-content-available-width":"var(--radix-popper-available-width)","--radix-popover-content-available-height":"var(--radix-popper-available-height)","--radix-popover-trigger-width":"var(--radix-popper-anchor-width)","--radix-popover-trigger-height":"var(--radix-popper-anchor-height)"}}))))});function t1(e){return e?"open":"closed"}const wR=cR,xR=fR,SR=hR,n1=mR,CR=wR,bR=xR,r1=p.forwardRef(({className:e,align:t="center",sideOffset:n=4,...r},o)=>S.jsx(SR,{children:S.jsx(n1,{ref:o,align:t,sideOffset:n,className:pe("shadow-card z-50 w-72 rounded-md border bg-white p-4 outline-none",e),...r})}));r1.displayName=n1.displayName;function $R({table:e}){return S.jsx("div",{className:"flex justify-end",children:S.jsxs(CR,{children:[S.jsx(bR,{asChild:!0,children:S.jsxs("button",{className:"btn inline-flex items-center gap-1 px-4 py-1.5 text-sm hover:bg-accent data-[state=open]:bg-accent",children:[S.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:2,stroke:"currentColor",className:"h-4 w-4",children:S.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 3c2.755 0 5.455.232 8.083.678.533.09.917.556.917 1.096v1.044a2.25 2.25 0 01-.659 1.591l-5.432 5.432a2.25 2.25 0 00-.659 1.591v2.927a2.25 2.25 0 01-1.244 2.013L9.75 21v-6.568a2.25 2.25 0 00-.659-1.591L3.659 7.409A2.25 2.25 0 013 5.818V4.774c0-.54.384-1.006.917-1.096A48.32 48.32 0 0112 3z"})}),S.jsx("span",{children:F("filter")})]})}),S.jsx(r1,{align:"end",className:"grid w-fit grid-cols-2 place-items-start gap-2",children:e.getAllColumns().filter(t=>t.getCanHide()).map((t,n)=>{var r;if(n!==0)return S.jsxs("button",{className:"btn inline-flex w-full items-center gap-3 px-2 py-1.5 text-sm hover:border-primary",onClick:()=>t.toggleVisibility(),children:[S.jsx("div",{className:"inline-flex h-4 w-4 items-center rounded-full border",children:t.getIsVisible()&&S.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:S.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M4.5 12.75l6 6 9-13.5"})})}),(r=t.columnDef.header)==null?void 0:r.toString()]},t.id)})})]})})}const Ah=20;function o1(){const e=Dr("reconciliationId");return hS({queryKey:["record-items"],queryFn:async({pageParam:t=0})=>await iu({url:"/api/eops/online-reconciliation/record-items",method:"POST",payload:{skip:t,limit:Ah,reconciliationId:e}}),getNextPageParam:({hasNextPage:t,skip:n})=>t?n+Ah:void 0,select:t=>{var n;return((n=t.pages)==null?void 0:n.flatMap(r=>r==null?void 0:r.data))??[]},initialPageParam:0,enabled:!!e&&e.length>0})}function ER(){const[e,t]=p.useState(!1),n=Dr("reconciliationId");async function r(){t(!0);try{const o=await fetch(`${Js}/api/eops/online-reconciliation/excel-export/${n}`),i=o.headers.get("Content-Disposition"),s=(i==null?void 0:i.split(";")[1].split("=")[1])??F("reconciliation-records"),a=await o.blob(),l=URL.createObjectURL(a),u=document.createElement("a");u.href=l,u.download=decodeURI(s),document.body.appendChild(u),u.click(),u.remove(),URL.revokeObjectURL(l)}catch{}t(!1)}return S.jsx("div",{className:"flex justify-center",children:S.jsxs("button",{onClick:()=>r(),className:"btn group relative inline-flex items-center justify-center gap-2 border-primary bg-primary px-4 py-1.5 text-sm text-white hover:border-primary-500 hover:bg-primary-500",disabled:e,children:[S.jsx("span",{className:"loading-spinner absolute h-5 w-5 border-white border-r-green-700 group-enabled:opacity-0"}),S.jsx("svg",{className:"h-4 w-4 group-disabled:opacity-0",viewBox:"0 0 15 15",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:S.jsx("path",{d:"M7.50005 1.04999C7.74858 1.04999 7.95005 1.25146 7.95005 1.49999V8.41359L10.1819 6.18179C10.3576 6.00605 10.6425 6.00605 10.8182 6.18179C10.994 6.35753 10.994 6.64245 10.8182 6.81819L7.81825 9.81819C7.64251 9.99392 7.35759 9.99392 7.18185 9.81819L4.18185 6.81819C4.00611 6.64245 4.00611 6.35753 4.18185 6.18179C4.35759 6.00605 4.64251 6.00605 4.81825 6.18179L7.05005 8.41359V1.49999C7.05005 1.25146 7.25152 1.04999 7.50005 1.04999ZM2.5 10C2.77614 10 3 10.2239 3 10.5V12C3 12.5539 3.44565 13 3.99635 13H11.0012C11.5529 13 12 12.5528 12 12V10.5C12 10.2239 12.2239 10 12.5 10C12.7761 10 13 10.2239 13 10.5V12C13 13.1041 12.1062 14 11.0012 14H3.99635C2.89019 14 2 13.103 2 12V10.5C2 10.2239 2.22386 10 2.5 10Z",fill:"currentColor",fillRule:"evenodd",clipRule:"evenodd"})}),S.jsx("span",{className:"group-disabled:opacity-0",children:F("export-excel")})]})})}function RR(){var l,u,c;const e=lR(),[t,n]=p.useState([]),[r,o]=p.useState({}),i=o1(),s=Qn(),a=mE({data:i.data??[],columns:e,onSortingChange:n,getSortedRowModel:fE(),onColumnVisibilityChange:o,getCoreRowModel:dE(),state:{sorting:t,columnVisibility:r}});return S.jsx(ks,{title:F("document-info"),children:S.jsxs("div",{className:"card-padding",children:[S.jsxs("div",{className:"mb-4 flex flex-col items-center justify-between gap-4 md:flex-row",children:[S.jsx("p",{className:"text-sm font-medium",children:F("result-count",{current:(l=i.data)==null?void 0:l.length,total:(u=s.data)==null?void 0:u.documentCount})}),S.jsxs("div",{className:"flex items-center gap-4",children:[S.jsx(ER,{}),S.jsx($R,{table:a})]})]}),S.jsxs(Df,{rootClassName:"max-h-[34rem]",children:[S.jsx(Lf,{children:a.getHeaderGroups().map(d=>S.jsx(Ns,{className:"bg-accent text-xs [&>th]:whitespace-nowrap [&>th]:border",children:d.headers.map(f=>S.jsx(an,{children:Oh(f.column.columnDef.header,f.getContext())},f.id))},d.id))}),S.jsx(If,{children:((c=a.getRowModel().rows)==null?void 0:c.length)>0&&a.getRowModel().rows.map(d=>S.jsx(Ns,{className:"cursor-default bg-white text-sm transition hover:bg-accent/50 [&>td]:border [&>td]:p-4",children:d.getVisibleCells().map(f=>S.jsx(ln,{children:Oh(f.column.columnDef.cell,f.getContext())},f.id))},d.id))})]}),i.hasNextPage&&S.jsx("div",{className:"flex justify-center",children:S.jsxs("button",{onClick:()=>i.fetchNextPage(),className:"btn btn-success group relative mt-4 inline-flex items-center justify-center gap-2 border-primary px-4 py-2.5 text-sm",disabled:i.isFetchingNextPage,children:[S.jsx("span",{className:"loading-spinner absolute h-5 w-5 border-white border-r-primary group-enabled:opacity-0"}),S.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:2,stroke:"currentColor",className:"h-4 w-4 group-disabled:opacity-0",children:S.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0l3.181 3.183a8.25 8.25 0 0013.803-3.7M4.031 9.865a8.25 8.25 0 0113.803-3.7l3.181 3.182m0-4.991v4.99"})}),S.jsx("span",{className:"group-disabled:opacity-0",children:F("load-more")})]})})]})})}function PR(){const{data:e}=Qn();return e!=null&&e.isExpired?S.jsx("div",{className:"shadow-card card-padding flex items-center justify-around gap-4 rounded-md border border-accent-100 bg-yellow-100 text-yellow-700 max-md:flex-wrap md:gap-12",children:F("reconciliation_expired")}):null}function _R(){const{data:e}=Qn(),t=Dr("locale")||"en";return e!=null&&e.periodName?S.jsxs("div",{className:"shadow-card card-padding flex items-center justify-around gap-4 rounded-md border border-blue-600 bg-blue-100 font-bold text-blue-700 max-md:flex-wrap md:gap-12",children:[F("period_description",{period:e.periodName})," (",new Date(e.startDate).toLocaleDateString(t)," -"," ",new Date(e.endDate).toLocaleDateString(t),")"]}):null}function kR(){return S.jsxs("div",{className:"container space-y-6 py-6",children:[S.jsx(PR,{}),S.jsx(_R,{}),S.jsx(N2,{}),S.jsx(ob,{}),S.jsx(RR,{}),S.jsx(k2,{})]})}function NR(){return S.jsx("div",{className:"page-loading",children:S.jsxs("div",{className:"page-loading-wrapper",children:[S.jsx("div",{className:"page-loading-bar"}),S.jsx("div",{className:"page-loading-bar"}),S.jsx("div",{className:"page-loading-bar"}),S.jsx("div",{className:"page-loading-bar"}),S.jsx("div",{className:"page-loading-bar"}),S.jsx("div",{className:"page-loading-bar"})]})})}function Md(){return S.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",width:"280",height:"57.24",viewBox:"0 0 280 57.24",className:"h-8",children:[S.jsx("path",{d:"M99.47 29.87a2.88 2.88 0 0 1-3 2.84H76.36a9.06 9.06 0 0 0 8.51 6.1 11.31 11.31 0 0 0 6.52-2 2.79 2.79 0 0 1 3.83 1.42 2.94 2.94 0 0 1-1.42 3.83 14.25 14.25 0 0 1-8.53 2.55c-6.78 0-13.77-5.23-15-11.9a14.75 14.75 0 0 1 14.45-17.58c7.09.14 14.75 5.24 14.75 14.74ZM76.36 27h16.87a8.57 8.57 0 0 0-8.51-6 8.87 8.87 0 0 0-8.36 6Zm57.71 2.8v11.56a3 3 0 0 1-1.88 2.89 2.78 2.78 0 0 1-3.79-2.71V29.89a9.1 9.1 0 0 0-6.32-8.8A9.24 9.24 0 0 0 110 29.87v11.49a3 3 0 0 1-1.87 2.89 2.79 2.79 0 0 1-3.8-2.71V29.8a14.89 14.89 0 1 1 29.78 0Zm19.68-11.34a2.93 2.93 0 0 1-2.91 2.91h-4v20.19a2.92 2.92 0 0 1-3.12 2.9 2.89 2.89 0 0 1-2.55-3V21.37h-3.8a3.08 3.08 0 0 1-3-2.12 2.78 2.78 0 0 1 2.63-3.56h4.15V4.45a2.91 2.91 0 0 1 3-3 2.79 2.79 0 0 1 2.65 2.75v11.49h4a2.82 2.82 0 0 1 2.95 2.77ZM185 29.87a2.88 2.88 0 0 1-3 2.84h-20.28a9 9 0 0 0 8.5 6.1 11.31 11.31 0 0 0 6.53-2 2.78 2.78 0 0 1 3.82 1.42 3 3 0 0 1-1.41 3.83 14.27 14.27 0 0 1-8.53 2.55c-6.78 0-13.77-5.23-15-11.9a14.75 14.75 0 0 1 14.48-17.59c7.34.15 14.89 5.25 14.89 14.75ZM161.86 27h16.87a8.57 8.57 0 0 0-8.51-6 8.86 8.86 0 0 0-8.36 6Zm44.8-8.75a2.93 2.93 0 0 1-2.91 2.91 8.67 8.67 0 0 0-8.75 8.71V41.3a2.84 2.84 0 1 1-5.67.05V29.87a14.46 14.46 0 0 1 14.38-14.38 2.81 2.81 0 0 1 2.95 2.76Zm31.06 11.62a2.88 2.88 0 0 1-3 2.84h-20.26a9.06 9.06 0 0 0 8.51 6.1 11.27 11.27 0 0 0 6.52-2 2.79 2.79 0 0 1 3.83 1.42 2.94 2.94 0 0 1-1.42 3.83 14.25 14.25 0 0 1-8.52 2.55c-6.79 0-13.78-5.23-15-11.9a14.75 14.75 0 0 1 14.48-17.59c7.34.15 14.86 5.25 14.86 14.75ZM214.6 27h16.88a8.58 8.58 0 0 0-8.51-6 8.87 8.87 0 0 0-8.37 6Zm44.52-8.75a2.93 2.93 0 0 1-2.91 2.91 8.67 8.67 0 0 0-8.71 8.71V41.3a2.84 2.84 0 1 1-5.67.05V29.87a14.46 14.46 0 0 1 14.38-14.38 2.81 2.81 0 0 1 2.91 2.76Zm6.81 23.42v14a2.93 2.93 0 0 1-3 3 2.8 2.8 0 0 1-2.65-2.76V30.19a14.39 14.39 0 1 1 14.39 14.39 15 15 0 0 1-8.74-2.91Zm.43-11.67a8.93 8.93 0 1 0 8.93-8.93 8.9 8.9 0 0 0-8.93 8.93Z",transform:"translate(-9.03 -1.45)",fill:"#282a2d"}),S.jsx("path",{d:"M50.27 48a4.12 4.12 0 0 0 3.9-5.44 4.17 4.17 0 0 0-4-2.78h-4v4.11a4 4 0 0 0 4.1 4.11Z",transform:"translate(-9.03 -1.45)",fill:"#f8aa18",fillRule:"evenodd"}),S.jsx("path",{d:"M54.3 15.26a4.13 4.13 0 0 0-4.88-4.87 4.25 4.25 0 0 0-3.26 4.22v3.92h3.92a4.25 4.25 0 0 0 4.22-3.27Z",transform:"translate(-9.03 -1.45)",fill:"#3eb17f",fillRule:"evenodd"}),S.jsx("path",{d:"M16.67 43.91a4.12 4.12 0 0 0 5.44 3.9 4.17 4.17 0 0 0 2.78-4v-4h-4a4.14 4.14 0 0 0-4.22 4.1Z",transform:"translate(-9.03 -1.45)",fill:"#eb593a",fillRule:"evenodd"}),S.jsx("path",{d:"M20.78 10.31a4.12 4.12 0 0 0-3.9 5.44 4.17 4.17 0 0 0 4 2.78h4v-4.11a4.3 4.3 0 0 0-4.11-4.11",transform:"translate(-9.03 -1.45)",fill:"#00b7d8",fillRule:"evenodd"}),S.jsx("path",{d:"M26.14 22.78H15.41A6.44 6.44 0 0 0 9 29.16a6.44 6.44 0 0 0 6.38 6.38h10.76l6.38-6.38Z",transform:"translate(-9.03 -1.45)",fill:"#00b7d8"}),S.jsx("path",{d:"M44.9 22.78h10.74A6.44 6.44 0 0 1 62 29.16a6.44 6.44 0 0 1-6.38 6.38H44.9l-6.38-6.38Z",transform:"translate(-9.03 -1.45)",fill:"#f8aa18"}),S.jsx("path",{d:"M29.14 38.69v10.73a6.44 6.44 0 0 0 6.38 6.38 6.44 6.44 0 0 0 6.38-6.38V38.69l-6.38-6.38Z",transform:"translate(-9.03 -1.45)",fill:"#eb593a"}),S.jsx("path",{d:"M29.14 19.92V9.19a6.44 6.44 0 0 1 6.38-6.38 6.44 6.44 0 0 1 6.38 6.38v10.73l-6.38 6.39Z",transform:"translate(-9.03 -1.45)",fill:"#3eb17f"})]})}function OR(){return S.jsx("div",{className:"shadow-card flex h-16 items-center border-t border-accent-200 bg-white",children:S.jsxs("div",{className:"container flex items-center justify-between gap-4",children:[S.jsx("p",{className:"text-xs font-medium",children:F("copyright",{date:new Date().getFullYear()})}),S.jsx("a",{href:"https://www.entererp.com",rel:"noopener noreferrer",target:"_blank",className:"transition hover:opacity-70",children:S.jsx(Md,{})})]})})}function TR(){const{data:e}=Qn();return S.jsx("div",{className:"shadow-card sticky top-0 z-50 flex h-16 items-center border-b border-accent-200 bg-white",children:S.jsx("div",{className:"container",children:e!=null&&e.company.logo?S.jsx("img",{src:`${Js}/files/${e==null?void 0:e.company.logo}`,alt:e==null?void 0:e.company.legalName,className:"h-8"}):S.jsx("p",{className:"font-medium",children:e==null?void 0:e.company.legalName})})})}function AR(){const[e,t]=p.useState(!0),n=Dr("locale");p.useEffect(()=>{$C(n),n==="tr"&&(document.title="Online Mutabakat"),t(!1)},[]);const{status:r,error:o}=Qn();return o1(),e?null:r==="pending"?S.jsxs("div",{className:"fixed inset-0 z-50 flex flex-col items-center justify-center gap-6",children:[S.jsx(Md,{}),S.jsx("div",{className:"rounded-md bg-primary px-6 py-2.5 text-white",children:S.jsx("p",{className:"text-lg font-medium",children:F("reconciliation-loading")})}),S.jsx(NR,{})]}):r==="error"?S.jsxs("div",{className:"fixed inset-0 z-50 flex flex-col items-center justify-center gap-6",children:[S.jsx(Md,{}),S.jsx("div",{className:"rounded-md bg-red-600 px-6 py-2.5 text-white",children:S.jsx("p",{className:"text-lg font-medium",children:F(o.message)})}),S.jsx("div",{className:"h-8"})]}):S.jsxs(S.Fragment,{children:[S.jsx(TR,{}),S.jsx(kR,{}),S.jsx(OR,{})]})}const MR=/&(?:amp|#38|lt|#60|gt|#62|apos|#39|quot|#34|nbsp|#160|copy|#169|reg|#174|hellip|#8230|#x2F|#47);/g,DR={"&amp;":"&","&#38;":"&","&lt;":"<","&#60;":"<","&gt;":">","&#62;":">","&apos;":"'","&#39;":"'","&quot;":'"',"&#34;":'"',"&nbsp;":" ","&#160;":" ","&copy;":"©","&#169;":"©","&reg;":"®","&#174;":"®","&hellip;":"…","&#8230;":"…","&#x2F;":"/","&#47;":"/"},LR=e=>DR[e],IR=e=>e.replace(MR,LR);let Mh={bindI18n:"languageChanged",bindI18nStore:"",transEmptyNodeValue:"",transSupportBasicHtmlNodes:!0,transWrapTextNodes:"",transKeepBasicHtmlNodesFor:["br","strong","i","p"],useSuspense:!0,unescape:IR};function FR(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};Mh={...Mh,...e}}const jR={type:"3rdParty",init(e){FR(e.options.react)}},zR="Something went wrong. Please try again later.",VR="© {{date}} Copyright",HR="Title",UR="Phone",BR="Fax",WR="Address",GR="File",KR="Detail",QR="Transaction",YR="Filter",qR="Cancel",XR="Save",ZR="Description",JR="Currency",eP="Debit",tP="Credit",nP="Balance",rP="Reference",oP="Total",iP="Status",sP="Approved",aP="Denied",lP="The reconciliation ID provided is not valid or does not exist.",uP="The requested reconciliation could not be found.",cP="The requested items could not be found.",dP="Amount",fP="Reconciliation data has expired.",pP="One or more fields contain invalid input.",gP="Some records you tried to access is unavailable at this time.",hP="We're sorry, there was an issue updating your information. Please try again later.",mP="The status you selected is invalid.",vP="{{period}} Period Current Account Reconciliation",yP="Period",wP={"reconciliation-loading":"Your reconciliation is being prepared...",common_error:zR,"try-again":"Try Again",copyright:VR,"sender-info":"Sender Information","receiver-info":"Receiver Information",title:HR,"tax-no":"Tax No","tax-department":"Tax Department","e-mail":"E-Mail",phone:UR,fax:BR,address:WR,"reconciliation-info":"Reconciliation Information","document-info":"Document Information","approve-btn":"Approve (Reconcile)","deny-btn":"Deny (Do not Reconcile)","reconciliation-action-info":"Reconciliation Approve / Deny Information","deny-reason":"Deny Reason","choose-deny-reason":"Choose Your Deny Reason","reconciliation-note":"Reconciliation Note",file:GR,"load-more":"Load More",detail:KR,transaction:QR,filter:YR,"document-no":"Document No","account-slip-no":"Account Slip No",cancel:qR,save:XR,"due-date":"Due Date","project-code":"Project Code","project-name":"Project Name",description:ZR,currency:JR,"opening-balance":"Opening Balance",debit:eP,credit:tP,balance:nP,"voucher-no":"Voucher No",reference:rP,"opening-balance-fc":"Opening Balance(FC)","debit-fc":"Debit(FC)","credit-fc":"Credit(FC)","balance-fc":"Balance(FC)","exchange-rate":"Exchange Rate","reconciliation-code":"Reconciliation Code","reconciliation-type":"Reconciliation Type","reconciliation-date":"Reconciliation Date",total:oP,"balance-type":"Balance Type","document-count":"Document Count",status:iP,"last-transaction-date":"Last Transaction Date","expiry-date":"Expiry Date","waiting-for-approval":"Waiting for Approval","partially-denied":"Partially Denied",approved:sP,denied:aP,invalid_reconciliation_id:lP,reconciliation_not_found:uP,"identity-no":"Identity No",record_items_not_found:cP,"issue-date":"Document Date",amount:dP,"result-count":"Displaying {{current}} records out of {{total}}","unprocessed-invoice":"You have an unprocessed invoice","unprocessed-payment":"I have an unprocessed payment","unprocessed-return-invoice":"We have an unprocessed return invoice","incorrect-record-entry":"Incorrect record entry","different-turnover":"There is a difference in the turnover number",reconciliation_expired:fP,invalid_inputs:pP,"reconciliation-records":"Reconciliation Records","export-excel":"Export to Excel","document-success":"The reconciliation process has been completed successfully. Please review the details.","record-item-success":"The reconciliation record details you submitted has been sent successfully.",has_denied_records:gP,failed_to_update:hP,invalid_status:mP,"deny-record":"Deny Record",period_description:vP,period:yP},xP="Bir şeyler yanlış gitti! Lütfen daha sonra tekrar deneyiniz.",SP="© {{date}} Tüm Hakları Saklıdır",CP="Ünvan",bP="Telefon",$P="Fax",EP="Adres",RP="Dosya",PP="Detay",_P="İşlem",kP="Filtrele",NP="İptal",OP="Kaydet",TP="Açıklama",AP="Para Birimi",MP="Borç",DP="Alacak",LP="Bakiye",IP="Referans",FP="Tutar",jP="Durum",zP="Onaylandı",VP="Reddedildi",HP="Sağlanan mutabakat kimliği geçerli değil veya mevcut değil.",UP="Talep edilen mutabakat bulunamadı.",BP="Talep edilen kayıtlar bulunamadı.",WP="Tutar",GP="Mutabakat verilerinin süresi doldu.",KP="One or more fields contain invalid input.",QP="Erişmeye çalıştığınız bazı kayıtlara şu anda ulaşılamıyor.",YP="Üzgünüz, bilgilerinizi güncellerken bir sorun oluştu. Lütfen daha sonra tekrar deneyin.",qP="Seçtiğiniz durum geçersiz.",XP="{{period}} Dönemi Cari Hesap Mutabakatı",ZP="Periyot",JP={"reconciliation-loading":"Mutabakatınız hazırlanıyor...",common_error:xP,"try-again":"Tekrar Dene",copyright:SP,"sender-info":"Gönderici Bilgileri","receiver-info":"Alıcı Bilgileri",title:CP,"tax-no":"Vergi No","tax-department":"Vergi Departmanı","e-mail":"E-Posta",phone:bP,fax:$P,address:EP,"reconciliation-info":"Mutabakat Bilgileri","document-info":"Belge Bilgileri","approve-btn":"Onayla (Mutabıkız)","deny-btn":"Reddet (Mutabık Değiliz)","reconciliation-action-info":"Mutabakat Onay / Red Bilgileri","deny-reason":"Red Nedeni","choose-deny-reason":"Red Nedeninizi Seçiniz","reconciliation-note":"Mutabakat Notu",file:RP,"load-more":"Daha Fazla Yükle",detail:PP,transaction:_P,filter:kP,"document-no":"Belge No","account-slip-no":"Muhasebe Fiş No",cancel:NP,save:OP,"due-date":"Vade Tarihi","project-code":"Proje Kodu","project-name":"Proje Adı",description:TP,currency:AP,"opening-balance":"Açılış Bakiye",debit:MP,credit:DP,balance:LP,"voucher-no":"Muhasebe Fiş No",reference:IP,"opening-balance-fc":"Açılış Bakiye(ypb)","debit-fc":"Borç(ypb)","credit-fc":"Alacak(ypb)","balance-fc":"Bakiye(ypb)","exchange-rate":"Kur Oranı","reconciliation-code":"Mutabakat Kodu","reconciliation-type":"Mutabakat Türü","reconciliation-date":"Mutabakat Tarihi",total:FP,"balance-type":"Bakiye Türü","document-count":"Belge Adedi",status:jP,"last-transaction-date":"Durum Tarihi","expiry-date":"Geçerlilik Tarihi","waiting-for-approval":"Onay Bekliyor","partially-denied":"Kısmi Reddedildi",approved:zP,denied:VP,invalid_reconciliation_id:HP,reconciliation_not_found:UP,"identity-no":"Kimlik No",record_items_not_found:BP,"issue-date":"Belge Tarihi","overdue-days":"Vadeyi Geçen Gün",amount:WP,"result-count":"{{total}} kayıttan {{current}} tanesi görüntüleniyor","unprocessed-invoice":"İşleme alınmamış faturanız var","unprocessed-payment":"İşleme alınmamış ödemem var","unprocessed-return-invoice":"İşleme alınmamış iade faturamız var","incorrect-record-entry":"Yanlış kayıt girişi var","different-turnover":"Devir rakamında fark var",reconciliation_expired:GP,invalid_inputs:KP,"reconciliation-records":"Mutabakat Kayıtları","export-excel":"Excel'e Aktar","document-success":"Mutabakat süreci başarıyla tamamlanmıştır. Lütfen detayları inceleyiniz.","record-item-success":"Göndermiş olduğunuz mutabakat kaydı bilgileri başarıyla iletilmiştir.",has_denied_records:QP,failed_to_update:YP,invalid_status:qP,"deny-record":"Kaydı Reddet",period_description:XP,period:ZP};ot.use(jR).init({lng:"en",fallbackLng:"en",resources:{en:{translation:wP},tr:{translation:JP}}});const e_=new qx({defaultOptions:{queries:{refetchOnWindowFocus:!1,refetchOnMount:!1,staleTime:1e3*30,retry:!1}}});uc.createRoot(document.getElementById("root")).render(S.jsx(A.StrictMode,{children:S.jsxs(tS,{client:e_,children:[S.jsx(AR,{}),S.jsx(rC,{position:"top-right",duration:1e4,richColors:!0,className:"[&>li]:gap-6 [&>li]:p-6 [&>li]:text-base [&_svg]:size-8"})]})}));
