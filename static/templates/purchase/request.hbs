<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8"/>
        <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
        <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
        <title>{{trans 'Purchase Request'}}</title>

        <link rel="preconnect" href="https://fonts.googleapis.com">
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link href="https://fonts.googleapis.com/css2?family=Nunito:ital,wght@0,400;0,600;0,700;1,400;1,600;1,700&display=swap" rel="stylesheet">

        <link rel="stylesheet" href="https://unpkg.com/spectre.css/dist/spectre.min.css"/>
        <link rel="stylesheet" href="https://unpkg.com/spectre.css/dist/spectre-exp.min.css"/>
        <link rel="stylesheet" href="https://unpkg.com/spectre.css/dist/spectre-icons.min.css"/>

        <style>
            body {
                padding: 60px;
                background-color: #f7f8f9;
                font-family: 'Nunito', -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
                font-size: 16px;
            }

            .card {
                margin: 0 auto;
                padding: 1.6rem;
                max-width: 1280px;
            }

            .card > table,
            .card > table > thead,
            .card > table > thead > tr,
            .card > table > thead > tr > td,
            .card > table > tbody,
            .card > table > tbody > tr,
            .card > table > tbody > tr > td {
                display: block;
                width: 100%;
            }

            .card-header {
                display: flex;
                flex-flow: row nowrap;
                align-items: center;
                padding: 0 0 1.5rem 0 !important;
                margin: 0 0 1.5rem 0;
                border-bottom: 1px solid #dadee4;
            }

            .card-header .logo {
                margin-right: 0.75rem;
                height: 60px;
            }

            .card-header .logo img {
                width: auto;
                height: 60px;
            }

            .card-header .details {
                flex: 1 1 0;
            }

            .card-header .qr {
                margin-left: 0.5rem;
                height: 60px;
            }

            .card-header .qr img {
                width: 60px;
                height: 60px;
            }

            .card-body {
                padding: 0 !important;
            }

            .card-body .top-details {
                display: flex;
                flex-flow: row nowrap;
                justify-content: space-between;
                page-break-inside: avoid;
            }

            .card-body .top-details .details-left {
                max-width: 360px;
            }

            .card-body .top-details .details-right .table td {
                padding: 2px 0 !important;
                border-bottom: none;
            }

            .card-body .top-details .details-right .table td:first-child {
                padding-right: 1rem !important;
            }

            .card-body .items-table {
                margin-top: 2rem;
                padding-bottom: 0;
            }

            .card-body .bottom-details {
                display: flex;
                flex-flow: row nowrap;
                justify-content: space-between;
                margin-top: 2rem;
                page-break-inside: avoid;
            }

            .card-body .bottom-details .details-left {
                flex: 1 1 0;
                max-width: 720px;
            }

            .card-body .bottom-details .details-right {
                margin-left: 30px;
            }

            .card-body .bottom-details .details-right .table td {
                padding: 3px 0 !important;
            }

            .card-body .bottom-details .details-right .table td:first-child {
                padding-right: 1rem !important;
            }

            @media only screen and (max-width: 760px) {
                body {
                    padding: 0;
                    background-color: #fff;
                }

                .card {
                    border: none;
                }

                .card {
                    padding: 1rem;
                }

                .card-header {
                    flex-flow: column nowrap;
                    align-items: center;
                    margin: 0 0 1rem 0;
                    padding: 0 0 1rem 0 !important;
                }

                .card-header .logo {
                    margin-right: 0;
                    height: 75px;
                }

                .card-header .logo img {
                    width: auto;
                    height: 75px;
                }

                .card-header .details {
                    flex: auto;
                    margin: 1rem 0;
                }

                .card-header .details .card-title, .card-header .details .card-subtitle {
                    text-align: center;
                }

                .card-header .qr {
                    margin-left: 0;
                    height: 75px;
                }

                .card-header .qr img {
                    width: 75px;
                    height: 75px;
                }

                .card-body .top-details {
                    flex-flow: column nowrap;
                    justify-content: flex-start;
                }

                .card-body .top-details .details-left {
                    max-width: none;
                    margin-bottom: 1rem;
                }

                .card-body .top-details .details-left .columns {
                    flex-flow: column;
                }

                .card-body .top-details .details-left .columns .column {
                    width: 100%;
                }

                .card-body .bottom-details {
                    flex-flow: column nowrap;
                    justify-content: flex-start;
                }

                .card-body .bottom-details .details-left {
                    max-width: none;
                    margin-bottom: 2rem;
                }

                .card-body .bottom-details .details-right {
                    margin-left: 0;
                }
            }

            @media print {
                @page {
                    margin: 1cm;
                    margin-top: 30px;
                    margin-bottom: 30px;
                }

                body {
                    padding: 0 !important;
                    background-color: #fff !important;
                    font-size: 14px;
                    color: #000;
                }

                table {
                    display: table !important;
                    page-break-inside: auto;
                    width: 100%;
                }

                thead {
                    display: table-header-group;
                }

                tfoot {
                    display: table-footer-group;
                }

                td {
                    border-top: 1px solid transparent;
                    border-left: 1px solid transparent;
                    border-right: 1px solid transparent;
                    min-width: auto !important;
                }

                tr {
                    page-break-inside: auto;
                }

                .card {
                    position: relative;
                    margin: 0 !important;
                    padding: 0 !important;
                    border: none !important;
                    border-radius: 0 !important;
                    max-width: initial !important;
                }

                .card > table > thead {
                    display: table-header-group !important;
                    width: 100%;
                }
            }
        </style>
    </head>
    <body>
        <section class="card">
            <table>
                <thead>
                <tr>
                    <td>
                        <div class="card-header">
                            <div class="logo">
                                <img class="s-rounded" src="{{company.logo}}"/>
                            </div>

                            <div class="details">
                                <div class="card-title h5">{{trans 'Purchase Request'}}</div>
                                <div class="card-subtitle">{{code}}</div>
                            </div>

                            {{#if qr}}
                                <div class="qr">
                                    <img class="s-rounded" src="{{qr}}"/>
                                </div>
                            {{/if}}
                        </div>
                    </td>
                </tr>
                </thead>

                <tbody>
                <tr>
                    <td>
                        <div class="card-body">
                            <div class="top-details">
                                <div class="details-left">
                                    <h6><strong>{{partnerName}}</strong></h6>
                                    <p style="margin-bottom: 1rem">{{partner.address}}</p>
                                </div>

                                <div class="details-right">
                                    <table class="table">
                                        <tbody>
                                        <tr>
                                            <td class="text-bold">{{firstUpperAll (trans 'Request date')}}</td>
                                            <td>: {{recordDate}}</td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <table class="items-table table table-striped table-hover table-scroll">
                                <thead>
                                <tr>
                                    <th style="width: 60px">#</th>
                                    <th style="min-width: 210px; width: 100%; white-space: nowrap">
                                        {{ trans 'Description' }}
                                    </th>
                                    <th style="width: 120px; white-space: nowrap">
                                        {{ trans 'Quantity' }}
                                    </th>
                                    <th style="width: 120px; white-space: nowrap">
                                        {{ trans 'U. Price' }}
                                    </th>
                                    <th style="width: 120px; white-space: nowrap">
                                        {{ trans 'Tax' }}
                                    </th>
                                    <th style="width: 120px; white-space: nowrap">
                                        {{ trans 'Total' }}
                                    </th>
                                </tr>
                                </thead>
                                <tbody>
                                {{#each items}}
                                    <tr>
                                        <td style="white-space: nowrap">{{lineNumber @index}}</td>
                                        <td style="min-width: 240px; width: 100%; white-space: normal">{{description}}</td>
                                        <td class="text-right" style="white-space: nowrap">{{quantity}}</td>
                                        <td class="text-right" style="white-space: nowrap">{{unitPrice}}</td>
                                        <td style="white-space: nowrap">{{taxPercentage}}</td>
                                        <td class="text-right" style="white-space: nowrap">{{total}}</td>
                                    </tr>
                                {{/each}}
                                </tbody>
                            </table>

                            <div class="bottom-details">
                                <div class="details-left">
                                    {{#if note}}
                                        <h6><strong>{{trans 'Note'}}</strong></h6>
                                        <p class="mb-0">{{{note}}}</p>
                                    {{/if}}
                                </div>
                                <div class="details-right">
                                    <table class="table">
                                        <tbody>
                                        <tr>
                                            <td class="text-bold">{{trans 'Subtotal'}}</td>
                                            <td class="text-right">{{subTotal}}</td>
                                        </tr>
                                        {{#each appliedTaxes}}
                                            <tr>
                                                {{#if label}}
                                                    <td>{{label}}</td>
                                                {{else}}
                                                    <td>{{name}}</td>
                                                {{/if}}
                                                <td class="text-right">{{amount}}</td>
                                            </tr>
                                        {{/each}}
                                        {{#if rounding}}
                                            <tr>
                                                <td>{{trans 'Rounding'}}</td>
                                                <td class="text-right">{{rounding}}</td>
                                            </tr>
                                        {{/if}}
                                        <tr style="font-weight: bold">
                                            <td>{{trans 'Total'}}</td>
                                            <td class="text-right">{{grandTotal}}</td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </td>
                </tr>
                </tbody>
            </table>
        </section>
    </body>
</html>